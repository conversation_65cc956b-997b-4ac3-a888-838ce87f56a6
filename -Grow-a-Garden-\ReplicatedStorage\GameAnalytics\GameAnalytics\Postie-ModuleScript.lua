-- Full Path: -Grow-a-Garden-\ReplicatedStorage\GameAnalytics\GameAnalytics\Postie-ModuleScript.lua
local u1 = game:GetService("HttpService")
local v2 = game:GetService("RunService")
local v3 = game:GetService("ReplicatedStorage")
if not v3:FindFirstChild("PostieSent") then
    local v4 = Instance.new("RemoteEvent")
    v4.Name = "PostieSent"
    v4.Parent = v3
end
if not v3:FindFirstChild("PostieReceived") then
    local v5 = Instance.new("RemoteEvent")
    v5.Name = "PostieReceived"
    v5.Parent = v3
end
local u6 = v3.PostieSent
local u7 = v3.PostieReceived
local u8 = v2:IsServer()
local u9 = {}
local u10 = {}
local v30 = {
    ["invokeClient"] = function(p11, u12, p13, ...) --[[Function name: invokeClient, line 81]]
        --[[
        Upvalues:
            [1] = u8
            [2] = u1
            [3] = u10
            [4] = u6
        --]]
        local v14 = u8
        assert(v14, "Postie.invokeClient can only be called from the server")
        local u15 = coroutine.running()
        local u16 = false
        local u17 = u1:GenerateGUID(false)
        u10[u17] = function(p18, p19, ...) --[[Anonymous function at line 89]]
            --[[
            Upvalues:
                [1] = u12
                [2] = u16
                [3] = u10
                [4] = u17
                [5] = u15
            --]]
            if p18 == u12 then
                u16 = true
                u10[u17] = nil
                if p19 then
                    task.spawn(u15, true, ...)
                else
                    task.spawn(u15, false)
                end
            else
                return
            end
        end
        task.delay(p13, function() --[[Anonymous function at line 104]]
            --[[
            Upvalues:
                [1] = u16
                [2] = u10
                [3] = u17
                [4] = u15
            --]]
            if not u16 then
                u10[u17] = nil
                task.spawn(u15, false)
            end
        end)
        u6:FireClient(u12, p11, u17, ...)
        return coroutine.yield()
    end,
    ["invokeServer"] = function(p20, p21, ...) --[[Function name: invokeServer, line 118]]
        --[[
        Upvalues:
            [1] = u8
            [2] = u1
            [3] = u10
            [4] = u6
        --]]
        local v22 = not u8
        assert(v22, "Postie.invokeServer can only be called from the client")
        local u23 = coroutine.running()
        local u24 = false
        local u25 = u1:GenerateGUID(false)
        u10[u25] = function(p26, ...) --[[Anonymous function at line 126]]
            --[[
            Upvalues:
                [1] = u24
                [2] = u10
                [3] = u25
                [4] = u23
            --]]
            u24 = true
            u10[u25] = nil
            if p26 then
                task.spawn(u23, true, ...)
            else
                task.spawn(u23, false)
            end
        end
        task.delay(p21, function() --[[Anonymous function at line 137]]
            --[[
            Upvalues:
                [1] = u24
                [2] = u10
                [3] = u25
                [4] = u23
            --]]
            if not u24 then
                u10[u25] = nil
                task.spawn(u23, false)
            end
        end)
        u6:FireServer(p20, u25, ...)
        return coroutine.yield()
    end,
    ["setCallback"] = function(p27, p28) --[[Function name: setCallback, line 151]]
        --[[
        Upvalues:
            [1] = u9
        --]]
        u9[p27] = p28
    end,
    ["getCallback"] = function(p29) --[[Function name: getCallback, line 155]]
        --[[
        Upvalues:
            [1] = u9
        --]]
        return u9[p29]
    end
}
if u8 then
    u7.OnServerEvent:Connect(function(p31, p32, p33, ...) --[[Anonymous function at line 161]]
        --[[
        Upvalues:
            [1] = u10
        --]]
        local v34 = u10[p32]
        if v34 then
            v34(p31, p33, ...)
        end
    end)
    u6.OnServerEvent:Connect(function(p35, p36, p37, ...) --[[Anonymous function at line 170]]
        --[[
        Upvalues:
            [1] = u9
            [2] = u7
        --]]
        local v38 = u9[p36]
        if v38 then
            u7:FireClient(p35, p37, true, v38(p35, ...))
        else
            u7:FireClient(p35, p37, false)
        end
    end)
    return v30
else
    u7.OnClientEvent:Connect(function(p39, p40, ...) --[[Anonymous function at line 180]]
        --[[
        Upvalues:
            [1] = u10
        --]]
        local v41 = u10[p39]
        if v41 then
            v41(p40, ...)
        end
    end)
    u6.OnClientEvent:Connect(function(p42, p43, ...) --[[Anonymous function at line 189]]
        --[[
        Upvalues:
            [1] = u9
            [2] = u7
        --]]
        local v44 = u9[p42]
        if v44 then
            u7:FireServer(p43, true, v44(...))
        else
            u7:FireServer(p43, false)
        end
    end)
    return v30
end