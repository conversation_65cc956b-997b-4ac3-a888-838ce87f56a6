-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\ByteNet\process\readRefs-ModuleScript.lua
local u1 = nil
return {
    ["set"] = function(p2) --[[Function name: set, line 6]]
        --[[
        Upvalues:
            [1] = u1
        --]]
        u1 = p2
    end,
    ["get"] = function() --[[Function name: get, line 10]]
        --[[
        Upvalues:
            [1] = u1
        --]]
        return u1
    end
}