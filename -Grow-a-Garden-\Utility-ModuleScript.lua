-- Full Path: -Grow-a-Garden-\\Utility-ModuleScript.lua
local u1 = {}
local v2 = RaycastParams.new()
v2.FilterType = Enum.RaycastFilterType.Include
v2.FilterDescendantsInstances = { workspace }
u1.RayParams = v2
function u1.AddItem(_, u3, p4) --[[Anonymous function at line 9]]
    task.delay(p4, function() --[[Anonymous function at line 10]]
        --[[
        Upvalues:
            [1] = u3
        --]]
        if u3.Parent ~= nil then
            u3:Destroy()
        end
    end)
end
function u1.PlaySound(_, p5, p6) --[[Anonymous function at line 17]]
    --[[
    Upvalues:
        [1] = u1
    --]]
    local v7 = p5:Clone()
    v7.Parent = p6
    v7:Play()
    u1:AddItem(v7, v7.TimeLength)
    return v7
end
function u1.WeldInPlace(_, p8, p9) --[[Anonymous function at line 27]]
    if p8 ~= p9 then
        local v10 = CFrame.new(p8.Position)
        local v11 = Instance.new("ManualWeld")
        v11.Part0 = p8
        v11.Part1 = p9
        v11.C0 = p8.CFrame:inverse() * v10
        v11.C1 = p9.CFrame:inverse() * v10
        v11.Parent = p8
        v11.Name = "Weld"
        return v11
    end
end
function u1.CreateLockPart(_, p12, p13, p14, p15) --[[Anonymous function at line 40]]
    p12:PivotTo(p13)
    local v16
    if p15 then
        v16 = workspace.World.ClientEffects:FindFirstChild(p15)
    else
        v16 = p15
    end
    if not v16 then
        v16 = script.LOCK:Clone()
        v16.Name = p15 or "LOCK"
        v16.Parent = workspace.World.Visuals
        v16.Transparency = 1
    end
    v16:PivotTo(p13)
    if p14 then
        task.delay(p14, v16.Destroy, v16)
    end
    v16.Weld.part1 = p12
    return v16
end
function u1.Round(_, p17, p18, p19) --[[Anonymous function at line 67]]
    local v20 = p19 ^ p18
    local v21 = p17 * v20 + 0.5
    return math.floor(v21) / v20
end
function u1.HasProperty(_, u22, u23) --[[Anonymous function at line 72]]
    local v24, _ = pcall(function() --[[Anonymous function at line 73]]
        --[[
        Upvalues:
            [1] = u22
            [2] = u23
        --]]
        local _ = u22[u23]
    end)
    return v24
end
return u1