-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\ByteNet\dataTypes\uint16-ModuleScript.lua
local v1 = require(script.Parent.Parent.process.bufferWriter)
require(script.Parent.Parent.types)
local u4 = {
    ["write"] = v1.u16,
    ["read"] = function(p2, p3) --[[Function name: read, line 9]]
        return buffer.readu16(p2, p3), 2
    end
}
return function() --[[Anonymous function at line 14]]
    --[[
    Upvalues:
        [1] = u4
    --]]
    return u4
end