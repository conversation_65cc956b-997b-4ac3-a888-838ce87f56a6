-- Full Path: -Grow-a-Garden-\\CutsceneService-ModuleScript.lua
local u1 = Instance.new("ObjectValue")
u1.Parent = game.ReplicatedStorage
u1.Name = "CameraTarget"
u1.Value = nil
local u2 = script:WaitForChild("SkipCutscene"):Clone()
game.TweenService:Create(u2.TextLabel, TweenInfo.new(2), {
    ["TextTransparency"] = 0,
    ["TextStrokeTransparency"] = 0
})
game.TweenService:Create(u2.TextLabel, TweenInfo.new(2), {
    ["TextTransparency"] = 1,
    ["TextStrokeTransparency"] = 1
})
u2.Parent = game.Players.LocalPlayer.PlayerGui
local u3 = false
local u4 = {}
local u5 = {}
local function u21(u6, u7, p8) --[[Anonymous function at line 25]]
    --[[
    Upvalues:
        [1] = u5
        [2] = u4
        [3] = u3
    --]]
    local u9 = require(u6.MarkerEvents)
    u5 = {}
    local v10 = u5
    table.insert(v10, u6)
    local v11 = u5
    table.insert(v11, u7)
    for v12, v13 in p8 do
        local v14 = u7.Rigs:FindFirstChild(v12)
        if v14 then
            local v15 = v14:FindFirstChild("Humanoid") or v14:FindFirstChild("AnimationController", true)
            if v15 then
                u4[v12] = v15:LoadAnimation(v13)
            else
                warn("Didn\'t find controller for " .. v12)
            end
        end
    end
    for v16, u17 in u9 do
        u4.CameraRig:GetMarkerReachedSignal(v16):Connect(function() --[[Anonymous function at line 51]]
            --[[
            Upvalues:
                [1] = u17
                [2] = u6
                [3] = u7
                [4] = u4
            --]]
            u17(u6, u7, u4)
        end)
    end
    for _, v18 in u4 do
        v18:Play()
    end
    print("LOADED AND SETUP!")
    u4.CameraRig.Stopped:Once(function() --[[Anonymous function at line 65]]
        --[[
        Upvalues:
            [1] = u3
            [2] = u9
            [3] = u6
            [4] = u4
        --]]
        if u3 == true then
            u9._OnEnd(u6)
        end
        u3 = false
        print("ANIMATION ENDED")
        for _, v19 in u4 do
            v19:Stop()
        end
    end)
    task.spawn(function() --[[Anonymous function at line 82]]
        --[[
        Upvalues:
            [1] = u3
            [2] = u4
        --]]
        repeat
            task.wait()
        until u3 == false
        for _, v20 in u4 do
            v20:Stop()
        end
    end)
end
local u28 = {
    ["attemptDelayedPreload"] = function(p22) --[[Function name: attemptDelayedPreload, line 95]]
        --[[
        Upvalues:
            [1] = u3
        --]]
        local v23 = require(p22.IDs)
        local _ = p22.CutsceneFolder
        local v24 = {}
        for _, v25 in v23 do
            local v26 = Instance.new("Animation")
            v26.AnimationId = "rbxassetid://" .. tonumber(v25)
            table.insert(v24, v26)
        end
        while true do
            local v27 = u3 == false and v24[1]
            if not v27 then
                break
            end
            game:GetService("ContentProvider"):PreloadAsync({ v27 })
            task.wait(Random.new():NextInteger(1, 80))
            table.remove(v24, 1)
        end
    end
}
local function u36(p29) --[[Anonymous function at line 130]]
    --[[
    Upvalues:
        [1] = u21
    --]]
    local v30 = require(p29.IDs)
    local v31 = p29.CutsceneFolder
    v31.Parent = workspace
    local v32 = {}
    for v33, v34 in v30 do
        local v35 = Instance.new("Animation")
        v35.AnimationId = "rbxassetid://" .. tonumber(v34)
        table.insert(v32, v35)
        v32[v33] = v35
    end
    game:GetService("ContentProvider"):PreloadAsync(v32)
    repeat
        task.wait(0)
    until game:GetService("ContentProvider").RequestQueueSize == 0
    task.wait(2)
    u21(p29, v31, v32)
    return v31
end
local function u61(u37, u38) --[[Anonymous function at line 158]]
    --[[
    Upvalues:
        [1] = u1
        [2] = u3
        [3] = u2
        [4] = u28
        [5] = u5
        [6] = u4
    --]]
    u1.Value = u37.DefaultCameraAttach.Value
    local u39 = require(u37.MarkerEvents)
    local u40 = require(u37.FoV)
    workspace.CurrentCamera.CameraType = Enum.CameraType.Scriptable
    local u41 = workspace.CurrentCamera
    local u42 = u41.FieldOfView
    local u43 = 0
    game:GetService("RunService"):BindToRenderStep("CameraTargetUpdate", Enum.RenderPriority.Camera.Value, function(p44) --[[Anonymous function at line 179]]
        --[[
        Upvalues:
            [1] = u3
            [2] = u1
            [3] = u43
            [4] = u40
            [5] = u41
            [6] = u42
        --]]
        if u3 == false then
            game:GetService("RunService"):UnbindFromRenderStep("CameraTargetUpdate")
            game.Players.LocalPlayer.Character.Humanoid.AutoRotate = true
            workspace.CurrentCamera.CameraType = Enum.CameraType.Custom
            workspace.CurrentCamera.FieldOfView = 70
            u1.Value = nil
        else
            workspace.CurrentCamera.CFrame = u1.Value.CFrame
            game.Players.LocalPlayer.Character.Humanoid.AutoRotate = false
            u43 = u43 + p44 * 75
            local v45 = u43
            local v46 = u40[math.ceil(v45)]
            if v46 ~= nil then
                local v47 = u43
                if math.ceil(v47) >= #u40 then
                    return
                end
                u41.FieldOfView = u42
                local v48 = u42
                local v49 = p44 * 10
                u42 = v48 + (v46 - v48) * v49
            end
        end
    end)
    local u50 = {}
    for _, v51 in game.Players.LocalPlayer.PlayerGui:GetChildren() do
        if v51:IsA("LayerCollector") and not v51:HasTag("ShowDuringCutscene") then
            u50[v51] = v51.Enabled
            v51.Enabled = false
        end
    end
    task.spawn(function() --[[Anonymous function at line 223]]
        --[[
        Upvalues:
            [1] = u3
            [2] = u2
        --]]
        task.wait(4)
        if not u3 then
            u2.TextLabel.TextTransparency = 1
            u2.TextLabel.UIStroke.Transparency = 1
        end
    end)
    game.Lighting.Blur.Enabled = false
    local u52 = 0
    local u53 = u37.CutsceneMaxLength.Value or 60
    task.spawn(function() --[[Anonymous function at line 239]]
        --[[
        Upvalues:
            [1] = u52
            [2] = u53
            [3] = u3
            [4] = u50
            [5] = u28
        --]]
        while u52 < u53 and u3 == true do
            u52 = u52 + game:GetService("RunService").Heartbeat:Wait()
        end
        for v54, v55 in u50 do
            if v54:IsDescendantOf(game.Players.LocalPlayer) then
                v54.Enabled = v55
            end
        end
        task.spawn(function() --[[Anonymous function at line 250]]
            local v56 = game.Players.LocalPlayer.PlayerGui
            v56.Bottom_UI.Enabled = true
            v56.GearHover_UI.Enabled = true
            v56.Gift_Notification.Enabled = true
            v56.Hud_UI.Enabled = true
            v56.NightQuest_HUD.Enabled = true
            v56.PetUI.Enabled = true
            v56.PickupTally.Enabled = true
            v56.PlantHover_UI.Enabled = true
            v56.Pop_Effect.Enabled = true
            v56.Rejoin_UI.Enabled = true
            v56.Sheckles_UI.Enabled = true
            v56.Teleport_UI.Enabled = true
            v56.Top_Notification.Enabled = true
            v56.Tutorial_UI.Enabled = true
            if v56:FindFirstChild("TouchGui") then
                v56.TouchGui.Enabled = true
            end
            v56.BackpackGui.Enabled = true
            v56.ProximityPrompts.Enabled = true
        end)
        game.Players.LocalPlayer.Character.Humanoid.AutoRotate = true
        workspace.CurrentCamera.CameraType = Enum.CameraType.Custom
        workspace.CurrentCamera.FieldOfView = 70
        task.delay(5, function() --[[Anonymous function at line 282]]
            game.Lighting.Cutscene.Brightness = 0
        end)
        u28.Stop()
    end)
    task.spawn(function() --[[Anonymous function at line 295]]
        --[[
        Upvalues:
            [1] = u39
            [2] = u37
            [3] = u38
        --]]
        u39._OnStart(u37, u38)
    end)
    repeat
        task.wait()
    until u3 == false
    u3 = false
    for v57, v58 in u50 do
        if v57:IsDescendantOf(game.Players.LocalPlayer) then
            v57.Enabled = v58
        end
    end
    for _, v59 in u5 do
        v59:Destroy()
    end
    for _, v60 in game.CollectionService:GetTagged("RemoveMeIfCutsceneEnd") do
        v60:Destroy()
    end
    u4 = {}
    u5 = {}
    u37:Destroy()
    game.SoundService.Music.Volume = 1
    game.Lighting.Blur.Enabled = true
end
function u28.Play(p62, u63) --[[Anonymous function at line 368]]
    --[[
    Upvalues:
        [1] = u3
        [2] = u36
        [3] = u61
    --]]
    if not u3 then
        print(u63)
        game.SoundService.Music.Volume = 0
        u3 = true
        if u63 then
            task.wait(3)
        end
        local v64 = p62:Clone()
        task.spawn(function() --[[Anonymous function at line 383]]
            --[[
            Upvalues:
                [1] = u63
            --]]
            if u63 then
                task.wait(2)
            end
        end)
        if u3 then
            u61(v64, (u36(v64)))
        end
        task.wait(3)
        u3 = false
    end
end
function u28.Stop() --[[Anonymous function at line 403]]
    --[[
    Upvalues:
        [1] = u3
    --]]
    if u3 then
        task.wait(3)
        u3 = false
        task.wait(2)
        task.spawn(function() --[[Anonymous function at line 413]]
            game.Lighting.Cutscene.Brightness = 0
        end)
        task.spawn(function() --[[Anonymous function at line 418]]
            local v65 = game.Players.LocalPlayer.PlayerGui
            v65.Bottom_UI.Enabled = true
            v65.GearHover_UI.Enabled = true
            v65.Gift_Notification.Enabled = true
            v65.Hud_UI.Enabled = true
            v65.NightQuest_HUD.Enabled = true
            v65.PetUI.Enabled = true
            v65.PickupTally.Enabled = true
            v65.PlantHover_UI.Enabled = true
            v65.Pop_Effect.Enabled = true
            v65.Rejoin_UI.Enabled = true
            v65.Sheckles_UI.Enabled = true
            v65.Teleport_UI.Enabled = true
            v65.Top_Notification.Enabled = true
            v65.Tutorial_UI.Enabled = true
            if v65:FindFirstChild("TouchGui") then
                v65.TouchGui.Enabled = true
            end
            v65.BackpackGui.Enabled = true
            v65.ProximityPrompts.Enabled = true
        end)
        game.Players.LocalPlayer.Character.Humanoid.AutoRotate = true
        workspace.CurrentCamera.CameraType = Enum.CameraType.Custom
        workspace.CurrentCamera.FieldOfView = 70
    end
end
return u28