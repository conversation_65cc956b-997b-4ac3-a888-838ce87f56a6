-- Full Path: -Grow-a-Garden-\ReplicatedStorage\CmdrClient\Types\BrickColor-ModuleScript.lua
local u1 = require(script.Parent.Parent.Shared.Util)
local u2 = u1.MakeFuzzyFinder({
    "White",
    "Grey",
    "Light yellow",
    "Brick yellow",
    "Light green (Mint)",
    "Light reddish violet",
    "Pastel Blue",
    "Light orange brown",
    "Nougat",
    "Bright red",
    "Med. reddish violet",
    "Bright blue",
    "Bright yellow",
    "Earth orange",
    "Black",
    "Dark grey",
    "Dark green",
    "Medium green",
    "Lig. Yellowich orange",
    "Bright green",
    "Dark orange",
    "Light bluish violet",
    "Transparent",
    "Tr. Red",
    "Tr. Lg blue",
    "Tr. Blue",
    "Tr. Yellow",
    "Light blue",
    "Tr. Flu. Reddish orange",
    "Tr. Green",
    "Tr. Flu. Green",
    "Phosph. White",
    "Light red",
    "Medium red",
    "Medium blue",
    "Light grey",
    "Bright violet",
    "Br. yellowish orange",
    "Bright orange",
    "Bright bluish green",
    "Earth yellow",
    "Bright bluish violet",
    "Tr. Brown",
    "Medium bluish violet",
    "Tr. Medi. reddish violet",
    "Med. yellowish green",
    "Med. bluish green",
    "Light bluish green",
    "Br. yellowish green",
    "Lig. yellowish green",
    "Med. yellowish orange",
    "Br. reddish orange",
    "Bright reddish violet",
    "Light orange",
    "Tr. Bright bluish violet",
    "Gold",
    "Dark nougat",
    "Silver",
    "Neon orange",
    "Neon green",
    "Sand blue",
    "Sand violet",
    "Medium orange",
    "Sand yellow",
    "Earth blue",
    "Earth green",
    "Tr. Flu. Blue",
    "Sand blue metallic",
    "Sand violet metallic",
    "Sand yellow metallic",
    "Dark grey metallic",
    "Black metallic",
    "Light grey metallic",
    "Sand green",
    "Sand red",
    "Dark red",
    "Tr. Flu. Yellow",
    "Tr. Flu. Red",
    "Gun metallic",
    "Red flip/flop",
    "Yellow flip/flop",
    "Silver flip/flop",
    "Curry",
    "Fire Yellow",
    "Flame yellowish orange",
    "Reddish brown",
    "Flame reddish orange",
    "Medium stone grey",
    "Royal blue",
    "Dark Royal blue",
    "Bright reddish lilac",
    "Dark stone grey",
    "Lemon metalic",
    "Light stone grey",
    "Dark Curry",
    "Faded green",
    "Turquoise",
    "Light Royal blue",
    "Medium Royal blue",
    "Rust",
    "Brown",
    "Reddish lilac",
    "Lilac",
    "Light lilac",
    "Bright purple",
    "Light purple",
    "Light pink",
    "Light brick yellow",
    "Warm yellowish orange",
    "Cool yellow",
    "Dove blue",
    "Medium lilac",
    "Slime green",
    "Smoky grey",
    "Dark blue",
    "Parsley green",
    "Steel blue",
    "Storm blue",
    "Lapis",
    "Dark indigo",
    "Sea green",
    "Shamrock",
    "Fossil",
    "Mulberry",
    "Forest green",
    "Cadet blue",
    "Electric blue",
    "Eggplant",
    "Moss",
    "Artichoke",
    "Sage green",
    "Ghost grey",
    "Lilac",
    "Plum",
    "Olivine",
    "Laurel green",
    "Quill grey",
    "Crimson",
    "Mint",
    "Baby blue",
    "Carnation pink",
    "Persimmon",
    "Maroon",
    "Gold",
    "Daisy orange",
    "Pearl",
    "Fog",
    "Salmon",
    "Terra Cotta",
    "Cocoa",
    "Wheat",
    "Buttermilk",
    "Mauve",
    "Sunrise",
    "Tawny",
    "Rust",
    "Cashmere",
    "Khaki",
    "Lily white",
    "Seashell",
    "Burgundy",
    "Cork",
    "Burlap",
    "Beige",
    "Oyster",
    "Pine Cone",
    "Fawn brown",
    "Hurricane grey",
    "Cloudy grey",
    "Linen",
    "Copper",
    "Dirt brown",
    "Bronze",
    "Flint",
    "Dark taupe",
    "Burnt Sienna",
    "Institutional white",
    "Mid gray",
    "Really black",
    "Really red",
    "Deep orange",
    "Alder",
    "Dusty Rose",
    "Olive",
    "New Yeller",
    "Really blue",
    "Navy blue",
    "Deep blue",
    "Cyan",
    "CGA brown",
    "Magenta",
    "Pink",
    "Deep orange",
    "Teal",
    "Toothpaste",
    "Lime green",
    "Camo",
    "Grime",
    "Lavender",
    "Pastel light blue",
    "Pastel orange",
    "Pastel violet",
    "Pastel blue-green",
    "Pastel green",
    "Pastel yellow",
    "Pastel brown",
    "Royal purple",
    "Hot pink"
})
local u10 = {
    ["Prefixes"] = "% teamColor",
    ["Transform"] = function(p3) --[[Function name: Transform, line 40]]
        --[[
        Upvalues:
            [1] = u2
        --]]
        local v4 = {}
        for v5, v6 in pairs(u2(p3)) do
            v4[v5] = BrickColor.new(v6)
        end
        return v4
    end,
    ["Validate"] = function(p7) --[[Function name: Validate, line 48]]
        return #p7 > 0, "No valid brick colors with that name could be found."
    end,
    ["Autocomplete"] = function(p8) --[[Function name: Autocomplete, line 52]]
        --[[
        Upvalues:
            [1] = u1
        --]]
        return u1.GetNames(p8)
    end,
    ["Parse"] = function(p9) --[[Function name: Parse, line 56]]
        return p9[1]
    end
}
local u12 = {
    ["Transform"] = u10.Transform,
    ["Validate"] = u10.Validate,
    ["Autocomplete"] = u10.Autocomplete,
    ["Parse"] = function(p11) --[[Function name: Parse, line 66]]
        return p11[1].Color
    end
}
return function(p13) --[[Anonymous function at line 71]]
    --[[
    Upvalues:
        [1] = u10
        [2] = u1
        [3] = u12
    --]]
    p13:RegisterType("brickColor", u10)
    p13:RegisterType("brickColors", u1.MakeListableType(u10, {
        ["Prefixes"] = "% teamColors"
    }))
    p13:RegisterType("brickColor3", u12)
    p13:RegisterType("brickColor3s", u1.MakeListableType(u12))
end