-- Full Path: -Grow-a-Garden-\ReplicatedStorage\CmdrClient\DefaultEventHandlers-ModuleScript.lua
local u1 = game:GetService("StarterGui")
local u2 = require(script.Parent.CmdrInterface.Window)
return function(p3) --[[Anonymous function at line 4]]
    --[[
    Upvalues:
        [1] = u1
        [2] = u2
    --]]
    p3:HandleEvent("Message", function(p4) --[[Anonymous function at line 5]]
        --[[
        Upvalues:
            [1] = u1
        --]]
        u1:SetCore("ChatMakeSystemMessage", {
            ["Text"] = ("[Announcement] %s"):format(p4),
            ["Color"] = Color3.fromRGB(249, 217, 56)
        })
    end)
    p3:HandleEvent("AddLine", function(...) --[[Anonymous function at line 12]]
        --[[
        Upvalues:
            [1] = u2
        --]]
        u2:AddLine(...)
    end)
end