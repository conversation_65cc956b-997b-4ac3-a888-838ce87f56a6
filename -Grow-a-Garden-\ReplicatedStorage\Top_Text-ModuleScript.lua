-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Top_Text-ModuleScript.lua
local v1 = {}
local u2 = game.ReplicatedStorage.NPC_UIS.Talk_UI
local u3 = game.ReplicatedStorage.NPC_UIS.Response_UI
local u4 = game.ReplicatedStorage.NPC_UIS.Option_UI
game:GetService("TweenService")
local u5 = TweenInfo.new(0.2, Enum.EasingStyle.Sine, Enum.EasingDirection.Out, 0, false, 0)
local u6 = game.SoundService.Response_Text
task.wait(1)
function effect(p7, p8, p9)
    --[[
    Upvalues:
        [1] = u6
    --]]
    local v10 = p7.Text:gsub("<.->", "")
    local v11 = string.len(v10)
    local v12 = string.len(v10)
    p7.MaxVisibleGraphemes = 0
    local v13 = p9 and game.SoundService.NPC_SFX:FindFirstChild(p9) or game.SoundService.NPC_Text
    if p8 == true then
        while v11 >= 1 do
            task.wait()
            if v13.TimePosition > 0.07 or v13.Playing == false then
                v13.TimePosition = 0
                v13.Playing = true
                v13.PlaybackSpeed = 1 + math.random(-5, 5) / 100
            end
            v11 = v11 - 1
            p7.MaxVisibleGraphemes = p7.MaxVisibleGraphemes + 1
        end
    else
        while v11 >= 1 do
            task.wait()
            local v14 = v11 / 3
            if math.floor(v14) * 3 == v11 or v11 == v12 then
                local v15 = u6:Clone()
                v15.Parent = game.SoundService
                v15.Name = "SFX"
                v15.PlaybackSpeed = 1 + math.random(-15, 15) / 100
                v15.Playing = true
                game.Debris:AddItem(v15, v15.TimeLength * v15.PlaybackSpeed)
            end
            v11 = v11 - 1
            p7.MaxVisibleGraphemes = p7.MaxVisibleGraphemes + 1
        end
    end
end
function v1.NpcText(p16, p17, p18) --[[Anonymous function at line 61]]
    --[[
    Upvalues:
        [1] = u2
    --]]
    local v19 = p16.Head:FindFirstChild(u2.Name)
    if v19 ~= nil then
        if p18 == true then
            for _, v20 in pairs(v19:GetDescendants()) do
                if v20:IsA("LocalScript") or v20:IsA("Script") then
                    v20:Destroy()
                end
            end
        end
        v19.TextLabel.Text = p17
        effect(v19.TextLabel, true, p16.Name)
        if p18 == true then
            for _, v21 in pairs(u2:GetDescendants()) do
                if v21:IsA("LocalScript") or v21:IsA("Script") then
                    for _, v22 in pairs(v19:GetDescendants()) do
                        if v22.Name == v21.Parent.Name then
                            local v23 = v21:Clone()
                            v23.Parent = v22
                            v23.Enabled = true
                        end
                    end
                end
            end
        end
        return v19
    end
    local v24 = u2:Clone()
    v24.Parent = p16.Head
    v24.TextLabel.Text = p17
    effect(v24.TextLabel, true, p16.Name)
    if p18 == true then
        for _, v25 in pairs(v24:GetDescendants()) do
            if v25:IsA("LocalScript") or v25:IsA("Script") then
                v25.Enabled = true
            end
        end
    end
    return v24
end
local u26 = game:GetService("TweenService")
function v1.ShowChoices(p27, p28) --[[Anonymous function at line 105]]
    --[[
    Upvalues:
        [1] = u4
        [2] = u26
    --]]
    local v29 = 0
    local v30 = {}
    for _, v31 in pairs(p28) do
        v29 = v29 + 1
        local v32 = u4:Clone()
        v32.Parent = p27.PlayerGui.Billboard_UI
        v32.Frame.Frame.Text_Element.Text = "[\"" .. v31 .. "\"]"
        v32.Frame.Frame.TextLabel.Text = "#" .. tostring(v29)
        local v33 = v32.Frame.Frame.Text_Element.UIPadding
        local v34 = TweenInfo.new(0.5, Enum.EasingStyle.Sine, Enum.EasingDirection.Out, 0, false, 0)
        v33.PaddingLeft = UDim.new(string.len(v31) * 0.001 + 0.04, 0)
        local v35 = u26:Create(v33, v34, {
            ["PaddingLeft"] = UDim.new(0, 0)
        })
        v35:Play()
        game.Debris:AddItem(v35, v34.Time)
        table.insert(v30, v32)
        v32.Frame.Frame.Text_Element:SetAttribute("Text", v31)
        task.wait(0.075)
    end
    if v30[1] then
        game:GetService("GamepadService"):EnableGamepadCursor(v30[1])
    end
    return v30
end
function v1.TakeAwayResponses(p36, p37) --[[Anonymous function at line 133]]
    --[[
    Upvalues:
        [1] = u26
        [2] = u5
    --]]
    for _, v38 in pairs(p37.PlayerGui.Billboard_UI:GetChildren()) do
        if v38.Name ~= "UIListLayout" then
            v38:Destroy()
        end
    end
    for _, v39 in pairs(p36.Head:GetChildren()) do
        if v39:IsA("BillboardGui") and (v39.Name == "Response_UI" or v39.Name == "Talk_UI") then
            for _, v40 in pairs(v39:GetChildren()) do
                if v40:IsA("TextLabel") then
                    u26:Create(v40, u5, {
                        ["TextTransparency"] = 1
                    }):Play()
                elseif v40:IsA("ImageLabel") then
                    u26:Create(v40, u5, {
                        ["ImageTransparency"] = 1
                    }):Play()
                end
            end
            game.Debris:AddItem(v39, u5.Time)
        end
    end
end
function v1.RemovePlayerSideFrame(p41) --[[Anonymous function at line 165]]
    for _, v42 in pairs(p41.PlayerGui.Billboard_UI:GetChildren()) do
        if v42.Name ~= "UIListLayout" then
            v42:Destroy()
        end
    end
    game:GetService("GamepadService"):DisableGamepadCursor()
end
function v1.ShowResponse(p43, p44, p45) --[[Anonymous function at line 177]]
    --[[
    Upvalues:
        [1] = u2
    --]]
    local v46 = p43.Head:FindFirstChild(u2.Name)
    if v46 ~= nil then
        if p45 == true then
            for _, v47 in pairs(v46:GetDescendants()) do
                if v47:IsA("LocalScript") or v47:IsA("Script") then
                    v47:Destroy()
                end
            end
        end
        v46.TextLabel.Text = p44
        effect(v46.TextLabel, true)
        if p45 == true then
            for _, v48 in pairs(u2:GetDescendants()) do
                if v48:IsA("LocalScript") or v48:IsA("Script") then
                    for _, v49 in pairs(v46:GetDescendants()) do
                        if v49.Name == v48.Parent.Name then
                            v48.Enabled = false
                            local v50 = v48:Clone()
                            v50.Parent = v49
                            v50.Enabled = true
                        end
                    end
                end
            end
        end
        return v46
    end
    local v51 = u2:Clone()
    v51.Parent = p43.Head
    v51.TextLabel.Text = p44
    effect(v51.TextLabel, true)
    if p45 == true then
        for _, v52 in pairs(v51:GetDescendants()) do
            if v52:IsA("LocalScript") or v52:IsA("Script") then
                v52.Enabled = true
            end
        end
    end
    return v51
end
function v1.PlayerResponse(p53, p54, p55) --[[Anonymous function at line 220]]
    --[[
    Upvalues:
        [1] = u2
        [2] = u3
    --]]
    if p54 ~= nil then
        local v56 = p53.Head:FindFirstChild(u2.Name)
        if v56 ~= nil then
            if p55 == true then
                for _, v57 in pairs(v56:GetDescendants()) do
                    if v57:IsA("LocalScript") or v57:IsA("Script") then
                        v57:Destroy()
                    end
                end
            end
            v56.TextLabel.Text = p54
            effect(v56.TextLabel, false)
            if p55 == true then
                for _, v58 in pairs(u3:GetDescendants()) do
                    if v58:IsA("LocalScript") or v58:IsA("Script") then
                        for _, v59 in pairs(v56:GetDescendants()) do
                            if v59.Name == v58.Parent.Name then
                                v58.Enabled = false
                                local v60 = v58:Clone()
                                v60.Parent = v59
                                v60.Enabled = true
                            end
                        end
                    end
                end
            end
            return v56
        end
        local v61 = u3:Clone()
        v61.Parent = p53.Head
        v61.TextLabel.Text = p54
        effect(v61.TextLabel, false)
        if p55 == true then
            for _, v62 in pairs(v61:GetDescendants()) do
                if v62:IsA("LocalScript") or v62:IsA("Script") then
                    v62.Enabled = true
                end
            end
        end
        return v61
    end
end
return v1