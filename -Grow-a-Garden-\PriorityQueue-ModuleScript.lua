-- Full Path: -Grow-a-Garden-\\PriorityQueue-ModuleScript.lua
local u1 = {}
u1.__index = u1
function u1.new() --[[Anonymous function at line 26]]
    --[[
    Upvalues:
        [1] = u1
    --]]
    local v2 = {
        ["_items"] = {},
        ["_priorities"] = {},
        ["_indices"] = {},
        ["_size"] = 0,
        ["_incomingBatch"] = {
            ["items"] = {},
            ["priorities"] = {},
            ["size"] = 0
        }
    }
    local v3 = u1
    return setmetatable(v2, v3)
end
function u1.addToIncomingBatch(p4, p5, p6) --[[Anonymous function at line 38]]
    local v7 = p4._incomingBatch
    v7.size = v7.size + 1
    v7.items[v7.size] = p5
    v7.priorities[v7.size] = p6
end
function u1.addMultipleToIncomingBatch(p8, p9, p10) --[[Anonymous function at line 45]]
    local v11 = p8._incomingBatch
    local v12 = #p9
    table.move(p9, 1, v12, v11.size + 1, v11.items)
    table.move(p10, 1, v12, v11.size + 1, v11.priorities)
    v11.size = v11.size + v12
end
function u1.enqueueIncomingBatch(p13) --[[Anonymous function at line 53]]
    local v14 = p13._incomingBatch
    if v14.size > 0 then
        p13:batchEnqueue(v14.items, v14.priorities)
    end
end
function u1.clearIncomingBatch(p15) --[[Anonymous function at line 60]]
    local v16 = p15._incomingBatch
    v16.size = 0
    v16.items = {}
    v16.priorities = {}
end
function u1.siftup(p17, p18) --[[Anonymous function at line 67]]
    local v19 = p17._items
    local v20 = p17._priorities
    local v21 = p17._indices
    local v22 = p18 // 2
    while p18 > 1 and v20[p18] < v20[v22] do
        local v23 = v20[v22]
        local v24 = v20[p18]
        v20[p18] = v23
        v20[v22] = v24
        local v25 = v19[v22]
        local v26 = v19[p18]
        v19[p18] = v25
        v19[v22] = v26
        local v27 = v19[p18]
        local v28 = v19[v22]
        v21[v27] = p18
        v21[v28] = v22
        local v29 = v22 // 2
        p18 = v22
        v22 = v29
    end
    return p18
end
function u1.siftdown(p30, p31) --[[Anonymous function at line 81]]
    local v32 = p30._items
    local v33 = p30._priorities
    local v34 = p30._indices
    local v35 = p30._size
    for v36 = p31, 1, -1 do
        local v37 = v36 + v36
        local v38 = v37 + 1
        local v39 = v36
        while v37 <= v35 do
            if v38 <= v35 then
                if v33[v38] >= v33[v37] then
                    v38 = v37
                end
            else
                v38 = v37
            end
            if v33[v38] >= v33[v39] then
                break
            end
            local v40 = v32[v38]
            local v41 = v32[v39]
            v32[v39] = v40
            v32[v38] = v41
            local v42 = v33[v38]
            local v43 = v33[v39]
            v33[v39] = v42
            v33[v38] = v43
            local v44 = v32[v39]
            local v45 = v32[v38]
            v34[v44] = v39
            v34[v45] = v38
            v37 = v38 + v38
            local v46 = v37 + 1
            v39 = v38
            v38 = v46
        end
    end
end
function u1.enqueue(p47, p48, p49) --[[Anonymous function at line 107]]
    local v50 = p47._items
    local v51 = p47._priorities
    local v52 = p47._indices
    if v52[p48] ~= nil then
        return p47
    end
    local v53 = p47._size + 1
    p47._size = v53
    v50[v53] = p48
    v51[v53] = p49
    v52[p48] = v53
    p47:siftup(v53)
    return p47
end
function u1.batchEnqueue(p54, p55, p56) --[[Anonymous function at line 119]]
    local v57 = p54._items
    local v58 = p54._priorities
    local v59 = p54._indices
    local v60 = p54._size
    for v61 = 1, #p55 do
        local v62 = p55[v61]
        local v63 = p56[v61]
        if v62 ~= nil and v59[v62] == nil then
            v60 = v60 + 1
            v57[v60] = v62
            v58[v60] = v63
            v59[v62] = v60
        end
    end
    p54._size = v60
    if v60 > 1 then
        p54:siftdown(v60 // 2)
    end
end
function u1.remove(p64, p65) --[[Anonymous function at line 137]]
    local v66 = p64._indices[p65]
    if v66 == nil then
        return false
    end
    local v67 = p64._size
    local v68 = p64._items
    local v69 = p64._priorities
    local v70 = p64._indices
    v70[p65] = nil
    if v67 == v66 then
        v68[v67] = nil
        v69[v67] = nil
        p64._size = v67 - 1
    else
        local v71 = v68[v67]
        local v72 = v68[v67]
        local v73 = v69[v67]
        v68[v66] = v72
        v69[v66] = v73
        v68[v67] = nil
        v69[v67] = nil
        v70[v71] = v66
        local v74 = v67 - 1
        p64._size = v74
        if v74 > 1 then
            p64:siftdown(p64:siftup(v66))
        end
    end
    return true
end
function u1.update(p75, p76, p77) --[[Anonymous function at line 163]]
    if not p75:remove(p76) then
        return false
    end
    p75:enqueue(p76, p77)
    return true
end
function u1.dequeue(p78) --[[Anonymous function at line 173]]
    local v79 = p78._size
    local v80 = v79 > 0
    assert(v80, "Heap is empty")
    local v81 = p78._items
    local v82 = p78._priorities
    local v83 = p78._indices
    local v84 = v81[1]
    local v85 = v82[1]
    v83[v84] = nil
    if v79 <= 1 then
        v81[1] = nil
        v82[1] = nil
        p78._size = 0
        return v84, v85
    end
    local v86 = v81[v79]
    local v87 = v82[v79]
    v81[1] = v86
    v82[1] = v87
    v81[v79] = nil
    v82[v79] = nil
    v83[v86] = 1
    p78._size = v79 - 1
    p78:siftdown(1)
    return v84, v85
end
function u1.peek(p88) --[[Anonymous function at line 198]]
    return p88._items[1], p88._priorities[1]
end
function u1.peekPriority(p89) --[[Anonymous function at line 202]]
    return p89._priorities[1]
end
function u1.items(p90) --[[Anonymous function at line 206]]
    return p90._items
end
function u1.clear(p91) --[[Anonymous function at line 210]]
    table.clear(p91._indices)
    table.clear(p91._items)
    table.clear(p91._priorities)
    p91._size = 0
end
function u1.len(p92) --[[Anonymous function at line 217]]
    return p92._size
end
function u1.contains(p93, p94) --[[Anonymous function at line 221]]
    return p93._indices[p94] ~= nil
end
function u1.isEmpty(p95) --[[Anonymous function at line 225]]
    return p95._size <= 0
end
return u1