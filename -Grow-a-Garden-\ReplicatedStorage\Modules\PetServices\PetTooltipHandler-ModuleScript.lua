-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\PetServices\PetTooltipHandler-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
local u2 = game:GetService("TweenService")
require(v1.Modules.CreateTagHandler)({
    ["Tag"] = "PetTooltip",
    ["OnInstanceAdded"] = function(p3) --[[Function name: Setup, line 14]]
        --[[
        Upvalues:
            [1] = u2
        --]]
        if p3 then
            local v4 = p3.Parent
            if v4 then
                local v5 = v4.Parent
                if v5 then
                    local v6 = p3:FindFirstChild("TooltipText")
                    v6.TextTransparency = 1
                    p3.BackgroundTransparency = 1
                    local u7 = u2:Create(p3, TweenInfo.new(0.2), {
                        ["BackgroundTransparency"] = 0.4
                    })
                    local u8 = u2:Create(v6, TweenInfo.new(0.2), {
                        ["TextTransparency"] = 0
                    })
                    local u9 = u2:Create(p3, TweenInfo.new(0.2), {
                        ["BackgroundTransparency"] = 1
                    })
                    local u10 = u2:Create(v6, TweenInfo.new(0.2), {
                        ["TextTransparency"] = 1
                    })
                    v5.MouseEnter:Connect(function() --[[Anonymous function at line 39]]
                        --[[
                        Upvalues:
                            [1] = u7
                            [2] = u8
                        --]]
                        u7:Play()
                        u8:Play()
                    end)
                    v5.MouseLeave:Connect(function() --[[Anonymous function at line 44]]
                        --[[
                        Upvalues:
                            [1] = u9
                            [2] = u10
                        --]]
                        u9:Play()
                        u10:Play()
                    end)
                end
            else
                return
            end
        else
            return
        end
    end
})
return {}