-- Full Path: -Grow-a-Garden-\ReplicatedStorage\CmdrClient-ModuleScript.lua
local v1 = game:GetService("RunService")
local v2 = game:GetService("StarterGui")
local v3 = game:GetService("Players").LocalPlayer
local v4 = script:WaitForChild("Shared")
local u5 = require(v4:WaitF<PERSON><PERSON>hild("Util"))
if v1:IsClient() == false then
    print(v1:IsClient())
    warn("Server scripts cannot require the client library. Please require the server library to use Cmdr in your own code.")
    return {}
end
local v6 = {
    ["ReplicatedRoot"] = script,
    ["RemoteFunction"] = script:WaitForChild("CmdrFunction"),
    ["RemoteEvent"] = script:WaitForChild("CmdrEvent"),
    ["ActivationKeys"] = {
        [Enum.KeyCode.F2] = true
    },
    ["Enabled"] = true,
    ["MashToEnable"] = false,
    ["ActivationUnlocksMouse"] = false,
    ["HideOnLostFocus"] = true,
    ["PlaceName"] = "Cmdr",
    ["Util"] = u5,
    ["Events"] = {}
}
local u10 = setmetatable(v6, {
    ["__index"] = function(u7, p8) --[[Function name: __index, line 30]]
        local u9 = u7.Dispatcher[p8]
        if u9 and type(u9) == "function" then
            return function(_, ...) --[[Anonymous function at line 33]]
                --[[
                Upvalues:
                    [1] = u9
                    [2] = u7
                --]]
                return u9(u7.Dispatcher, ...)
            end
        end
    end
})
u10.Registry = require(v4.Registry)(u10)
u10.Dispatcher = require(v4.Dispatcher)(u10)
if v2:WaitForChild("Cmdr") and (wait() and v3:WaitForChild("PlayerGui"):FindFirstChild("Cmdr") == nil) then
    v2.Cmdr:Clone().Parent = v3.PlayerGui
end
local u11 = require(script.CmdrInterface)(u10)
function u10.SetActivationKeys(p12, p13) --[[Anonymous function at line 51]]
    --[[
    Upvalues:
        [1] = u5
    --]]
    p12.ActivationKeys = u5.MakeDictionary(p13)
end
function u10.SetPlaceName(p14, p15) --[[Anonymous function at line 56]]
    --[[
    Upvalues:
        [1] = u11
    --]]
    p14.PlaceName = p15
    u11.Window:UpdateLabel()
end
function u10.SetEnabled(p16, p17) --[[Anonymous function at line 62]]
    p16.Enabled = p17
end
function u10.SetActivationUnlocksMouse(p18, p19) --[[Anonymous function at line 67]]
    p18.ActivationUnlocksMouse = p19
end
function u10.Show(p20) --[[Anonymous function at line 72]]
    --[[
    Upvalues:
        [1] = u11
    --]]
    if p20.Enabled then
        u11.Window:Show()
    end
end
function u10.Hide(_) --[[Anonymous function at line 81]]
    --[[
    Upvalues:
        [1] = u11
    --]]
    u11.Window:Hide()
end
function u10.Toggle(p21) --[[Anonymous function at line 86]]
    --[[
    Upvalues:
        [1] = u11
    --]]
    if not p21.Enabled then
        return p21:Hide()
    end
    u11.Window:SetVisible(not u11.Window:IsVisible())
end
function u10.SetMashToEnable(p22, p23) --[[Anonymous function at line 95]]
    p22.MashToEnable = p23
    if p23 then
        p22:SetEnabled(false)
    end
end
function u10.SetHideOnLostFocus(p24, p25) --[[Anonymous function at line 104]]
    p24.HideOnLostFocus = p25
end
function u10.HandleEvent(p26, p27, p28) --[[Anonymous function at line 109]]
    p26.Events[p27] = p28
end
if v1:IsServer() == false then
    u10.Registry:RegisterTypesIn(script:WaitForChild("Types"))
    u10.Registry:RegisterCommandsIn(script:WaitForChild("Commands"))
end
u10.RemoteEvent.OnClientEvent:Connect(function(p29, ...) --[[Anonymous function at line 120]]
    --[[
    Upvalues:
        [1] = u10
    --]]
    if u10.Events[p29] then
        u10.Events[p29](...)
    end
end)
require(script.DefaultEventHandlers)(u10)
return u10