-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\PetServices\PetCardUserInterfaceService-ModuleScript.lua
game:GetService("Selection")
local v1 = game:GetService("Players")
local v2 = game:GetService("ReplicatedStorage")
local u3 = game:GetService("TweenService")
game:GetService("CollectionService")
game:GetService("RunService")
game:GetService("UserInputService")
game:GetService("GuiService")
local u4 = workspace.CurrentCamera
workspace:GetPropertyChangedSignal("CurrentCamera"):Connect(function() --[[Anonymous function at line 12]]
    --[[
    Upvalues:
        [1] = u4
    --]]
    u4 = workspace.CurrentCamera
end)
local u5 = require(v2.Modules.PetServices.ActivePetsService)
local v6 = require(v2.Modules.WaitForDescendant)
local u7 = require(v2.Data.PetRegistry)
local u8 = require(v2.Modules.PetServices.PetUtilities)
local u9 = require(v2.Modules.PlayClickSound)
local u10 = require(v2.Modules.Chalk)
local u11 = require(v2.Modules.PlayHoverSound)
local u12 = u7.PetList
local u13 = u7.PetConfig.XP_CONFIG.MAX_LEVEL
local u14 = v1.LocalPlayer:WaitForChild("PlayerGui"):WaitForChild("PetUI"):WaitForChild("PetCard")
local u15 = u14:WaitForChild("DropShadowHolder")
local u16 = u14:WaitForChild("Main")
local u17 = v6(u14, "PET_TEXT")
local u18 = v6(u14, "PET_NAME_CONFIG")
local u19 = v6(u14, "PET_LEVEL")
local u20 = v6(u14, "PET_WEIGHT")
local u21 = v6(u14, "XP_TEXT")
local u22 = v6(u14, "HUNGER_TEXT")
local u23 = v6(u14, "EXP_BAR")
local u24 = v6(u14, "HUNGER_BAR")
v6(u14, "PASSIVE_INSERTION_POINT")
local u25 = v6(u14, "PET_DESCRIPTION")
v6(u14, "PASSIVE_TEMPLATE")
local u26 = v6(u14, "PET_IMAGE")
local u27 = v6(u14, "EXIT_BUTTON")
local v28 = require(v2.Modules.SetupBrightnessAnimationFrame)(u27)
local u29 = UDim2.fromScale(0.875, 0.5)
local u30 = u29 + UDim2.fromScale(2, 0)
local u31 = {
    ["Active"] = false,
    ["Target"] = nil
}
local u32 = {}
function u31.SetTarget(_, p33) --[[Anonymous function at line 81]]
    --[[
    Upvalues:
        [1] = u31
        [2] = u5
        [3] = u12
        [4] = u7
        [5] = u10
        [6] = u25
        [7] = u17
        [8] = u26
        [9] = u32
    --]]
    u31.Target = p33
    if p33 then
        local v34 = u5:GetPetDataFromPetObject(p33)
        if v34 then
            local v35 = v34.PetType
            local v36 = v34.PetData
            local v37 = u12[v35]
            local v38 = v37.Rarity or "Common"
            local v39 = u7.PetRarities[v38].Color
            local v40, v41, v42 = v39:ToHSV()
            local v43 = v42 + -0.4
            local v44 = math.clamp(v43, 0, 1)
            local v45 = {
                ["Color"] = Color3.fromHSV(v40, v41, v44),
                ["Joins"] = "Round",
                ["Thickness"] = 2
            }
            local v46 = u10.color(v39).stroke(v45)
            local v47 = u10.color(Color3.fromRGB(255, 255, 255)).stroke({
                ["Color"] = Color3.fromRGB(35, 35, 35),
                ["Joins"] = "Round",
                ["Thickness"] = 2
            })
            local v48 = v46((("[%*]"):format(v38)))
            local v49 = v47(v35)
            u25.Text = v37.Description
            u17.Text = ("%* %*"):format(v49, v48)
            u26.Image = v37.Icon
            local v50 = u12[v35]
            for _, v51 in u32 do
                v51:Destroy()
            end
            local v52 = v50.Passives
            local _ = v36.Level
            for _, _ in v52 do

            end
            u31:Update()
        end
    else
        return
    end
end
local u53 = require(v2.Data.DecimalNumberFormat)
function u31.Update(_) --[[Anonymous function at line 198]]
    --[[
    Upvalues:
        [1] = u31
        [2] = u5
        [3] = u12
        [4] = u8
        [5] = u20
        [6] = u53
        [7] = u18
        [8] = u13
        [9] = u3
        [10] = u23
        [11] = u24
        [12] = u22
        [13] = u19
        [14] = u21
        [15] = u25
    --]]
    local v54 = u31.Target
    if v54 then
        local v55 = u5:GetPetDataFromPetObject(v54)
        if v55 then
            local v56 = u12[v55.PetType]
            local v57 = v56.DefaultHunger
            local v58 = v55.PetData
            u20.Text = ("Weight: %* KG"):format((u53((u8:CalculateWeight(v58.BaseWeight, v58.Level)))))
            local v59 = v58.Name
            if v59 == "" then
                v59 = nil
            end
            u18.Text = v59 or "Unnamed"
            local v60 = v58.Level
            local v61 = u8:GetCurrentLevelXPCost(v60)
            local v62 = u13 <= v60
            local v63 = v58.LevelProgress or 0
            local v64 = v62 and 1 or v63 / v61
            u3:Create(u23.ACTUAL_BAR, TweenInfo.new(0.2), {
                ["Size"] = UDim2.fromScale(v64, 1)
            }):Play()
            local v65 = v58.Hunger
            local v66 = v65 / v57
            u3:Create(u24.ACTUAL_BAR, TweenInfo.new(0.2), {
                ["Size"] = UDim2.fromScale(v66, 1)
            }):Play()
            u22.Text = ("%* / %* HGR"):format(u53(v65), v57)
            u23.ACTUAL_BAR.BAR_END.Visible = not v62
            u19.Text = ("Age: %*"):format(v60)
            u21.Text = v62 and "MAXED" or ("%* / %* EXP"):format(u53(v63), (math.round(v61)))
            local v67 = v56.Passives
            local v68 = u8:CalculateWeight(v58.BaseWeight, v60)
            local v69 = ""
            for _, v70 in v67 do
                v69 = v69 .. ("%*\n"):format((u8:GetPassiveString(v68, v70)))
            end
            u25.Text = v69
        end
    else
        return
    end
end
function u31.Toggle(_, u71) --[[Anonymous function at line 269]]
    --[[
    Upvalues:
        [1] = u14
        [2] = u31
        [3] = u3
        [4] = u15
        [5] = u29
        [6] = u30
        [7] = u16
        [8] = u27
    --]]
    if u71 then
        u14.Visible = true
    end
    u31.Active = u71
    local v72 = {
        ["Position"] = u71 and u29 or u30
    }
    u3:Create(u15, TweenInfo.new(0.5), v72):Play()
    local v73 = {
        ["Position"] = u71 and u29 or u30
    }
    local v74 = u3:Create(u16, TweenInfo.new(0.5), v73)
    v74:Play()
    v74.Completed:Connect(function() --[[Anonymous function at line 285]]
        --[[
        Upvalues:
            [1] = u71
            [2] = u14
        --]]
        if not u71 then
            u14.Visible = false
        end
    end)
    if u71 then
        game:GetService("GamepadService"):EnableGamepadCursor(u27)
    else
        game:GetService("GamepadService"):DisableGamepadCursor()
    end
end
v28.MouseEnter:Connect(function() --[[Anonymous function at line 302]]
    --[[
    Upvalues:
        [1] = u11
    --]]
    u11()
end)
v28.MouseButton1Click:Connect(function() --[[Anonymous function at line 306]]
    --[[
    Upvalues:
        [1] = u9
        [2] = u31
    --]]
    u9()
    u31:Toggle(false)
end)
task.spawn(function() --[[Anonymous function at line 311]]
    --[[
    Upvalues:
        [1] = u31
    --]]
    while true do
        task.wait(1)
        u31:Update()
    end
end)
return u31