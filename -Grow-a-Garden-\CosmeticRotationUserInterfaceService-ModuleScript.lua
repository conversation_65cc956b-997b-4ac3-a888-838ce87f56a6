-- Full Path: -Grow-a-Garden-\\CosmeticRotationUserInterfaceService-ModuleScript.lua
local v1 = game:GetService("Players")
local v2 = game:GetService("RunService")
local u3 = game:GetService("UserInputService")
local u4 = game:GetService("GuiService")
local v5 = game:GetService("ReplicatedStorage")
local u6 = v1.LocalPlayer:WaitForChild("PlayerGui"):WaitForChild("CosmeticUI"):WaitForChild("RotationIcon")
local u7 = u6:WaitForChild("ActualIcon")
local u8 = require(v5.Modules.RoundToNearestNumber)
local v9 = require(v5.Data.CosmeticRegistry)
u6.Visible = false
local u10 = v9.InputConfig.ROTATION_CONFIG
local u11 = {
    ["Active"] = false,
    ["CurrentOffset"] = 0
}
local u12 = 0
function u11.Toggle(_, p13) --[[Anonymous function at line 34]]
    --[[
    Upvalues:
        [1] = u11
        [2] = u3
        [3] = u12
        [4] = u4
        [5] = u6
        [6] = u7
    --]]
    u11.CurrentOffset = 0
    u11.Active = p13
    u12 = u3:GetMouseLocation().X
    local v14 = u3:GetMouseLocation()
    local v15 = u4:GetGuiInset()
    u6.Position = UDim2.fromOffset(v14.X + v15.X, v14.Y - v15.Y)
    u7.Position = UDim2.fromScale(0.5, 0.5)
    u6.Visible = p13
end
v2.RenderStepped:Connect(function() --[[Anonymous function at line 49]]
    --[[
    Upvalues:
        [1] = u11
        [2] = u3
        [3] = u12
        [4] = u8
        [5] = u10
        [6] = u7
    --]]
    if u11.Active then
        local v16 = u3:GetMouseLocation().X
        local v17 = v16 - u12
        local v18 = u11
        v18.CurrentOffset = v18.CurrentOffset + v17
        u12 = v16
        local v19 = u8(u11.CurrentOffset, u10.ROTATION_ROUNDING_PIXEL)
        u7.Position = UDim2.fromScale(0.5, 0.5) + UDim2.fromOffset(v19, 0)
    end
end)
local u22 = {
    [Enum.KeyCode.DPadLeft] = {
        ["Test"] = function() --[[Anonymous function at line 66]]
            --[[
            Upvalues:
                [1] = u11
            --]]
            local v20 = u11
            v20.CurrentOffset = v20.CurrentOffset - 30
        end
    },
    [Enum.KeyCode.DPadRight] = {
        ["Test"] = function() --[[Anonymous function at line 71]]
            --[[
            Upvalues:
                [1] = u11
            --]]
            local v21 = u11
            v21.CurrentOffset = v21.CurrentOffset + 30
        end
    }
}
local u23 = {}
u3.InputBegan:Connect(function(u24, _) --[[Anonymous function at line 81]]
    --[[
    Upvalues:
        [1] = u22
    --]]
    local v25 = u22[u24.KeyCode] or u22[u24.UserInputType]
    if v25 then
        for _, u26 in v25 do
            task.spawn(function() --[[Anonymous function at line 85]]
                --[[
                Upvalues:
                    [1] = u26
                    [2] = u24
                --]]
                u26(u24)
            end)
        end
    end
end)
u3.InputEnded:Connect(function(u27, _) --[[Anonymous function at line 91]]
    --[[
    Upvalues:
        [1] = u23
    --]]
    local v28 = u23[u27.KeyCode] or u23[u27.UserInputType]
    if v28 then
        for _, u29 in v28 do
            task.spawn(function() --[[Anonymous function at line 95]]
                --[[
                Upvalues:
                    [1] = u29
                    [2] = u27
                --]]
                u29(u27)
            end)
        end
    end
end)
return u11