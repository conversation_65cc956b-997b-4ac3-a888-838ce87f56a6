-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\ByteNet\process\read-ModuleScript.lua
local v1 = require(script.Parent.Parent.namespaces.packetIDs)
local u2 = require(script.Parent.readRefs)
local u3 = v1.ref()
local u4 = nil
local function u7(p5, ...) --[[Anonymous function at line 8]]
    --[[
    Upvalues:
        [1] = u4
    --]]
    local v6 = u4
    u4 = nil
    p5(...)
    u4 = v6
end
local function u8() --[[Anonymous function at line 15]]
    --[[
    Upvalues:
        [1] = u7
    --]]
    while true do
        u7(coroutine.yield())
    end
end
local function u10(p9, ...) --[[Anonymous function at line 21]]
    --[[
    Upvalues:
        [1] = u4
        [2] = u8
    --]]
    if u4 == nil then
        u4 = coroutine.create(u8)
        coroutine.resume(u4)
    end
    task.spawn(u4, p9, ...)
end
return function(p11, p12, p13) --[[Anonymous function at line 30]]
    --[[
    Upvalues:
        [1] = u2
        [2] = u3
        [3] = u10
    --]]
    local v14 = buffer.len(p11)
    u2.set(p12)
    local v15 = 0
    while v15 < v14 do
        local v16 = u3[buffer.readu8(p11, v15)]
        local v17 = v15 + 1
        local v18, v19 = v16.reader(p11, v17)
        v15 = v17 + v19
        for _, v20 in v16.getListeners() do
            u10(v20, v18, p13)
        end
    end
end