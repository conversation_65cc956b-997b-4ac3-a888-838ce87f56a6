-- Full Path: -Grow-a-Garden-\ReplicatedStorage\CmdrClient\Commands\bind-ModuleScript.lua
local u1 = game:GetService("UserInputService")
local v13 = {
    ["Name"] = "bind",
    ["Aliases"] = {},
    ["Description"] = "Binds a command string to a key or mouse input.",
    ["Group"] = "DefaultUtil",
    ["Args"] = {
        {
            ["Type"] = "userInput ! bindableResource @ player",
            ["Name"] = "Input",
            ["Description"] = "The key or input type you\'d like to bind the command to."
        },
        {
            ["Type"] = "command",
            ["Name"] = "Command",
            ["Description"] = "The command you want to run on this input"
        },
        {
            ["Type"] = "string",
            ["Name"] = "Arguments",
            ["Description"] = "The arguments for the command",
            ["Default"] = ""
        }
    },
    ["ClientRun"] = function(u2, u3, p4, p5) --[[Function name: ClientR<PERSON>, line 27]]
        --[[
        Upvalues:
            [1] = u1
        --]]
        local v6 = u2:GetStore("CMDR_Binds")
        local u7 = p4 .. " " .. p5
        if v6[u3] then
            v6[u3]:Disconnect()
        end
        local v8 = u2:GetArgument(1).Type.Name
        if v8 == "userInput" then
            v6[u3] = u1.InputBegan:Connect(function(p9, p10) --[[Anonymous function at line 39]]
                --[[
                Upvalues:
                    [1] = u3
                    [2] = u2
                    [3] = u7
                --]]
                if not p10 then
                    if p9.UserInputType == u3 or p9.KeyCode == u3 then
                        u2:Reply(u2.Dispatcher:EvaluateAndRun(u2.Cmdr.Util.RunEmbeddedCommands(u2.Dispatcher, u7)))
                    end
                end
            end)
        else
            if v8 == "bindableResource" then
                return "Unimplemented..."
            end
            if v8 == "player" then
                v6[u3] = u3.Chatted:Connect(function(p11) --[[Anonymous function at line 51]]
                    --[[
                    Upvalues:
                        [1] = u2
                        [2] = u7
                        [3] = u3
                    --]]
                    local v12 = u2.Cmdr.Util.RunEmbeddedCommands(u2.Dispatcher, u2.Cmdr.Util.SubstituteArgs(u7, { p11 }))
                    u2:Reply(("%s $ %s : %s"):format(u3.Name, v12, u2.Dispatcher:EvaluateAndRun(v12)), Color3.fromRGB(244, 92, 66))
                end)
            end
        end
        return "Bound command to input."
    end
}
return v13