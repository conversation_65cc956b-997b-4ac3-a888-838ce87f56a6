-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\EffectController\Libraries\Cam_Shaker-ModuleScript.lua
local u1 = require(script:Wait<PERSON><PERSON><PERSON>hild("Shake"))
local u2 = workspace.CurrentCamera
local v3 = require(script:<PERSON><PERSON><PERSON><PERSON>hil<PERSON>("Settings"))
local u4 = v3.DD
local u5 = v3.Min_Distance
local u6 = require(script:<PERSON><PERSON><PERSON><PERSON>hil<PERSON>("Check_Can_Shake"))
local u7 = {
    ["punch_shake"] = {
        ["FadeInTime"] = 0,
        ["Frequency"] = 0.05,
        ["Amplitude"] = 0.35,
        ["SustainTime"] = 0.145,
        ["FadeOutTime"] = 0.15,
        ["RotationInfluence"] = Vector3.new(0.1, 0.1, 0.1),
        ["PositionInfluence"] = Vector3.new(0.4, 0.4, 0.4)
    },
    ["tiny_shake"] = {
        ["FadeInTime"] = 0,
        ["Frequency"] = 0.07,
        ["Amplitude"] = 0.25,
        ["SustainTime"] = 0.05,
        ["FadeOutTime"] = 0.2,
        ["RotationInfluence"] = Vector3.new(0.1, 0.1, 0.1),
        ["PositionInfluence"] = Vector3.new(0.5, 0.5, 0.5)
    },
    ["tiny_shake_less_aggresive"] = {
        ["FadeInTime"] = 0,
        ["Frequency"] = 0.15,
        ["Amplitude"] = 0.25,
        ["SustainTime"] = 0.1,
        ["FadeOutTime"] = 0.3,
        ["RotationInfluence"] = Vector3.new(0.1, 0.1, 0.1),
        ["PositionInfluence"] = Vector3.new(0.5, 0.5, 0.5)
    },
    ["medium_shake"] = {
        ["FadeInTime"] = 0,
        ["Frequency"] = 0.055,
        ["Amplitude"] = 0.98,
        ["SustainTime"] = 0.14,
        ["FadeOutTime"] = 0.5,
        ["RotationInfluence"] = Vector3.new(0.25, 0.25, 0.25),
        ["PositionInfluence"] = Vector3.new(3.5, 3.5, 3.5)
    },
    ["medium_shake_longer"] = {
        ["FadeInTime"] = 0,
        ["Frequency"] = 0.1,
        ["Amplitude"] = 0.4,
        ["SustainTime"] = 0.9,
        ["FadeOutTime"] = 0.75,
        ["RotationInfluence"] = Vector3.new(0.25, 0.25, 0.25),
        ["PositionInfluence"] = Vector3.new(3.5, 3.5, 3.5)
    },
    ["medium_tiny_shake"] = {
        ["FadeInTime"] = 0,
        ["Frequency"] = 0.1,
        ["Amplitude"] = 0.5,
        ["SustainTime"] = 0.14,
        ["FadeOutTime"] = 0.5,
        ["RotationInfluence"] = Vector3.new(0.25, 0.25, 0.25),
        ["PositionInfluence"] = Vector3.new(3.5, 3.5, 3.5)
    },
    ["medium_tiny_shake2"] = {
        ["FadeInTime"] = 0,
        ["Frequency"] = 0.15,
        ["Amplitude"] = 0.4,
        ["SustainTime"] = 0.14,
        ["FadeOutTime"] = 0.5,
        ["RotationInfluence"] = Vector3.new(0.15, 0.15, 0.15),
        ["PositionInfluence"] = Vector3.new(1, 1, 1)
    },
    ["activate_shake_less_aggresive"] = {
        ["FadeInTime"] = 0,
        ["Frequency"] = 0.22,
        ["Amplitude"] = 0.1,
        ["SustainTime"] = 0.2,
        ["FadeOutTime"] = 0.4,
        ["RotationInfluence"] = Vector3.new(0.25, 0.25, 0.25),
        ["PositionInfluence"] = Vector3.new(0.5, 0.5, 0.5)
    },
    ["activate_shake"] = {
        ["FadeInTime"] = 0,
        ["Frequency"] = 0.15,
        ["Amplitude"] = 0.25,
        ["SustainTime"] = 0.1,
        ["FadeOutTime"] = 0.5,
        ["RotationInfluence"] = Vector3.new(0.25, 0.25, 0.25),
        ["PositionInfluence"] = Vector3.new(1, 1, 1)
    }
}
local u8 = typeof
return function(p9, p10) --[[Anonymous function at line 93]]
    --[[
    Upvalues:
        [1] = u6
        [2] = u8
        [3] = u7
        [4] = u1
        [5] = u2
        [6] = u5
        [7] = u4
    --]]
    if u6() == true then
        local u11
        if p9 == nil or p10 ~= nil then
            u11 = p9
            p9 = p10
        else
            u11 = nil
        end
        if u8(p9) ~= "table" and u7[p9] then
            p9 = u7[p9]
        end
        if p9 ~= nil then
            if u8(p9) == "table" then
                local v12 = Enum.RenderPriority.Camera.Value
                local u13 = u1.new()
                if p9 then
                    for v14, v15 in pairs(p9) do
                        u13[v14] = v15
                    end
                end
                u13:Start()
                u13:BindToRenderStep(u1.NextRenderName(), v12, function(p16, p17, p18) --[[Anonymous function at line 129]]
                    --[[
                    Upvalues:
                        [1] = u13
                        [2] = u11
                        [3] = u2
                        [4] = u5
                        [5] = u4
                    --]]
                    local v19 = u13.center_pos or u11
                    local v20 = u11
                    if typeof(v20) ~= "BasePart" then
                        local v21 = u11
                        if typeof(v21) ~= "Part" then
                            ::l5::
                            if v19 ~= nil then
                                local v22 = (u2.CFrame.Position - v19).Magnitude
                                local v23 = u5
                                local v24 = math.clamp(v22, v23, 999999)
                                p16 = u13.InverseSquare(p16, v24) * u4
                                p17 = u13.InverseSquare(p17, v24) * u4
                            end
                            local v25 = u2
                            v25.CFrame = v25.CFrame * (CFrame.new(p16) * CFrame.Angles(p17.X, p17.Y, p17.Z))
                            if p18 == true then
                                u13:Stop()
                                u13:Destroy()
                            end
                            return
                        end
                    end
                    v19 = u11.Position
                    goto l5
                end)
                return u13
            end
        end
    else
        return
    end
end