-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\userprint-ModuleScript.lua
local u1 = game:GetService("Players")
return function(u2) --[[Function name: userprint, line 3]]
    --[[
    Upvalues:
        [1] = u1
    --]]
    return function(...) --[[Function name: perthprint, line 4]]
        --[[
        Upvalues:
            [1] = u1
            [2] = u2
        --]]
        if not u1:FindFirstChild(u2) then
            return print(...)
        end
    end
end