-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Zone\OldSignal-ModuleScript.lua
local u1 = game:GetService("HttpService")
local u2 = game:GetService("RunService").Heartbeat
local u3 = {}
u3.__index = u3
u3.ClassName = "Signal"
u3.totalConnections = 0
function u3.new(p4) --[[Anonymous function at line 12]]
    --[[
    Upvalues:
        [1] = u3
    --]]
    local v5 = u3
    local v6 = setmetatable({}, v5)
    if p4 then
        v6.connectionsChanged = u3.new()
    end
    v6.connections = {}
    v6.totalConnections = 0
    v6.waiting = {}
    v6.totalWaiting = 0
    return v6
end
function u3.Fire(p7, ...) --[[Anonymous function at line 30]]
    for _, v8 in pairs(p7.connections) do
        task.spawn(v8.<PERSON><PERSON>, ...)
    end
    if p7.totalWaiting > 0 then
        local v9 = table.pack(...)
        for v10, _ in pairs(p7.waiting) do
            p7.waiting[v10] = v9
        end
    end
end
u3.fire = u3.Fire
function u3.Connect(u11, p12) --[[Anonymous function at line 44]]
    --[[
    Upvalues:
        [1] = u1
    --]]
    if type(p12) ~= "function" then
        error(("connect(%s)"):format((typeof(p12))), 2)
    end
    local u13 = u1:GenerateGUID(false)
    local u14 = {
        ["Connected"] = true,
        ["ConnectionId"] = u13,
        ["Handler"] = p12
    }
    u11.connections[u13] = u14
    function u14.Disconnect(_) --[[Anonymous function at line 57]]
        --[[
        Upvalues:
            [1] = u11
            [2] = u13
            [3] = u14
        --]]
        u11.connections[u13] = nil
        u14.Connected = false
        local v15 = u11
        v15.totalConnections = v15.totalConnections - 1
        if u11.connectionsChanged then
            u11.connectionsChanged:Fire(-1)
        end
    end
    u14.Destroy = u14.Disconnect
    u14.destroy = u14.Disconnect
    u14.disconnect = u14.Disconnect
    u11.totalConnections = u11.totalConnections + 1
    if u11.connectionsChanged then
        u11.connectionsChanged:Fire(1)
    end
    return u14
end
u3.connect = u3.Connect
function u3.Wait(p16) --[[Anonymous function at line 77]]
    --[[
    Upvalues:
        [1] = u1
        [2] = u2
    --]]
    local v17 = u1:GenerateGUID(false)
    p16.waiting[v17] = true
    p16.totalWaiting = p16.totalWaiting + 1
    repeat
        u2:Wait()
    until p16.waiting[v17] ~= true
    p16.totalWaiting = p16.totalWaiting - 1
    local v18 = p16.waiting[v17]
    p16.waiting[v17] = nil
    return unpack(v18)
end
u3.wait = u3.Wait
function u3.Destroy(p19) --[[Anonymous function at line 89]]
    if p19.bindableEvent then
        p19.bindableEvent:Destroy()
        p19.bindableEvent = nil
    end
    if p19.connectionsChanged then
        p19.connectionsChanged:Fire(-p19.totalConnections)
        p19.connectionsChanged:Destroy()
        p19.connectionsChanged = nil
    end
    p19.totalConnections = 0
    for v20, _ in pairs(p19.connections) do
        p19.connections[v20] = nil
    end
end
u3.destroy = u3.Destroy
u3.Disconnect = u3.Destroy
u3.disconnect = u3.Destroy
return u3