-- Full Path: -Grow-a-Garden-\StarterPlayer\StarterPlayerScripts\TooltipUpdater-LocalScript.lua
local u1 = game:GetService("Players").LocalPlayer
local function v7(_) --[[Anonymous function at line 5]]
    --[[
    Upvalues:
        [1] = u1
    --]]
    local v2 = u1:Find<PERSON>irstChild("Backpack")
    if v2 then
        local function u4(u3) --[[Anonymous function at line 11]]
            if u3:IsA("Tool") and string.find(u3.Name, "(kg)") then
                u3.ToolTip = u3.Name
                u3:GetPropertyChangedSignal("Name"):Connect(function() --[[Anonymous function at line 17]]
                    --[[
                    Upvalues:
                        [1] = u3
                    --]]
                    if string.find(u3.Name, "(kg)") then
                        u3.ToolTip = u3.Name
                    end
                end)
            end
        end
        for _, v5 in ipairs(v2:GetChildren()) do
            u4(v5)
        end
        v2.ChildAdded:Connect(function(p6) --[[Anonymous function at line 31]]
            --[[
            Upvalues:
                [1] = u4
            --]]
            u4(p6)
        end)
    end
end
u1.CharacterAdded:Connect(v7)
if u1.Character then
    v7(u1.Character)
end