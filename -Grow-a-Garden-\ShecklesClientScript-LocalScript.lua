-- Full Path: -Grow-a-Garden-\\ShecklesClientScript-LocalScript.lua
local v1 = game:GetService("Players")
local u2 = game:GetService("TweenService")
local u3 = game:GetService("Debris")
local v4 = game:GetService("UserInputService")
local u5 = game:GetService("SoundService")
local v6 = game:GetService("ReplicatedStorage")
local v7 = v6:WaitF<PERSON><PERSON>hild("GameEvents"):WaitFor<PERSON>hild("ShecklesClient")
v1.LocalPlayer:WaitForChild("leaderstats")
local u8 = require(v6.Modules.DataService)
local u9 = script.Parent
local u10 = u9.val
local u11 = TweenInfo.new(0.7, Enum.EasingStyle.Quad, Enum.EasingDirection.Out)
local u12 = game.ReplicatedStorage:WaitForChild("Appear_Effect")
local u13 = require(game.ReplicatedStorage:Wait<PERSON><PERSON><PERSON>hild("Comma_Module"))
local function u24(p14, p15) --[[Anonymous function at line 39]]
    --[[
    Upvalues:
        [1] = u13
        [2] = u12
        [3] = u9
        [4] = u2
        [5] = u3
        [6] = u5
    --]]
    local v16 = math.random(7, 11) * 0.1
    local v17 = TweenInfo.new(v16, Enum.EasingStyle.Quad, Enum.EasingDirection.Out)
    local v18 = u13.Comma(p14)
    local v19 = u12:Clone()
    v19.Parent = u9.Parent
    v19.Position = u9.Position
    v19.TextColor3 = p15 and Color3.new(1, 1, 0) or Color3.new(1, 0, 0)
    v19.Text = (p15 and "+" or "-") .. v18 .. "\194\162"
    local v20 = p15 and 0.1 or math.random(4, 7) * 0.01
    local v21 = u2:Create(v19, v17, {
        ["Position"] = v19.Position - UDim2.new(0, 0, v20, 0)
    })
    local v22 = u2:Create(v19, v17, {
        ["TextTransparency"] = 1,
        ["TextStrokeTransparency"] = 1
    })
    v21:Play()
    v22:Play()
    u3:AddItem(v19, v17.Time)
    local v23 = u5:FindFirstChild("Cash Register")
    if v23 then
        v23.TimePosition = 0
        v23.PlaybackSpeed = 1 + math.random(-15, 15) / 100
        v23:Play()
    end
end
v7.OnClientEvent:Connect(function(_) --[[Anonymous function at line 69]] end)
u10:GetPropertyChangedSignal("Value"):Connect(function() --[[Anonymous function at line 78]]
    --[[
    Upvalues:
        [1] = u9
        [2] = u13
        [3] = u10
    --]]
    local v25 = u9
    local v26 = u13.Comma
    local v27 = u10.Value
    local v28 = math.round(v27)
    v25.Text = v26((tostring(v28))) .. "\194\162"
end)
if v4.GamepadEnabled then
    u9.Visible = false
    u9.Parent.Enabled = false
end
u2:Create(u10, u11, {
    ["Value"] = u8:GetData().Sheckles or 0
}):Play()
local u29 = u8:GetData().Sheckles or 0
u8:GetPathSignal("Sheckles"):Connect(function() --[[Anonymous function at line 95]]
    --[[
    Upvalues:
        [1] = u8
        [2] = u29
        [3] = u24
        [4] = u2
        [5] = u10
        [6] = u11
    --]]
    local v30 = u8:GetData().Sheckles or 0
    if u29 == v30 then
        return
    else
        local v31 = v30 - u29
        local v32 = math.abs(v31)
        if v32 ~= 0 then
            u24(v32, u29 < v30)
            u29 = v30
            u2:Create(u10, u11, {
                ["Value"] = u8:GetData().Sheckles or 0
            }):Play()
        end
    end
end)