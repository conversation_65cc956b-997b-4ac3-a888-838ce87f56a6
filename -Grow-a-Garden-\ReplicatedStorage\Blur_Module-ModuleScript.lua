-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Blur_Module-ModuleScript.lua
local v1 = {}
local u2 = game:GetService("TweenService")
local u3 = game.Lighting.Blur
function v1.Blur(p4, p5) --[[Anonymous function at line 4]]
    --[[
    Upvalues:
        [1] = u2
        [2] = u3
    --]]
    local v6 = u2:Create(u3, TweenInfo.new(p5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out, 0, false, 0), {
        ["Size"] = p4
    })
    v6:Play()
    game.Debris:AddItem(v6, p5)
end
return v1