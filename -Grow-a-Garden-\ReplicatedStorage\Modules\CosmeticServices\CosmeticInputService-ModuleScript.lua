-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\CosmeticServices\CosmeticInputService-ModuleScript.lua
local u1 = game:GetService("UserInputService")
game:GetService("GuiService")
local u2 = game:GetService("CollectionService")
game:GetService("RunService")
local u3 = game:GetService("TweenService")
local v4 = game:GetService("ReplicatedStorage")
local v5 = game:GetService("Players").LocalPlayer
local u6 = workspace.CurrentCamera
workspace:GetPropertyChangedSignal("CurrentCamera"):Connect(function() --[[Anonymous function at line 12]]
    --[[
    Upvalues:
        [1] = u6
    --]]
    u6 = workspace.CurrentCamera
end)
local u7 = require(v4.Modules.CosmeticServices.UserInterface.CosmeticActionUserInterfaceService)
local u8 = require(v4.Modules.CosmeticServices.UserInterface.CosmeticInventoryUserInterfaceService)
local u9 = require(v4.Modules.GetMouseToWorld)
local v10 = require(v4.Modules.GetFarmAsync)
local u11 = require(v4.Modules.GetUIElementWithTag)
local u12 = v10(v5)
local u13 = Instance.new("Highlight")
u13.FillTransparency = 1
local u14 = nil
local u15 = nil
local function u16() --[[Anonymous function at line 31]]
    --[[
    Upvalues:
        [1] = u15
        [2] = u3
        [3] = u13
    --]]
    if u15 then
        u3:Create(u13, TweenInfo.new(0.2), {
            ["OutlineTransparency"] = 0.8
        }):Play()
    end
end
local function u17() --[[Anonymous function at line 40]]
    --[[
    Upvalues:
        [1] = u15
        [2] = u3
        [3] = u13
        [4] = u7
    --]]
    if u15 then
        u3:Create(u13, TweenInfo.new(0.2), {
            ["OutlineTransparency"] = 0
        }):Play()
        if u7.Target ~= u15 then
            if u7.Target ~= nil then
                u7:Toggle(false)
            end
            task.delay(0.1, function() --[[Anonymous function at line 57]]
                --[[
                Upvalues:
                    [1] = u7
                    [2] = u15
                --]]
                u7:SetTarget(u15)
                u7:Toggle(true)
            end)
        end
    else
        return
    end
end
local u18 = {
    [Enum.UserInputType.Touch] = { function(_) --[[Anonymous function at line 65]]
            --[[
            Upvalues:
                [1] = u16
            --]]
            u16()
        end },
    [Enum.UserInputType.MouseButton1] = { function() --[[Anonymous function at line 70]]
            --[[
            Upvalues:
                [1] = u16
            --]]
            u16()
        end },
    [Enum.KeyCode.ButtonR2] = { function() --[[Anonymous function at line 75]]
            --[[
            Upvalues:
                [1] = u16
            --]]
            u16()
        end }
}
local u19 = {
    [Enum.UserInputType.Touch] = { function(_) --[[Anonymous function at line 83]]
            --[[
            Upvalues:
                [1] = u17
            --]]
            u17()
        end },
    [Enum.UserInputType.MouseButton1] = { function() --[[Anonymous function at line 88]]
            --[[
            Upvalues:
                [1] = u17
            --]]
            u17()
        end },
    [Enum.KeyCode.ButtonR2] = { function() --[[Anonymous function at line 93]]
            --[[
            Upvalues:
                [1] = u17
            --]]
            u17()
        end }
}
u1.InputBegan:Connect(function(u20, _) --[[Anonymous function at line 99]]
    --[[
    Upvalues:
        [1] = u18
    --]]
    local v21 = u18[u20.KeyCode] or u18[u20.UserInputType]
    if v21 then
        for _, u22 in v21 do
            task.spawn(function() --[[Anonymous function at line 103]]
                --[[
                Upvalues:
                    [1] = u22
                    [2] = u20
                --]]
                u22(u20)
            end)
        end
    end
end)
u1.InputEnded:Connect(function(u23, _) --[[Anonymous function at line 110]]
    --[[
    Upvalues:
        [1] = u19
    --]]
    local v24 = u19[u23.KeyCode] or u19[u23.UserInputType]
    if v24 then
        for _, u25 in v24 do
            task.spawn(function() --[[Anonymous function at line 114]]
                --[[
                Upvalues:
                    [1] = u25
                    [2] = u23
                --]]
                u25(u23)
            end)
        end
    end
end)
u1.TouchTap:Connect(function() --[[Anonymous function at line 120]]
    --[[
    Upvalues:
        [1] = u16
    --]]
    u16()
end)
u1.TouchEnded:Connect(function() --[[Anonymous function at line 124]]
    --[[
    Upvalues:
        [1] = u17
    --]]
    u17()
end)
u13.Parent = script
task.spawn(function() --[[Anonymous function at line 131]]
    --[[
    Upvalues:
        [1] = u1
        [2] = u14
        [3] = u2
        [4] = u9
        [5] = u12
        [6] = u8
        [7] = u11
        [8] = u13
        [9] = u15
    --]]
    while true do
        u14 = u1:GetMouseLocation()
        local v26 = RaycastParams.new()
        v26.FilterDescendantsInstances = u2:GetTagged("CosmeticHitBox")
        v26.FilterType = Enum.RaycastFilterType.Include
        local v27 = u9(v26, 1000)
        if v27 then
            v27 = v27.Instance
        end
        if v27 and not v27:IsDescendantOf(u12) then
            v27 = nil
        elseif not u8.State or u11("ICON_TEMPLATE") then
            v27 = nil
        end
        if v27 then
            v27 = v27.Parent
        end
        u13.Adornee = v27 or script
        u15 = v27
        task.wait(0.025)
    end
end)
return {}