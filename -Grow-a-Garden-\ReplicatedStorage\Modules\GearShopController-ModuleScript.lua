-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\GearShopController-ModuleScript.lua
local u1 = game:GetService("ReplicatedStorage")
game:GetService("TweenService")
local u2 = game.Players.LocalPlayer.PlayerGui:WaitForChild("Gear_Shop")
local u3 = require(u1.Modules.GuiController)
local u4 = require(u1.Modules.DataService)
local u5 = require(u1.Modules.UpdateService)
local u6 = require(u1.Comma_Module)
local v7 = require(u1.Modules.Signal)
local u8 = require(u1.Modules.FastTween)
local u9 = require(u1.Data.GearShopData)
local u10 = require(u1.Data.GearData)
local u11 = require(u1.Item_Module)
require(u1.Modules.RetryPcall)
local u12 = require(u1.Modules.MarketController)
local u13 = require(u1.Mo<PERSON>les.GiftController)
local u14 = require(u1.<PERSON><PERSON><PERSON>.NumberUtil)
local u15 = u2:WaitForChild("Frame"):WaitFor<PERSON>hild("ScrollingFrame")
local u16 = u2:WaitForChild("Frame"):WaitForChild("Frame"):WaitForChild("Timer")
local u17 = u15:WaitForChild("ItemFrame")
u17.Parent = script
local u18 = u15:WaitForChild("ItemPadding")
u18.Parent = u15
local u19 = {}
local v20 = {}
local u21 = nil
local u22 = v7.new()
local u23 = game.SoundService.Click
local function u27() --[[Anonymous function at line 55]]
    --[[
    Upvalues:
        [1] = u4
        [2] = u9
        [3] = u16
        [4] = u14
        [5] = u1
    --]]
    while true do
        local v24 = workspace:GetServerTimeNow()
        local v25 = u4:GetData()
        local v26 = v25.GearStock.ForcedGearEndTimestamp and (v25.GearStock.ForcedGearEndTimestamp - v24 or -1) or -1
        if v26 < 0 then
            v26 = u9.RefreshTime - v24 % u9.RefreshTime
        end
        u16.Text = v26 <= 0 and "Restocking" or "New gear in " .. u14.compactFormat(v26)
        if v26 <= 1 then
            require(u1.Modules.Notification):CreateNotification("<font color=\"#90EE90\"><b>Your Gear Shop stock has been reset!</b></font>")
        end
        task.wait(1)
    end
end
local function u51() --[[Anonymous function at line 77]]
    --[[
    Upvalues:
        [1] = u27
        [2] = u10
        [3] = u17
        [4] = u19
        [5] = u11
        [6] = u6
        [7] = u15
        [8] = u18
        [9] = u1
        [10] = u12
        [11] = u23
        [12] = u13
        [13] = u21
        [14] = u8
        [15] = u22
        [16] = u4
        [17] = u5
    --]]
    task.spawn(u27)
    local u28 = {}
    for u29, u30 in u10 do
        if u30.LayoutOrder ~= -1 then
            local u31 = u17:Clone()
            u19[u29] = u31
            u31.Name = u29
            u31.LayoutOrder = u30.LayoutOrder * 10
            table.insert(u28, u31)
            local u32 = u31.Main_Frame
            local v33 = u11.Return_Rarity_Data(u30.GearRarity)
            if v33 then
                u32.Rarity_Text.Text = v33[1]
                local v34 = u32.Rarity_Text.UIStroke
                local v35, v36, v37 = v33[2]:ToHSV()
                v34.Color = Color3.fromHSV(v35, v36, v37 / 2)
                u32.Rarity_BG.ImageColor3 = v33[2]
            end
            u32.Gear_Text.Text = u30.GearName
            u32.Gear_Text_Shadow.Text = u30.GearName
            u32.Description_Text.Text = u30.GearDescription
            u32.Cost_Text.Text = u6.Comma(u30.Price) .. "\194\162"
            u32:WaitForChild("Gear_Image").Image = u30.Asset
            u31.Parent = u15
            local u38 = u18:Clone()
            u38.LayoutOrder = u30.LayoutOrder * 10 + 1
            u38.Name = u29 .. "_Padding"
            u38.Parent = u15
            local u39 = u31.Frame
            u39.Sheckles_Buy.In_Stock.Cost_Text.Text = u6.Comma(u30.Price) .. "\194\162"
            u39.Sheckles_Buy.Activated:Connect(function() --[[Anonymous function at line 121]]
                --[[
                Upvalues:
                    [1] = u1
                    [2] = u29
                --]]
                u1.GameEvents.BuyGearStock:FireServer(u29)
            end)
            u39.Robux_Buy.Activated:Connect(function() --[[Anonymous function at line 125]]
                --[[
                Upvalues:
                    [1] = u12
                    [2] = u30
                --]]
                u12:PromptPurchase(u30.PurchaseID, Enum.InfoType.Product)
            end)
            u39.Gift.Visible = u30.GiftPurchaseID ~= nil
            if u30.GiftPurchaseID then
                u39.Gift.Activated:Connect(function() --[[Anonymous function at line 131]]
                    --[[
                    Upvalues:
                        [1] = u23
                        [2] = u13
                        [3] = u30
                    --]]
                    u23.PlaybackSpeed = 1 + math.random(-15, 15) / 100
                    u23.TimePosition = 0
                    u23.Playing = true
                    u13:PromptGiftFromGiftId(u30.GiftPurchaseID)
                end)
            end
            local function v40() --[[Anonymous function at line 139]]
                --[[
                Upvalues:
                    [1] = u21
                    [2] = u29
                    [3] = u39
                    [4] = u8
                    [5] = u38
                --]]
                if u21 == u29 then
                    u39.Visible = true
                    u8(u39, TweenInfo.new(0.25), {
                        ["Position"] = UDim2.fromScale(0.5, 1.3)
                    })
                    u8(u38, TweenInfo.new(0.25), {
                        ["Size"] = UDim2.fromScale(0.13, 0.25)
                    })
                else
                    u39.Visible = false
                    u8(u39, TweenInfo.new(0.25), {
                        ["Position"] = UDim2.fromScale(0.5, 0.5)
                    })
                    u8(u38, TweenInfo.new(0.25), {
                        ["Size"] = UDim2.fromScale(0.13, 0.02)
                    })
                end
            end
            u22:Connect(v40)
            task.spawn(v40)
            local u41 = false
            u32.Activated:Connect(function() --[[Anonymous function at line 155]]
                --[[
                Upvalues:
                    [1] = u8
                    [2] = u15
                    [3] = u31
                    [4] = u28
                    [5] = u21
                    [6] = u29
                    [7] = u22
                    [8] = u41
                    [9] = u12
                    [10] = u39
                    [11] = u30
                --]]
                u8(u15, TweenInfo.new(0.35), {
                    ["CanvasPosition"] = Vector2.new(0, u31.AbsoluteSize.Y * (table.find(u28, u31) - 1))
                })
                local v42
                if u21 == u29 then
                    v42 = nil
                else
                    v42 = u29
                end
                u21 = v42
                u22:Fire()
                if not u41 then
                    u12:SetPriceLabel(u39.Robux_Buy.Price, u30.PurchaseID, ":robux::value:")
                    u41 = true
                end
            end)
            local function u47() --[[Anonymous function at line 169]]
                --[[
                Upvalues:
                    [1] = u4
                    [2] = u29
                    [3] = u32
                    [4] = u30
                    [5] = u39
                    [6] = u12
                    [7] = u6
                --]]
                local v43 = u4:GetData()
                if v43 then
                    v43 = v43.GearStock.Stocks[u29]
                end
                local v44 = v43 and v43.Stock or 0
                u32.Stock_Text.Text = u30.RobuxOnly and "" or ("X%* Stock"):format(v44)
                u39.Sheckles_Buy.In_Stock.Visible = v44 > 0
                u39.Sheckles_Buy.No_Stock.Visible = v44 <= 0
                u39.Sheckles_Buy.HoverImage = v44 > 0 and "rbxassetid://71551639169723" or "rbxassetid://138411009141674"
                u39.Sheckles_Buy.Image = v44 > 0 and "rbxassetid://96160773850314" or "rbxassetid://104713419928195"
                u39.Sheckles_Buy.Visible = not u30.RobuxOnly
                if u30.RobuxOnly then
                    u32.Cost_Text.Text = "???"
                    u12:SetPriceLabel(u32.Cost_Text, u30.PurchaseID, ":robux::value:")
                else
                    u32.Cost_Text.Text = v44 <= 0 and "NO STOCK" or u6.Comma(u30.Price) .. "\194\162"
                    local v45 = u32.Cost_Text
                    local v46
                    if v44 <= 0 then
                        v46 = Color3.fromRGB(255, 0, 0)
                    else
                        v46 = Color3.fromRGB(0, 255, 0)
                    end
                    v45.TextColor3 = v46
                end
            end
            task.spawn(u47)
            task.spawn(function() --[[Anonymous function at line 195]]
                --[[
                Upvalues:
                    [1] = u4
                    [2] = u47
                --]]
                local v48 = u4:GetPathSignal("GearStock/@")
                if v48 then
                    v48:Connect(u47)
                end
            end)
            if u5:IsHiddenFromUpdate(u30.GearName) then
                u32.Visible = false
                task.delay(u5:GetRemainingTimeUntilUpdate(), function() --[[Anonymous function at line 204]]
                    --[[
                    Upvalues:
                        [1] = u32
                    --]]
                    u32.Visible = true
                end)
            end
        end
    end
    table.sort(u28, function(p49, p50) --[[Anonymous function at line 211]]
        if p49.LayoutOrder == p50.LayoutOrder then
            return p49.Name < p50.Name
        else
            return p49.LayoutOrder < p50.LayoutOrder
        end
    end)
end
function v20.Start(_) --[[Anonymous function at line 219]]
    --[[
    Upvalues:
        [1] = u3
        [2] = u2
        [3] = u12
        [4] = u51
    --]]
    u3:UsePopupAnims(u2)
    u2.Frame.Frame.ExitButton.Activated:Connect(function() --[[Anonymous function at line 222]]
        --[[
        Upvalues:
            [1] = u3
            [2] = u2
        --]]
        u3:Close(u2)
    end)
    u2.Frame.Frame.Restock.Activated:Connect(function() --[[Anonymous function at line 226]]
        --[[
        Upvalues:
            [1] = u12
        --]]
        u12:PromptPurchase(3265941675, Enum.InfoType.Product)
    end)
    u51()
end
task.spawn(v20.Start, v20)
return v20