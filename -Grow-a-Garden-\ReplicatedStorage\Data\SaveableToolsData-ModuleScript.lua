-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Data\SaveableToolsData-ModuleScript.lua
local u1 = {}
local v2 = {
    ["Trowel"] = {
        ["Uses"] = 5
    },
    ["Watering Can"] = {
        ["Uses"] = 10
    },
    ["Lightning Rod"] = {},
    ["Sprinkler"] = {}
}
u1.DefaultData = v2
function u1.GetDefaultData(_, p3) --[[Anonymous function at line 35]]
    --[[
    Upvalues:
        [1] = u1
    --]]
    return u1.DefaultData[p3] or nil
end
return u1