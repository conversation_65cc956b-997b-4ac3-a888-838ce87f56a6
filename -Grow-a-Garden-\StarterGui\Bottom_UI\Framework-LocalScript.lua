-- Full Path: -Grow-a-Garden-\StarterGui\Bottom_UI\Framework-LocalScript.lua
local u1 = game:GetService("TweenService")
local v2 = game:GetService("ReplicatedStorage")
local v3 = game:GetService("UserInputService")
local u4 = game:GetService("Players")
local u5 = require(v2.Modules.SessionTimeLuckController)
local u6 = require(v2.Modules.PlayerLuck)
local v7 = v2.GameEvents:WaitForChild("SendClientWeatherEvents")
local v8 = v2.GameEvents:WaitForChild("WeatherEventStarted")
local v9 = v2.GameEvents:WaitForChild("SendClientNightEvent")
local v10 = v2.GameEvents:WaitForChild("NightStartedEvent")
local v11 = script.Parent:WaitForChild("BottomFrame")
local u12 = v11:WaitForChild("Holder")
local u13 = v11:WaitForChild("Info")
local u15 = {
    ["Rain"] = {
        ["Title"] = "Rain",
        ["Description"] = "+50% Grow Speed! Higher <font color=\"#0055ff\">WET</font> Fruit Chance!"
    },
    ["ChocolateRain"] = {
        ["Title"] = "Chocolate Rain",
        ["Description"] = "Higher <font color=\"#666633\">Chocolate</font> Fruit Chance!"
    },
    ["Thunderstorm"] = {
        ["Title"] = "Thunderstorm",
        ["Description"] = "+50% Grow Speed! Higher <font color=\"#0055ff\">SHOCKED</font> Fruit Chance!"
    },
    ["Frost"] = {
        ["Title"] = "Frost",
        ["Description"] = "+50% Grow Speed! Higher <font color=\"#66ccff\">CHILLED</font> and <font color=\"#00e5ff\">FROZEN</font> Fruit Chance!"
    },
    ["Night"] = {
        ["Title"] = "Night",
        ["Description"] = "Your fruit can become <font color=\"#a347fd\">MOONLIT</font>!"
    },
    ["BloodMoon"] = {
        ["Title"] = "Blood Moon",
        ["Description"] = "Your fruit can become <font color=\"#550000\">BLOODLIT</font>!"
    },
    ["MeteorShower"] = {
        ["Title"] = "Meteor Shower",
        ["Description"] = "Higher <font color=\"#0055ff\">CELESTIAL</font> Fruit Chance!"
    },
    ["Tornado"] = {
        ["Title"] = "Tornado",
        ["Description"] = "Higher <font color=\"#D3D3D3\">TWISTED</font> Fruit Chance!"
    },
    ["Disco"] = {
        ["Title"] = "DISCO",
        ["Description"] = "Small chance for fruit to turn <font color=\"#FF007F\">D</font><font color=\"#FFD700\">I</font><font color=\"#00FFFF\">S</font><font color=\"#00FF00\">C</font><font color=\"#8A2BE2\">O</font>!"
    },
    ["Luck"] = {
        ["Title"] = "Luck",
        ["Description"] = function() --[[Function name: Description, line 58]]
            --[[
            Upvalues:
                [1] = u5
            --]]
            local v14 = u5:GetCurrentLuck() * 100
            return ("+%*%% Playtime Luck"):format((math.floor(v14)))
        end
    }
}
local u16 = false
local u17 = {}
local u18 = {}
local function u37() --[[Anonymous function at line 95]]
    --[[
    Upvalues:
        [1] = u17
        [2] = u15
        [3] = u16
        [4] = u12
        [5] = u1
        [6] = u13
    --]]
    for _, v19 in u17 do
        if u15[v19] then
            v36 = true
            ::l4::
            if v36 or not u16 then
                if not u16 then
                    u16 = true
                    u12.Position = UDim2.fromScale(0.5, -0.25)
                    u12.Visible = true
                    for _, v20 in u12.List:GetChildren() do
                        if v20:IsA("Frame") then
                            v20.Visible = false
                        end
                    end
                    local v21 = u12
                    local v22 = {
                        ["Position"] = UDim2.fromScale(1, 1)
                    }
                    local v23 = Enum.EasingStyle.Back
                    local v24 = Enum.EasingDirection.Out
                    u1:Create(v21, TweenInfo.new(0.5, v23 or Enum.EasingStyle.Linear, v24 or Enum.EasingDirection.InOut, 0, false, 0), v22):Play()
                    task.wait(0.4)
                end
                for _, u25 in u12.List:GetChildren() do
                    if u25:IsA("Frame") then
                        if table.find(u17, u25.Name) then
                            u25.UIScale.Scale = 0
                            u25.Visible = true
                            local v26 = u25.UIScale
                            local v27 = Enum.EasingStyle.Back
                            local v28 = Enum.EasingDirection.Out
                            u1:Create(v26, TweenInfo.new(0.25, v27 or Enum.EasingStyle.Linear, v28 or Enum.EasingDirection.InOut, 0, false, 0), {
                                ["Scale"] = 1
                            }):Play()
                        else
                            local v29 = u25.UIScale
                            local v30 = Enum.EasingStyle.Back
                            local v31 = Enum.EasingDirection.In
                            u1:Create(v29, TweenInfo.new(0.25, v30 or Enum.EasingStyle.Linear, v31 or Enum.EasingDirection.InOut, 0, false, 0), {
                                ["Scale"] = 0
                            }):Play()
                            task.delay(0.25, function() --[[Anonymous function at line 136]]
                                --[[
                                Upvalues:
                                    [1] = u25
                                --]]
                                u25.Visible = false
                            end)
                        end
                    end
                end
            else
                u16 = false
                local v32 = u12
                local v33 = {
                    ["Position"] = UDim2.fromScale(1, 1.2)
                }
                local v34 = Enum.EasingStyle.Quint
                local v35 = Enum.EasingDirection.In
                u1:Create(v32, TweenInfo.new(0.25, v34 or Enum.EasingStyle.Linear, v35 or Enum.EasingDirection.InOut, 0, false, 0), v33):Play()
                u13.Visible = false
                task.delay(0.25, function() --[[Anonymous function at line 103]]
                    --[[
                    Upvalues:
                        [1] = u12
                    --]]
                    u12.Visible = false
                end)
            end
        end
    end
    local v36 = false
    goto l4
end
v7.OnClientEvent:Connect(function(p38, u39, p40) --[[Anonymous function at line 187]]
    --[[
    Upvalues:
        [1] = u18
        [2] = u17
        [3] = u37
    --]]
    u18 = p38
    if u39 then
        if p40 then
            local v41 = workspace:GetServerTimeNow()
            local v42 = p40 + u18[u39].eventLength - v41
            if v42 > 0 then
                local v43 = u17
                table.insert(v43, u39)
                u37()
                task.delay(v42, function() --[[Anonymous function at line 149]]
                    --[[
                    Upvalues:
                        [1] = u17
                        [2] = u39
                        [3] = u37
                    --]]
                    local v44 = table.find(u17, u39)
                    if v44 then
                        table.remove(u17, v44)
                        u37()
                    end
                end)
            end
        else
            return
        end
    else
        return
    end
end)
v8.OnClientEvent:Connect(function(u45, p46) --[[Anonymous function at line 200]]
    --[[
    Upvalues:
        [1] = u17
        [2] = u37
    --]]
    local v47 = u17
    table.insert(v47, u45)
    u37()
    task.delay(p46, function() --[[Anonymous function at line 149]]
        --[[
        Upvalues:
            [1] = u17
            [2] = u45
            [3] = u37
        --]]
        local v48 = table.find(u17, u45)
        if v48 then
            table.remove(u17, v48)
            u37()
        end
    end)
end)
v9.OnClientEvent:Connect(function(p49, p50, p51) --[[Anonymous function at line 204]]
    --[[
    Upvalues:
        [1] = u17
        [2] = u37
    --]]
    local v52 = workspace:GetServerTimeNow()
    local v53 = p49 + p50 - v52
    if v53 <= 0 then
        return
    elseif p51 then
        local v54 = u17
        table.insert(v54, "BloodMoon")
        u37()
        local u55 = "BloodMoon"
        task.delay(v53, function() --[[Anonymous function at line 149]]
            --[[
            Upvalues:
                [1] = u17
                [2] = u55
                [3] = u37
            --]]
            local v56 = table.find(u17, u55)
            if v56 then
                table.remove(u17, v56)
                u37()
            end
        end)
    else
        local v57 = u17
        table.insert(v57, "Night")
        u37()
        local u58 = "Night"
        task.delay(v53, function() --[[Anonymous function at line 149]]
            --[[
            Upvalues:
                [1] = u17
                [2] = u58
                [3] = u37
            --]]
            local v59 = table.find(u17, u58)
            if v59 then
                table.remove(u17, v59)
                u37()
            end
        end)
    end
end)
v10.OnClientEvent:Connect(function(p60, p61) --[[Anonymous function at line 216]]
    --[[
    Upvalues:
        [1] = u17
        [2] = u37
    --]]
    if p61 then
        local v62 = u17
        table.insert(v62, "BloodMoon")
        u37()
        local u63 = "BloodMoon"
        task.delay(p60, function() --[[Anonymous function at line 149]]
            --[[
            Upvalues:
                [1] = u17
                [2] = u63
                [3] = u37
            --]]
            local v64 = table.find(u17, u63)
            if v64 then
                table.remove(u17, v64)
                u37()
            end
        end)
    else
        local v65 = u17
        table.insert(v65, "Night")
        u37()
        local u66 = "Night"
        task.delay(p60, function() --[[Anonymous function at line 149]]
            --[[
            Upvalues:
                [1] = u17
                [2] = u66
                [3] = u37
            --]]
            local v67 = table.find(u17, u66)
            if v67 then
                table.remove(u17, v67)
                u37()
            end
        end)
    end
end)
workspace:GetAttributeChangedSignal("NightEvent"):Connect(function() --[[Anonymous function at line 225]]
    --[[
    Upvalues:
        [1] = u17
        [2] = u37
    --]]
    if not workspace:GetAttribute("NightEvent") then
        local v68 = table.find(u17, "Night")
        if not v68 then
            return
        end
        table.remove(u17, v68)
        u37()
    end
end)
if v3.TouchEnabled then
    v11.Position = UDim2.new(1, 0, 0.1, 0)
    u13.Position = UDim2.new(0.95, 0, 1.8, 0)
    for _, u69 in u12.List:GetChildren() do
        if u69:IsA("Frame") then
            u69.InputBegan:Connect(function() --[[Anonymous function at line 246]]
                --[[
                Upvalues:
                    [1] = u17
                    [2] = u69
                    [3] = u13
                    [4] = u15
                --]]
                for _, v70 in u17 do
                    if u69.Name == v70 then
                        local v71 = u13.Title
                        local v72 = u15[v70]
                        local v73
                        if v72 then
                            v73 = v72.Title
                            if type(v73) == "function" then
                                v73 = v73()
                            end
                        else
                            v73 = "Event Not Found"
                        end
                        v71.Text = v73
                        local v74 = u13.Description
                        local v75 = u15[v70]
                        local v76
                        if v75 then
                            v76 = v75.Description
                            if type(v76) == "function" then
                                v76 = v76()
                            end
                        else
                            v76 = "Event Not Found"
                        end
                        v74.Text = v76
                        u13.Visible = true
                    end
                end
            end)
            u69.InputEnded:Connect(function() --[[Anonymous function at line 258]]
                --[[
                Upvalues:
                    [1] = u13
                --]]
                u13.Visible = false
            end)
        end
    end
else
    for _, u77 in u12.List:GetChildren() do
        if u77:IsA("Frame") then
            u77.MouseEnter:Connect(function() --[[Anonymous function at line 266]]
                --[[
                Upvalues:
                    [1] = u17
                    [2] = u77
                    [3] = u13
                    [4] = u15
                --]]
                for _, v78 in u17 do
                    if u77.Name == v78 then
                        local v79 = u13.Title
                        local v80 = u15[v78]
                        local v81
                        if v80 then
                            v81 = v80.Title
                            if type(v81) == "function" then
                                v81 = v81()
                            end
                        else
                            v81 = "Event Not Found"
                        end
                        v79.Text = v81
                        local v82 = u13.Description
                        local v83 = u15[v78]
                        local v84
                        if v83 then
                            v84 = v83.Description
                            if type(v84) == "function" then
                                v84 = v84()
                            end
                        else
                            v84 = "Event Not Found"
                        end
                        v82.Text = v84
                        u13.Visible = true
                    end
                end
            end)
            u77.MouseLeave:Connect(function() --[[Anonymous function at line 278]]
                --[[
                Upvalues:
                    [1] = u13
                --]]
                u13.Visible = false
            end)
        end
    end
end
u6.Listen(u4.LocalPlayer, function() --[[Anonymous function at line 285]]
    --[[
    Upvalues:
        [1] = u6
        [2] = u4
        [3] = u17
        [4] = u37
    --]]
    if #u6.GetModifiers(u4.LocalPlayer, nil, "BottomDisplay") > 1 then
        if not table.find(u17, "Luck") then
            local v85 = u17
            table.insert(v85, "Luck")
            u37()
            return
        end
    else
        local v86 = table.find(u17, "Luck")
        if v86 then
            table.remove(u17, v86)
            u37()
        end
    end
end)