-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Zone-ModuleScript.lua
local v1 = game:GetService("Players")
local u2 = game:GetService("RunService")
local u3 = u2.Heartbeat
local u4 = u2:IsClient()
if u4 then
    u4 = v1.LocalPlayer
end
game:GetService("ReplicatedStorage")
local u5 = game:GetService("HttpService")
local u6 = require(script.Enum).enums
local u7 = require(script.Janitor)
local u8 = require(script.Signal)
local v9 = require(script.ZonePlusReference)
local v10 = v9.getObject()
local v11 = script.ZoneController
local u12 = v11.Tracker
local u13 = v11.CollectiveWorldModel
local u14 = require(v11)
local v15 = game:GetService("RunService"):IsClient() and "Client" or "Server"
local v16
if v10 then
    v16 = v10:FindFirstChild(v15)
else
    v16 = v10
end
if v16 then
    return require(v10.Value)
end
local u17 = {}
u17.__index = u17
if not v16 then
    v9.addToReplicatedStorage()
end
u17.enum = u6
function u17.new(p18) --[[Anonymous function at line 34]]
    --[[
    Upvalues:
        [1] = u17
        [2] = u6
        [3] = u7
        [4] = u5
        [5] = u14
        [6] = u8
        [7] = u4
    --]]
    local u19 = {}
    local v20 = u17
    setmetatable(u19, v20)
    local v21 = typeof(p18)
    if v21 ~= "table" and v21 ~= "Instance" then
        error("The zone container must be a model, folder, basepart or table!")
    end
    u19.accuracy = u6.Accuracy.High
    u19.autoUpdate = true
    u19.respectUpdateQueue = true
    local v22 = u7.new()
    u19.janitor = v22
    u19._updateConnections = v22:add(u7.new(), "destroy")
    u19.container = p18
    u19.zoneParts = {}
    u19.overlapParams = {}
    u19.region = nil
    u19.volume = nil
    u19.boundMin = nil
    u19.boundMax = nil
    u19.recommendedMaxParts = nil
    u19.zoneId = u5:GenerateGUID()
    u19.activeTriggers = {}
    u19.occupants = {}
    u19.trackingTouchedTriggers = {}
    u19.enterDetection = u6.Detection.Centre
    u19.exitDetection = u6.Detection.Centre
    u19._currentEnterDetection = nil
    u19._currentExitDetection = nil
    u19.totalPartVolume = 0
    u19.allZonePartsAreBlocks = true
    u19.trackedItems = {}
    u19.settingsGroupName = nil
    u19.worldModel = workspace
    u19.onItemDetails = {}
    u19.itemsToUntrack = {}
    u14.updateDetection(u19)
    u19.updated = v22:add(u8.new(), "destroy")
    local v23 = {
        "player",
        "part",
        "localPlayer",
        "item"
    }
    local v24 = { "entered", "exited" }
    for _, u25 in pairs(v23) do
        local u26 = 0
        local u27 = 0
        for _, v28 in pairs(v24) do
            local v29 = v22:add(u8.new(true), "destroy")
            local u30 = v28:sub(1, 1):upper() .. v28:sub(2)
            u19[u25 .. u30] = v29
            v29.connectionsChanged:Connect(function(p31) --[[Anonymous function at line 105]]
                --[[
                Upvalues:
                    [1] = u25
                    [2] = u4
                    [3] = u30
                    [4] = u26
                    [5] = u27
                    [6] = u14
                    [7] = u19
                --]]
                if u25 == "localPlayer" and (not u4 and p31 == 1) then
                    error(("Can only connect to \'localPlayer%s\' on the client!"):format(u30))
                end
                u26 = u27
                u27 = u27 + p31
                if u26 == 0 and u27 > 0 then
                    u14._registerConnection(u19, u25, u30)
                elseif u26 > 0 and u27 == 0 then
                    u14._deregisterConnection(u19, u25)
                end
            end)
        end
    end
    u17.touchedConnectionActions = {}
    for _, v32 in pairs(v23) do
        local u33 = u19[("_%sTouchedZone"):format(v32)]
        if u33 then
            u19.trackingTouchedTriggers[v32] = {}
            u17.touchedConnectionActions[v32] = function(p34) --[[Anonymous function at line 129]]
                --[[
                Upvalues:
                    [1] = u33
                    [2] = u19
                --]]
                u33(u19, p34)
            end
        end
    end
    u19:_update()
    u14._registerZone(u19)
    v22:add(function() --[[Anonymous function at line 140]]
        --[[
        Upvalues:
            [1] = u14
            [2] = u19
        --]]
        u14._deregisterZone(u19)
    end, true)
    return u19
end
function u17.fromRegion(p35, p36) --[[Anonymous function at line 147]]
    --[[
    Upvalues:
        [1] = u17
    --]]
    local u37 = Instance.new("Model")
    local function u43(p38, p39) --[[Anonymous function at line 150]]
        --[[
        Upvalues:
            [1] = u43
            [2] = u37
        --]]
        if p39.X > 2024 or (p39.Y > 2024 or p39.Z > 2024) then
            local v40 = p39 * 0.25
            local v41 = p39 * 0.5
            u43(p38 * CFrame.new(-v40.X, -v40.Y, -v40.Z), v41)
            u43(p38 * CFrame.new(-v40.X, -v40.Y, v40.Z), v41)
            u43(p38 * CFrame.new(-v40.X, v40.Y, -v40.Z), v41)
            u43(p38 * CFrame.new(-v40.X, v40.Y, v40.Z), v41)
            u43(p38 * CFrame.new(v40.X, -v40.Y, -v40.Z), v41)
            u43(p38 * CFrame.new(v40.X, -v40.Y, v40.Z), v41)
            u43(p38 * CFrame.new(v40.X, v40.Y, -v40.Z), v41)
            u43(p38 * CFrame.new(v40.X, v40.Y, v40.Z), v41)
        else
            local v42 = Instance.new("Part")
            v42.CFrame = p38
            v42.Size = p39
            v42.Anchored = true
            v42.Parent = u37
        end
    end
    u43(p35, p36)
    local v44 = u17.new(u37)
    v44:relocate()
    return v44
end
function u17._calculateRegion(_, p45, p46) --[[Anonymous function at line 179]]
    local v47 = {
        ["Min"] = {},
        ["Max"] = {}
    }
    for u48, v49 in pairs(v47) do
        v49.Values = {}
        function v49.parseCheck(p50, p51) --[[Anonymous function at line 183]]
            --[[
            Upvalues:
                [1] = u48
            --]]
            if u48 == "Min" then
                return p50 <= p51
            end
            if u48 == "Max" then
                return p51 <= p50
            end
        end
        function v49.parse(p52, p53) --[[Anonymous function at line 190]]
            for v54, v55 in pairs(p53) do
                local v56 = p52.Values[v54] or v55
                if p52.parseCheck(v55, v56) then
                    p52.Values[v54] = v55
                end
            end
        end
    end
    for _, v57 in pairs(p45) do
        local v58 = v57.Size * 0.5
        local v59 = {
            v57.CFrame * CFrame.new(-v58.X, -v58.Y, -v58.Z),
            v57.CFrame * CFrame.new(-v58.X, -v58.Y, v58.Z),
            v57.CFrame * CFrame.new(-v58.X, v58.Y, -v58.Z),
            v57.CFrame * CFrame.new(-v58.X, v58.Y, v58.Z),
            v57.CFrame * CFrame.new(v58.X, -v58.Y, -v58.Z),
            v57.CFrame * CFrame.new(v58.X, -v58.Y, v58.Z),
            v57.CFrame * CFrame.new(v58.X, v58.Y, -v58.Z),
            v57.CFrame * CFrame.new(v58.X, v58.Y, v58.Z)
        }
        for _, v60 in pairs(v59) do
            local v61, v62, v63 = v60:GetComponents()
            local v64 = { v61, v62, v63 }
            v47.Min:parse(v64)
            v47.Max:parse(v64)
        end
    end
    local v65 = {}
    local v66 = {}
    for v67, v68 in pairs(v47) do
        for _, v72 in pairs(v68.Values) do
            local v70 = v67 == "Min" and v66 and v66 or v65
            if not p46 then
                local v71 = (v72 + (v67 == "Min" and -2 or 2) + 2) / 4
                local v72 = math.floor(v71) * 4
            end
            table.insert(v70, v72)
        end
    end
    local v73 = unpack
    local v74 = Vector3.new(v73(v66))
    local v75 = unpack
    local v76 = Vector3.new(v75(v65))
    return Region3.new(v74, v76), v74, v76
end
function u17._displayBounds(p77) --[[Anonymous function at line 245]]
    if not p77.displayBoundParts then
        p77.displayBoundParts = true
        local v78 = {
            ["BoundMin"] = p77.boundMin,
            ["BoundMax"] = p77.boundMax
        }
        for v79, v80 in pairs(v78) do
            local v81 = Instance.new("Part")
            v81.Anchored = true
            v81.CanCollide = false
            v81.Transparency = 0.5
            v81.Size = Vector3.new(1, 1, 1)
            v81.Color = Color3.fromRGB(255, 0, 0)
            v81.CFrame = CFrame.new(v80)
            v81.Name = v79
            v81.Parent = workspace
            p77.janitor:add(v81, "Destroy")
        end
    end
end
function u17._update(u82) --[[Anonymous function at line 264]]
    --[[
    Upvalues:
        [1] = u2
    --]]
    local v83 = u82.container
    local v84 = {}
    local u85 = 0
    u82._updateConnections:clean()
    local v86 = typeof(v83)
    local v87 = {}
    if v86 == "table" then
        for _, v88 in pairs(v83) do
            if v88:IsA("BasePart") then
                table.insert(v84, v88)
            end
        end
    elseif v86 == "Instance" then
        if v83:IsA("BasePart") then
            table.insert(v84, v83)
        else
            table.insert(v87, v83)
            for _, v89 in pairs(v83:GetDescendants()) do
                if v89:IsA("BasePart") then
                    table.insert(v84, v89)
                else
                    table.insert(v87, v89)
                end
            end
        end
    end
    u82.zoneParts = v84
    u82.overlapParams = {}
    local v90 = true
    for _, u91 in pairs(v84) do
        local _, v92 = pcall(function() --[[Anonymous function at line 298]]
            --[[
            Upvalues:
                [1] = u91
            --]]
            return u91.Shape.Name
        end)
        if v92 ~= "Block" then
            v90 = false
        end
    end
    u82.allZonePartsAreBlocks = v90
    local v93 = OverlapParams.new()
    v93.FilterType = Enum.RaycastFilterType.Whitelist
    v93.MaxParts = #v84
    v93.FilterDescendantsInstances = v84
    u82.overlapParams.zonePartsWhitelist = v93
    local v94 = OverlapParams.new()
    v94.FilterType = Enum.RaycastFilterType.Blacklist
    v94.FilterDescendantsInstances = v84
    u82.overlapParams.zonePartsIgnorelist = v94
    local function v97() --[[Anonymous function at line 318]]
        --[[
        Upvalues:
            [1] = u82
            [2] = u85
            [3] = u2
        --]]
        if u82.autoUpdate then
            local u95 = os.clock()
            if u82.respectUpdateQueue then
                u85 = u85 + 1
                u95 = u95 + 0.1
            end
            local u96 = nil
            u96 = u2.Heartbeat:Connect(function() --[[Anonymous function at line 326]]
                --[[
                Upvalues:
                    [1] = u95
                    [2] = u96
                    [3] = u82
                    [4] = u85
                --]]
                if u95 <= os.clock() then
                    u96:Disconnect()
                    if u82.respectUpdateQueue then
                        u85 = u85 - 1
                    end
                    if u85 == 0 and u82.zoneId then
                        u82:_update()
                    end
                end
            end)
        end
    end
    local v98 = { "Size", "Position" }
    for _, u99 in pairs(v84) do
        for _, v100 in pairs(v98) do
            u82._updateConnections:add(u99:GetPropertyChangedSignal(v100):Connect(v97), "Disconnect")
        end
        if u99.CollisionGroupId ~= 0 then
            error("Zone parts must belong to the \'Default\' (0) CollisionGroup! Consider using zone:relocate() if you wish to move zones outside of workspace to prevent them interacting with other parts.")
        end
        u82._updateConnections:add(u99:GetPropertyChangedSignal("CollisionGroupId"):Connect(function() --[[Anonymous function at line 350]]
            --[[
            Upvalues:
                [1] = u99
            --]]
            if u99.CollisionGroupId ~= 0 then
                error("Zone parts must belong to the \'Default\' (0) CollisionGroup! Consider using zone:relocate() if you wish to move zones outside of workspace to prevent them interacting with other parts.")
            end
        end), "Disconnect")
    end
    local v101 = { "ChildAdded", "ChildRemoved" }
    for _, _ in pairs(v87) do
        for _, v102 in pairs(v101) do
            u82._updateConnections:add(u82.container[v102]:Connect(function(p103) --[[Anonymous function at line 357]]
                --[[
                Upvalues:
                    [1] = u82
                    [2] = u85
                    [3] = u2
                --]]
                if p103:IsA("BasePart") and u82.autoUpdate then
                    local u104 = os.clock()
                    if u82.respectUpdateQueue then
                        u85 = u85 + 1
                        u104 = u104 + 0.1
                    end
                    local u105 = nil
                    u105 = u2.Heartbeat:Connect(function() --[[Anonymous function at line 326]]
                        --[[
                        Upvalues:
                            [1] = u104
                            [2] = u105
                            [3] = u82
                            [4] = u85
                        --]]
                        if u104 <= os.clock() then
                            u105:Disconnect()
                            if u82.respectUpdateQueue then
                                u85 = u85 - 1
                            end
                            if u85 == 0 and u82.zoneId then
                                u82:_update()
                            end
                        end
                    end)
                end
            end), "Disconnect")
        end
    end
    local v106, v107, v108 = u82:_calculateRegion(v84)
    local v109, _, _ = u82:_calculateRegion(v84, true)
    u82.region = v106
    u82.exactRegion = v109
    u82.boundMin = v107
    u82.boundMax = v108
    local v110 = v106.Size
    u82.volume = v110.X * v110.Y * v110.Z
    u82:_updateTouchedConnections()
    u82.updated:Fire()
end
function u17._updateOccupants(p111, p112, p113) --[[Anonymous function at line 393]]
    local v114 = p111.occupants[p112]
    if not v114 then
        v114 = {}
        p111.occupants[p112] = v114
    end
    local v115 = {}
    for v116, v117 in pairs(v114) do
        local v118 = p113[v116]
        if v118 == nil or v118 ~= v117 then
            v114[v116] = nil
            if not v115.exited then
                v115.exited = {}
            end
            local v119 = v115.exited
            table.insert(v119, v116)
        end
    end
    for v120, _ in pairs(p113) do
        if v114[v120] == nil then
            v114[v120] = v120:IsA("Player") and (v120.Character or true) or true
            if not v115.entered then
                v115.entered = {}
            end
            local v121 = v115.entered
            table.insert(v121, v120)
        end
    end
    return v115
end
function u17._formTouchedConnection(p122, p123) --[[Anonymous function at line 423]]
    --[[
    Upvalues:
        [1] = u7
    --]]
    local v124 = "_touchedJanitor" .. p123
    local v125 = p122[v124]
    if v125 then
        v125:clean()
    else
        p122[v124] = p122.janitor:add(u7.new(), "destroy")
    end
    p122:_updateTouchedConnection(p123)
end
function u17._updateTouchedConnection(p126, p127) --[[Anonymous function at line 435]]
    local v128 = p126["_touchedJanitor" .. p127]
    if v128 then
        for _, v129 in pairs(p126.zoneParts) do
            v128:add(v129.Touched:Connect(p126.touchedConnectionActions[p127], p126), "Disconnect")
        end
    end
end
function u17._updateTouchedConnections(p130) --[[Anonymous function at line 444]]
    for v131, _ in pairs(p130.touchedConnectionActions) do
        local v132 = p130["_touchedJanitor" .. v131]
        if v132 then
            v132:cleanup()
            p130:_updateTouchedConnection(v131)
        end
    end
end
function u17._disconnectTouchedConnection(p133, p134) --[[Anonymous function at line 455]]
    local v135 = "_touchedJanitor" .. p134
    local v136 = p133[v135]
    if v136 then
        v136:cleanup()
        p133[v135] = nil
    end
end
function u17._partTouchedZone(u137, u138) --[[Anonymous function at line 467]]
    --[[
    Upvalues:
        [1] = u7
        [2] = u3
        [3] = u6
    --]]
    local u139 = u137.trackingTouchedTriggers.part
    if not u139[u138] then
        local u140 = 0
        local u141 = false
        local u142 = u138.Position
        local u143 = os.clock()
        local u144 = u137.janitor:add(u7.new(), "destroy")
        u139[u138] = u144
        if not ({
            ["Seat"] = true,
            ["VehicleSeat"] = true
        })[u138.ClassName] and ({
            ["HumanoidRootPart"] = true
        })[u138.Name] then
            u138.CanTouch = false
        end
        local v145 = u138.Size.X * u138.Size.Y * u138.Size.Z * 100000
        local u146 = math.round(v145) * 0.00001
        u137.totalPartVolume = u137.totalPartVolume + u146
        u144:add(u3:Connect(function() --[[Anonymous function at line 485]]
            --[[
            Upvalues:
                [1] = u140
                [2] = u6
                [3] = u137
                [4] = u138
                [5] = u141
                [6] = u142
                [7] = u143
                [8] = u144
            --]]
            local v147 = os.clock()
            if u140 <= v147 then
                local v148 = u6.Accuracy.getProperty(u137.accuracy)
                u140 = v147 + v148
                local v149 = u137:findPoint(u138.CFrame) or u137:findPart(u138)
                if u141 then
                    if not v149 then
                        u141 = false
                        u142 = u138.Position
                        u143 = os.clock()
                        u137.partExited:Fire(u138)
                    end
                else
                    if v149 then
                        u141 = true
                        u137.partEntered:Fire(u138)
                        return
                    end
                    if (u138.Position - u142).Magnitude > 1.5 and v148 <= v147 - u143 then
                        u144:cleanup()
                        return
                    end
                end
            end
        end), "Disconnect")
        u144:add(function() --[[Anonymous function at line 516]]
            --[[
            Upvalues:
                [1] = u139
                [2] = u138
                [3] = u137
                [4] = u146
            --]]
            u139[u138] = nil
            u138.CanTouch = true
            local v150 = u137
            local v151 = (u137.totalPartVolume - u146) * 100000
            v150.totalPartVolume = math.round(v151) * 0.00001
        end, true)
    end
end
local u155 = {
    ["Ball"] = function(p152) --[[Anonymous function at line 524]]
        return "GetPartBoundsInRadius", { p152.Position, p152.Size.X }
    end,
    ["Block"] = function(p153) --[[Anonymous function at line 527]]
        return "GetPartBoundsInBox", { p153.CFrame, p153.Size }
    end,
    ["Other"] = function(p154) --[[Anonymous function at line 530]]
        return "GetPartsInPart", { p154 }
    end
}
function u17._getRegionConstructor(p156, u157, p158) --[[Anonymous function at line 534]]
    --[[
    Upvalues:
        [1] = u155
    --]]
    local v159, v160 = pcall(function() --[[Anonymous function at line 535]]
        --[[
        Upvalues:
            [1] = u157
        --]]
        return u157.Shape.Name
    end)
    local v161 = nil
    local v162 = nil
    if v159 and p156.allZonePartsAreBlocks then
        local v163 = u155[v160]
        if v163 then
            v161, v162 = v163(u157)
        end
    end
    if not v161 then
        v161, v162 = u155.Other(u157)
    end
    if p158 then
        table.insert(v162, p158)
    end
    return v161, v162
end
function u17.findLocalPlayer(p164) --[[Anonymous function at line 555]]
    --[[
    Upvalues:
        [1] = u4
    --]]
    if not u4 then
        error("Can only call \'findLocalPlayer\' on the client!")
    end
    return p164:findPlayer(u4)
end
function u17._find(p165, p166, p167) --[[Anonymous function at line 562]]
    --[[
    Upvalues:
        [1] = u14
    --]]
    u14.updateDetection(p165)
    local v168 = u14.trackers[p166]
    local v169 = u14.getTouchingZones(p167, false, p165._currentEnterDetection, v168)
    for _, v170 in pairs(v169) do
        if v170 == p165 then
            return true
        end
    end
    return false
end
function u17.findPlayer(p171, p172) --[[Anonymous function at line 574]]
    local v173 = p172.Character
    if v173 then
        v173 = v173:FindFirstChildOfClass("Humanoid")
    end
    if v173 then
        return p171:_find("player", p172.Character)
    else
        return false
    end
end
function u17.findItem(p174, p175) --[[Anonymous function at line 583]]
    return p174:_find("item", p175)
end
function u17.findPart(p176, p177) --[[Anonymous function at line 587]]
    local v178, v179 = p176:_getRegionConstructor(p177, p176.overlapParams.zonePartsWhitelist)
    local v180 = p176.worldModel[v178](p176.worldModel, unpack(v179))
    if #v180 > 0 then
        return true, v180
    else
        return false
    end
end
function u17.getCheckerPart(p181) --[[Anonymous function at line 597]]
    --[[
    Upvalues:
        [1] = u14
    --]]
    local v182 = p181.checkerPart
    if not v182 then
        v182 = p181.janitor:add(Instance.new("Part"), "Destroy")
        v182.Size = Vector3.new(0.1, 0.1, 0.1)
        v182.Name = "ZonePlusCheckerPart"
        v182.Anchored = true
        v182.Transparency = 1
        v182.CanCollide = false
        p181.checkerPart = v182
    end
    local v183 = p181.worldModel
    if v183 == workspace then
        v183 = u14.getWorkspaceContainer()
    end
    if v182.Parent ~= v183 then
        v182.Parent = v183
    end
    return v182
end
function u17.findPoint(p184, p185) --[[Anonymous function at line 618]]
    if typeof(p185) == "Vector3" then
        p185 = CFrame.new(p185)
    end
    local v186 = p184:getCheckerPart()
    v186.CFrame = p185
    local v187, v188 = p184:_getRegionConstructor(v186, p184.overlapParams.zonePartsWhitelist)
    local v189 = p184.worldModel[v187](p184.worldModel, unpack(v188))
    if #v189 > 0 then
        return true, v189
    else
        return false
    end
end
function u17._getAll(p190, p191) --[[Anonymous function at line 635]]
    --[[
    Upvalues:
        [1] = u14
    --]]
    u14.updateDetection(p190)
    local v192 = {}
    local v193 = u14._getZonesAndItems(p191, {
        ["self"] = true
    }, p190.volume, false, p190._currentEnterDetection)[p190]
    if v193 then
        for v194, _ in pairs(v193) do
            table.insert(v192, v194)
        end
    end
    return v192
end
function u17.getPlayers(p195) --[[Anonymous function at line 648]]
    return p195:_getAll("player")
end
function u17.getItems(p196) --[[Anonymous function at line 652]]
    return p196:_getAll("item")
end
function u17.getParts(p197) --[[Anonymous function at line 656]]
    local v198 = {}
    if p197.activeTriggers.part then
        local v199 = p197.trackingTouchedTriggers.part
        for v200, _ in pairs(v199) do
            table.insert(v198, v200)
        end
        return v198
    else
        local v201 = p197.worldModel:GetPartBoundsInBox(p197.region.CFrame, p197.region.Size, p197.overlapParams.zonePartsIgnorelist)
        for _, v202 in pairs(v201) do
            if p197:findPart(v202) then
                table.insert(v198, v202)
            end
        end
        return v198
    end
end
function u17.getRandomPoint(p203) --[[Anonymous function at line 677]]
    local v204 = p203.exactRegion
    local v205 = v204.Size
    local v206 = v204.CFrame
    local v207 = Random.new()
    local v208 = nil
    repeat
        local v209 = v206 * CFrame.new(v207:NextNumber(-v205.X / 2, v205.X / 2), v207:NextNumber(-v205.Y / 2, v205.Y / 2), v207:NextNumber(-v205.Z / 2, v205.Z / 2))
        local v210, v211 = p203:findPoint(v209)
        v208 = v210 and true or v208
    until v208
    return v209.Position, v211
end
function u17.setAccuracy(p212, p213) --[[Anonymous function at line 696]]
    --[[
    Upvalues:
        [1] = u6
    --]]
    local v214 = tonumber(p213)
    if v214 then
        if not u6.Accuracy.getName(v214) then
            error(("%s is an invalid enumId!"):format(v214))
        end
    else
        v214 = u6.Accuracy[p213]
        if not v214 then
            error(("\'%s\' is an invalid enumName!"):format(p213))
        end
    end
    p212.accuracy = v214
end
function u17.setDetection(p215, p216) --[[Anonymous function at line 712]]
    --[[
    Upvalues:
        [1] = u6
    --]]
    local v217 = tonumber(p216)
    if v217 then
        if not u6.Detection.getName(v217) then
            error(("%s is an invalid enumId!"):format(v217))
        end
    else
        v217 = u6.Detection[p216]
        if not v217 then
            error(("\'%s\' is an invalid enumName!"):format(p216))
        end
    end
    p215.enterDetection = v217
    p215.exitDetection = v217
end
function u17.trackItem(u218, u219) --[[Anonymous function at line 729]]
    --[[
    Upvalues:
        [1] = u7
        [2] = u12
    --]]
    local v220 = u219:IsA("BasePart")
    local v221
    if v220 then
        v221 = false
    else
        v221 = u219:FindFirstChildOfClass("Humanoid")
        if v221 then
            v221 = u219:FindFirstChild("HumanoidRootPart")
        end
    end
    assert(v220 or v221, "Only BaseParts or Characters/NPCs can be tracked!")
    if not u218.trackedItems[u219] then
        if u218.itemsToUntrack[u219] then
            u218.itemsToUntrack[u219] = nil
        end
        local v222 = u218.janitor:add(u7.new(), "destroy")
        local v223 = {
            ["janitor"] = v222,
            ["item"] = u219,
            ["isBasePart"] = v220,
            ["isCharacter"] = v221
        }
        u218.trackedItems[u219] = v223
        v222:add(u219.AncestryChanged:Connect(function() --[[Anonymous function at line 754]]
            --[[
            Upvalues:
                [1] = u219
                [2] = u218
            --]]
            if not u219:IsDescendantOf(game) then
                u218:untrackItem(u219)
            end
        end), "Disconnect")
        require(u12).itemAdded:Fire(v223)
    end
end
function u17.untrackItem(p224, p225) --[[Anonymous function at line 764]]
    --[[
    Upvalues:
        [1] = u12
    --]]
    local v226 = p224.trackedItems[p225]
    if v226 then
        v226.janitor:destroy()
    end
    p224.trackedItems[p225] = nil
    require(u12).itemRemoved:Fire(v226)
end
function u17.bindToGroup(p227, p228) --[[Anonymous function at line 775]]
    --[[
    Upvalues:
        [1] = u14
    --]]
    p227:unbindFromGroup()
    (u14.getGroup(p228) or u14.setGroup(p228))._memberZones[p227.zoneId] = p227
    p227.settingsGroupName = p228
end
function u17.unbindFromGroup(p229) --[[Anonymous function at line 782]]
    --[[
    Upvalues:
        [1] = u14
    --]]
    if p229.settingsGroupName then
        local v230 = u14.getGroup(p229.settingsGroupName)
        if v230 then
            v230._memberZones[p229.zoneId] = nil
        end
        p229.settingsGroupName = nil
    end
end
function u17.relocate(p231) --[[Anonymous function at line 792]]
    --[[
    Upvalues:
        [1] = u13
    --]]
    if not p231.hasRelocated then
        local v232 = require(u13).setupWorldModel(p231)
        p231.worldModel = v232
        p231.hasRelocated = true
        local v233 = p231.container
        if typeof(v233) == "table" then
            v233 = Instance.new("Folder")
            for _, v234 in pairs(p231.zoneParts) do
                v234.Parent = v233
            end
        end
        p231.relocationContainer = p231.janitor:add(v233, "Destroy", "RelocationContainer")
        v233.Parent = v232
    end
end
function u17._onItemCallback(u235, p236, p237, u238, u239) --[[Anonymous function at line 813]]
    local v240 = u235.onItemDetails[u238]
    if not v240 then
        v240 = {}
        u235.onItemDetails[u238] = v240
    end
    if #v240 == 0 then
        u235.itemsToUntrack[u238] = true
    end
    table.insert(v240, u238)
    u235:trackItem(u238)
    if u235:findItem(u238) == p237 then
        u239()
        if u235.itemsToUntrack[u238] then
            u235.itemsToUntrack[u238] = nil
            u235:untrackItem(u238)
            return
        end
    else
        local u241 = nil
        u241 = u235[p236]:Connect(function(p242) --[[Anonymous function at line 838]]
            --[[
            Upvalues:
                [1] = u241
                [2] = u238
                [3] = u239
                [4] = u235
            --]]
            if u241 and p242 == u238 then
                u241:Disconnect()
                u241 = nil
                u239()
                if u235.itemsToUntrack[u238] then
                    u235.itemsToUntrack[u238] = nil
                    u235:untrackItem(u238)
                end
            end
        end)
    end
end
function u17.onItemEnter(p243, ...) --[[Anonymous function at line 859]]
    p243:_onItemCallback("itemEntered", true, ...)
end
function u17.onItemExit(p244, ...) --[[Anonymous function at line 863]]
    p244:_onItemCallback("itemExited", false, ...)
end
function u17.destroy(p245) --[[Anonymous function at line 867]]
    p245:unbindFromGroup()
    p245.janitor:destroy()
end
u17.Destroy = u17.destroy
return u17