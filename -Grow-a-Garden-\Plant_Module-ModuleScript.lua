-- Full Path: -Grow-a-Garden-\\Plant_Module-ModuleScript.lua
local v1 = {}
require(game.ReplicatedStorage.Item_Module)
local u2 = game:GetService("TweenService")
local v3 = game:GetService("ServerScriptService")
local v4 = game:GetService("ReplicatedStorage")
local u5 = TweenInfo.new(0.25, Enum.EasingStyle.Quad, Enum.EasingDirection.Out, 0, false, 0)
local u6 = TweenInfo.new(1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out, 0, false, 0)
local u7 = game.ReplicatedStorage.GameEvents.PlaySound
local u8 = require(game.ReplicatedStorage.Data_Module)
local u9 = require(game.ReplicatedStorage.Item_Module)
local u10 = require(v3.Modules.QuestsService)
local u11 = require(v4.Modules.PlayerLuck)
local u12 = require(v4.Data.GrowableData)
function v1.Plant_Item(p13, p14, p15, p16, p17) --[[Anonymous function at line 16]]
    --[[
    Upvalues:
        [1] = u12
        [2] = u9
        [3] = u2
        [4] = u5
        [5] = u7
        [6] = u6
        [7] = u8
        [8] = u10
        [9] = u11
    --]]
    if p17 == "Super" then
        local v18 = math.random(1, 10000000)
        local v19 = game.ServerStorage.Collectables[p13]
        local v20 = u12:GetDataForPlant(v19)
        if v20 then
            local v21 = v19:Clone()
            v21.Variant.Value = u9.Return_Random_Super_Rarity()
            v21.Item_Seed.Value = v18
            v19.Item_Seed.Value = v18
            v21:AddTag(v21.Name)
            local v22 = CFrame.new
            local v23 = v20.PlantDown
            local v24 = v22(p14 - Vector3.new(0, v23, 0))
            math.randomseed(v21.Item_Seed.Value)
            local v25 = math.random(-180, 180)
            local v26 = v24 * CFrame.Angles(0, v25, 0)
            v21:PivotTo(v26)
            v21.Parent = p15.Important.Plants_Physical
            local u27 = game.ReplicatedStorage.Dirt:Clone()
            u27.Parent = game.Workspace.Dirt_VFX
            u27.Position = p14 - Vector3.new(0, 0.01, 0)
            local v28 = u27.Orientation
            local v29 = math.random(-180, 180)
            u27.Orientation = v28 + Vector3.new(0, v29, 0)
            u27.Size = Vector3.new(0.1, 1, 1)
            u27.Transparency = 1
            u2:Create(u27, u5, {
                ["Size"] = Vector3.new(0.1, 2, 2),
                ["Transparency"] = 0
            }):Play()
            u7:FireClient(p16, "Plant_SFX")
            task.spawn(function() --[[Anonymous function at line 49]]
                --[[
                Upvalues:
                    [1] = u2
                    [2] = u27
                    [3] = u6
                --]]
                task.wait(math.random(7, 11))
                u2:Create(u27, u6, {
                    ["Size"] = Vector3.new(0.1, 1.75, 1.75),
                    ["Transparency"] = 1
                }):Play()
                game.Debris:AddItem(u27, u6.Time)
            end)
            u8.AddPlantedObject(p16, p13, v18, v26, v19, p15.Owner_Tag.CFrame)
            u10:Progress(p16, "Plant", 1, { p13 })
        end
    else
        local v30 = math.random(1, 10000000)
        local v31 = game.ServerStorage.Collectables[p13]
        local v32 = u12:GetDataForPlant(v31)
        if v32 then
            local v33 = v31:Clone()
            if p17 then
                v33.Variant.Value = p17
            elseif game.ReplicatedStorage.Fruit_Spawn:FindFirstChild(v33.Name) then
                v33.Variant.Value = "Normal"
            else
                v33.Variant.Value = u9.Return_Random_Rarity(u11.GetLuck(p16, v33, "Plant"))
            end
            v33.Item_Seed.Value = v30
            v31.Item_Seed.Value = v30
            local v34 = CFrame.new
            local v35 = v32.PlantDown
            local v36 = v34(p14 - Vector3.new(0, v35, 0))
            math.randomseed(v33.Item_Seed.Value)
            local v37 = math.random(-180, 180)
            local v38 = v36 * CFrame.Angles(0, v37, 0)
            v33:PivotTo(v38)
            v33.Parent = p15.Important.Plants_Physical
            local u39 = game.ReplicatedStorage.Dirt:Clone()
            u39.Parent = game.Workspace.Dirt_VFX
            u39.Position = p14 - Vector3.new(0, 0.01, 0)
            local v40 = u39.Orientation
            local v41 = math.random(-180, 180)
            u39.Orientation = v40 + Vector3.new(0, v41, 0)
            u39.Size = Vector3.new(0.1, 1, 1)
            u39.Transparency = 1
            u2:Create(u39, u5, {
                ["Size"] = Vector3.new(0.1, 2, 2),
                ["Transparency"] = 0
            }):Play()
            u7:FireClient(p16, "Plant_SFX")
            task.spawn(function() --[[Anonymous function at line 89]]
                --[[
                Upvalues:
                    [1] = u2
                    [2] = u39
                    [3] = u6
                --]]
                task.wait(math.random(7, 11))
                u2:Create(u39, u6, {
                    ["Size"] = Vector3.new(0.1, 1.75, 1.75),
                    ["Transparency"] = 1
                }):Play()
                game.Debris:AddItem(u39, u6.Time)
            end)
            u8.AddPlantedObject(p16, p13, v30, v38, v31, p15.Owner_Tag.CFrame)
            u10:Progress(p16, "Plant", 1, { p13 })
        end
    end
end
return v1