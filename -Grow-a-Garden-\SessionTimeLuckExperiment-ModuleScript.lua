-- Full Path: -Grow-a-Garden-\\SessionTimeLuckExperiment-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
game:GetService("RunService")
require(v1.Modules.ABTestExperiments.ABTestTypes)
local v2 = {
    ["RemoteConfig"] = "SessionTimeLuck",
    ["Disabled"] = false,
    ["DefaultState"] = false
}
local v5 = {
    [false] = {
        ["Server"] = function(p3, _) --[[Function name: Server, line 16]]
            p3:SetAttribute("SessionTimeLuckDisabled", true)
        end
    },
    [true] = {
        ["Server"] = function(p4, _) --[[Function name: Server, line 21]]
            p4:SetAttribute("SessionTimeLuckDisabled", false)
        end
    }
}
v2.States = v5
return v2