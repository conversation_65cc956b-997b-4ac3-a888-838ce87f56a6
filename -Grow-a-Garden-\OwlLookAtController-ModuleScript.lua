-- Full Path: -Grow-a-Garden-\\OwlLookAtController-ModuleScript.lua
game:GetService("CollectionService")
local v1 = game:GetService("ReplicatedStorage")
local u2 = game:GetService("RunService")
local v3 = game:GetService("Players")
local u4 = game:GetService("TweenService")
local u5 = require(v1.Modules.Observers)
local u6 = v3.LocalPlayer
local u7 = {}
local u8 = {}
local u9 = nil
local u10 = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966)
local u11 = 0
local u12 = nil
local u13 = 0
function u8.UpdateTranslation(_, p14) --[[Anonymous function at line 36]]
    --[[
    Upvalues:
        [1] = u7
        [2] = u9
        [3] = u12
        [4] = u11
        [5] = u13
        [6] = u10
        [7] = u4
    --]]
    local v15 = p14 * -25
    local v16 = math.exp(v15)
    for v17, v18 in next, u7 do
        local v19 = v17:FindFirstChild("Head")
        if v19 then
            local v20
            if v18.TargetPart then
                v20 = v18.TargetPart:GetPivot()
            else
                v20 = nil
            end
            if u9 == nil then
                u9 = v19.CFrame.YVector
            end
            if u12 ~= nil then
                local v21 = v19.CFrame.YVector
                local v22 = v19.CFrame.XVector
                local v23 = u12:Dot(v21)
                local v24 = math.clamp(v23, -1, 1)
                local v25 = math.acos(v24)
                local v26 = u12:Cross(v21):Dot(v22)
                local v27 = v25 * math.sign(v26)
                u11 = u11 + math.deg(v27)
            end
            v19.Anchored = true
            if u11 > 270 or u13 == 1 then
                u13 = u11 <= 75 and 0 or 1
                v20 = CFrame.new(v19.Position.X - v19.CFrame.ZVector.X, v19.Position.Y - v19.CFrame.ZVector.Y, v19.Position.Z - v19.CFrame.ZVector.Z)
                v16 = v16 * 0.4
            end
            if u11 < -270 or u13 == 2 then
                u13 = u11 >= -75 and 0 or 2
                v20 = CFrame.new(v19.Position.X + v19.CFrame.ZVector.X, v19.Position.Y + v19.CFrame.ZVector.Y, v19.Position.Z + v19.CFrame.ZVector.Z)
                v16 = v16 * 0.4
            end
            if v20 then
                local v28 = CFrame.lookAt(v19.Position, v20.Position)
                local v29 = v28.Rotation.LookVector.Y
                local v30 = math.asin(v29)
                local v31 = math.deg(v30) - -15
                if math.deg(v30) < -15 then
                    v28 = v28 * CFrame.Angles(-math.rad(v31), 0, 0)
                end
                local v32 = {
                    ["CFrame"] = v28 * u10
                }
                u4:Create(v19, TweenInfo.new(v16, Enum.EasingStyle.Linear), v32):Play()
            else
                local v33 = {
                    ["CFrame"] = CFrame.lookAt(v19.Position, v19.Position + u9) * u10
                }
                u4:Create(v19, TweenInfo.new(v16, Enum.EasingStyle.Linear), v33):Play()
            end
            u12 = v19.CFrame.YVector
        end
    end
end
function u8.Update(_) --[[Anonymous function at line 116]]
    --[[
    Upvalues:
        [1] = u6
        [2] = u7
    --]]
    local v34 = u6.Character
    local v35
    if v34 then
        v35 = v34:FindFirstChild("Head")
    else
        v35 = nil
    end
    local v36
    if v35 then
        v36 = v35:GetPivot()
    else
        v36 = nil
    end
    for v37, v38 in next, u7 do
        if v38.Neck then
            local v39 = false
            if v36 then
                local v40 = v36.Position - v37:GetPivot().Position
                v39 = vector.magnitude(v40) <= 100 and true or v39
            end
            if v39 then
                v38.TargetPart = v35
            else
                v38.TargetPart = nil
            end
        end
    end
end
function u8.AddNPC(_, p41) --[[Anonymous function at line 141]]
    --[[
    Upvalues:
        [1] = u7
    --]]
    local v42 = p41:WaitForChild("Torso")
    if v42 then
        local v43 = p41:WaitForChild("Head")
        if v43 then
            local v44 = v42:WaitForChild("Neck")
            if v44 then
                u7[p41] = {
                    ["Torso"] = v42,
                    ["Head"] = v43,
                    ["Neck"] = v44,
                    ["InitialNeckC0"] = v44.C0
                }
            else
                warn((("Failed to find Neck for npc \"%*\""):format((p41:GetFullName()))))
            end
        else
            warn((("Failed to find Head for npc \"%*\""):format((p41:GetFullName()))))
            return
        end
    else
        warn((("Failed to find Torso for npc \"%*\""):format((p41:GetFullName()))))
        return
    end
end
function u8.RemoveNPC(_, p45) --[[Anonymous function at line 169]]
    --[[
    Upvalues:
        [1] = u7
    --]]
    u7[p45] = nil
end
function u8.Start(_) --[[Anonymous function at line 173]]
    --[[
    Upvalues:
        [1] = u5
        [2] = u8
        [3] = u2
    --]]
    u5.observeTag("OwlLookAt", function(u46) --[[Anonymous function at line 174]]
        --[[
        Upvalues:
            [1] = u8
        --]]
        u8:AddNPC(u46)
        return function() --[[Anonymous function at line 177]]
            --[[
            Upvalues:
                [1] = u8
                [2] = u46
            --]]
            u8:RemoveNPC(u46)
        end
    end, { workspace })
    local u47 = 0
    u2.PostSimulation:Connect(function(p48) --[[Anonymous function at line 183]]
        --[[
        Upvalues:
            [1] = u8
            [2] = u47
        --]]
        u8:UpdateTranslation(p48)
        u47 = u47 + p48
        if u47 >= 0.05 then
            u47 = 0
            u8:Update()
        end
    end)
end
task.spawn(u8.Start, u8)
return u8