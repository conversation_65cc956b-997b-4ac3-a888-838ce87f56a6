-- Full Path: -Grow-a-Garden-\\CosmeticService-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
game:GetService("ServerScriptService")
game:GetService("HttpService")
local u2 = {}
local u3 = require(v1.Modules.DataService)
local v4 = require(v1.Data.CosmeticRegistry)
local u5 = require(v1.Modules.CountDictionary)
local u6 = require(v1.Modules.CosmeticServices.CosmeticUtilities)
local u7 = require(v1.Modules.Notification)
local u8 = require(v1.Data.CosmeticRegistry.DefaultMutableStats)
local u9 = v1:WaitForChild("GameEvents"):WaitForChild("CosmeticService")
local u10 = v4.InputConfig.DEFAULT_PLACEMENT_CONFIG
local _ = v4.CosmeticList
function u2.HasMaxInventory(_) --[[Anonymous function at line 23]]
    --[[
    Upvalues:
        [1] = u3
        [2] = u2
        [3] = u5
    --]]
    local v11 = u3:GetData()
    if v11 then
        local v12 = v11.CosmeticData
        if v12.MutableStats then
            local v13 = u2:GetMutableStat("MaxCosmeticsInInventory")
            if v13 then
                local v14 = v12.Inventory
                if v14 then
                    return v13 <= u5(v14)
                else
                    return warn("CosmeticService:HasMaxInventory Passed player has corrupted CosmeticData.Inventory!")
                end
            else
                return warn("CosmeticService:HasMaxInventory Passed player has corrupted MaxCosmeticsInInventory Stats!")
            end
        else
            return warn("CosmeticService:HasMaxInventory Passed player has corrupted MutableStats!")
        end
    else
        return
    end
end
function u2.HasMaxEquipped(_) --[[Anonymous function at line 36]]
    --[[
    Upvalues:
        [1] = u3
        [2] = u2
        [3] = u5
    --]]
    local v15 = u3:GetData()
    if v15 then
        local v16 = v15.CosmeticData
        if v16.MutableStats then
            local v17 = u2:GetMutableStat("MaxEquippedCosmetics")
            if v17 then
                local v18 = v16.Equipped
                if v18 then
                    return v17 <= u5(v18)
                else
                    return warn("CosmeticService:HasMaxInventory Passed player has corrupted CosmeticData.Inventory!")
                end
            else
                return warn("CosmeticService:HasMaxInventory Passed player has corrupted MaxEquippedCosmetics Stats!")
            end
        else
            return warn("CosmeticService:HasMaxInventory Passed player has corrupted MutableStats!")
        end
    else
        return
    end
end
function u2.GetMutableStat(_, p19) --[[Anonymous function at line 49]]
    --[[
    Upvalues:
        [1] = u3
        [2] = u8
    --]]
    local v20 = u3:GetData()
    if v20 then
        local v21 = v20.CosmeticData.MutableStats
        if v21 then
            return (u8[p19] or 0) + v21[p19]
        else
            return warn("CosmeticService:HasMaxInventory Passed player has corrupted MutableStats!")
        end
    else
        return
    end
end
function u2.CanEquipAtLocation(_, p22) --[[Anonymous function at line 59]]
    --[[
    Upvalues:
        [1] = u10
        [2] = u6
    --]]
    local v23 = p22.Character
    if v23 then
        return u6:IsWithinFarm(p22, v23:GetPivot() * u10.CFRAME_OFFSET)
    else
        return false
    end
end
function u2.Equip(_, p24) --[[Anonymous function at line 66]]
    --[[
    Upvalues:
        [1] = u3
        [2] = u2
        [3] = u7
        [4] = u5
        [5] = u9
    --]]
    local v25 = u3:GetData()
    if v25 then
        local v26 = v25.CosmeticData.Equipped
        if not v26 then
            return warn("CosmeticService:HasMaxInventory Passed player has corrupted CosmeticData.Inventory!")
        end
        if u2:HasMaxEquipped() then
            return u7:CreateNotification((("Cannot place anymore you have max placed objects! %*"):format((u5(v26)))))
        end
        u9:FireServer("Equip", p24)
        return true
    end
end
function u2.Unequip(_, p27) --[[Anonymous function at line 77]]
    --[[
    Upvalues:
        [1] = u9
    --]]
    u9:FireServer("Unequip", p27)
    return true
end
function u2.DestroyCosmetic(_, p28) --[[Anonymous function at line 82]]
    --[[
    Upvalues:
        [1] = u9
    --]]
    u9:FireServer("DestroyCosmetic", p28)
    return true
end
function u2.UnequipAll(_) --[[Anonymous function at line 87]]
    --[[
    Upvalues:
        [1] = u9
    --]]
    u9:FireServer("UnequipAll")
    return true
end
function u2.GetAllCosmetics(_) --[[Anonymous function at line 92]]
    --[[
    Upvalues:
        [1] = u3
    --]]
    local v29 = u3:GetData()
    if v29 then
        return v29.CosmeticData.Inventory or warn("CosmeticService:GetAllCosmetics Passed player has corrupted CosmeticData.Inventory!")
    end
end
function u2.GetAllEquippedCosmetics(_) --[[Anonymous function at line 99]]
    --[[
    Upvalues:
        [1] = u3
    --]]
    local v30 = u3:GetData()
    if v30 then
        return v30.CosmeticData.Equipped or warn("CosmeticService:GetAllCosmetics Passed player has corrupted CosmeticData.Equipped!")
    end
end
function u2.IsCosmeticEquipped(_, p31) --[[Anonymous function at line 107]]
    --[[
    Upvalues:
        [1] = u3
    --]]
    local v32 = u3:GetData()
    if v32 then
        local v33 = v32.CosmeticData.Equipped
        if v33 then
            return v33[p31] ~= nil
        else
            return warn("CosmeticService:IsCosmeticEquipped Passed player has corrupted CosmeticData.Equipped!")
        end
    else
        return
    end
end
return u2