-- Full Path: -Grow-a-Garden-\\CosmeticShopUIController-ModuleScript.lua
local u1 = game:GetService("ReplicatedStorage")
game:GetService("UserInputService")
game:GetService("ContentProvider")
local u2 = game:GetService("TweenService")
local v3 = game.Players.LocalPlayer.PlayerGui
local u4 = v3:WaitFor<PERSON>hild("CosmeticShop_UI")
local u5 = require(u1.Modules.GuiController)
local u6 = require(u1.Modules.DataService)
local u7 = require(u1.Modules.UpdateService)
require(u1.Comma_Module)
local v8 = require(u1.Modules.Signal)
local v9 = require(u1.Modules.WaitForDescendant)
local u10 = require(u1.Modules.NumberUtil)
require(u1.Modules.FastTween)
local u11 = require(u1.Modules.MarketController)
require(u1.Modules.GiftController)
local u12 = require(u1.Data.CosmeticShopConfigData)
local u13 = require(u1.Item_Module)
require(u1.Modules.DumpTable)
local u14 = require(u1.Data.DecimalNumberFormat)
local u15 = require(u1.Data.CosmeticRegistry).CosmeticList
local u16 = require(u1.Data.CosmeticCrateRegistry).CosmeticCrates
local u17 = require(u1.Data.CosmeticItemShopData)
local u18 = require(u1.Data.CosmeticCrateShopData)
local u19 = require(game.ReplicatedStorage.Frame_Popup_Module)
v8.new()
local u20 = u4:FindFirstChild("ContentFrame", true)
local u21 = u4:FindFirstChild("TimerLabel", true)
local u22 = u20:FindFirstChild("TopItemTemplate", true)
u22.Parent = script
local u23 = u20:FindFirstChild("BottomItemTemplate", true)
u23.Parent = script
local u24 = v3.ShowCrateOdds
local u25 = v9(u24, "CrateChanceDetails")
local u26 = v9(u24, "CrateItemTemplate")
local u27 = v9(u24, "HeaderTitle")
local u28 = v9(u24, "ExitButton")
local u29 = false
local u30 = {}
local v31 = {}
local function u35() --[[Anonymous function at line 76]]
    --[[
    Upvalues:
        [1] = u6
        [2] = u12
        [3] = u21
        [4] = u10
    --]]
    while true do
        local v32 = workspace:GetServerTimeNow()
        local v33 = u6:GetData()
        local v34 = v33.CosmeticStock.ForcedCosmeticEndTimestamp and (v33.CosmeticStock.ForcedCosmeticEndTimestamp - v32 or -1) or -1
        if v34 < 0 then
            v34 = u12.RefreshTime - v32 % u12.RefreshTime
        end
        u21.Text = v34 <= 0 and "Restocking" or "RESETS IN: " .. u10.compactFormat(v34)
        task.wait(1)
    end
end
local function u51(p36) --[[Anonymous function at line 100]]
    --[[
    Upvalues:
        [1] = u16
        [2] = u27
        [3] = u25
        [4] = u26
        [5] = u15
        [6] = u14
        [7] = u24
        [8] = u19
    --]]
    local v37 = u16[p36]
    local v38 = v37.Color
    local v39 = v37.CosmeticRolls.Items
    local v40 = v38.R * 255
    local v41 = math.floor(v40)
    local v42 = v38.G * 255
    local v43 = math.floor(v42)
    local v44 = v38.B * 255
    local v45 = math.floor(v44)
    local v46 = string.format("#%02X%02X%02X", v41, v43, v45)
    u27.Text = string.format("<font color=\"%s\">%s</font> Chances", v46, p36)
    for _, v47 in u25:GetChildren() do
        if v47:IsA("Frame") and v47.Visible then
            v47:Destroy()
        end
    end
    for v48, v49 in v39 do
        local v50 = u26:Clone()
        v50.Parent = u25
        v50.Visible = true
        if u15[v48] and u15[v48].Icon then
            v50.Detail.CrateImageLabel.Image = u15[v48].Icon
        end
        v50.CrateChance.Text = ("%*%%"):format((u14(v49.NormalizedOdd)))
        v50.LayoutOrder = 100 / v49.NormalizedOdd
        v50.CrateName.Text = v48
    end
    u24.Enabled = true
    u19.Show(u24)
end
local function u99(u52, u53, u54, u55) --[[Anonymous function at line 138]]
    --[[
    Upvalues:
        [1] = u51
        [2] = u16
        [3] = u15
        [4] = u13
        [5] = u20
        [6] = u2
        [7] = u11
        [8] = u10
        [9] = u29
        [10] = u1
        [11] = u6
        [12] = u7
    --]]
    local u56 = u52.Main
    local v57
    if u55 then
        local u58 = u54.CrateName
        local v59 = u56:FindFirstChild("CrateInfoButton", true)
        v59.Visible = true
        v59.Activated:Connect(function() --[[Anonymous function at line 147]]
            --[[
            Upvalues:
                [1] = u51
                [2] = u58
            --]]
            u51(u58)
        end)
        v57 = u58
    else
        v57 = u54.CosmeticName
    end
    local v60
    if u55 then
        v60 = u16[v57]
    else
        v60 = u15[v57]
    end
    local u61 = u56:FindFirstChild("ITEM_NAME", true)
    local u62 = u56:FindFirstChild("ITEM_NAME_SHADOW", true)
    u61.Text = v57
    u62.Text = v57
    local v63 = u55 and u54.CrateRarity or v60.Rarity
    local v64 = u13.Return_Rarity_Data(v63)
    if v64 then
        local v65 = v64[1]
        local v66 = v64[2]
        local v67 = v64[3]
        u61.TextColor3 = v66
        local v68, v69, v70 = v66:ToHSV()
        u62.TextColor3 = Color3.fromHSV(v68, v69, v70 / 2)
        u56.BackgroundColor3 = v66
        if u52:IsDescendantOf(u20.TopSegment) then
            u2:Create(u56, TweenInfo.new(1, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut, -1, true), {
                ["BackgroundTransparency"] = 0.3
            }):Play()
        end
        if v67 and v65 == "Prismatic" then
            local u71 = nil
            u71 = game:GetService("RunService").Heartbeat:Connect(function() --[[Anonymous function at line 204]]
                --[[
                Upvalues:
                    [1] = u52
                    [2] = u71
                    [3] = u61
                    [4] = u62
                    [5] = u56
                --]]
                if u52 and u52.Parent then
                    local v72 = tick() * 0.1 % 1
                    local v73 = Color3.fromHSV(v72, 1, 1)
                    u61.TextColor3 = v73
                    local v74 = u62
                    local v75, v76, v77 = v73:ToHSV()
                    v74.TextColor3 = Color3.fromHSV(v75, v76, v77 / 2)
                    local v78 = u56
                    local v79, v80, v81 = v73:ToHSV()
                    v78.BackgroundColor3 = Color3.fromHSV(v79, v80, v81 / 2)
                else
                    u71:Disconnect()
                end
            end)
        end
    end
    u56:FindFirstChild("ITEM_IMAGE", true).Image = v60.Icon
    if v60.Rarity ~= "Common" and v60.Rarity ~= "Uncommon" then
        local u82 = u56:FindFirstChild("ROBUX_BUY_BUTTON", true)
        u82.Activated:Connect(function() --[[Anonymous function at line 228]]
            --[[
            Upvalues:
                [1] = u11
                [2] = u54
            --]]
            u11:PromptPurchase(u54.PurchaseID, Enum.InfoType.Product)
        end)
        task.spawn(function() --[[Anonymous function at line 232]]
            --[[
            Upvalues:
                [1] = u11
                [2] = u82
                [3] = u54
            --]]
            u11:SetPriceLabel(u82.ROBUX_BUY_TEXT, u54.PurchaseID, ":robux::value:")
        end)
    end
    local u83 = u56:FindFirstChild("SHECKLES_BUY_FRAME", true)
    local v84 = u83:FindFirstChild("SHECKLES_BUY_BUTTON", true)
    local u85 = u83:FindFirstChild("SHECKLES_BUY_TEXT", true)
    u85.Text = u10.FormatCompactPrice(u54.Price)
    v84.Activated:Connect(function() --[[Anonymous function at line 243]]
        --[[
        Upvalues:
            [1] = u53
            [2] = u29
            [3] = u55
            [4] = u1
        --]]
        if workspace:GetAttribute("InTutorial") and u53 ~= "Carrot" or u29 then
            print("Buuton Bounced")
        else
            u29 = true
            if u55 then
                u1.GameEvents.BuyCosmeticCrate:FireServer(u53)
            else
                u1.GameEvents.BuyCosmeticItem:FireServer(u53)
            end
            task.wait(0.5)
            u29 = false
        end
    end)
    local function v98() --[[Anonymous function at line 260]]
        --[[
        Upvalues:
            [1] = u6
            [2] = u55
            [3] = u53
            [4] = u56
            [5] = u83
            [6] = u85
            [7] = u10
            [8] = u54
        --]]
        local v86 = u6:GetData()
        local v87
        if u55 then
            v87 = v86.CosmeticStock.CrateStocks
        else
            v87 = v86.CosmeticStock.ItemStocks
        end
        if v86 then
            v86 = v87[u53]
        end
        local v88 = v86 and v86.Stock or 0
        local v89 = ("X%* Stock"):format(v88)
        u56:FindFirstChild("STOCK_TEXT", true).Text = v89
        local v90 = u83
        local v91
        if v88 <= 0 then
            v91 = Color3.fromRGB(169, 169, 169)
        else
            v91 = Color3.fromRGB(83, 171, 52)
        end
        v90.BackgroundColor3 = v91
        local v92 = u83.UIStroke
        local v93
        if v88 <= 0 then
            v93 = Color3.fromRGB(105, 105, 105)
        else
            v93 = Color3.fromRGB(105, 207, 64)
        end
        v92.Color = v93
        u85.Text = v88 <= 0 and "NO STOCK" or u10.FormatCompactPrice(u54.Price)
        local v94 = u85
        local v95
        if v88 <= 0 then
            v95 = Color3.fromRGB(192, 192, 192)
        else
            v95 = Color3.fromRGB(0, 255, 0)
        end
        v94.TextColor3 = v95
        local v96 = u85.UIStroke
        local v97
        if v88 <= 0 then
            v97 = Color3.fromRGB(105, 105, 105)
        else
            v97 = Color3.fromRGB(57, 116, 35)
        end
        v96.Color = v97
    end
    task.spawn(v98)
    if u7:IsHiddenFromUpdate(v60.CosmeticName) then
        u56.Visible = false
        task.delay(u7:GetRemainingTimeUntilUpdate(), function() --[[Anonymous function at line 303]]
            --[[
            Upvalues:
                [1] = u56
            --]]
            u56.Visible = true
        end)
    end
end
local function u116() --[[Anonymous function at line 310]]
    --[[
    Upvalues:
        [1] = u35
        [2] = u30
        [3] = u6
        [4] = u18
        [5] = u22
        [6] = u20
        [7] = u99
        [8] = u17
        [9] = u15
        [10] = u23
    --]]
    task.spawn(u35)
    for _, v100 in u30 do
        v100:Destroy()
    end
    local v101 = u6:GetData()
    local v102 = v101.CosmeticStock.ItemStocks
    local v103 = v101.CosmeticStock.CrateStocks
    local v104 = {}
    for v105, _ in pairs(v103) do
        local v106 = u18[v105]
        local v107 = u22:Clone()
        v107.Parent = u20.TopSegment
        u30[v105] = v107
        v107.Name = v105
        table.insert(v104, v107)
        u99(v107, v105, v106, true)
    end
    for v108, _ in pairs(v102) do
        local v109 = u17[v108]
        local v110 = u15[v108]
        local v111
        if v110.Rarity == "Common" or v110.Rarity == "Uncommon" then
            v111 = u23:Clone()
            v111.Parent = u20.BottomSegment
        else
            v111 = u22:Clone()
            v111.Parent = u20.TopSegment
        end
        u30[v108] = v111
        v111.Name = v108
        table.insert(v104, v111)
        u99(v111, v108, v109)
    end
    table.sort(v104, function(p112, p113) --[[Anonymous function at line 362]]
        local v114 = p112.LayoutOrder
        local v115 = p113.LayoutOrder
        if v114 == v115 then
            return p112.Name < p113.Name
        else
            return v114 < v115
        end
    end)
end
function v31.Start(_) --[[Anonymous function at line 372]]
    --[[
    Upvalues:
        [1] = u4
        [2] = u5
        [3] = u11
        [4] = u28
        [5] = u24
        [6] = u6
        [7] = u116
    --]]
    u4:FindFirstChild("ExitButton", true).Activated:Connect(function() --[[Anonymous function at line 376]]
        --[[
        Upvalues:
            [1] = u5
            [2] = u4
        --]]
        u5:Close(u4)
        u4.Enabled = false
    end)
    u4:FindFirstChild("RestockButton", true).Activated:Connect(function() --[[Anonymous function at line 382]]
        --[[
        Upvalues:
            [1] = u11
        --]]
        u11:PromptPurchase(3290193471, Enum.InfoType.Product)
    end)
    u28.Activated:Connect(function() --[[Anonymous function at line 386]]
        --[[
        Upvalues:
            [1] = u24
        --]]
        u24.Enabled = false
    end)
    local v117 = u6:GetPathSignal("CosmeticStock/@")
    if v117 then
        v117:Connect(u116)
    end
    local v118 = u6:GetPathSignal("CosmeticStock")
    if v118 then
        v118:Connect(u116)
    end
    u116()
end
task.spawn(v31.Start, v31)
return v31