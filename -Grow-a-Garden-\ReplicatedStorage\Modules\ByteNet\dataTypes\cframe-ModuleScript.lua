-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\ByteNet\dataTypes\cframe-ModuleScript.lua
local v1 = require(script.Parent.Parent.process.bufferWriter)
require(script.Parent.Parent.types)
local u2 = v1.f32NoAlloc
local u3 = v1.alloc
local u28 = {
    ["read"] = function(p4, p5) --[[Function name: read, line 9]]
        local v6 = buffer.readf32(p4, p5)
        local v7 = p5 + 4
        local v8 = buffer.readf32(p4, v7)
        local v9 = p5 + 8
        local v10 = buffer.readf32(p4, v9)
        local v11 = p5 + 12
        local v12 = buffer.readf32(p4, v11)
        local v13 = p5 + 16
        local v14 = buffer.readf32(p4, v13)
        local v15 = p5 + 20
        local v16 = buffer.readf32(p4, v15)
        local v17 = Vector3.new(v12, v14, v16)
        local v18 = v17.Magnitude
        return CFrame.fromAxisAngle(v17, v18) + Vector3.new(v6, v8, v10), 24
    end,
    ["write"] = function(p19) --[[Function name: write, line 23]]
        --[[
        Upvalues:
            [1] = u3
            [2] = u2
        --]]
        local v20 = p19.X
        local v21 = p19.Y
        local v22 = p19.Z
        local v23, v24 = p19:ToAxisAngle()
        local v25 = v23.X
        local v26 = v23.Y
        local v27 = v23.Z
        local _ = v23 * v24
        u3(24)
        u2(v20)
        u2(v21)
        u2(v22)
        u2(v25)
        u2(v26)
        u2(v27)
    end
}
return function() --[[Anonymous function at line 42]]
    --[[
    Upvalues:
        [1] = u28
    --]]
    return u28
end