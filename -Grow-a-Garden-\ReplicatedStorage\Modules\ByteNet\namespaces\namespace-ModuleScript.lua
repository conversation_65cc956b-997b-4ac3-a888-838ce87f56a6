-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\ByteNet\namespaces\namespace-ModuleScript.lua
local v1 = game:GetService("RunService")
local u2 = require(script.Parent.Parent.replicated.values)
require(script.Parent.Parent.types)
local u3 = require(script.Parent.namespacesDependencies)
local u4 = require(script.Parent.packetIDs)
local u5 = v1:IsServer() and "server" or "client"
local u6 = 0
return function(p7, p8) --[[Anonymous function at line 19]]
    --[[
    Upvalues:
        [1] = u2
        [2] = u3
        [3] = u5
        [4] = u6
        [5] = u4
    --]]
    local v9 = u2.access(p7)
    u3.start(p7)
    local v10 = p8()
    local v11 = u3.empty()
    local v12 = {}
    if u5 ~= "server" then
        if u5 == "client" then
            local v13 = v9:read()
            for v14, v15 in v10 do
                v12[v14] = v15(v13.packets[v14])
                u4.set(v13.packets[v14], v12[v14])
            end
        end
        return v12
    end
    local v16 = {
        ["structs"] = {},
        ["packets"] = {}
    }
    for v17 in v10 do
        u6 = u6 + 1
        v16.packets[v17] = u6
        v12[v17] = v10[v17](u6)
        u4.set(u6, v12[v17])
    end
    for v18, v19 in v11 do
        v16.structs[v18] = v19
    end
    v9:write(v16)
    return v12
end