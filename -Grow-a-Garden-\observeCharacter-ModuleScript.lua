-- Full Path: -Grow-a-Garden-\\observeCharacter-ModuleScript.lua
local u1 = require(script.Parent.observePlayer)
return function(u2) --[[Function name: <PERSON><PERSON><PERSON><PERSON>, line 21]]
    --[[
    Upvalues:
        [1] = u1
    --]]
    return u1(function(u3) --[[Anonymous function at line 22]]
        --[[
        Upvalues:
            [1] = u2
        --]]
        local u4 = nil
        local u5 = nil
        local function u11(u6) --[[Anonymous function at line 27]]
            --[[
            Upvalues:
                [1] = u2
                [2] = u3
                [3] = u5
                [4] = u4
            --]]
            local u7 = nil
            task.defer(function() --[[Anonymous function at line 31]]
                --[[
                Upvalues:
                    [1] = u2
                    [2] = u3
                    [3] = u6
                    [4] = u5
                    [5] = u7
                    [6] = u4
                --]]
                local v8 = u2(u3, u6)
                if typeof(v8) == "function" then
                    if u5.Connected and u6.Parent then
                        u7 = v8
                        u4 = v8
                        return
                    end
                    task.spawn(v8)
                end
            end)
            local u9 = nil
            u9 = u6.AncestryChanged:Connect(function(_, p10) --[[Anonymous function at line 47]]
                --[[
                Upvalues:
                    [1] = u9
                    [2] = u7
                    [3] = u4
                --]]
                if p10 == nil and u9.Connected then
                    u9:Disconnect()
                    if u7 ~= nil then
                        task.spawn(u7)
                        if u4 == u7 then
                            u4 = nil
                        end
                        u7 = nil
                    end
                end
            end)
        end
        u5 = u3.CharacterAdded:Connect(u11)
        task.defer(function() --[[Anonymous function at line 65]]
            --[[
            Upvalues:
                [1] = u3
                [2] = u5
                [3] = u11
            --]]
            if u3.Character and u5.Connected then
                task.spawn(u11, u3.Character)
            end
        end)
        return function() --[[Anonymous function at line 72]]
            --[[
            Upvalues:
                [1] = u5
                [2] = u4
            --]]
            u5:Disconnect()
            if u4 ~= nil then
                task.spawn(u4)
                u4 = nil
            end
        end
    end)
end