-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Code\LightningBolt-ModuleScript.lua
local u1 = os.clock
local v2 = game:GetService("Workspace")
local u3 = game:GetService("RunService")
local v4 = v2.CurrentCamera
local v5 = Instance.new("Part")
v5.TopSurface = 0
v5.BottomSurface = 0
v5.Anchored = true
v5.CanCollide = false
v5.Locked = true
v5.CastShadow = false
v5.Shape = "Cylinder"
v5.Name = "BoltPart"
v5.Material = Enum.Material.Neon
v5.Color = Color3.new(1, 1, 1)
v5.Transparency = 1
local u6 = require(script.Parent.PartCache).new(v5, 120)
u6:SetCacheParent(v4)
local function u12(p7, p8, p9, p10, p11) --[[Anonymous function at line 33]]
    return p8 * (1 - p7) ^ 3 + p9 * 3 * p7 * (1 - p7) ^ 2 + p10 * 3 * (1 - p7) * p7 ^ 2 + p11 * p7 ^ 3
end
local function u23(p13, p14, p15, p16, p17, p18, p19) --[[Anonymous function at line 40]]
    local v20 = p16 / (2 * p17)
    local v21 = (p13 - p14 * p15 + 0.5 * p16) / p17
    local v22 = v20 - math.abs(v21)
    return math.clamp(v22, p18, p19)
end
local function u26(p24) --[[Anonymous function at line 44]]
    local v25 = -5000 * (p24 - 0.5) ^ 10
    return math.exp(v25)
end
local u27 = CFrame.lookAt(Vector3.new(), Vector3.new(1, 0, 0)):inverse()
local u28 = {}
local u29 = {
    ["__type"] = "LightningBolt"
}
u29.__index = u29
function u29.new(p30, p31, p32) --[[Anonymous function at line 69]]
    --[[
    Upvalues:
        [1] = u29
        [2] = u12
        [3] = u23
        [4] = u26
        [5] = u6
        [6] = u1
        [7] = u28
    --]]
    local v33 = u29
    local v34 = setmetatable({}, v33)
    v34.Enabled = true
    v34.Attachment0 = p30
    v34.Attachment1 = p31
    v34.CurveSize0 = 0
    v34.CurveSize1 = 0
    v34.MinRadius = 0
    v34.MaxRadius = 2.4
    v34.Frequency = 1
    v34.AnimationSpeed = 7
    v34.Thickness = 1
    v34.MinThicknessMultiplier = 0.2
    v34.MaxThicknessMultiplier = 1
    v34.MinTransparency = 0
    v34.MaxTransparency = 1
    v34.PulseSpeed = 2
    v34.PulseLength = 1000000
    v34.FadeLength = 0.2
    v34.ContractFrom = 0.5
    v34.Color = Color3.new(1, 1, 1)
    v34.ColorOffsetSpeed = 3
    v34.SpaceCurveFunction = u12
    v34.OpacityProfileFunction = u23
    v34.RadialProfileFunction = u26
    v34._Parts = {}
    for v35 = 1, p32 or 30 do
        v34._Parts[v35] = u6:GetPart()
    end
    v34._PartsHidden = false
    v34._DisabledTransparency = 1
    v34._StartT = u1()
    v34._RanNum = math.random() * 100
    v34._RefIndex = #u28 + 1
    u28[v34._RefIndex] = v34
    return v34
end
function u29.Destroy(p36) --[[Anonymous function at line 154]]
    --[[
    Upvalues:
        [1] = u28
        [2] = u6
    --]]
    u28[p36._RefIndex] = nil
    for v37 = 1, #p36._Parts do
        u6:ReturnPart(p36._Parts[v37])
    end
end
function u29.DestroyDissipate(u38, p39, p40) --[[Anonymous function at line 165]]
    --[[
    Upvalues:
        [1] = u1
        [2] = u3
    --]]
    local u41 = p39 or 0.2
    local u42 = p40 or 0.5
    local u43 = u1()
    local u44 = u38.MinTransparency
    local u45 = u38.ContractFrom
    local u46 = u38.ContractFrom + 1 / (#u38._Parts * u38.FadeLength)
    local u47 = u38.MaxRadius
    local u48 = u38.MinThicknessMultiplier
    local u49 = nil
    u49 = u3.Heartbeat:Connect(function() --[[Anonymous function at line 175]]
        --[[
        Upvalues:
            [1] = u1
            [2] = u43
            [3] = u38
            [4] = u48
            [5] = u41
            [6] = u44
            [7] = u45
            [8] = u46
            [9] = u47
            [10] = u42
            [11] = u49
        --]]
        local v50 = u1() - u43
        u38.MinThicknessMultiplier = u48 + (-2 - u48) * v50 / u41
        if v50 < u41 * 0.4 then
            local v51 = v50 / (u41 * 0.4)
            u38.MinTransparency = u44 + (u45 - u44) * v51
            return
        elseif v50 < u41 then
            local v52 = (v50 - u41 * 0.4) / (u41 * 0.6)
            u38.MinTransparency = u45 + (u46 - u45) * v52
            u38.MaxRadius = u47 * (1 + u42 * v52)
            u38.MinRadius = u38.MinRadius + (u38.MaxRadius - u38.MinRadius) * v52
        else
            if u1() - u38._StartT < (u38.PulseLength + 1) / u38.PulseSpeed then
                u38:Destroy()
            end
            u49:Disconnect()
            u49 = nil
        end
    end)
end
function u29._UpdateGeometry(p53, p54, p55, p56, p57, p58, p59) --[[Anonymous function at line 203]]
    --[[
    Upvalues:
        [1] = u27
    --]]
    local v60 = 1 - p53.MaxTransparency
    local v61 = 1 - p53.MinTransparency
    local v62 = p53.OpacityProfileFunction(p55, p56, p53.PulseSpeed, p53.PulseLength, p53.FadeLength, v60, v61)
    local v63 = p53.Thickness * p57 * v62
    local v64 = v63 > 0 and v62 and v62 or 0
    local v65 = 1 - p53.ContractFrom
    local v66 = #p53._Parts
    if v65 < v64 then
        local v67 = (p59 - p58).Magnitude
        p54.Size = Vector3.new(v67, v63, v63)
        p54.CFrame = CFrame.lookAt((p58 + p59) * 0.5, p59) * u27
        p54.Transparency = 1 - v64
        return
    elseif v65 - 1 / (v66 * p53.FadeLength) < v64 then
        local v68 = (1 - (v64 - (v65 - 1 / (v66 * p53.FadeLength))) * v66 * p53.FadeLength) * (p55 < p56 * p53.PulseSpeed - 0.5 * p53.PulseLength and 1 or -1)
        local v69 = (1 - math.abs(v68)) * (p59 - p58).Magnitude
        p54.Size = Vector3.new(v69, v63, v63)
        p54.CFrame = CFrame.lookAt(p58 + (p59 - p58) * (math.max(0, v68) + (1 - math.abs(v68)) * 0.5), p59) * u27
        p54.Transparency = 1 - v64
    else
        p54.Transparency = 1
    end
end
function u29._UpdateColor(p70, p71, p72, p73) --[[Anonymous function at line 249]]
    local v74 = p70.Color
    if typeof(v74) == "Color3" then
        p71.Color = p70.Color
    else
        local v75 = (p70._RanNum + p72 - p73 * p70.ColorOffsetSpeed) % 1
        local v76 = p70.Color.Keypoints
        for v77 = 1, #v76 - 1 do
            if v76[v77].Time < v75 and v75 < v76[v77 + 1].Time then
                p71.Color = v76[v77].Value:lerp(v76[v77 + 1].Value, (v75 - v76[v77].Time) / (v76[v77 + 1].Time - v76[v77].Time))
                return
            end
        end
    end
end
function u29._Disable(p78) --[[Anonymous function at line 267]]
    p78.Enabled = false
    for _, v79 in ipairs(p78._Parts) do
        v79.Transparency = p78._DisabledTransparency
    end
end
u3.Heartbeat:Connect(function() --[[Anonymous function at line 274]]
    --[[
    Upvalues:
        [1] = u28
        [2] = u1
    --]]
    debug.profilebegin("LightningBolt")
    for _, v80 in pairs(u28) do
        if v80.Enabled == true then
            v80._PartsHidden = false
            local v81 = v80.MinRadius
            local v82 = v80.MaxRadius
            local v83 = v80._Parts
            local v84 = #v83
            local v85 = v80._RanNum
            local v86 = v80.AnimationSpeed
            local v87 = v80.Frequency
            local v88 = v80.MinThicknessMultiplier
            local v89 = v80.MaxThicknessMultiplier
            local v90 = u1() - v80._StartT
            local v91 = v80.SpaceCurveFunction
            local v92 = v80.RadialProfileFunction
            local v93 = (v80.PulseLength + 1) / v80.PulseSpeed
            local v94 = v80.Attachment0
            local v95 = v80.Attachment1
            local v96 = v80.CurveSize0
            local v97 = v80.CurveSize1
            local v98 = v94.WorldPosition
            local v99 = v94.WorldPosition + v94.WorldAxis * v96
            local v100 = v95.WorldPosition - v95.WorldAxis * v97
            local v101 = v95.WorldPosition
            local v102 = v91(0, v98, v99, v100, v101)
            if v90 < v93 then
                local v103 = v102
                for v104 = 1, v84 do
                    local v105 = v83[v104]
                    local v106 = v104 / v84
                    local v107 = v86 * -v90 + v87 * 10 * v106 - 0.2 + v85 * 4
                    local v108 = 5 * (v86 * 0.01 * -v90 / 10 + v87 * v106) + v85 * 4
                    local v109 = 5 * v107
                    local v110 = 1 * v108
                    local v111 = 0 + 0.6283185307179586 * (math.noise(v109, 1.5, v110) + 0.5)
                    local v112 = 0.5 * v107
                    local v113 = 0.1 * v108
                    local v114 = v111 + (0 + 5.654866776461628 * (math.noise(v112, 1.5, v113) + 0.5))
                    local v115 = (v81 + (v82 - v81) * (math.noise(3.4, v108, v107) + 0.5)) * v92(v106)
                    local v116 = v88 + (v89 - v88) * (math.noise(2.3, v108, v107) + 0.5)
                    local v117 = v91(v106, v98, v99, v100, v101)
                    local v118
                    if v104 == v84 then
                        v118 = v117
                    else
                        local v119 = CFrame.new(v103, v117) * CFrame.Angles(0, 0, v114)
                        local v120 = CFrame.Angles
                        local v121 = 6.123233995736766e-17 + 0.9999999999999999 * (math.noise(v108, v107, 2.7) + 0.5)
                        local v122 = math.clamp(v121, -1, 1)
                        v118 = (v119 * v120(math.acos(v122), 0, 0) * CFrame.new(0, 0, -v115)).Position or v117
                    end
                    v80:_UpdateGeometry(v105, v106, v90, v116, v102, v118)
                    v80:_UpdateColor(v105, v106, v90)
                    v103 = v117
                    v102 = v118
                end
            else
                v80:Destroy()
            end
        elseif v80._PartsHidden == false then
            v80._PartsHidden = true
            v80:_Disable()
        end
    end
    debug.profileend()
end)
return u29