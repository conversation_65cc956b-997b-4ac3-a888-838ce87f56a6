-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Data\ExoticPackData-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
local v2 = require(v1.Data.CosmeticRegistry)
local v3 = {}
for v4, v5 in require(v1.Data.CosmeticCrateRegistry).CosmeticCrates["Exclusive Cosmetic Crate"].CosmeticRolls.Items do
    local v6 = v5.Name or v4
    local v7 = {
        ["Chance"] = v5.NormalizedOdd or v5.ItemOdd,
        ["RewardId"] = v6,
        ["Icon"] = v2.CosmeticList[v6] and (v2.CosmeticList[v6].Icon or "") or ""
    }
    table.insert(v3, v7)
end
table.sort(v3, function(p8, p9) --[[Anonymous function at line 20]]
    local v10 = p8.Chance
    local v11 = p9.Chance
    if v10 == v11 then
        return p8.RewardId > p9.RewardId
    else
        return v11 < v10
    end
end)
return {
    ["Products"] = {
        [1] = 3290672626,
        [3] = 3290672712,
        [10] = 3290672848
    },
    ["GiftProducts"] = {
        [1] = 3290672926,
        [3] = 3290673007,
        [10] = 3290673073
    },
    ["Title"] = "EXOTIC COSMETIC CRATE!",
    ["Vector"] = "rbxassetid://140481838960536",
    ["VectorPosition"] = UDim2.fromScale(0.102, 0.744),
    ["VectorSize"] = UDim2.fromScale(0.276, 0.727),
    ["Display"] = "Crate",
    ["DisplayPlural"] = "Crates",
    ["Items"] = v3,
    ["BiggerTemplateIndex"] = 6,
    ["MidTemplateIndex"] = 4,
    ["EndTime"] = DateTime.fromUniversalTime(2025, 5, 31, 14)
}