-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\Notification-ModuleScript.lua
local u1 = game.Players.LocalPlayer:Wait<PERSON><PERSON><PERSON><PERSON><PERSON>("PlayerGui"):Wait<PERSON><PERSON><PERSON>hild("Top_Notification"):Wait<PERSON><PERSON><PERSON>hild("Frame")
local u2 = game:GetService("TweenService")
TweenInfo.new(0.25, Enum.EasingStyle.Quad, Enum.EasingDirection.Out)
TweenInfo.new(0.5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out)
local u3 = game.SoundService.Notification
game:GetService("RunService")
local u4 = game.ReplicatedStorage.Notification_UI_Mobile
local u5 = game.ReplicatedStorage.Notification_UI
task.spawn(function() --[[Anonymous function at line 17]]
    --[[
    Upvalues:
        [1] = u1
        [2] = u2
    --]]
    while true do
        for _, v6 in u1:GetChildren() do
            if v6:IsA("Frame") and (v6.Name == "Notification_UI" or v6.Name == "Notification_UI_Mobile") then
                local v7 = v6:GetAttribute("NotificationTimer")
                if v7 and type(v7) == "number" then
                    local v8 = v7 - 1
                    if v8 <= 0 then
                        v6:SetAttribute("NotificationTimer", nil)
                        local v9 = TweenInfo.new(0.25, Enum.EasingStyle.Quad, Enum.EasingDirection.Out)
                        local v10 = TweenInfo.new(0.5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out)
                        local v11 = v6.TextLabel.Position
                        u2:Create(v6.TextLabel, v10, {
                            ["Position"] = v11 + UDim2.new(0, 0, 0.2, 0)
                        }):Play()
                        u2:Create(v6.ImageLabel, v9, {
                            ["ImageTransparency"] = 1
                        }):Play()
                        u2:Create(v6.TextLabel, v9, {
                            ["TextTransparency"] = 1
                        }):Play()
                        u2:Create(v6.TextLabel, v9, {
                            ["TextStrokeTransparency"] = 1
                        }):Play()
                        game.Debris:AddItem(v6, v9.Time)
                    else
                        v6:SetAttribute("NotificationTimer", v8)
                    end
                end
            end
        end
        task.wait(1)
    end
end)
local function u27(p12, p13, p14, p15) --[[Anonymous function at line 47]]
    --[[
    Upvalues:
        [1] = u4
        [2] = u5
        [3] = u1
        [4] = u2
        [5] = u3
    --]]
    local v16 = p15 or 3.5
    local v17 = TweenInfo.new(0.25, Enum.EasingStyle.Quad, Enum.EasingDirection.Out)
    local v18 = TweenInfo.new(0.5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out)
    local v19 = p12 and u4 or u5
    local v20 = nil
    if not p14 then
        for _, v21 in ipairs(u1:GetChildren()) do
            if v21.Name == "Notification_UI" and v21:GetAttribute("OG") == p13 then
                v20 = v21
                break
            end
        end
    end
    if v20 then
        local v22 = v20:FindFirstChild("VAL_OBJ")
        if v22 then
            v22.Value = v22.Value + 1
            v20.TextLabel.Text = p13 .. " [X" .. v22.Value .. "]"
        end
        local _ = v20:GetAttribute("NotificationTimer") or 0
        v20:SetAttribute("NotificationTimer", v16)
    else
        local v23 = v19:Clone()
        v23.TextLabel.Text = p13
        v23:SetAttribute("OG", p13)
        v23:SetAttribute("NotificationTimer", v16)
        local v24 = Instance.new("IntValue")
        v24.Name = "VAL_OBJ"
        v24.Value = 1
        v24.Parent = v23
        local v25 = v23.TextLabel.Position
        local v26 = v23.TextLabel
        v26.Position = v26.Position - UDim2.new(0, 0, 0.2, 0)
        v23.ImageLabel.ImageTransparency = 1
        v23.TextLabel.TextTransparency = 1
        v23.TextLabel.TextStrokeTransparency = 1
        v23.Parent = u1
        u2:Create(v23.TextLabel, v18, {
            ["Position"] = v25
        }):Play()
        u2:Create(v23.ImageLabel, v17, {
            ["ImageTransparency"] = 0.5
        }):Play()
        u2:Create(v23.TextLabel, v17, {
            ["TextTransparency"] = 0
        }):Play()
        u2:Create(v23.TextLabel, v17, {
            ["TextStrokeTransparency"] = 0
        }):Play()
        u3.PlaybackSpeed = 1 + math.random(-15, 15) / 100
        u3.Playing = true
        u3.TimePosition = 0
    end
end
if game:GetService("UserInputService").TouchEnabled then
    game.ReplicatedStorage:WaitForChild("GameEvents").Notification.OnClientEvent:Connect(function(...) --[[Anonymous function at line 116]]
        --[[
        Upvalues:
            [1] = u27
        --]]
        u27(true, ...)
    end)
else
    game.ReplicatedStorage:WaitForChild("GameEvents").Notification.OnClientEvent:Connect(function(...) --[[Anonymous function at line 120]]
        --[[
        Upvalues:
            [1] = u27
        --]]
        u27(false, ...)
    end)
end
return {
    ["CreateNotification"] = function(_, p28) --[[Function name: CreateNotification, line 128]]
        --[[
        Upvalues:
            [1] = u27
        --]]
        u27(game.UserInputService.TouchEnabled, p28)
    end
}