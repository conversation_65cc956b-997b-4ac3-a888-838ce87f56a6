-- Full Path: -Grow-a-Garden-\\NPCLookAtController-ModuleScript.lua
game:GetService("CollectionService")
local v1 = game:GetService("ReplicatedStorage")
local u2 = game:GetService("RunService")
local v3 = game:GetService("Players")
local u4 = require(v1.Modules.Observers)
local u5 = v3.LocalPlayer
local u6 = {}
local u39 = {
    ["UpdateTranslation"] = function(_, p7) --[[Function name: UpdateTranslation, line 26]]
        --[[
        Upvalues:
            [1] = u6
        --]]
        local v8 = p7 * -15
        local v9 = math.exp(v8)
        for v10, v11 in next, u6 do
            local v12
            if v11.TargetPart then
                v12 = v11.TargetPart:GetPivot()
            else
                v12 = nil
            end
            if v12 and v10:GetPivot():ToObjectSpace(v12).Z < 0 then
                local v13 = v11.Head.CFrame.Position - v12.Position
                local v14 = v11.Neck.C0
                local v15 = v11.InitialNeckC0
                local v16 = CFrame.Angles
                local v17 = v13.Y / vector.magnitude(v13)
                local v18 = math.atan(v17) * 0.5
                local v19 = vector.normalize(v13)
                local v20 = v11.Torso.CFrame.LookVector
                local v21 = v15 * v16(v18, 0, vector.cross(v19, v20).y * 0.8)
                v11.Neck.C0 = v21:Lerp(v14, v9)
            else
                local v22 = v11.Neck.C0
                local v23 = v11.InitialNeckC0
                v11.Neck.C0 = v23:Lerp(v22, v9)
            end
        end
    end,
    ["Update"] = function(_) --[[Function name: Update, line 53]]
        --[[
        Upvalues:
            [1] = u5
            [2] = u6
        --]]
        local v24 = u5.Character
        local v25
        if v24 then
            v25 = v24:FindFirstChild("Head")
        else
            v25 = nil
        end
        local v26
        if v25 then
            v26 = v25:GetPivot()
        else
            v26 = nil
        end
        for v27, v28 in next, u6 do
            if v28.Neck then
                local v29 = false
                if v26 then
                    local v30 = v26.Position - v27:GetPivot().Position
                    v29 = vector.magnitude(v30) <= 30 and true or v29
                end
                if v29 then
                    v28.TargetPart = v25
                else
                    v28.TargetPart = nil
                end
            end
        end
    end,
    ["AddNPC"] = function(_, p31) --[[Function name: AddNPC, line 78]]
        --[[
        Upvalues:
            [1] = u6
        --]]
        local v32 = p31:WaitForChild("Torso")
        if v32 then
            local v33 = p31:WaitForChild("Head")
            if v33 then
                local v34 = v32:WaitForChild("Neck")
                if v34 then
                    u6[p31] = {
                        ["Torso"] = v32,
                        ["Head"] = v33,
                        ["Neck"] = v34,
                        ["InitialNeckC0"] = v34.C0
                    }
                else
                    warn((("Failed to find Neck for npc \"%*\""):format((p31:GetFullName()))))
                end
            else
                warn((("Failed to find Head for npc \"%*\""):format((p31:GetFullName()))))
                return
            end
        else
            warn((("Failed to find Torso for npc \"%*\""):format((p31:GetFullName()))))
            return
        end
    end,
    ["RemoveNPC"] = function(_, p35) --[[Function name: RemoveNPC, line 106]]
        --[[
        Upvalues:
            [1] = u6
        --]]
        u6[p35] = nil
    end,
    ["Start"] = function(_) --[[Function name: Start, line 110]]
        --[[
        Upvalues:
            [1] = u4
            [2] = u39
            [3] = u2
        --]]
        u4.observeTag("NPCLookAt", function(u36) --[[Anonymous function at line 111]]
            --[[
            Upvalues:
                [1] = u39
            --]]
            u39:AddNPC(u36)
            return function() --[[Anonymous function at line 114]]
                --[[
                Upvalues:
                    [1] = u39
                    [2] = u36
                --]]
                u39:RemoveNPC(u36)
            end
        end, { workspace })
        local u37 = 0
        u2.PostSimulation:Connect(function(p38) --[[Anonymous function at line 120]]
            --[[
            Upvalues:
                [1] = u39
                [2] = u37
            --]]
            u39:UpdateTranslation(p38)
            u37 = u37 + p38
            if u37 >= 0.05 then
                u37 = 0
                u39:Update()
            end
        end)
    end
}
task.spawn(u39.Start, u39)
return u39