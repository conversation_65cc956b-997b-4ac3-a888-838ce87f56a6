-- Full Path: -Grow-a-Garden-\\Shake-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
local u2 = game:GetService("RunService")
local u3 = require(v1.Modules.Trove)
local u4 = Random.new()
local u5 = 0
local u44 = {
    ["new"] = function() --[[Function name: new, line 179]]
        --[[
        Upvalues:
            [1] = u44
            [2] = u2
            [3] = u4
            [4] = u3
        --]]
        local v6 = {
            ["__index"] = u44
        }
        local u7 = setmetatable({}, v6)
        u7.Amplitude = 1
        u7.Frequency = 1
        u7.FadeInTime = 1
        u7.FadeOutTime = 1
        u7.SustainTime = 0
        u7.Sustain = false
        u7.PositionInfluence = Vector3.new(1, 1, 1)
        u7.RotationInfluence = Vector3.new(1, 1, 1)
        local v8
        if u2:IsRunning() then
            v8 = time
        else
            v8 = os.clock
        end
        u7.TimeFunction = v8
        u7._timeOffset = u4:NextNumber(-1000000000, 1000000000)
        u7._startTime = 0
        u7._trove = u3.new()
        u7._trove:Add(function() --[[Anonymous function at line 198]]
            --[[
            Upvalues:
                [1] = u7
            --]]
            if u7 then
                local v9 = u7
                if getmetatable(v9) ~= nil then
                    local v10 = u7
                    getmetatable(v10).__mode = "kv"
                end
            end
        end)
        u7._running = false
        return u7
    end,
    ["InverseSquare"] = function(p11, p12) --[[Function name: InverseSquare, line 240]]
        local v13 = p12 < 1 and 1 or p12
        return p11 * (1 / (v13 * v13))
    end,
    ["NextRenderName"] = function() --[[Function name: NextRenderName, line 256]]
        --[[
        Upvalues:
            [1] = u5
        --]]
        u5 = u5 + 1
        return ("__shake_%.4i__"):format(u5)
    end,
    ["Start"] = function(u14) --[[Function name: Start, line 269]]
        u14._startTime = u14.TimeFunction()
        u14._running = true
        u14._trove:Add(function() --[[Anonymous function at line 272]]
            --[[
            Upvalues:
                [1] = u14
            --]]
            u14._running = false
        end)
    end,
    ["Stop"] = function(p15) --[[Function name: Stop, line 284]]
        if p15._trove then
            p15._trove:Clean()
        end
    end,
    ["IsShaking"] = function(p16) --[[Function name: IsShaking, line 294]]
        return p16._running
    end,
    ["StopSustain"] = function(p17) --[[Function name: StopSustain, line 303]]
        local v18 = p17.TimeFunction()
        p17.Sustain = false
        p17.SustainTime = v18 - p17._startTime - p17.FadeInTime
    end,
    ["Update"] = function(p19, p20) --[[Function name: Update, line 331]]
        local v21 = false
        local v22 = p19.TimeFunction()
        local v23 = v22 - p19._startTime
        local v24 = (v22 + p19._timeOffset) / p19.Frequency % 1000000
        local v25 = 1
        local v26 = v23 >= p19.FadeInTime and 1 or v23 / p19.FadeInTime
        if not p19.Sustain and p19.FadeInTime + p19.SustainTime < v23 then
            if p19.FadeOutTime == 0 then
                v21 = true
            else
                v25 = 1 - (v23 - p19.FadeInTime - p19.SustainTime) / p19.FadeOutTime
                if not p19.Sustain and p19.FadeInTime + p19.SustainTime + p19.FadeOutTime <= v23 then
                    v21 = true
                end
            end
        end
        local v27 = p20 * 80 * 1.25
        local v28 = math.noise(v24, 0) / 2
        local v29 = math.noise(0, v24) / 2
        local v30 = math.noise(v24, v24) / 2
        local v31 = Vector3.new(v28, v29, v30) * (p19.Amplitude * v27) * math.min(v26, v25)
        if v21 then
            p19:Stop()
        end
        return p19.PositionInfluence * v31, p19.RotationInfluence * v31, v21
    end,
    ["OnSignal"] = function(u32, p33, u34) --[[Function name: OnSignal, line 389]]
        return u32._trove:Connect(p33, function() --[[Anonymous function at line 390]]
            --[[
            Upvalues:
                [1] = u34
                [2] = u32
            --]]
            u34(u32:Update())
        end)
    end,
    ["BindToRenderStep"] = function(u35, p36, p37, u38) --[[Function name: BindToRenderStep, line 415]]
        u35._trove:BindToRenderStep(p36, p37, function(p39) --[[Anonymous function at line 416]]
            --[[
            Upvalues:
                [1] = u38
                [2] = u35
            --]]
            u38(u35:Update(p39))
        end)
    end,
    ["Clone"] = function(p40) --[[Function name: Clone, line 450]]
        --[[
        Upvalues:
            [1] = u44
        --]]
        local v41 = u44.new()
        for _, v42 in {
            "Amplitude",
            "Frequency",
            "FadeInTime",
            "FadeOutTime",
            "SustainTime",
            "Sustain",
            "PositionInfluence",
            "RotationInfluence",
            "TimeFunction"
        } do
            v41[v42] = p40[v42]
        end
        return v41
    end,
    ["Destroy"] = function(p43) --[[Function name: Destroy, line 472]]
        p43:Stop()
    end
}
return u44