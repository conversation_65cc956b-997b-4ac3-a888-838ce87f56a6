-- Full Path: -Grow-a-Garden-\\Shovel_Client-LocalScript.lua
local u1 = game:GetService("UserInputService")
local u2 = game:GetService("CollectionService")
local v3 = game:GetService("ReplicatedStorage")
local u4 = game:GetService("TweenService")
local u5 = game:GetService("RunService")
local u6 = v3:WaitF<PERSON><PERSON>hild("GameEvents"):WaitForChild("Remove_Item")
local u7 = require(v3.Modules.GetFarm)
local u8 = require(v3.Modules.Notification)
local u9 = game.Players.LocalPlayer
local u10 = workspace.CurrentCamera
local u11 = u9.PlayerGui:WaitForChild("ShovelPrompt")
local v12 = u11:WaitForChild("ConfirmFrame")
local v13 = v12:WaitForChild("Confirm")
local v14 = v12:WaitForChild("Cancel")
local v15 = v12:WaitFor<PERSON>hild("ExitButton")
local u16 = v12:WaitForChild("FruitName")
local u17 = require(v3.Data.SeedData)
local u18 = require(v3.Data.EasterData)
local u19 = require(v3.Item_Module)
local u20 = script.Highlight
local u21 = {
    "Purple Cabbage",
    "Coconut",
    "Cactus",
    "Eggplant",
    "Dragon Fruit",
    "Mango",
    "Grape",
    "Mushroom",
    "Pepper",
    "Cacao",
    "Beanstalk",
    "Peach",
    "Raspberry",
    "Pineapple",
    "Papaya",
    "Banana",
    "Passionfruit",
    "Soul Fruit",
    "Cursed Fruit",
    "Cranberry",
    "Durian",
    "Eggplant",
    "Lotus",
    "Venus Fly Trap",
    "Candy Blossom",
    "Easter Egg",
    "Moonflower",
    "Starfruit",
    "Moonglow",
    "Moon Blossom",
    "Glowshroom",
    "Nightshade",
    "Blood Banana",
    "Moon Melon",
    "Cherry Blossom",
    "Lemon",
    "Pear",
    "Avocado",
    "Crimson Vine"
}
local u22 = { "Carrot" }
local function u25(p23) --[[Anonymous function at line 94]]
    --[[
    Upvalues:
        [1] = u21
    --]]
    for _, v24 in u21 do
        if string.find(string.lower(p23), string.lower(v24)) then
            return true
        end
    end
    return false
end
local function u28(p26) --[[Anonymous function at line 103]]
    --[[
    Upvalues:
        [1] = u22
    --]]
    for _, v27 in u22 do
        if string.find(string.lower(p26), string.lower(v27)) then
            return true
        end
    end
    return false
end
local u29 = nil
local u30 = nil
v13.MouseButton1Click:Connect(function() --[[Anonymous function at line 142]]
    --[[
    Upvalues:
        [1] = u30
        [2] = u6
        [3] = u11
        [4] = u29
    --]]
    if u30 then
        u6:FireServer(u30)
        u30 = nil
    end
    u11.Enabled = false
    if u29 then
        u29:Disconnect()
        u29 = nil
    end
end)
v14.MouseButton1Click:Connect(function() --[[Anonymous function at line 151]]
    --[[
    Upvalues:
        [1] = u30
        [2] = u11
        [3] = u29
    --]]
    u30 = nil
    u11.Enabled = false
    if u29 then
        u29:Disconnect()
        u29 = nil
    end
end)
v15.MouseButton1Click:Connect(function() --[[Anonymous function at line 157]]
    --[[
    Upvalues:
        [1] = u30
        [2] = u11
        [3] = u29
    --]]
    u30 = nil
    u11.Enabled = false
    if u29 then
        u29:Disconnect()
        u29 = nil
    end
end)
local function u35(p31) --[[Anonymous function at line 163]]
    --[[
    Upvalues:
        [1] = u10
        [2] = u2
    --]]
    local v32 = u10:ViewportPointToRay(p31.X, p31.Y)
    local v33 = RaycastParams.new()
    v33.FilterType = Enum.RaycastFilterType.Exclude
    v33.FilterDescendantsInstances = { u2:GetTagged("ShovelIgnore") }
    local v34 = workspace:Raycast(v32.Origin, v32.Direction * 500, v33)
    if v34 and v34.Instance then
        return v34
    end
end
u5.RenderStepped:Connect(function() --[[Anonymous function at line 177]]
    --[[
    Upvalues:
        [1] = u9
        [2] = u1
        [3] = u35
        [4] = u20
        [5] = u7
        [6] = u4
    --]]
    if u9 and (u9.Character and u9.Character:FindFirstChild("Shovel [Destroy Plants]")) then
        local v36 = u35((u1:GetMouseLocation()))
        if v36 then
            local v37 = u7(u9)
            if v37 and v36.Instance:IsDescendantOf(v37) then
                if v36.Instance.Parent and v36.Instance.Parent:FindFirstChild("Grow") then
                    if u20.Adornee ~= v36.Instance.Parent then
                        u20.FillTransparency = 1
                        u4:Create(u20, TweenInfo.new(0.25), {
                            ["FillTransparency"] = 0.65
                        }):Play()
                    end
                    u20.Adornee = v36.Instance.Parent
                else
                    u20.Adornee = nil
                end
            else
                return
            end
        else
            u20.Adornee = nil
            return
        end
    else
        u20.Adornee = nil
        return
    end
end)
local function u68(p38, p39) --[[Anonymous function at line 204]]
    --[[
    Upvalues:
        [1] = u9
        [2] = u35
        [3] = u7
        [4] = u8
        [5] = u28
        [6] = u25
        [7] = u17
        [8] = u18
        [9] = u19
        [10] = u16
        [11] = u29
        [12] = u5
        [13] = u30
        [14] = u11
        [15] = u6
    --]]
    if p39 then
        return
    end
    if not (u9.Character and u9.Character:FindFirstChild("Shovel [Destroy Plants]")) then
        return
    end
    local v40 = u35(p38)
    if not v40 then
        return
    end
    local v41 = v40.Instance.Parent
    if not (v41 and v41:FindFirstChild("Grow")) then
        return
    end
    local v42 = u7(u9)
    if not (v42 and v40.Instance:IsDescendantOf(v42)) then
        return
    end
    local v43 = nil
    if v41:GetAttribute("Favorited") then
        u8:CreateNotification("This plant is favorited!")
        return
    end
    local v44 = v41:FindFirstChild("Fruits")
    if v44 then
        for _, v45 in v44:GetChildren() do
            if v45:GetAttribute("Favorited") then
                v43 = true
                break
            end
        end
    end
    if v43 then
        u8:CreateNotification("This plant has favorited fruit!")
        return
    else
        local v46 = v41.Name
        if u28(v46) then
            u8:CreateNotification((("You cannot shovel %*!"):format(v46)))
            return
        elseif u25(v46) then
            local v47 = u17[v46] or u18[v46]
            local v48
            if v47 then
                v48 = u19.Return_Rarity_Data(v47.SeedRarity) or nil
            else
                v48 = nil
            end
            local v49
            if v48 then
                local v50 = v48[2]
                local v51 = v50.R * 255
                local v52 = math.floor(v51)
                local v53 = v50.G * 255
                local v54 = math.floor(v53)
                local v55 = v50.B * 255
                local v56 = math.floor(v55)
                v49 = string.format("#%02X%02X%02X", v52, v54, v56) or "#FFFFFF"
            else
                v49 = "#FFFFFF"
            end
            u16.Text = v46
            if u29 then
                u29:Disconnect()
                u29 = nil
            end
            if v47 and v47.SeedRarity == "Prismatic" then
                local u57 = u16
                if u29 then
                    u29:Disconnect()
                end
                local u58 = 0
                u29 = u5.RenderStepped:Connect(function(p59) --[[Anonymous function at line 121]]
                    --[[
                    Upvalues:
                        [1] = u58
                        [2] = u57
                    --]]
                    u58 = (u58 + p59 * 0.2) % 1
                    local v60 = Color3.fromHSV(u58, 1, 1)
                    local v61 = u57
                    local v62 = string.format
                    local v63 = v60.R * 255
                    local v64 = math.floor(v63)
                    local v65 = v60.G * 255
                    local v66 = math.floor(v65)
                    local v67 = v60.B * 255
                    v61.Text = v62("<font color=\"#%02X%02X%02X\">%s</font>", v64, v66, math.floor(v67), u57.Text:gsub("<.->", ""))
                end)
            else
                u16.Text = string.format("<font color=\"%s\">%s</font>", v49, v46)
            end
            u30 = v40.Instance
            u11.Enabled = true
        else
            u6:FireServer(v40.Instance)
        end
    end
end
if u1.TouchEnabled then
    u1.TouchTapInWorld:Connect(u68)
else
    u9:GetMouse().Button1Down:Connect(function() --[[Anonymous function at line 281]]
        --[[
        Upvalues:
            [1] = u68
            [2] = u1
        --]]
        u68(u1:GetMouseLocation(), false)
    end)
end