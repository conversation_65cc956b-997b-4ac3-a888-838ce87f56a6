-- Full Path: -Grow-a-Garden-\StarterPlayer\StarterPlayerScripts\LocalScript_31-LocalScript.lua
local u1 = game.Players.LocalPlayer
function UPD()
    --[[
    Upvalues:
        [1] = u1
    --]]
    for _, v2 in pairs(game.Workspace.Farm:GetChildren()) do
        local v3 = v2:WaitForChild("Sign").Core_Part.ProximityPrompt
        local v4 = v2.Important.Data.Owner.Value
        if v4 == "" then
            v3.Enabled = false
        elseif v4 == u1.Name then
            v3.Enabled = false
        else
            v3.Enabled = true
        end
    end
end
UPD()
for _, v5 in pairs(game.Workspace.Farm:GetChildren()) do
    v5.Important.Data.Owner:GetPropertyChangedSignal("Value"):Connect(function() --[[Anonymous function at line 20]]
        UPD()
    end)
end