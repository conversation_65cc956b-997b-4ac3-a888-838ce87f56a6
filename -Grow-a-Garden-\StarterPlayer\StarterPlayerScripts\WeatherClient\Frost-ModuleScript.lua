-- Full Path: -Grow-a-Garden-\StarterPlayer\StarterPlayerScripts\WeatherClient\Frost-ModuleScript.lua
local v1 = {}
local u2 = game.ReplicatedStorage.SnowParticle
local u3 = Random.new()
local u4 = workspace.CurrentCamera
local u5 = RaycastParams.new()
u5.FilterDescendantsInstances = { workspace.Terrain, workspace }
u5.FilterType = Enum.RaycastFilterType.Include
local u6 = {}
local u7 = false
local u8 = game.Lighting.ColorCorrection:Clone()
u8.Name = script.Name
u8.Parent = game.Lighting
local function u12(p9) --[[Anonymous function at line 23]]
    --[[
    Upvalues:
        [1] = u2
        [2] = u6
    --]]
    local v10 = {
        ["particle"] = u2:Clone(),
        ["position"] = p9,
        ["spawnTime"] = os.clock(),
        ["visible"] = false,
        ["lastupdate"] = 0
    }
    game.TweenService:Create(v10.particle, TweenInfo.new(0.7), {
        ["Transparency"] = 0.2
    }):Play()
    local v11 = u6
    table.insert(v11, v10)
end
local function u34(p13, p14, p15) --[[Anonymous function at line 40]]
    --[[
    Upvalues:
        [1] = u5
    --]]
    local _ = p14 - p13.spawnTime + p15
    local v16 = p13.position
    local v17 = (Vector3.new(-7, 0, 0)).Unit * 45 * p15
    local v18 = p13.spawnTime * 90
    local v19 = math.rad(v18)
    local v20 = math.sin(v19)
    local v21 = p13.spawnTime * 90
    local v22 = math.rad(v21)
    local v23 = (math.sin(v22) + 1) / 2 * -5
    local v24 = p13.spawnTime * 90
    local v25 = math.rad(v24)
    local v26 = math.sin(v25)
    local v27 = v17 + Vector3.new(v20, v23, v26) * p15
    local v28 = p15 * 0.5
    local v29 = os.clock() * 35
    local v30 = math.rad(v29) + p13.spawnTime
    local v31 = v28 * math.sin(v30)
    local v32 = v27 + Vector3.new(v31, 0, 0)
    local v33 = workspace:Spherecast(v16, 0.15, v32 * p15, u5)
    if v33 then
        return v33.Position, true
    end
    p13.position = v16 + v32 * p15
    return v16
end
local u35 = script.Sky
local u36 = require(game.ReplicatedStorage.Code.BodyShaker)
local u37 = require(game.ReplicatedStorage.Modules.SkyboxManager)
u37.AddSkybox(u35)
local function u49() --[[Anonymous function at line 74]]
    --[[
    Upvalues:
        [1] = u7
        [2] = u36
        [3] = u37
        [4] = u35
        [5] = u8
        [6] = u6
        [7] = u3
        [8] = u4
        [9] = u12
    --]]
    u7 = true
    task.delay(1, function() --[[Anonymous function at line 77]]
        --[[
        Upvalues:
            [1] = u36
        --]]
        local v38 = game.Players.LocalPlayer.Character
        if v38 and v38:IsDescendantOf(workspace) then
            local u39 = script.coldSound:Clone()
            u39.Parent = v38.PrimaryPart
            u39:Play()
            game.TweenService:Create(u39, TweenInfo.new(1), {
                ["Volume"] = 0.02
            }):Play()
            task.delay(25, function() --[[Anonymous function at line 85]]
                --[[
                Upvalues:
                    [1] = u39
                --]]
                game.TweenService:Create(u39, TweenInfo.new(1), {
                    ["Volume"] = 0
                }):Play()
                game.Debris:AddItem(u39, 1)
            end)
        end
        u36.EnableShaking()
    end)
    u37.UpdateSkybox(u35, 2)
    game.TweenService:Create(game.Lighting.Atmosphere, TweenInfo.new(3), {
        ["Density"] = 0.493,
        ["Glare"] = 0.85,
        ["Haze"] = 2.11,
        ["Offset"] = 1,
        ["Color"] = Color3.fromRGB(215, 244, 255),
        ["Decay"] = Color3.fromRGB(190, 238, 255)
    }):Play()
    game.TweenService:Create(game.Lighting, TweenInfo.new(3), {
        ["Ambient"] = Color3.fromRGB(131, 179, 255),
        ["ExposureCompensation"] = 0.4,
        ["Brightness"] = 1
    }):Play()
    game.TweenService:Create(u8, TweenInfo.new(3), {
        ["Brightness"] = 0.1,
        ["Saturation"] = -0.2
    }):Play()
    game.Workspace.Terrain.Clouds.Enabled = true
    game.TweenService:Create(game.Workspace.Terrain.Clouds, TweenInfo.new(3), {
        ["Cover"] = 0.777,
        ["Density"] = 0.123,
        ["Color"] = Color3.fromRGB(206, 238, 255)
    }):Play()
    local u40 = game.ReplicatedStorage.WindyFrostEffect:Clone()
    u40.Parent = workspace
    task.spawn(function() --[[Anonymous function at line 135]]
        --[[
        Upvalues:
            [1] = u7
            [2] = u6
            [3] = u3
            [4] = u4
            [5] = u12
            [6] = u37
            [7] = u35
            [8] = u40
            [9] = u36
            [10] = u8
        --]]
        while u7 do
            game:GetService("RunService").Heartbeat:Wait()
            if #u6 <= 200 then
                for _ = 1, 3 do
                    local v41 = u3:NextNumber(10, 180)
                    local v42 = 2 * v41
                    local v43 = u4.FieldOfView / 2
                    local v44 = math.rad(v43)
                    local v45 = v42 * math.tan(v44)
                    local v46 = v45 * (u4.ViewportSize.X / u4.ViewportSize.Y)
                    local v47 = u4.CFrame * CFrame.new(u3:NextNumber(-v46 / 2, v46 / 2), u3:NextNumber(-v45 / 2, v45 / 2 + 20), -v41)
                    if not workspace:Raycast(v47.Position, Vector3.new(0, 150, 0)) then
                        u12(v47.Position)
                    end
                end
            end
        end
        u37.UpdateSkybox(u35, 0)
        for _, v48 in u40:GetChildren() do
            v48.Enabled = false
        end
        task.delay(6, function() --[[Anonymous function at line 189]]
            --[[
            Upvalues:
                [1] = u40
            --]]
            u40:Destroy()
        end)
        u36.DisableShaking()
        game.TweenService:Create(game.Lighting.Atmosphere, TweenInfo.new(3), {
            ["Density"] = 0,
            ["Haze"] = 0,
            ["Glare"] = 0,
            ["Offset"] = 0
        }):Play()
        game.TweenService:Create(u8, TweenInfo.new(3), {
            ["Brightness"] = 0,
            ["Saturation"] = 0,
            ["TintColor"] = Color3.fromRGB(255, 255, 255)
        }):Play()
        game.TweenService:Create(game.Lighting, TweenInfo.new(3), {
            ["Ambient"] = game.Lighting:GetAttribute("DefaultAmbient"),
            ["ExposureCompensation"] = game.Lighting:GetAttribute("DefaultExposure"),
            ["Brightness"] = game.Lighting:GetAttribute("DefaultBrightness")
        }):Play()
        game.TweenService:Create(game.Workspace.Terrain.Clouds, TweenInfo.new(3), {
            ["Cover"] = 0,
            ["Density"] = 0,
            ["Color"] = Color3.fromRGB(22, 40, 70)
        }):Play()
        task.delay(3, function() --[[Anonymous function at line 219]]
            game.Workspace.Terrain.Clouds.Enabled = false
        end)
    end)
end
workspace:GetAttributeChangedSignal("FrostEvent"):Connect(function() --[[Anonymous function at line 230]]
    --[[
    Upvalues:
        [1] = u49
        [2] = u7
    --]]
    if workspace:GetAttribute("FrostEvent") then
        u49()
    else
        u7 = false
    end
end)
task.spawn(function() --[[Anonymous function at line 239]]
    --[[
    Upvalues:
        [1] = u6
        [2] = u34
        [3] = u4
    --]]
    while true do
        local v50 = game:GetService("RunService").RenderStepped:Wait()
        local v51 = os.clock()
        local v52 = v50 * 3
        local v53 = v50 * 5
        local v54 = math.min(1, v53)
        local v55 = {}
        local v56 = {}
        for v57, v58 in u6 do
            local v59, v60 = u34(v58, v51, v54)
            local v61, v62 = u4:WorldToScreenPoint(v59)
            local v63 = v58.visible
            local v64 = (u4.CFrame.Position - v59).Magnitude / 120
            local v65 = v64 * v64
            local v66 = 1 / math.random(60, 120)
            local v67 = v52 * v65 + 0.016666666666666666
            if v64 > 1.5 then
                v58.particle:Destroy()
                table.remove(u6, v57)
            elseif v51 - v58.lastupdate + v66 > v67 then
                v58.lastupdate = v51
                if v62 and v61.Z < 200 then
                    v58.visible = true
                    local v68 = v58.particle
                    table.insert(v55, v68)
                    local v69 = CFrame.new(v59, u4.CFrame.Position) * CFrame.Angles(1.5707963267948966, 0, 0)
                    table.insert(v56, v69)
                else
                    v58.visible = false
                end
                if v58.visible ~= v63 then
                    if v58.visible then
                        v58.particle.Parent = workspace.WeatherVisuals
                    else
                        v58.particle.Parent = nil
                    end
                end
                if v60 then
                    game.Debris:AddItem(v58.particle, 1)
                    game.TweenService:Create(v58.particle, TweenInfo.new(1), {
                        ["Transparency"] = 1
                    }):Play()
                    table.remove(u6, v57)
                elseif os.clock() - v58.spawnTime > 7 then
                    game.Debris:AddItem(v58.particle, 0.3)
                    game.TweenService:Create(v58.particle, TweenInfo.new(0.3), {
                        ["Transparency"] = 1
                    }):Play()
                    table.remove(u6, v57)
                end
            end
        end
        debug.profilebegin("Weather_" .. script.Name)
        workspace:BulkMoveTo(v55, v56, Enum.BulkMoveMode.FireCFrameChanged)
        debug.profileend()
    end
end)
if workspace:GetAttribute("FrostEvent") then
    task.defer(u49)
else
    u7 = false
end
return v1