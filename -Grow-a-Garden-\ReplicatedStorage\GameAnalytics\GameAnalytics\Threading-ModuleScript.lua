-- Full Path: -Grow-a-Garden-\ReplicatedStorage\GameAnalytics\GameAnalytics\Threading-ModuleScript.lua
local u1 = {
    ["_canSafelyClose"] = true,
    ["_endThread"] = false,
    ["_isRunning"] = false,
    ["_blocks"] = {},
    ["_scheduledBlock"] = nil,
    ["_hasScheduledBlockRun"] = true
}
local u2 = require(script.Parent.Logger)
local u3 = game:GetService("RunService")
local function u11() --[[Anonymous function at line 24]]
    --[[
    Upvalues:
        [1] = u2
        [2] = u1
        [3] = u3
    --]]
    task.spawn(function() --[[Anonymous function at line 26]]
        --[[
        Upvalues:
            [1] = u2
            [2] = u1
        --]]
        u2:d("Starting GA thread")
        while not u1._endThread do
            u1._canSafelyClose = false
            if #u1._blocks ~= 0 then
                for _, v4 in pairs(u1._blocks) do
                    local v5, v6 = pcall(v4.block)
                    if not v5 then
                        u2:e(v6)
                    end
                end
                u1._blocks = {}
            end
            local v7 = tick()
            local v8
            if u1._hasScheduledBlockRun or (u1._scheduledBlock == nil or u1._scheduledBlock.deadline > v7) then
                v8 = nil
            else
                u1._hasScheduledBlockRun = true
                v8 = u1._scheduledBlock
            end
            if v8 ~= nil then
                local v9, v10 = pcall(v8.block)
                if not v9 then
                    u2:e(v10)
                end
            end
            u1._canSafelyClose = true
            task.wait(1)
        end
        u2:d("GA thread stopped")
    end)
    game:BindToClose(function() --[[Anonymous function at line 59]]
        --[[
        Upvalues:
            [1] = u3
            [2] = u1
        --]]
        if not u3:IsStudio() then
            task.wait(1)
            if not u1._canSafelyClose then
                repeat
                    task.wait()
                until u1._canSafelyClose
            end
            task.wait(3)
        end
    end)
end
function u1.scheduleTimer(p12, p13, p14) --[[Anonymous function at line 80]]
    --[[
    Upvalues:
        [1] = u11
    --]]
    if not p12._endThread then
        if not p12._isRunning then
            p12._isRunning = true
            u11()
        end
        local v15 = {
            ["block"] = p14,
            ["deadline"] = tick() + p13
        }
        if p12._hasScheduledBlockRun then
            p12._scheduledBlock = v15
            p12._hasScheduledBlockRun = false
        end
    end
end
function u1.performTaskOnGAThread(p16, p17) --[[Anonymous function at line 101]]
    --[[
    Upvalues:
        [1] = u11
    --]]
    if not p16._endThread then
        if not p16._isRunning then
            p16._isRunning = true
            u11()
        end
        p16._blocks[#p16._blocks + 1] = {
            ["block"] = p17
        }
    end
end
function u1.stopThread(p18) --[[Anonymous function at line 118]]
    p18._endThread = true
end
return u1