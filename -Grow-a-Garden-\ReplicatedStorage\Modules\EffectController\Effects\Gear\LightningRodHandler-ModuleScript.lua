-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\EffectController\Effects\Gear\LightningRodHandler-ModuleScript.lua
local u1 = game:GetService("ReplicatedStorage")
local u2 = game:GetService("CollectionService")
require(u1.Modules.EffectController.Types)
return {
    ["Create"] = function(p3) --[[Function name: Create, line 11]]
        --[[
        Upvalues:
            [1] = u1
            [2] = u2
        --]]
        local v4 = p3.Parameters.ID
        local _ = p3.Parameters.Redirects
        p3.Cache[v4] = {}
        local v5 = p3.Parameters.LightningRodType
        local v6 = p3.Parameters.LightningRodCFrame
        local v7 = u1.ObjectModels:FindFirstChild(v5)
        if v7 then
            local v8 = v7:Clone()
            if v7 then
                u2:AddTag(v8, "LightningRod")
                v8:SetAttribute("ROD_ID", v4)
                for _, v9 in v8:GetDescendants() do
                    if v9:IsA("BasePart") then
                        v9.CanQuery = true
                        v9.CanCollide = true
                    end
                end
                p3.Cache[v4].Rod = v8
                local _, v10 = v8:GetBoundingBox()
                local v11 = v10.Y / 2 - 1
                v8.PrimaryPart.CFrame = v6 * CFrame.new(0, v11, 0)
                v8.Parent = workspace.Visuals
                local v12 = p3.Default:CreateEffect({
                    ["Object"] = script.PlaceEffect,
                    ["Emit"] = true,
                    ["Position"] = v6,
                    ["DebrisTime"] = 2
                })
                p3.Default:PlaySound(script.PlaceSFX, v12)
                local v13 = p3.Libraries.BoatTween
                local v14 = v8.PrimaryPart
                local v15 = {
                    ["Time"] = 0.8,
                    ["EasingStyle"] = "Smoother",
                    ["EasingDirection"] = "In",
                    ["StepType"] = "Heartbeat",
                    ["Goal"] = {
                        ["CFrame"] = v6 * CFrame.new(0, v11, 0)
                    }
                }
                v13:Create(v14, v15):Play()
                p3.Cache[v4].Rod = v8
            end
        else
            return
        end
    end,
    ["Destroy"] = function(p16) --[[Function name: Destroy, line 68]]
        --[[
        Upvalues:
            [1] = u2
        --]]
        local v17 = p16.Parameters.ID
        local u18 = p16.Cache[v17]
        if u18 then
            u18 = p16.Cache[v17].Rod
        end
        if u18 then
            u2:RemoveTag(u18, "LightningRod")
            local v19 = p16.Libraries.BoatTween
            local v20 = u18.PrimaryPart
            local v21 = {
                ["Time"] = 0.6,
                ["EasingStyle"] = "ExitExpressive",
                ["EasingDirection"] = "In",
                ["StepType"] = "Heartbeat",
                ["Goal"] = {
                    ["CFrame"] = p16.Parameters.LightningRodCFrame * CFrame.new(0, 3, 0)
                }
            }
            v19:Create(v20, v21):Play()
            p16.Default:PlaySound(script.DestroySFX, u18.PrimaryPart)
            task.delay(1.5, function() --[[Anonymous function at line 86]]
                --[[
                Upvalues:
                    [1] = u18
                --]]
                u18:Destroy()
            end)
        end
    end,
    ["Cancel"] = function(p22) --[[Function name: Cancel, line 92]]
        p22.Container:Clean()
        p22.Cache = {}
    end
}