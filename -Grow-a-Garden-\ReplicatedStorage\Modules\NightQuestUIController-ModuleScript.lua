-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\NightQuestUIController-ModuleScript.lua
local v1 = game:GetService("Players")
local u2 = game:GetService("ReplicatedStorage")
game:GetService("ServerScriptService")
game:GetService("MarketplaceService")
game:GetService("PolicyService")
game:GetService("RunService")
game:GetService("CollectionService")
local u3 = u2:WaitForChild("GameEvents"):WaitForChild("NightQuestRemoteEvent")
local u4 = require(u2.Modules.MarketController)
require(u2.Modules.GiftController)
local u5 = require(u2.Modules.GuiController)
local u6 = require(u2.Modules.WaitForDescendant)
local u7 = require(u2.Modules.DataService)
require(u2.Modules.RetryPcall)
local u8 = require(u2.Modules.CommaFormatNumber)
require(u2.Comma_Module)
local u9 = require(u2.Modules.NightQuestHelper)
local u10 = require(u2.Modules.NumberUtil)
local u11 = require(u2.Modules.UpdateService)
local u12 = require(u2.Blur_Module)
require(u2.Data.SeedData)
require(u2.Data.SeedPackData)
local u13 = require(u2.Data.NightQuestData)
local u14 = require(u2.Data.NightQuestRewardData)
local u15 = require(u2.Code.Particle2D)
local u16 = v1.LocalPlayer
local v17 = u16.PlayerGui
local u18 = v17:WaitForChild("NightQuest_UI")
local v19 = u18.Frame
local u20 = u6(v19, "Main")
local u21 = u6(v19, "ResetProgressionFrame")
local u22 = u6(v19, "SmallReward")
local u23 = u6(v19, "RobuxButton")
local u24 = u6(v19, "ShecklesButton")
local u25 = u6(v19, "EXIT_BUTTON")
local u26 = u6(v19, "CONFIRM_BUTTON")
local u27 = u6(v19, "TRACK_BUTTON")
local u28 = u6(v19, "CURRENT_REWARD")
u6(v19, "REWARD_ICON")
local u29 = u6(v19, "PremiumCurrency")
local u30 = u6(v19, "PROGRESSION_AMOUNT")
local u31 = u6(v19, "QUEST_DETAILS")
local u32 = u6(v19, "PROGRESS_FILL")
local u33 = u6(v19, "TIMER_LABEL")
local u34 = u6(v19, "REWARD_TEMPLATE")
local u35 = u6(v19, "UNCLAIMED_CONTENT")
u6(v19, "CLAIMREWARD_FRAME")
local u36 = u6(v19, "QUEST_DESC_FRAME")
local u37 = u6(u18, "RewardTrack")
local u38 = u6(u37, "RewardScrollingFrame")
local u39 = u6(u37, "REWARDTRACK_TEMPLATE")
local u40 = u6(u37, "RETURN_BUTTON")
local u41 = v17:WaitForChild("NightQuest_HUD")
local u42 = u6(u41, "PROGRESS_FILL")
local u43 = u6(u41, "PremiumCurrency")
local u44 = u6(u41, "CURRENT_REWARD")
local u45 = u6(u41, "PROGRESSION_AMOUNT")
local v46 = u6(workspace.NightEvent, "PassInfoProximityPrompt")
local u47 = nil
local u48 = nil
local u49 = nil
local u50 = game:GetService("TweenService")
local u51 = tick()
local u52 = {}
local u53 = nil
local function u56() --[[Anonymous function at line 86]]
    --[[
    Upvalues:
        [1] = u14
        [2] = u39
        [3] = u38
    --]]
    for _, v54 in u14 do
        local v55 = u39:Clone()
        v55:WaitForChild("ITEM_IMAGE").Image = v54.RewardIcon
        v55:WaitForChild("PointAmount").Text = v54.RequiredExperience
        if v54.RewardAmount > 1 then
            v55:WaitForChild("Count").Text = "+" .. v54.RewardAmount
        else
            v55:WaitForChild("Count").Text = ""
        end
        v55.Parent = u38
    end
end
local function u60() --[[Anonymous function at line 102]]
    --[[
    Upvalues:
        [1] = u47
        [2] = u2
        [3] = u48
        [4] = u6
        [5] = u49
    --]]
    local v57 = workspace:GetAttribute("BloodMoonEvent") or false
    if not u47 then
        u47 = workspace:WaitForChild("NightEvent"):FindFirstChild("BloodMoonShrine", true) or u2.Modules.UpdateService:FindFirstChild("BloodMoonShrine", true)
    end
    if not u48 then
        u48 = u6(workspace, "BloodMoonProximityPrompt")
    end
    if not u49 then
        u49 = u6(workspace, "BloodMoonArrow")
    end
    for _, v58 in u47:GetDescendants() do
        if v58:IsA("ParticleEmitter") then
            v58.Enabled = v57
        end
    end
    for _, v59 in u49:GetDescendants() do
        if v59:IsA("BasePart") then
            v59.Transparency = v57 and 0 or 1
        end
    end
    u48.Enabled = v57
end
function u52.DisplayUI(_) --[[Anonymous function at line 127]]
    --[[
    Upvalues:
        [1] = u37
        [2] = u20
        [3] = u52
        [4] = u5
        [5] = u18
    --]]
    u37.Visible = false
    u20.Visible = true
    task.spawn(function() --[[Anonymous function at line 130]]
        --[[
        Upvalues:
            [1] = u52
        --]]
        u52:UpdateProgression()
    end)
    u5:Open(u18)
end
local u61 = nil
local u62 = nil
function u52.UpdateProgression(_) --[[Anonymous function at line 147]]
    --[[
    Upvalues:
        [1] = u7
        [2] = u62
        [3] = u16
        [4] = u9
        [5] = u32
        [6] = u30
        [7] = u28
        [8] = u31
        [9] = u36
        [10] = u21
        [11] = u22
        [12] = u61
        [13] = u41
        [14] = u15
        [15] = u29
        [16] = u53
        [17] = u50
        [18] = u35
        [19] = u34
    --]]
    local v63 = u7:GetData()
    local v64 = tick()
    u62 = v64
    if v63 then
        local u65 = v63.NightQuestData
        if u9:IsQuestFinished(u65) then
            u32.Size = UDim2.new(1, 0, 1, 0)
            u30.Text = ("%*/%*"):format(u65.Experience, u65.Experience)
            u28.Image = "rbxassetid://0"
            u31.Text = "Night Quest is complete - Reset Quest?"
            u36.Visible = false
            u21.Visible = true
            u22.Visible = false
        else
            u31.Text = "Free Rewards"
            u21.Visible = false
            u36.Visible = true
            u22.Visible = true
            local u66, u67 = u9:GetNextQuestReward(u65)
            local u68 = u9:ReturnRewardData()
            if not u61 then
                u61 = u67
            end
            local function u87() --[[Anonymous function at line 188]]
                --[[
                Upvalues:
                    [1] = u28
                    [2] = u41
                    [3] = u15
                --]]
                local v69 = Random.new()
                for _ = 1, 10 do
                    local v70 = v69:NextNumber(0, 359)
                    local v71 = v69:NextInteger(20, 45)
                    local u72 = v69:NextInteger(1, 0) == 1 and script.Sparkle:Clone() or script.Sparkle2:Clone()
                    local v73 = v69:NextNumber(-10, 10)
                    local v74 = math.rad(v70)
                    local v75 = v73 * math.cos(v74)
                    local v76 = math.rad(v70)
                    local v77 = v73 * math.sin(v76)
                    local v78 = u28.AbsolutePosition + u28.AbsoluteSize / 2
                    u72.Position = UDim2.new(0, v78.X, 0, v78.Y) + UDim2.new(0, v75, 0, v77)
                    u72.Size = UDim2.new(0, v71, 0, v71)
                    u72.Parent = u41.Frame
                    local u79 = v69:NextNumber(0, 0.5)
                    task.spawn(function() --[[Anonymous function at line 211]]
                        --[[
                        Upvalues:
                            [1] = u72
                            [2] = u79
                        --]]
                        local v80 = {
                            ["ImageTransparency"] = u79
                        }
                        game.TweenService:Create(u72, TweenInfo.new(1), v80):Play()
                        task.wait(1)
                        game.TweenService:Create(u72, TweenInfo.new(1), {
                            ["ImageTransparency"] = 1
                        }):Play()
                    end)
                    local v81 = UDim2.new
                    local v82 = math.rad(v70)
                    local v83 = math.cos(v82) * 2500
                    local v84 = math.rad(v70)
                    local v85 = v81(0, v83, 0, math.sin(v84) * -1900)
                    local v86 = UDim2.new(0, 0, 4, 0)
                    u15.createParticle(u72, 3, v85, v69:NextInteger(-45, 45), v86, UDim2.new(0, 5, 0, 5), 0, 0, nil, 0.3)
                end
            end
            if u61 ~= u67 then
                for u88 = u61, u67 - 1 do
                    if u62 ~= v64 then
                        return
                    end
                    game.TweenService:Create(u29.UIScale, TweenInfo.new(0.2, Enum.EasingStyle.Quad, Enum.EasingDirection.InOut, 0, true), {
                        ["Scale"] = 1.3
                    }):Play()
                    local u89 = u88 ~= u61 and 0 or u53
                    u32.Size = UDim2.new(0, 0, 1, 0)
                    u50:Create(u32, TweenInfo.new(0.5), {
                        ["Size"] = UDim2.new(1, 0, 1, 0)
                    }):Play()
                    u28.Image = u68[u88].RewardIcon
                    task.spawn(function() --[[Anonymous function at line 244]]
                        --[[
                        Upvalues:
                            [1] = u89
                            [2] = u68
                            [3] = u88
                            [4] = u30
                        --]]
                        local v90 = 0
                        while v90 < 0.5 do
                            v90 = v90 + game:GetService("RunService").Heartbeat:Wait()
                            local v91 = v90 / 0.5
                            local v92 = u89
                            local v93 = v92 + (u68[u88].RequiredExperience - v92) * v91
                            u30.Text = ("%*/%*"):format(math.round(v93), u68[u88].RequiredExperience)
                        end
                    end)
                    task.wait(0.5)
                    game.TweenService:Create(u28.Parent.UIScale, TweenInfo.new(0.4, Enum.EasingStyle.Quad, Enum.EasingDirection.InOut, 0, true), {
                        ["Scale"] = 1.3
                    }):Play()
                    u87()
                    task.wait(0.8)
                end
                if u62 ~= v64 then
                    return
                end
                u32.Size = UDim2.new(0, 0, 1, 0)
            end
            if u66 then
                local v94 = u65.Experience / u66.RequiredExperience
                local v95 = math.min(1, v94)
                u50:Create(u32, TweenInfo.new(0.5), {
                    ["Size"] = UDim2.new(v95, 0, 1, 0)
                }):Play()
                task.spawn(function() --[[Anonymous function at line 291]]
                    --[[
                    Upvalues:
                        [1] = u61
                        [2] = u67
                        [3] = u53
                        [4] = u28
                        [5] = u66
                        [6] = u65
                        [7] = u30
                    --]]
                    local v96 = u61 ~= u67 and 0 or u53
                    u28.Image = u66.RewardIcon
                    local v97 = 0
                    while v97 < 0.5 do
                        v97 = v97 + game:GetService("RunService").Heartbeat:Wait()
                        local v98 = v97 / 0.5
                        local v99 = v96 + (u65.Experience - v96) * v98
                        u30.Text = ("%*/%*"):format(math.round(v99), u66.RequiredExperience)
                    end
                end)
                task.delay(0.5, function() --[[Anonymous function at line 314]]
                    --[[
                    Upvalues:
                        [1] = u30
                        [2] = u65
                        [3] = u66
                    --]]
                    u30.Text = ("%*/%*"):format(u65.Experience, u66.RequiredExperience)
                end)
            else
                u50:Create(u32, TweenInfo.new(0.5), {
                    ["Size"] = UDim2.new(1, 0, 1, 0)
                }):Play()
                task.delay(0.5, function() --[[Anonymous function at line 277]]
                    --[[
                    Upvalues:
                        [1] = u87
                        [2] = u30
                        [3] = u65
                        [4] = u28
                    --]]
                    u87()
                    u30.Text = ("%*/%*"):format(u65.Experience, u65.Experience)
                    u28.Image = "rbxassetid://0"
                end)
            end
            u53 = u65.Experience
            u61 = u67
            local v100 = u9:GetUnclaimedQuestRewards(u65)
            for _, v101 in u35:GetChildren() do
                if v101:IsA("Frame") then
                    v101:Destroy()
                end
            end
            for _, v102 in v100 do
                local v103 = u34:Clone()
                v103:WaitForChild("ITEM_IMAGE").Image = v102.RewardIcon
                if v102.RewardAmount > 1 then
                    v103:WaitForChild("Count").Text = "+" .. v102.RewardAmount
                else
                    v103:WaitForChild("Count").Text = ""
                end
                v103.Parent = u35
            end
        end
    else
        warn((("No player data for %*!"):format(u16)))
        return
    end
end
function u52.Start(_) --[[Anonymous function at line 346]]
    --[[
    Upvalues:
        [1] = u5
        [2] = u18
        [3] = u27
        [4] = u37
        [5] = u20
        [6] = u40
        [7] = u25
        [8] = u26
        [9] = u3
        [10] = u24
        [11] = u12
        [12] = u8
        [13] = u13
        [14] = u23
        [15] = u4
        [16] = u33
        [17] = u10
        [18] = u11
        [19] = u56
        [20] = u52
        [21] = u60
    --]]
    u5:GetStateForGui(u18)
    u5:UsePopupAnims(u18)
    u27.Activated:Connect(function() --[[Anonymous function at line 350]]
        --[[
        Upvalues:
            [1] = u37
            [2] = u20
        --]]
        u37.Visible = true
        u20.Visible = false
    end)
    u40.Activated:Connect(function() --[[Anonymous function at line 355]]
        --[[
        Upvalues:
            [1] = u37
            [2] = u20
        --]]
        u37.Visible = false
        u20.Visible = true
    end)
    u25.Activated:Connect(function() --[[Anonymous function at line 361]]
        --[[
        Upvalues:
            [1] = u5
            [2] = u18
        --]]
        u5:Close(u18)
    end)
    u26.Activated:Connect(function() --[[Anonymous function at line 365]]
        --[[
        Upvalues:
            [1] = u3
            [2] = u5
            [3] = u18
        --]]
        u3:FireServer("ClaimQuestReward")
        u5:Close(u18)
    end)
    u24.Activated:Connect(function() --[[Anonymous function at line 370]]
        --[[
        Upvalues:
            [1] = u12
            [2] = u3
            [3] = u5
            [4] = u18
        --]]
        u12.Blur(0, 0.3)
        u3:FireServer("ResetQuestData")
        u5:Close(u18)
    end)
    u24.TextLabel.Text = u8(u13.ShecklesResetCost) .. "\194\162"
    u23.Activated:Connect(function() --[[Anonymous function at line 377]]
        --[[
        Upvalues:
            [1] = u12
            [2] = u4
            [3] = u5
            [4] = u18
        --]]
        u12.Blur(0, 0.3)
        u4:PromptPurchase(3280732293, Enum.InfoType.Product)
        u5:Close(u18)
    end)
    task.spawn(function() --[[Anonymous function at line 383]]
        --[[
        Upvalues:
            [1] = u4
            [2] = u23
        --]]
        u4:SetPriceLabel(u23.TextLabel, 3280732293, ":robux::value:")
    end)
    task.spawn(function() --[[Anonymous function at line 387]]
        --[[
        Upvalues:
            [1] = u33
            [2] = u10
            [3] = u11
        --]]
        while wait(1) do
            u33.Text = ("Ends in %*"):format((u10.compactFormat(u11:GetUpdateTime() - os.time())))
        end
        u33.Text = ""
    end)
    u56()
    u52:UpdateProgression()
    u60()
end
local v104 = u7:GetData()
if not v104 then
    warn((("No player data for %*!"):format(u16)))
    return {}
end
local v105 = v104.NightQuestData
u53 = v105.Experience
local _, v106 = u9:GetNextQuestReward(v105)
u9:ReturnRewardData()
local u107 = v106
local u108 = require(script.ItemRewarder)
u3.OnClientEvent:Connect(function() --[[Anonymous function at line 425]]
    --[[
    Upvalues:
        [1] = u51
        [2] = u41
        [3] = u7
        [4] = u16
        [5] = u9
        [6] = u107
        [7] = u53
        [8] = u44
        [9] = u15
        [10] = u43
        [11] = u42
        [12] = u50
        [13] = u45
        [14] = u108
    --]]
    task.wait(1)
    local u109 = tick()
    u51 = u109
    u41.Progress.Visible = true
    game.TweenService:Create(u41.Progress, TweenInfo.new(0.4), {
        ["GroupTransparency"] = 0
    }):Play()
    local v110 = u7:GetData()
    if not v110 then
        warn((("No player data for %*!"):format(u16)))
        return
    end
    local u111 = v110.NightQuestData
    local u112, u113 = u9:GetNextQuestReward(u111)
    local u114 = u9:ReturnRewardData()
    if u107 then
        if u107 == "RESET" then
            u107 = 1
            u53 = 0
        end
    else
        u107 = u113
    end
    local function u135() --[[Anonymous function at line 454]]
        --[[
        Upvalues:
            [1] = u44
            [2] = u41
            [3] = u15
        --]]
        local v115 = Random.new()
        for _ = 1, 10 do
            local v116 = v115:NextNumber(0, 359)
            local v117 = v115:NextInteger(20, 45)
            local u118 = v115:NextInteger(1, 0) == 1 and script.Sparkle:Clone() or script.Sparkle2:Clone()
            local v119 = v115:NextNumber(-10, 10)
            local v120 = math.rad(v116)
            local v121 = v119 * math.cos(v120)
            local v122 = math.rad(v116)
            local v123 = v119 * math.sin(v122)
            local v124 = u44.AbsolutePosition + u44.AbsoluteSize / 2
            u118.Position = UDim2.new(0, v124.X, 0, v124.Y) + UDim2.new(0, v121, 0, v123)
            u118.Size = UDim2.new(0, v117, 0, v117)
            u118.Parent = u41.Frame
            local u125 = v115:NextNumber(0, 0.5)
            task.spawn(function() --[[Anonymous function at line 477]]
                --[[
                Upvalues:
                    [1] = u118
                    [2] = u125
                --]]
                local v126 = {
                    ["ImageTransparency"] = u125
                }
                game.TweenService:Create(u118, TweenInfo.new(1), v126):Play()
                task.wait(1)
                game.TweenService:Create(u118, TweenInfo.new(1), {
                    ["ImageTransparency"] = 1
                }):Play()
            end)
            local v127 = UDim2.new
            local v128 = math.rad(v116)
            local v129 = math.cos(v128) * 2500
            local v130 = math.rad(v116)
            local v131 = v127(0, v129, 0, math.sin(v130) * -1900)
            local v132 = UDim2.new(0, 0, 4, 0)
            u15.createParticle(u118, 3, v131, v115:NextInteger(-45, 45), v132, UDim2.new(0, 5, 0, 5), 0, 0, nil, 0.3)
        end
        local v133 = script.Shockwave:Clone()
        local v134 = u44.AbsolutePosition + u44.AbsoluteSize / 2
        v133.Position = UDim2.new(0, v134.X, 0, v134.Y)
        v133.Parent = u41.Frame
        v133.ZIndex = 1
        game.TweenService:Create(v133.UIScale, TweenInfo.new(0.4, Enum.EasingStyle.Quad), {
            ["Scale"] = 7
        }):Play()
        v133.ImageTransparency = 0
        game.TweenService:Create(v133, TweenInfo.new(0.3, Enum.EasingStyle.Quad), {
            ["ImageTransparency"] = 1
        }):Play()
        game.Debris:AddItem(v133, 1)
    end
    local v136 = {}
    if u107 ~= u113 then
        local v137 = nil
        if u113 == nil then
            u113 = u107 + 1
            v137 = true
        elseif u113 < u107 then
            u107 = 1
        end
        for u138 = u107, u113 - 1 do
            local v139 = u114[u138]
            table.insert(v136, v139)
            if u51 ~= u109 then
                break
            end
            game.TweenService:Create(u43.UIScale, TweenInfo.new(0.2, Enum.EasingStyle.Quad, Enum.EasingDirection.InOut, 0, true), {
                ["Scale"] = 1.3
            }):Play()
            local u140 = u138 ~= u107 and 0 or u53
            u42.Size = UDim2.new(0, 0, 1, 0)
            u50:Create(u42, TweenInfo.new(0.5), {
                ["Size"] = UDim2.new(1, 0, 1, 0)
            }):Play()
            u44.Image = u114[u138].RewardIcon
            task.spawn(function() --[[Anonymous function at line 532]]
                --[[
                Upvalues:
                    [1] = u140
                    [2] = u114
                    [3] = u138
                    [4] = u45
                --]]
                local v141 = 0
                while v141 < 0.5 do
                    v141 = v141 + game:GetService("RunService").Heartbeat:Wait()
                    local v142 = v141 / 0.5
                    local v143 = u140
                    local v144 = v143 + (u114[u138].RequiredExperience - v143) * v142
                    u45.Text = ("%*/%*"):format(math.round(v144), u114[u138].RequiredExperience)
                end
            end)
            task.wait(0.5)
            game.TweenService:Create(u44.Parent.UIScale, TweenInfo.new(0.4, Enum.EasingStyle.Quad, Enum.EasingDirection.InOut, 0, true), {
                ["Scale"] = 1.3
            }):Play()
            u135()
            task.wait(0.8)
        end
        if u51 ~= u109 then
            return
        end
        if v137 then
            u113 = nil
        end
        u42.Size = UDim2.new(0, 0, 1, 0)
    end
    if u112 then
        local v145 = u111.Experience / u112.RequiredExperience
        local v146 = math.min(1, v145)
        u50:Create(u42, TweenInfo.new(0.5), {
            ["Size"] = UDim2.new(v146, 0, 1, 0)
        }):Play()
        task.spawn(function() --[[Anonymous function at line 584]]
            --[[
            Upvalues:
                [1] = u107
                [2] = u113
                [3] = u53
                [4] = u44
                [5] = u112
                [6] = u111
                [7] = u45
            --]]
            local v147 = u107 ~= u113 and 0 or u53
            u44.Image = u112.RewardIcon
            local v148 = 0
            while v148 < 0.5 do
                v148 = v148 + game:GetService("RunService").Heartbeat:Wait()
                local v149 = v148 / 0.5
                local v150 = v147 + (u111.Experience - v147) * v149
                u45.Text = ("%*/%*"):format(math.round(v150), u112.RequiredExperience)
            end
            u53 = u111.Experience
        end)
        task.delay(0.5, function() --[[Anonymous function at line 609]]
            --[[
            Upvalues:
                [1] = u45
                [2] = u111
                [3] = u112
            --]]
            u45.Text = ("%*/%*"):format(u111.Experience, u112.RequiredExperience)
        end)
    else
        u50:Create(u42, TweenInfo.new(0.5), {
            ["Size"] = UDim2.new(1, 0, 1, 0)
        }):Play()
        task.delay(0.5, function() --[[Anonymous function at line 570]]
            --[[
            Upvalues:
                [1] = u135
                [2] = u45
                [3] = u111
                [4] = u44
            --]]
            u135()
            u45.Text = ("%*/%*"):format(u111.Experience, u111.Experience)
            u44.Image = "rbxassetid://0"
        end)
    end
    if u113 == nil then
        u107 = "RESET"
    else
        u107 = u113
    end
    task.delay(3, function() --[[Anonymous function at line 625]]
        --[[
        Upvalues:
            [1] = u51
            [2] = u109
            [3] = u41
        --]]
        if u51 == u109 then
            game.TweenService:Create(u41.Progress, TweenInfo.new(0.4), {
                ["GroupTransparency"] = 1
            }):Play()
            task.delay(0.4, function() --[[Anonymous function at line 629]]
                --[[
                Upvalues:
                    [1] = u51
                    [2] = u109
                    [3] = u41
                --]]
                if u51 == u109 then
                    u41.Progress.Visible = false
                end
            end)
        end
    end)
    if #v136 > 0 then
        u108.UnlockItems(v136)
    end
end)
workspace:GetAttributeChangedSignal("BloodMoonEvent"):Connect(function() --[[Anonymous function at line 643]]
    --[[
    Upvalues:
        [1] = u60
    --]]
    u60()
end)
task.spawn(u52.Start, u52)
v46.Triggered:Connect(function() --[[Anonymous function at line 650]]
    --[[
    Upvalues:
        [1] = u52
    --]]
    u52:DisplayUI()
end)
return u52