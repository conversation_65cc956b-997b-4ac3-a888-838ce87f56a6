-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\Observers\observePlayer-ModuleScript.lua
local u1 = game:GetService("Players")
return function(u2) --[[Function name: observePlayer, line 21]]
    --[[
    Upvalues:
        [1] = u1
    --]]
    local u3 = nil
    local u4 = {}
    local function u7(u5) --[[Anonymous function at line 27]]
        --[[
        Upvalues:
            [1] = u3
            [2] = u2
            [3] = u4
        --]]
        if u3.Connected then
            task.spawn(function() --[[Anonymous function at line 32]]
                --[[
                Upvalues:
                    [1] = u2
                    [2] = u5
                    [3] = u3
                    [4] = u4
                --]]
                local v6 = u2(u5)
                if typeof(v6) == "function" then
                    if u3.Connected and u5.Parent then
                        u4[u5] = v6
                        return
                    end
                    task.spawn(v6)
                end
            end)
        end
    end
    u3 = u1.PlayerAdded:Connect(u7)
    local u10 = u1.PlayerRemoving:Connect(function(p8) --[[Function name: OnPlayerRemoving, line 44]]
        --[[
        Upvalues:
            [1] = u4
        --]]
        local v9 = u4[p8]
        u4[p8] = nil
        if typeof(v9) == "function" then
            task.spawn(v9)
        end
    end)
    task.defer(function() --[[Anonymous function at line 57]]
        --[[
        Upvalues:
            [1] = u3
            [2] = u1
            [3] = u7
        --]]
        if u3.Connected then
            for _, v11 in u1:GetPlayers() do
                task.spawn(u7, v11)
            end
        end
    end)
    return function() --[[Anonymous function at line 68]]
        --[[
        Upvalues:
            [1] = u3
            [2] = u10
            [3] = u4
        --]]
        u3:Disconnect()
        u10:Disconnect()
        local v12 = next(u4)
        while v12 do
            local v13 = u4[v12]
            u4[v12] = nil
            if typeof(v13) == "function" then
                task.spawn(v13)
            end
            v12 = next(u4)
        end
    end
end