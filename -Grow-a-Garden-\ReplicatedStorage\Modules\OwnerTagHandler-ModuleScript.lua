-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\OwnerTagHandler-ModuleScript.lua
local v1 = game:GetService("RunService")
local u2 = game:GetService("Players")
local u3 = game:GetService("TweenService")
local v4 = game:GetService("ReplicatedStorage")
local v5 = v4:WaitForChild("Modules")
require(v5:WaitFor<PERSON>hild("GetFarm"))
local u6 = require(v4.Modules.GetFarmAsync)
local u7 = u2.LocalPlayer
local u8 = u7.Character or u7.CharacterAdded:Wait()
u7.CharacterAdded:Connect(function(p9) --[[Anonymous function at line 19]]
    --[[
    Upvalues:
        [1] = u8
    --]]
    u8 = p9
end)
local u10 = u6(u7)
local v11 = u10:WaitForChild("Owner_Tag")
local v12 = v11.Position.X
local v13 = v11.Position.Z
local u14 = Vector3.new(v12, 0, v13)
local u15 = false
local u16 = script:WaitForChild("OwnerTag")
u16:PivotTo(v11:GetPivot())
u16.Parent = workspace
task.spawn(function() --[[Anonymous function at line 33]]
    --[[
    Upvalues:
        [1] = u10
        [2] = u6
        [3] = u7
        [4] = u14
        [5] = u15
        [6] = u16
    --]]
    while true do
        task.wait(1)
        u10 = u6(u7)
        local v17 = u10:WaitForChild("Owner_Tag")
        local v18 = v17.Position.X
        local v19 = v17.Position.Z
        u14 = Vector3.new(v18, 0, v19)
        u15 = false
        u16:PivotTo(v17:GetPivot())
        u16.Parent = workspace
    end
end)
task.delay(2, function() --[[Anonymous function at line 48]]
    --[[
    Upvalues:
        [1] = u16
        [2] = u2
        [3] = u7
    --]]
    u16:FindFirstChild("PROFILE_IMAGE", true).Image = u2:GetUserThumbnailAsync(u7.UserId, Enum.ThumbnailType.HeadShot, Enum.ThumbnailSize.Size420x420)
end)
v1.RenderStepped:Connect(function() --[[Anonymous function at line 53]]
    --[[
    Upvalues:
        [1] = u8
        [2] = u14
        [3] = u15
        [4] = u3
        [5] = u16
    --]]
    local v20 = u8:GetPivot().Position
    local v21 = v20.X
    local v22 = v20.Z
    local v23 = (u14 - Vector3.new(v21, 0, v22)).Magnitude >= 80
    if u15 ~= v23 then
        u15 = v23
        u3:Create(u16.Part1.BillboardGui.Frame.CanvasGroup, TweenInfo.new(0.2), {
            ["GroupTransparency"] = v23 and 0 or 1
        }):Play()
    end
end)
return {}