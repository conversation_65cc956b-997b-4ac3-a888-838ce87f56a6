-- Full Path: -Grow-a-Garden-\ReplicatedStorage\CmdrClient\Types\SeedName-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
local u2 = require(script.Parent.Parent.Shared.Util)
local u3 = v1:Wait<PERSON><PERSON><PERSON>hild("Seed_Models")
local u8 = {
    ["Transform"] = function(p4) --[[Function name: Transform, line 7]]
        --[[
        Upvalues:
            [1] = u2
            [2] = u3
        --]]
        return u2.MakeFuzzyFinder(u3:GetChildren())(p4)
    end,
    ["Validate"] = function(p5) --[[Function name: Validate, line 13]]
        return #p5 > 0, "No seeds could be found."
    end,
    ["Autocomplete"] = function(p6) --[[Function name: Autocomplete, line 17]]
        --[[
        Upvalues:
            [1] = u2
        --]]
        return u2.GetNames(p6)
    end,
    ["Parse"] = function(p7) --[[Function name: Parse, line 21]]
        return p7[1]
    end,
    ["Default"] = function(_) --[[Function name: Default, line 25]]
        return "Carrot"
    end,
    ["ArgumentOperatorAliases"] = {
        ["me"] = ".",
        ["all"] = "*",
        ["others"] = "**",
        ["random"] = "?"
    }
}
return function(p9) --[[Anonymous function at line 37]]
    --[[
    Upvalues:
        [1] = u8
    --]]
    p9:RegisterType("seedname", u8)
end