-- Full Path: -Grow-a-Garden-\\PlantZoneEffect-LocalScript.lua
local v1 = game:GetService("Players")
local v2 = game:GetService("ReplicatedStorage")
local u3 = game:GetService("TweenService")
local u4 = require(v2.Modules.PetServices.ActivePetsService)
local u5 = require(v2.Modules.PetServices.PetUtilities)
local u6 = require(v2.Data.PetRegistry).PetList
local v7 = v2.GameEvents.PetZoneAbility
local u8 = v1.LocalPlayer
local u9 = {}
v7.OnClientEvent:Connect(function(p10, p11) --[[Anonymous function at line 17]]
    --[[
    Upvalues:
        [1] = u4
        [2] = u8
        [3] = u6
        [4] = u9
        [5] = u5
        [6] = u3
    --]]
    local v12 = u4:GetClientPetState(u8.Name)[p10]
    local v13 = u4:GetPetData(u8.Name, p10)
    if v13 then
        local v14 = u6[v13.PetType]
        local v15 = v13.PetData
        local v16 = v15.Level
        local v17 = v12.Asset
        if v17 then
            local v18 = v14.Passives
            if not u9[p10] then
                u9[p10] = {}
            end
            v17:FindFirstChildWhichIsA("Model", true)
            if p11 then
                for _, v19 in v18 do
                    local v20 = u5:GetCurrentLevelState(u5:CalculateWeight(v15.BaseWeight, v16), v19).Range
                    if v20 then
                        local v21 = script.Zone:Clone()
                        v21.Parent = v17
                        v21:PivotTo(CFrame.new(v17:GetPivot().Position.X, 0.145, v17:GetPivot().Position.Z))
                        v21.Size = Vector3.new(0, 0, 0)
                        u3:Create(v21, TweenInfo.new(0.8, Enum.EasingStyle.Quad, Enum.EasingDirection.InOut), {
                            ["Size"] = Vector3.new(v20, 0.1, v20)
                        }):Play()
                        local v22 = Instance.new("WeldConstraint")
                        v22.Part0 = v17
                        v22.Part1 = v21
                        v22.Parent = v21
                        u9[p10][v19] = v21
                    end
                end
                return
            elseif u9[p10] then
                for _, v23 in v18 do
                    local u24 = u9[p10][v23]
                    if u24 then
                        local v25 = u3:Create(u24, TweenInfo.new(0.8, Enum.EasingStyle.Quad, Enum.EasingDirection.InOut), {
                            ["Size"] = Vector3.new(0, 0, 0)
                        })
                        v25:Play()
                        v25.Completed:Once(function() --[[Anonymous function at line 92]]
                            --[[
                            Upvalues:
                                [1] = u24
                            --]]
                            u24:Destroy()
                        end)
                        u9[p10][v23] = nil
                    end
                end
            end
        else
            return
        end
    else
        return
    end
end)
task.spawn(function() --[[Anonymous function at line 104]]
    --[[
    Upvalues:
        [1] = u9
    --]]
    while true do
        for _, v26 in u9 do
            for v27, v28 in v26 do
                if v28 == nil or typeof(v28) ~= "Instance" then
                    v26[v27] = nil
                else
                    local v29 = v28:FindFirstChild("OuterRange")
                    if v29 then
                        local v30 = v29.Image
                        v30.Rotation = v30.Rotation + 0.3
                    else
                        v26[v27] = nil
                    end
                end
            end
        end
        task.wait(0.01)
    end
end)