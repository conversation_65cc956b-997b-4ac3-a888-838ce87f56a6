-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\SetupBrightnessAnimationFrame-ModuleScript.lua
local u1 = game:GetService("TweenService")
return function(p2, p3) --[[Function name: SetupBrightnessAnimation, line 9]]
    --[[
    Upvalues:
        [1] = u1
    --]]
    if not p2:<PERSON><PERSON><PERSON><PERSON>but<PERSON>("DefaultColor") then
        p2:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("DefaultColor", p2.BackgroundColor3)
    end
    local v4 = p2:GetAttribute("DefaultColor")
    local v5, v6, v7 = v4:ToHSV()
    local v8 = v7 + (p3 or 0.1)
    local v9 = math.clamp(v8, 0, 1)
    local v10 = Color3.fromHSV(v5, v6, v9)
    local v11 = p2:FindFirstChild("SENSOR", true)
    p2:FindFirstChild("UIScale", true)
    local u12 = u1:Create(p2, TweenInfo.new(0.25), {
        ["BackgroundColor3"] = v10
    })
    local u13 = u1:Create(p2, TweenInfo.new(0.25), {
        ["BackgroundColor3"] = v4
    })
    v11.MouseEnter:Connect(function() --[[Anonymous function at line 28]]
        --[[
        Upvalues:
            [1] = u12
        --]]
        u12:Play()
    end)
    v11.MouseLeave:Connect(function() --[[Anonymous function at line 32]]
        --[[
        Upvalues:
            [1] = u13
        --]]
        u13:Play()
    end)
    return v11
end