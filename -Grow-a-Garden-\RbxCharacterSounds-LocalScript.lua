-- Full Path: -Grow-a-Garden-\\RbxCharacterSounds-LocalScript.lua
local u1 = game:GetService("Players")
local u2 = game:GetService("RunService")
local u3 = game:GetService("SoundService")
local u4 = "UserSoundsUseRelativeVelocity2"
local v5, v6 = pcall(function() --[[Anonymous function at line 13]]
    --[[
    Upvalues:
        [1] = u4
    --]]
    return UserSettings():IsUserFeatureEnabled(u4)
end)
local u7 = v5 and v6
local u8 = "UserNewCharacterSoundsApi3"
local v9, v10 = pcall(function() --[[Anonymous function at line 13]]
    --[[
    Upvalues:
        [1] = u8
    --]]
    return UserSettings():IsUserFeatureEnabled(u8)
end)
local u11 = v9 and v10
local u12 = {
    ["Climbing"] = {
        ["SoundId"] = "rbxasset://sounds/action_footsteps_plastic.mp3",
        ["Looped"] = true
    },
    ["Died"] = {
        ["SoundId"] = "rbxasset://sounds/uuhhh.mp3"
    },
    ["FreeFalling"] = {
        ["SoundId"] = "rbxasset://sounds/action_falling.ogg",
        ["Looped"] = true
    },
    ["GettingUp"] = {
        ["SoundId"] = "rbxasset://sounds/action_get_up.mp3"
    },
    ["Jumping"] = {
        ["SoundId"] = "rbxasset://sounds/action_jump.mp3"
    },
    ["Landing"] = {
        ["SoundId"] = "rbxasset://sounds/action_jump_land.mp3"
    },
    ["Running"] = {
        ["SoundId"] = "rbxasset://sounds/action_footsteps_plastic.mp3",
        ["Looped"] = true,
        ["Pitch"] = 1.85
    },
    ["Splash"] = {
        ["SoundId"] = "rbxasset://sounds/impact_water.mp3"
    },
    ["Swimming"] = {
        ["SoundId"] = "rbxasset://sounds/action_swim.mp3",
        ["Looped"] = true,
        ["Pitch"] = 1.6
    }
}
local u13 = {
    ["Climbing"] = {
        ["AssetId"] = "rbxasset://sounds/action_footsteps_plastic.mp3",
        ["Looping"] = true
    },
    ["Died"] = {
        ["AssetId"] = "rbxasset://sounds/uuhhh.mp3"
    },
    ["FreeFalling"] = {
        ["AssetId"] = "rbxasset://sounds/action_falling.ogg",
        ["Looping"] = true
    },
    ["GettingUp"] = {
        ["AssetId"] = "rbxasset://sounds/action_get_up.mp3"
    },
    ["Jumping"] = {
        ["AssetId"] = "rbxasset://sounds/action_jump.mp3"
    },
    ["Landing"] = {
        ["AssetId"] = "rbxasset://sounds/action_jump_land.mp3"
    },
    ["Running"] = {
        ["AssetId"] = "rbxasset://sounds/action_footsteps_plastic.mp3",
        ["Looping"] = true,
        ["PlaybackSpeed"] = 1.85
    },
    ["Splash"] = {
        ["AssetId"] = "rbxasset://sounds/impact_water.mp3"
    },
    ["Swimming"] = {
        ["AssetId"] = "rbxasset://sounds/action_swim.mp3",
        ["Looping"] = true,
        ["PlaybackSpeed"] = 1.6
    }
}
local function u17(p14, p15) --[[Anonymous function at line 99]]
    if p14 then
        local v16 = p14.ActiveController and (not (p14.ActiveController:IsA("GroundController") and p14.GroundSensor) and p14.ActiveController:IsA("ClimbController"))
        if v16 then
            v16 = p14.ClimbSensor
        end
        if v16 and v16.SensedPart then
            return p15 - v16.SensedPart:GetVelocityAtPosition(p14.RootPart.Position)
        else
            return p15
        end
    else
        return p15
    end
end
local u93 = require(script:WaitForChild("AtomicBinding")).new({
    ["humanoid"] = "Humanoid",
    ["rootPart"] = "HumanoidRootPart"
}, function(p18) --[[Function name: initializeSoundSystem, line 163]]
    --[[
    Upvalues:
        [1] = u7
        [2] = u11
        [3] = u3
        [4] = u1
        [5] = u13
        [6] = u12
        [7] = u17
        [8] = u2
    --]]
    local u19 = p18.humanoid
    local u20 = p18.rootPart
    local u21
    if u7 then
        u21 = u19.Parent:FindFirstChild("ControllerManager")
    else
        u21 = nil
    end
    local u22 = {}
    if u11 and u3.CharacterSoundsUseNewApi == Enum.RolloutState.Enabled then
        local v23 = u1.LocalPlayer.Character
        local v24 = 5
        local v25 = {}
        while v24 < 150 do
            v25[v24] = 5 / v24
            v24 = v24 * 1.25
        end
        v25[150] = 0
        local v26 = Instance.new("AudioEmitter", v23)
        v26.Name = "RbxCharacterSoundsEmitter"
        v26:SetDistanceAttenuation(v25)
        for v27, v28 in pairs(u13) do
            local v29 = Instance.new("AudioPlayer")
            local v30 = Instance.new("Wire")
            v29.Name = v27
            v30.Name = v27 .. "Wire"
            v29.Archivable = false
            v29.Volume = 0.65
            for v31, v32 in pairs(v28) do
                v29[v31] = v32
            end
            v29.Parent = u20
            v30.Parent = v29
            v30.SourceInstance = v29
            v30.TargetInstance = v26
            u22[v27] = v29
        end
    else
        for v33, v34 in pairs(u12) do
            local v35 = Instance.new("Sound")
            v35.Name = v33
            v35.Archivable = false
            v35.RollOffMinDistance = 5
            v35.RollOffMaxDistance = 150
            v35.Volume = 0.65
            for v36, v37 in pairs(v34) do
                v35[v36] = v37
            end
            v35.Parent = u20
            u22[v33] = v35
        end
    end
    local u38 = {}
    local function u47(p39) --[[Anonymous function at line 228]]
        --[[
        Upvalues:
            [1] = u38
            [2] = u11
        --]]
        local v40 = pairs
        local v41 = u38
        local v42 = {}
        local v43 = p39 or nil
        for v44, v45 in pairs(v41) do
            v42[v44] = v45
        end
        for v46 in v40(v42) do
            if v46 ~= v43 then
                if u11 and v46:IsA("AudioPlayer") then
                    v46:Stop()
                else
                    v46.Playing = false
                end
                u38[v46] = nil
            end
        end
    end
    local u68 = {
        [Enum.HumanoidStateType.FallingDown] = function() --[[Anonymous function at line 240]]
            --[[
            Upvalues:
                [1] = u47
            --]]
            u47()
        end,
        [Enum.HumanoidStateType.GettingUp] = function() --[[Anonymous function at line 244]]
            --[[
            Upvalues:
                [1] = u47
                [2] = u22
                [3] = u11
            --]]
            u47()
            local v48 = u22.GettingUp
            v48.TimePosition = 0
            if u11 and v48:IsA("AudioPlayer") then
                v48:Play()
            else
                v48.Playing = true
            end
        end,
        [Enum.HumanoidStateType.Jumping] = function() --[[Anonymous function at line 249]]
            --[[
            Upvalues:
                [1] = u47
                [2] = u22
                [3] = u11
            --]]
            u47()
            local v49 = u22.Jumping
            v49.TimePosition = 0
            if u11 and v49:IsA("AudioPlayer") then
                v49:Play()
            else
                v49.Playing = true
            end
        end,
        [Enum.HumanoidStateType.Swimming] = function() --[[Anonymous function at line 254]]
            --[[
            Upvalues:
                [1] = u20
                [2] = u22
                [3] = u11
                [4] = u47
                [5] = u38
            --]]
            local v50 = u20.AssemblyLinearVelocity.Y
            local v51 = math.abs(v50)
            if v51 > 0.1 then
                local v52 = u22.Splash
                local v53 = (v51 - 100) * 0.72 / 250 + 0.28
                v52.Volume = math.clamp(v53, 0, 1)
                local v54 = u22.Splash
                v54.TimePosition = 0
                if u11 and v54:IsA("AudioPlayer") then
                    v54:Play()
                else
                    v54.Playing = true
                end
            end
            u47(u22.Swimming)
            local v55 = u22.Swimming
            if u11 and v55:IsA("AudioPlayer") then
                v55:Play()
            else
                v55.Playing = true
            end
            u38[u22.Swimming] = true
        end,
        [Enum.HumanoidStateType.Freefall] = function() --[[Anonymous function at line 265]]
            --[[
            Upvalues:
                [1] = u22
                [2] = u47
                [3] = u11
                [4] = u38
            --]]
            u22.FreeFalling.Volume = 0
            u47(u22.FreeFalling)
            local v56 = u22.FreeFalling
            if u11 and v56:IsA("AudioPlayer") then
                v56.Looping = true
            else
                v56.Looped = true
            end
            if u22.FreeFalling:IsA("Sound") then
                u22.FreeFalling.PlaybackRegionsEnabled = true
            end
            u22.FreeFalling.LoopRegion = NumberRange.new(2, 9)
            local v57 = u22.FreeFalling
            v57.TimePosition = 0
            if u11 and v57:IsA("AudioPlayer") then
                v57:Play()
            else
                v57.Playing = true
            end
            u38[u22.FreeFalling] = true
        end,
        [Enum.HumanoidStateType.Landed] = function() --[[Anonymous function at line 279]]
            --[[
            Upvalues:
                [1] = u47
                [2] = u20
                [3] = u22
                [4] = u11
            --]]
            u47()
            local v58 = u20.AssemblyLinearVelocity.Y
            local v59 = math.abs(v58)
            if v59 > 75 then
                local v60 = u22.Landing
                local v61 = (v59 - 50) * 1 / 50 + 0
                v60.Volume = math.clamp(v61, 0, 1)
                local v62 = u22.Landing
                v62.TimePosition = 0
                if u11 and v62:IsA("AudioPlayer") then
                    v62:Play()
                    return
                end
                v62.Playing = true
            end
        end,
        [Enum.HumanoidStateType.Running] = function() --[[Anonymous function at line 288]]
            --[[
            Upvalues:
                [1] = u47
                [2] = u22
                [3] = u11
                [4] = u38
            --]]
            u47(u22.Running)
            local v63 = u22.Running
            if u11 and v63:IsA("AudioPlayer") then
                v63:Play()
            else
                v63.Playing = true
            end
            u38[u22.Running] = true
        end,
        [Enum.HumanoidStateType.Climbing] = function() --[[Anonymous function at line 294]]
            --[[
            Upvalues:
                [1] = u22
                [2] = u20
                [3] = u7
                [4] = u17
                [5] = u21
                [6] = u11
                [7] = u47
                [8] = u38
            --]]
            local v64 = u22.Climbing
            local v65 = u20.AssemblyLinearVelocity
            if u7 then
                v65 = u17(u21, v65)
            end
            local v66 = v65.Y
            if math.abs(v66) > 0.1 then
                if u11 and v64:IsA("AudioPlayer") then
                    v64:Play()
                else
                    v64.Playing = true
                end
                u47(v64)
            else
                u47()
            end
            u38[v64] = true
        end,
        [Enum.HumanoidStateType.Seated] = function() --[[Anonymous function at line 307]]
            --[[
            Upvalues:
                [1] = u47
            --]]
            u47()
        end,
        [Enum.HumanoidStateType.Dead] = function() --[[Anonymous function at line 311]]
            --[[
            Upvalues:
                [1] = u47
                [2] = u22
                [3] = u11
            --]]
            u47()
            local v67 = u22.Died
            v67.TimePosition = 0
            if u11 and v67:IsA("AudioPlayer") then
                v67:Play()
            else
                v67.Playing = true
            end
        end
    }
    local u79 = {
        [u22.Climbing] = function(_, p69, p70) --[[Anonymous function at line 319]]
            --[[
            Upvalues:
                [1] = u7
                [2] = u17
                [3] = u21
                [4] = u11
            --]]
            if u7 then
                p70 = u17(u21, p70)
            end
            local v71 = p70.Magnitude > 0.1
            if u11 and p69:IsA("AudioPlayer") then
                if p69.IsPlaying and not v71 then
                    p69:Stop()
                    return
                end
                if not p69.IsPlaying and v71 then
                    p69:Play()
                    return
                end
            else
                p69.Playing = v71
            end
        end,
        [u22.FreeFalling] = function(p72, p73, p74) --[[Anonymous function at line 324]]
            if p74.Magnitude > 75 then
                local v75 = p73.Volume + p72 * 0.9
                p73.Volume = math.clamp(v75, 0, 1)
            else
                p73.Volume = 0
            end
        end,
        [u22.Running] = function(_, p76, p77) --[[Anonymous function at line 332]]
            --[[
            Upvalues:
                [1] = u19
                [2] = u11
            --]]
            local v78
            if p77.Magnitude > 0.5 then
                v78 = u19.MoveDirection.Magnitude > 0.5
            else
                v78 = false
            end
            if u11 and p76:IsA("AudioPlayer") then
                if p76.IsPlaying and not v78 then
                    p76:Stop()
                    return
                end
                if not p76.IsPlaying and v78 then
                    p76:Play()
                    return
                end
            else
                p76.Playing = v78
            end
        end
    }
    local u80 = {
        [Enum.HumanoidStateType.RunningNoPhysics] = Enum.HumanoidStateType.Running
    }
    local u81 = u80[u19:GetState()] or u19:GetState()
    local v82 = u81
    local v83 = u68[v82]
    if v83 then
        v83()
    end
    u81 = v82
    local u87 = u19.StateChanged:Connect(function(_, p84) --[[Anonymous function at line 356]]
        --[[
        Upvalues:
            [1] = u80
            [2] = u81
            [3] = u68
        --]]
        local v85 = u80[p84] or p84
        if v85 ~= u81 then
            local v86 = u68[v85]
            if v86 then
                v86()
            end
            u81 = v85
        end
    end)
    local u91 = u2.Stepped:Connect(function(_, p88) --[[Anonymous function at line 364]]
        --[[
        Upvalues:
            [1] = u38
            [2] = u79
            [3] = u20
        --]]
        for v89 in pairs(u38) do
            local v90 = u79[v89]
            if v90 then
                v90(p88, v89, u20.AssemblyLinearVelocity)
            end
        end
    end)
    return function() --[[Function name: terminate, line 375]]
        --[[
        Upvalues:
            [1] = u87
            [2] = u91
            [3] = u22
        --]]
        u87:Disconnect()
        u91:Disconnect()
        for _, v92 in pairs(u22) do
            v92:Destroy()
        end
        table.clear(u22)
    end
end)
local u94 = {}
local function u96(p95) --[[Anonymous function at line 398]]
    --[[
    Upvalues:
        [1] = u93
    --]]
    u93:bindRoot(p95)
end
local function u98(p97) --[[Anonymous function at line 402]]
    --[[
    Upvalues:
        [1] = u93
    --]]
    u93:unbindRoot(p97)
end
local function v105(p99) --[[Anonymous function at line 406]]
    --[[
    Upvalues:
        [1] = u94
        [2] = u93
        [3] = u96
        [4] = u98
    --]]
    local v100 = u94[p99]
    if not v100 then
        v100 = {}
        u94[p99] = v100
    end
    if p99.Character then
        u93:bindRoot(p99.Character)
    end
    local v101 = p99.CharacterAdded
    local v102 = u96
    table.insert(v100, v101:Connect(v102))
    local v103 = p99.CharacterRemoving
    local v104 = u98
    table.insert(v100, v103:Connect(v104))
end
local function v109(p106) --[[Anonymous function at line 420]]
    --[[
    Upvalues:
        [1] = u94
        [2] = u93
    --]]
    local v107 = u94[p106]
    if v107 then
        for _, v108 in ipairs(v107) do
            v108:Disconnect()
        end
        u94[p106] = nil
    end
    if p106.Character then
        u93:unbindRoot(p106.Character)
    end
end
for _, v110 in ipairs(u1:GetPlayers()) do
    task.spawn(v105, v110)
end
u1.PlayerAdded:Connect(v105)
u1.PlayerRemoving:Connect(v109)