-- Full Path: -Grow-a-Garden-\\EventShopData-ModuleScript.lua
local _ = game.ReplicatedStorage.Seed_Models
local v1 = {
    ["Bloodmoon Crate"] = {
        ["SeedName"] = "Bloodmoon Crate",
        ["SeedRarity"] = "Legendary",
        ["StockChance"] = 60,
        ["StockAmount"] = { 1, 1 },
        ["Price"] = 120000000,
        ["PurchaseID"] = 3292233680,
        ["DisplayInShop"] = true,
        ["LayoutOrder"] = -1,
        ["Asset"] = "rbxassetid://71576642947312",
        ["FruitIcon"] = "rbxassetid://140481838960536",
        ["ItemType"] = "Crate",
        ["Stack"] = 1,
        ["Description"] = "What could be inside?"
    },
    ["Night Egg"] = {
        ["SeedName"] = "Night Egg",
        ["SeedRarity"] = "Rare",
        ["StockChance"] = 3,
        ["StockAmount"] = { 1, 3 },
        ["Price"] = 25000000,
        ["PurchaseID"] = 3286450626,
        ["DisplayInShop"] = true,
        ["LayoutOrder"] = 2,
        ["Asset"] = "rbxassetid://133763620419151",
        ["FruitIcon"] = "rbxassetid://114657244974527",
        ["ItemType"] = "Egg",
        ["Stack"] = 1,
        ["Description"] = ""
    },
    ["Night Seed Pack"] = {
        ["SeedName"] = "Night Seed Pack",
        ["SeedRarity"] = "Rare",
        ["StockChance"] = 3,
        ["StockAmount"] = { 1, 3 },
        ["Price"] = 10000000,
        ["PurchaseID"] = 3286450936,
        ["DisplayInShop"] = true,
        ["LayoutOrder"] = 3,
        ["Asset"] = "rbxassetid://114322381508527",
        ["FruitIcon"] = "rbxassetid://114657244974527",
        ["ItemType"] = "Seed Pack",
        ["Stack"] = 1,
        ["Description"] = ""
    },
    ["Blood Banana"] = {
        ["SeedName"] = "Blood Banana Seed",
        ["SeedRarity"] = "Mythical",
        ["StockChance"] = 1,
        ["StockAmount"] = { 2, 4 },
        ["Price"] = 200000,
        ["PurchaseID"] = 3286451547,
        ["DisplayInShop"] = true,
        ["LayoutOrder"] = 10,
        ["Asset"] = "rbxassetid://95779631318856",
        ["FruitIcon"] = "rbxassetid://72770649334593",
        ["ItemType"] = "Seed",
        ["Stack"] = 1,
        ["Description"] = ""
    },
    ["Moon Melon"] = {
        ["SeedName"] = "Moon Melon Seed",
        ["SeedRarity"] = "Mythical",
        ["StockChance"] = 2,
        ["StockAmount"] = { 2, 4 },
        ["Price"] = 500000,
        ["PurchaseID"] = 3286451918,
        ["DisplayInShop"] = true,
        ["LayoutOrder"] = 11,
        ["Asset"] = "rbxassetid://130825828882096",
        ["FruitIcon"] = "rbxassetid://111440276773067",
        ["ItemType"] = "Seed",
        ["Stack"] = 1,
        ["Description"] = ""
    },
    ["Star Caller"] = {
        ["SeedName"] = "Star Caller",
        ["SeedRarity"] = "Legendary",
        ["StockChance"] = 2,
        ["StockAmount"] = { 1, 3 },
        ["Price"] = 12000000,
        ["PurchaseID"] = 3286452366,
        ["DisplayInShop"] = true,
        ["LayoutOrder"] = 20,
        ["Asset"] = "rbxassetid://108537992961712",
        ["FruitIcon"] = "rbxassetid://114657244974527",
        ["ItemType"] = "Gear",
        ["Stack"] = 1,
        ["Description"] = "Attracts shooting stars during a meteor shower"
    },
    ["Blood Hedgehog"] = {
        ["SeedName"] = "Blood Hedgehog",
        ["SeedRarity"] = "Legendary",
        ["StockChance"] = 1,
        ["StockAmount"] = { 1, 1 },
        ["Price"] = 23000000,
        ["PurchaseID"] = 3286452685,
        ["DisplayInShop"] = true,
        ["LayoutOrder"] = 30,
        ["Asset"] = "rbxassetid://76471191139414",
        ["FruitIcon"] = "rbxassetid://114657244974527",
        ["ItemType"] = "Pet",
        ["Stack"] = 1,
        ["Description"] = "Sanguine Spike: Makes prickly fruit grow bigger"
    },
    ["Blood Kiwi"] = {
        ["SeedName"] = "Blood Kiwi",
        ["SeedRarity"] = "Mythical",
        ["StockChance"] = 2,
        ["StockAmount"] = { 1, 1 },
        ["Price"] = 20000000,
        ["PurchaseID"] = 3286452503,
        ["DisplayInShop"] = true,
        ["LayoutOrder"] = 31,
        ["Asset"] = "rbxassetid://87343374343285",
        ["FruitIcon"] = "rbxassetid://114657244974527",
        ["ItemType"] = "Pet",
        ["Stack"] = 1,
        ["Description"] = "Crimson Cradle: Occasionally reduces the hatch time of the egg with the most hatch time left"
    },
    ["Blood Owl"] = {
        ["SeedName"] = "Blood Owl",
        ["SeedRarity"] = "Divine",
        ["StockChance"] = 10,
        ["StockAmount"] = { 1, 1 },
        ["Price"] = 60000000,
        ["PurchaseID"] = 3286452771,
        ["DisplayInShop"] = true,
        ["LayoutOrder"] = 32,
        ["Asset"] = "rbxassetid://81262783747840",
        ["FruitIcon"] = "rbxassetid://114657244974527",
        ["ItemType"] = "Pet",
        ["Stack"] = 1,
        ["Description"] = "Monarch of Midnight: Grants bonus experience per second gain to all active pets.",
        3530250235
    },
    ["Chocolate Carrot"] = {
        ["SeedName"] = "Chocolate Carrot Seed",
        ["SeedRarity"] = "Common",
        ["StockChance"] = 0,
        ["StockAmount"] = { 5, 25 },
        ["Price"] = 10000,
        ["PurchaseID"] = 3268186353,
        ["DisplayInShop"] = false,
        ["LayoutOrder"] = 3,
        ["Asset"] = "rbxassetid://115881753102059",
        ["FruitIcon"] = "rbxassetid://114657244974527",
        ["ItemType"] = "Seed",
        ["Stack"] = 1,
        ["Description"] = ""
    },
    ["Red Lollipop"] = {
        ["SeedName"] = "Red Lollipop",
        ["SeedRarity"] = "Uncommon",
        ["StockChance"] = 0,
        ["StockAmount"] = { 5, 25 },
        ["Price"] = 45000,
        ["PurchaseID"] = 3268186603,
        ["DisplayInShop"] = false,
        ["LayoutOrder"] = 2,
        ["Asset"] = "rbxassetid://115881753102059",
        ["FruitIcon"] = "rbxassetid://114657244974527",
        ["ItemType"] = "Seed",
        ["Stack"] = 1,
        ["Description"] = ""
    },
    ["Candy Sunflower"] = {
        ["SeedName"] = "Candy Sunflower",
        ["SeedRarity"] = "Rare",
        ["StockChance"] = 0,
        ["StockAmount"] = { 3, 10 },
        ["Price"] = 75000,
        ["PurchaseID"] = 3268187175,
        ["DisplayInShop"] = false,
        ["LayoutOrder"] = 4,
        ["Asset"] = "rbxassetid://115881753102059",
        ["FruitIcon"] = "rbxassetid://114657244974527",
        ["ItemType"] = "Seed",
        ["Stack"] = 1,
        ["Description"] = ""
    },
    ["Easter Egg"] = {
        ["SeedName"] = "Easter Egg",
        ["SeedRarity"] = "Legendary",
        ["StockChance"] = 0,
        ["StockAmount"] = { 3, 5 },
        ["Price"] = 500000,
        ["PurchaseID"] = 3268187332,
        ["DisplayInShop"] = false,
        ["LayoutOrder"] = 5,
        ["Asset"] = "rbxassetid://115881753102059",
        ["FruitIcon"] = "rbxassetid://114657244974527",
        ["ItemType"] = "Seed",
        ["Stack"] = 1,
        ["Description"] = ""
    },
    ["Chocolate Sprinkler"] = {
        ["SeedName"] = "Chocolate Sprinkler",
        ["SeedRarity"] = "Mythical",
        ["StockChance"] = 0,
        ["StockAmount"] = { 1, 3 },
        ["Price"] = 500000,
        ["PurchaseID"] = 3268187887,
        ["DisplayInShop"] = false,
        ["LayoutOrder"] = 6,
        ["Asset"] = "rbxassetid://115881753102059",
        ["FruitIcon"] = "rbxassetid://114657244974527",
        ["ItemType"] = "Gear",
        ["Stack"] = 1,
        ["Description"] = "Covers plants in chocolate! Lasts 1 minute"
    },
    ["Candy Blossom"] = {
        ["SeedName"] = "Candy Blossom",
        ["SeedRarity"] = "Divine",
        ["StockChance"] = 0,
        ["StockAmount"] = { 1, 1 },
        ["Price"] = 10000000,
        ["PurchaseID"] = 3268187638,
        ["DisplayInShop"] = false,
        ["LayoutOrder"] = 7,
        ["Asset"] = "rbxassetid://115881753102059",
        ["FruitIcon"] = "rbxassetid://114657244974527",
        ["ItemType"] = "Seed",
        ["Stack"] = 1,
        ["Description"] = ""
    }
}
return v1