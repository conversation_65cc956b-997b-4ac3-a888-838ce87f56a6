-- Full Path: -Grow-a-Garden-\\GearName-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
game:GetService("ServerStorage")
local u2 = require(script.Parent.Parent.Shared.Util)
local v3 = require(v1.Data.GearData)
local v4 = require(v1.Data.EasterData)
local u5 = {}
for v6 in v3 do
    table.insert(u5, v6)
end
for v7 in v4 do
    table.insert(u5, v7)
end
local u12 = {
    ["Transform"] = function(p8) --[[Function name: Transform, line 33]]
        --[[
        Upvalues:
            [1] = u2
            [2] = u5
        --]]
        return u2.MakeFuzzyFinder(u5)(p8)
    end,
    ["Validate"] = function(p9) --[[Function name: Validate, line 39]]
        return #p9 > 0, "No seeds could be found."
    end,
    ["Autocomplete"] = function(p10) --[[Function name: Autocomplete, line 43]]
        --[[
        Upvalues:
            [1] = u2
        --]]
        return u2.GetNames(p10)
    end,
    ["Parse"] = function(p11) --[[Function name: Parse, line 47]]
        return p11[1]
    end,
    ["Default"] = function(_) --[[Function name: Default, line 51]]
        return "Creative"
    end,
    ["ArgumentOperatorAliases"] = {
        ["me"] = ".",
        ["all"] = "*",
        ["others"] = "**",
        ["random"] = "?"
    }
}
return function(p13) --[[Anonymous function at line 63]]
    --[[
    Upvalues:
        [1] = u12
    --]]
    p13:RegisterType("gearname", u12)
end