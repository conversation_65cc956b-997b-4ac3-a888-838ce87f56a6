-- Full Path: -Grow-a-Garden-\StarterPlayer\StarterCharacterScripts\GiftPrompt-LocalScript.lua
local v1 = game.Players.LocalPlayer
local u2 = v1.Character
local u3 = false
while u2 == nil do
    task.wait()
    u2 = v1.Character
end
u2.ChildAdded:Connect(function(p4) --[[Anonymous function at line 10]]
    --[[
    Upvalues:
        [1] = u2
        [2] = u3
    --]]
    if u2:FindFirstChildWhichIsA("Tool") and p4:FindFirstChild("Weight") then
        u3 = true
    end
end)
u2.ChildRemoved:Connect(function(_) --[[Anonymous function at line 19]]
    --[[
    Upvalues:
        [1] = u2
        [2] = u3
    --]]
    local v5 = u2:FindFirstChildWhichIsA("Tool")
    if v5 == nil then
        u3 = false
    elseif v5:FindFirstChild("Weight") == nil then
        u3 = false
    end
end)
task.spawn(function() --[[Anonymous function at line 30]]
    --[[
    Upvalues:
        [1] = u3
    --]]
    while true do
        while true do
            task.wait(0.1)
            if u3 == true then
                break
            end
            for _, v6 in pairs(game.Players:GetChildren()) do
                if v6 and (v6.Character and v6.Character.PrimaryPart) then
                    local v7 = v6.Character.HumanoidRootPart
                    if v7 then
                        for _, v8 in pairs(v7:GetChildren()) do
                            if v8:IsA("ProximityPrompt") then
                                v8.Enabled = false
                            end
                        end
                    end
                end
            end
        end
        for _, v9 in pairs(game.Players:GetChildren()) do
            if v9.Name == game.Players.LocalPlayer.Name then
                if v9 and (v9.Character and v9.Character.PrimaryPart) then
                    local v10 = v9.Character.HumanoidRootPart
                    if v10 then
                        for _, v11 in pairs(v10:GetChildren()) do
                            if v11:IsA("ProximityPrompt") then
                                v11.Enabled = false
                            end
                        end
                    end
                end
            elseif v9 and (v9.Character and v9.Character.PrimaryPart) then
                local v12 = v9.Character.HumanoidRootPart
                if v12 then
                    for _, v13 in pairs(v12:GetChildren()) do
                        if v13:IsA("ProximityPrompt") then
                            v13.Enabled = true
                        end
                    end
                end
            end
        end
    end
end)