-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\PetServices\PetVFXHandler-ModuleScript.lua
local u1 = {}
u1.__index = u1
local u2 = game:GetService("ReplicatedStorage")
workspace:WaitFor<PERSON>hild("PetsPhysical")
local u3 = {}
local v4 = {
    ["Dig"] = {
        ["effect"] = "DigEffect",
        ["duration"] = 1.5
    }
}
u3.Dog = v4
local v5 = {
    ["Dig"] = {
        ["effect"] = "DigEffect",
        ["duration"] = 1.5
    }
}
u3["Golden Lab"] = v5
local v6 = {
    ["Nap"] = {
        ["effect"] = "ZzzEffect",
        ["duration"] = 10
    }
}
u3.Cat = v6
local v7 = {
    ["Nap"] = {
        ["effect"] = "ZzzEffect",
        ["duration"] = 10
    }
}
u3["Orange Tabby"] = v7
local v8 = {
    ["Nap"] = {
        ["effect"] = "ZzzEffect",
        ["duration"] = 10
    }
}
u3["Moon Cat"] = v8
local v9 = {
    ["Chomp"] = {
        ["effect"] = "CarrotParticles",
        ["duration"] = 1.5
    }
}
u3.Bunny = v9
local v10 = {
    ["Chomp"] = {
        ["effect"] = "CarrotParticles",
        ["duration"] = 1.5
    }
}
u3["Black Bunny"] = v10
local v11 = {
    ["Chomp"] = {
        ["effect"] = "BambooParticles",
        ["duration"] = 1.5
    }
}
u3.Panda = v11
local v12 = {
    ["Frenzy"] = {
        ["effect"] = "MudSplashEffect",
        ["duration"] = 15
    }
}
u3.Pig = v12
local v13 = {
    ["Roar"] = {
        ["effect"] = "FrostBreath",
        ["duration"] = 3
    }
}
u3["Polar Bear"] = v13
local v14 = {
    ["Pray"] = {
        ["effect"] = "ShinyEffect",
        ["duration"] = 10
    }
}
u3["Praying Mantis"] = v14
local v15 = {
    ["Nurse"] = {
        ["effect"] = "MusicNotes",
        ["duration"] = 3
    }
}
u3.Kiwi = v15
local v16 = {
    ["DigDown"] = {
        ["effect"] = "DigAroundEffect",
        ["duration"] = 2
    },
    ["DigUp"] = {
        ["effect"] = "DigAroundEffect",
        ["duration"] = 2
    }
}
u3.Mole = v16
local v17 = {
    ["Croak"] = {
        ["effect"] = "Shockwave",
        ["duration"] = 2
    }
}
u3.Frog = v17
local v18 = {
    ["Croak"] = {
        ["effect"] = "ShockwaveCool",
        ["duration"] = 2
    }
}
u3["Echo Frog"] = v18
local v19 = {
    ["Nurse"] = {
        ["effect"] = "RedMusicNotes",
        ["duration"] = 3
    }
}
u3["Blood Kiwi"] = v19
function u1.new(p20, p21, p22) --[[Anonymous function at line 114]]
    --[[
    Upvalues:
        [1] = u1
    --]]
    local v23 = u1
    local v24 = setmetatable({}, v23)
    v24.PetType = p20
    v24.PetModel = p21
    v24.UUID = p22
    v24.ActiveEmitters = {}
    v24.CurrentAnimation = nil
    return v24
end
function u1.cleanupVFX(p25, p26) --[[Anonymous function at line 126]]
    if p25.ActiveEmitters[p26] then
        for _, v27 in pairs(p25.ActiveEmitters[p26]) do
            v27:Destroy()
        end
        p25.ActiveEmitters[p26] = nil
    end
end
function u1.playVFX(u28, u29) --[[Anonymous function at line 136]]
    --[[
    Upvalues:
        [1] = u3
        [2] = u2
    --]]
    if u28.CurrentAnimation == u29 then
        return
    else
        if u28.CurrentAnimation then
            u28:cleanupVFX(u28.CurrentAnimation)
        end
        u28.CurrentAnimation = u29
        local v30 = u3[u28.PetType]
        if v30 then
            local v31 = v30[u29]
            if v31 then
                local v32 = v31.effect
                if v32 then
                    local v33 = u2:FindFirstChild("Assets"):FindFirstChild("PetVFX"):FindFirstChild(v32)
                    if v33 then
                        local u34 = v33:Clone()
                        u34.Parent = u28.PetModel.PrimaryPart or u28.PetModel
                        u34.Name = v32
                        if not u28.ActiveEmitters[u29] then
                            u28.ActiveEmitters[u29] = {}
                        end
                        local v35 = u28.ActiveEmitters[u29]
                        table.insert(v35, u34)
                        if v31.duration then
                            task.delay(v31.duration, function() --[[Anonymous function at line 180]]
                                --[[
                                Upvalues:
                                    [1] = u34
                                    [2] = u28
                                    [3] = u29
                                --]]
                                if u34 and u34.Parent then
                                    local u36 = tick()
                                    local u37 = nil
                                    u37 = game:GetService("RunService").RenderStepped:Connect(function() --[[Anonymous function at line 186]]
                                        --[[
                                        Upvalues:
                                            [1] = u36
                                            [2] = u34
                                            [3] = u37
                                            [4] = u28
                                            [5] = u29
                                        --]]
                                        local v38 = (tick() - u36) / 1
                                        local v39 = math.clamp(v38, 0, 1)
                                        u34.Transparency = NumberSequence.new({ NumberSequenceKeypoint.new(0, v39), NumberSequenceKeypoint.new(1, v39) })
                                        if v39 >= 1 then
                                            u37:Disconnect()
                                            if u34 and u34.Parent then
                                                u34:Destroy()
                                                for v40, v41 in pairs(u28.ActiveEmitters[u29] or {}) do
                                                    if v41 == u34 then
                                                        table.remove(u28.ActiveEmitters[u29], v40)
                                                        return
                                                    end
                                                end
                                            end
                                        end
                                    end)
                                end
                            end)
                        end
                    end
                else
                    return
                end
            else
                return
            end
        else
            return
        end
    end
end
function u1.destroy(p42) --[[Anonymous function at line 213]]
    for _, v43 in pairs(p42.ActiveEmitters) do
        for _, v44 in pairs(v43) do
            v44:Destroy()
        end
    end
    p42.ActiveEmitters = {}
end
return u1