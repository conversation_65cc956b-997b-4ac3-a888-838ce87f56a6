-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Object_UI\CanvasGroup\TextButton\LocalScript_32-LocalScript.lua
local u1 = game.SoundService.Hover
local u2 = game.SoundService.Click
local u3 = script.Parent.BackgroundColor3
local v4 = game:GetService("UserInputService")
v4.InputBegan:Connect(function(p5, _) --[[Anonymous function at line 6]]
    --[[
    Upvalues:
        [1] = u2
    --]]
    if p5.KeyCode == Enum.KeyCode.ButtonR2 or p5.KeyCode == Enum.KeyCode.E then
        script.Parent.Parent.Parent.Start_Val.Value = true
        u2.PlaybackSpeed = 1 + math.random(-15, 15) / 100
        u2.TimePosition = 0
        u2.Playing = true
        script.Parent.BackgroundColor3 = Color3.new(0, 0.7, 0)
    end
end)
v4.InputEnded:Connect(function(p6, _) --[[Anonymous function at line 15]]
    --[[
    Upvalues:
        [1] = u3
    --]]
    if p6.KeyCode == Enum.KeyCode.ButtonR2 or p6.KeyCode == Enum.KeyCode.E then
        script.Parent.Parent.Parent.Start_Val.Value = false
        script.Parent.BackgroundColor3 = u3
    end
end)
script.Parent.MouseButton1Down:Connect(function() --[[Anonymous function at line 21]]
    --[[
    Upvalues:
        [1] = u2
    --]]
    script.Parent.Parent.Parent.Start_Val.Value = true
    u2.PlaybackSpeed = 1 + math.random(-15, 15) / 100
    u2.TimePosition = 0
    u2.Playing = true
    script.Parent.BackgroundColor3 = Color3.new(0, 0.7, 0)
end)
script.Parent.MouseLeave:Connect(function() --[[Anonymous function at line 29]]
    --[[
    Upvalues:
        [1] = u3
    --]]
    script.Parent.Parent.Parent.Start_Val.Value = false
    script.Parent.BackgroundColor3 = u3
end)
script.Parent.MouseButton1Up:Connect(function() --[[Anonymous function at line 34]]
    --[[
    Upvalues:
        [1] = u3
    --]]
    script.Parent.Parent.Parent.Start_Val.Value = false
    script.Parent.BackgroundColor3 = u3
end)
script.Parent.MouseEnter:Connect(function() --[[Anonymous function at line 39]]
    --[[
    Upvalues:
        [1] = u1
    --]]
    u1.PlaybackSpeed = 1 + math.random(-15, 15) / 100
    u1.TimePosition = 0
    u1.Playing = true
    script.Parent.BackgroundColor3 = Color3.new(0.3, 1, 0.3)
end)