-- Full Path: -Grow-a-Garden-\\Config-ModuleScript.lua
local u1 = {}
u1.__index = u1
function u1.new() --[[Anonymous function at line 30]]
    --[[
    Upvalues:
        [1] = u1
    --]]
    local v2 = u1
    local v3 = setmetatable({}, v2)
    v3.voxelSize = 100
    v3.renderDistanceTarget = 600
    v3.searchTimeBudget = 0.0008
    v3.ingestTimeBudget = 0.0014
    v3.updateTimeBudget = 0.00035
    v3.bestRefreshRate = 0.016666666666666666
    v3.worstRefreshRate = 0.06666666666666667
    v3.computeVisibilityOnlyOnDemand = false
    v3.strictlyEnforceWorstRefreshRate = false
    v3.dynamicRenderDistance = true
    v3._renderDistance = v3.renderDistanceTarget
    v3._minRenderDistance = v3.renderDistanceTarget / 3
    v3._maxRenderDistance = v3.renderDistanceTarget * 5
    v3._halfVoxelSizeVec = Vector3.new(1, 1, 1) * (v3.voxelSize / 2)
    v3._radiusThresholdForCorners = v3.voxelSize * 0.125
    v3._refreshRateRange = v3.worstRefreshRate - v3.bestRefreshRate
    v3._refreshRateMidpoint = (v3.bestRefreshRate + v3.worstRefreshRate) / 2
    return v3
end
return u1