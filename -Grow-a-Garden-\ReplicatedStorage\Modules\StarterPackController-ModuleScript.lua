-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\StarterPackController-ModuleScript.lua
local v1 = game:GetService("Players")
local v2 = game:GetService("ReplicatedStorage")
local v3 = v1.LocalPlayer.PlayerGui
local u4 = v3.Hud_UI
local u5 = v3.StarterPack_UI
local u6 = require(v2.Modules.MarketController)
local u7 = require(v2.Modules.GuiController)
local u8 = require(v2.Modules.DataService)
local u9 = require(v2.Modules.NumberUtil)
local v18 = {
    ["Start"] = function(_) --[[Function name: Start, line 17]]
        --[[
        Upvalues:
            [1] = u7
            [2] = u5
            [3] = u6
            [4] = u4
            [5] = u8
            [6] = u9
        --]]
        u7:UsePopupAnims(u5)
        local u10 = u7:GetStateForGui(u5)
        u5.Frame.Buy.Activated:Connect(function() --[[Anonymous function at line 21]]
            --[[
            Upvalues:
                [1] = u6
            --]]
            u6:PromptPurchase(3250075709, Enum.InfoType.Product)
        end)
        u5.Frame.Close.Activated:Connect(function() --[[Anonymous function at line 25]]
            --[[
            Upvalues:
                [1] = u7
                [2] = u5
            --]]
            u7:Close(u5)
        end)
        u4.SideBtns.StarterPack.Activated:Connect(function() --[[Anonymous function at line 29]]
            --[[
            Upvalues:
                [1] = u7
                [2] = u5
            --]]
            u7:Open(u5)
        end)
        local function u16() --[[Anonymous function at line 33]]
            --[[
            Upvalues:
                [1] = u8
                [2] = u9
                [3] = u5
                [4] = u4
                [5] = u10
                [6] = u7
            --]]
            local v11 = u8:GetData()
            if v11 then
                local v12 = v11.StarterPack.Timer
                if v12 then
                    local v13 = u9.compactFormat(v12)
                    u5.Frame.Timer.Text = ("Expires in %*"):format(v13)
                    u4.SideBtns.StarterPack.Timer.Text = ("%* Left"):format(v13)
                end
                local v14 = u4.SideBtns.StarterPack
                local v15
                if v12 then
                    v15 = v12 > 0
                else
                    v15 = v12
                end
                v14.Visible = v15
                if v12 and (v12 <= 0 and u10.Visible:Get()) then
                    u7:Close(u5)
                end
            end
        end
        local v17 = u8
        assert(v17:GetPathSignal("StarterPack/@")):Connect(function() --[[Anonymous function at line 53]]
            --[[
            Upvalues:
                [1] = u16
            --]]
            u16()
        end)
        task.spawn(u16)
    end
}
task.spawn(v18.Start, v18)
return v18