-- Full Path: -Grow-a-Garden-\\SeedShopController-ModuleScript.lua
local u1 = game:GetService("ReplicatedStorage")
local u2 = game:GetService("UserInputService")
local u3 = game:GetService("ContentProvider")
game:GetService("TweenService")
local u4 = game.Players.LocalPlayer
local u5 = u4.PlayerGui:WaitForChild("Seed_Shop")
local u6 = require(u1.Modules.GuiController)
local u7 = require(u1.Modules.DataService)
local u8 = require(u1.Modules.UpdateService)
local u9 = require(u1.Modules.NumberUtil)
local u10 = require(u1.Modules.Observers)
local u11 = require(u1.Comma_Module)
local v12 = require(u1.Modules.Signal)
local u13 = require(u1.Modules.FastTween)
local u14 = require(u1.Modules.MarketController)
local u15 = require(u1.Modules.GiftController)
local u16 = require(u1.Data.SeedShopData)
local u17 = require(u1.Item_Module)
require(u1.Modules.DumpTable)
local u18 = nil
local u19 = v12.new()
local u20 = u5:WaitForChild("Frame"):WaitForChild("ScrollingFrame")
local u21 = u5:WaitForChild("Frame"):WaitForChild("Frame"):WaitForChild("Timer")
local u22 = u20:WaitForChild("ItemFrame")
u22.Parent = script
local u23 = u20:WaitForChild("ItemPadding")
u23.Parent = u20
local u24 = require(game.ReplicatedStorage.Data.SeedData)
local u25 = {}
local v26 = {}
local function u37() --[[Anonymous function at line 58]]
    --[[
    Upvalues:
        [1] = u10
        [2] = u4
        [3] = u7
        [4] = u16
        [5] = u21
        [6] = u9
    --]]
    local u27 = {}
    u10.observeAttribute(u4, "AB_RestockTimer", function(p28) --[[Anonymous function at line 61]]
        --[[
        Upvalues:
            [1] = u10
            [2] = u27
        --]]
        if p28 then
            return u10.observeTag("SeedShopRestock", function(u29) --[[Anonymous function at line 66]]
                --[[
                Upvalues:
                    [1] = u27
                --]]
                local u30 = u29.Parent
                u30.Enabled = true
                local v31 = u27
                table.insert(v31, u29)
                return function() --[[Anonymous function at line 71]]
                    --[[
                    Upvalues:
                        [1] = u30
                        [2] = u27
                        [3] = u29
                    --]]
                    u30.Enabled = false
                    local v32 = table.find(u27, u29)
                    if v32 then
                        table.remove(u27, v32)
                    end
                end
            end)
        else
            return nil
        end
    end)
    while true do
        local v33 = workspace:GetServerTimeNow()
        local v34 = u7:GetData()
        local v35 = v34.SeedStock.ForcedSeedEndTimestamp and (v34.SeedStock.ForcedSeedEndTimestamp - v33 or -1) or -1
        if v35 < 0 then
            v35 = u16.RefreshTime - v33 % u16.RefreshTime
        end
        u21.Text = v35 <= 0 and "Restocking" or "New seeds in " .. u9.compactFormat(v35)
        for _, v36 in u27 do
            v36.Text = v35 <= 0 and "Now" or u9.compactFormat(v35)
        end
        if v35 <= 1 then
            require(game.ReplicatedStorage.Modules.Notification):CreateNotification("<font color=\"#ADD8E6\"><b>Your Seed Shop stock has been reset!</b></font>")
        end
        task.wait(1)
    end
end
local u38 = game.SoundService.Click
local function u85() --[[Anonymous function at line 109]]
    --[[
    Upvalues:
        [1] = u37
        [2] = u2
        [3] = u24
        [4] = u3
        [5] = u22
        [6] = u25
        [7] = u17
        [8] = u11
        [9] = u20
        [10] = u13
        [11] = u5
        [12] = u23
        [13] = u1
        [14] = u14
        [15] = u38
        [16] = u15
        [17] = u18
        [18] = u19
        [19] = u7
        [20] = u8
    --]]
    task.spawn(u37)
    if not u2.TouchEnabled then
        local v39 = {}
        for _, v40 in u24 do
            if v40.DisplayInShop then
                local v41 = Instance.new("ImageLabel")
                v41.Image = v40.FruitIcon
                table.insert(v39, v41)
            end
        end
        u3:PreloadAsync(v39)
        for _, v42 in v39 do
            v42:Destroy()
        end
        table.clear(v39)
    end
    local u43 = {}
    for u44, u45 in u24 do
        if u45.DisplayInShop then
            local u46 = u22:Clone()
            u25[u44] = u46
            u46.Name = u44
            u46.LayoutOrder = u45.LayoutOrder * 10
            table.insert(u43, u46)
            local u47 = u46.Main_Frame
            local v48 = u17.Return_Rarity_Data(u45.SeedRarity)
            if v48 then
                local v49 = v48[1]
                local v50 = v48[2]
                local v51 = v48[3]
                u47.Rarity_Text.Text = v49
                if v51 and v49 == "Prismatic" then
                    local u52 = nil
                    u52 = game:GetService("RunService").Heartbeat:Connect(function(_) --[[Anonymous function at line 157]]
                        --[[
                        Upvalues:
                            [1] = u47
                            [2] = u52
                        --]]
                        if u47 and u47.Parent then
                            local v53 = tick() * 0.1 % 1
                            local v54 = Color3.fromHSV(v53, 1, 1)
                            u47.Rarity_BG.ImageColor3 = v54
                            if u47.Rarity_Text.UIStroke then
                                local v55 = u47.Rarity_Text.UIStroke
                                local v56, v57, v58 = v54:ToHSV()
                                v55.Color = Color3.fromHSV(v56, v57, v58 / 2)
                            end
                        else
                            u52:Disconnect()
                        end
                    end)
                else
                    if u47.Rarity_Text.UIStroke then
                        local v59 = u47.Rarity_Text.UIStroke
                        local v60, v61, v62 = v50:ToHSV()
                        v59.Color = Color3.fromHSV(v60, v61, v62 / 2)
                    end
                    u47.Rarity_BG.ImageColor3 = v50
                end
            end
            u47.Seed_Text.Text = u45.SeedName
            u47.Seed_Text_Shadow.Text = u45.SeedName
            u47.Cost_Text.Text = u11.Comma(u45.Price) .. "\194\162"
            u46.Parent = u20
            u46.Main_Frame.CanvasGroup.ShopItem_Image.Image = u45.Asset
            local u63 = nil
            local u64 = nil
            u47.MouseEnter:Connect(function() --[[Anonymous function at line 192]]
                --[[
                Upvalues:
                    [1] = u63
                    [2] = u13
                    [3] = u46
                    [4] = u64
                    [5] = u45
                --]]
                u63 = u13(u46.Main_Frame.CanvasGroup.ShopItem_Image, TweenInfo.new(0.1, Enum.EasingStyle.Linear, Enum.EasingDirection.Out, -1, true, 0), {
                    ["Rotation"] = u46.Main_Frame.CanvasGroup.ShopItem_Image.Rotation + 10
                })
                u64 = u13(u46.Main_Frame.CanvasGroup.ShopItem_Image.UIScale, TweenInfo.new(0.5, Enum.EasingStyle.Linear, Enum.EasingDirection.Out, 0, false, 0), {
                    ["Scale"] = 1.5
                })
                u64.Completed:Connect(function(p65) --[[Anonymous function at line 208]]
                    --[[
                    Upvalues:
                        [1] = u46
                        [2] = u45
                    --]]
                    if p65 == Enum.PlaybackState.Completed then
                        u46.Main_Frame.CanvasGroup.ShopItem_Image.Image = u45.FruitIcon
                    end
                end)
            end)
            u47.MouseLeave:Connect(function() --[[Anonymous function at line 215]]
                --[[
                Upvalues:
                    [1] = u63
                    [2] = u46
                    [3] = u45
                    [4] = u13
                --]]
                if u63 then
                    u63:Cancel()
                end
                u46.Main_Frame.CanvasGroup.ShopItem_Image.Rotation = 0
                u46.Main_Frame.CanvasGroup.ShopItem_Image.Image = u45.Asset
                u13(u46.Main_Frame.CanvasGroup.ShopItem_Image.UIScale, TweenInfo.new(0.25, Enum.EasingStyle.Linear, Enum.EasingDirection.Out, 0, false, 0), {
                    ["Scale"] = 1
                })
            end)
            u5:GetPropertyChangedSignal("Enabled"):Connect(function() --[[Anonymous function at line 232]]
                --[[
                Upvalues:
                    [1] = u5
                    [2] = u63
                    [3] = u46
                    [4] = u45
                    [5] = u13
                --]]
                if not u5.Enabled then
                    if u63 then
                        u63:Cancel()
                    end
                    u46.Main_Frame.CanvasGroup.ShopItem_Image.Rotation = 0
                    u46.Main_Frame.CanvasGroup.ShopItem_Image.Image = u45.Asset
                    u13(u46.Main_Frame.CanvasGroup.ShopItem_Image.UIScale, TweenInfo.new(0.25, Enum.EasingStyle.Linear, Enum.EasingDirection.Out, 0, false, 0), {
                        ["Scale"] = 1
                    })
                end
            end)
            local u66 = u23:Clone()
            u66.LayoutOrder = u45.LayoutOrder * 10 + 1
            u66.Name = ("%*_Padding"):format(u44)
            u66.Parent = u20
            local u67 = u46.Frame
            u67.Sheckles_Buy.In_Stock.Cost_Text.Text = u11.Comma(u45.Price) .. "\194\162"
            u67.Sheckles_Buy.Activated:Connect(function() --[[Anonymous function at line 260]]
                --[[
                Upvalues:
                    [1] = u44
                    [2] = u1
                --]]
                if not workspace:GetAttribute("InTutorial") or u44 == "Carrot" then
                    u1.GameEvents.BuySeedStock:FireServer(u44)
                end
            end)
            u67.Robux_Buy.Activated:Connect(function() --[[Anonymous function at line 268]]
                --[[
                Upvalues:
                    [1] = u14
                    [2] = u45
                --]]
                u14:PromptPurchase(u45.PurchaseID, Enum.InfoType.Product)
            end)
            u67.Gift.Visible = u45.GiftPurchaseID ~= nil
            if u45.GiftPurchaseID then
                u67.Gift.Activated:Connect(function() --[[Anonymous function at line 274]]
                    --[[
                    Upvalues:
                        [1] = u38
                        [2] = u15
                        [3] = u45
                    --]]
                    u38.PlaybackSpeed = 1 + math.random(-15, 15) / 100
                    u38.TimePosition = 0
                    u38.Playing = true
                    u15:PromptGiftFromGiftId(u45.GiftPurchaseID)
                end)
            end
            local function v68() --[[Anonymous function at line 282]]
                --[[
                Upvalues:
                    [1] = u18
                    [2] = u44
                    [3] = u67
                    [4] = u13
                    [5] = u66
                --]]
                if u18 == u44 then
                    u67.Visible = true
                    u13(u67, TweenInfo.new(0.25, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
                        ["Position"] = UDim2.fromScale(0.5, 1.3)
                    })
                    u13(u66, TweenInfo.new(0.25, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
                        ["Size"] = UDim2.fromScale(0.13, 0.25)
                    })
                else
                    u67.Visible = false
                    u13(u67, TweenInfo.new(0.25, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
                        ["Position"] = UDim2.fromScale(0.5, 0.5)
                    })
                    u13(u66, TweenInfo.new(0.25, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
                        ["Size"] = UDim2.fromScale(0.13, 0.02)
                    })
                end
            end
            u19:Connect(v68)
            task.spawn(v68)
            task.spawn(function() --[[Anonymous function at line 307]]
                --[[
                Upvalues:
                    [1] = u47
                    [2] = u43
                    [3] = u46
                    [4] = u13
                    [5] = u20
                    [6] = u18
                    [7] = u44
                    [8] = u19
                    [9] = u14
                    [10] = u67
                    [11] = u45
                --]]
                task.wait(2)
                local u69 = false
                u47.Activated:Connect(function() --[[Anonymous function at line 326]]
                    --[[
                    Upvalues:
                        [1] = u43
                        [2] = u46
                        [3] = u13
                        [4] = u20
                        [5] = u18
                        [6] = u44
                        [7] = u19
                        [8] = u69
                        [9] = u14
                        [10] = u67
                        [11] = u45
                    --]]
                    local v70 = table.find(u43, u46)
                    local v71 = 1.2 * v70
                    u13(u20, TweenInfo.new(0.35), {
                        ["CanvasPosition"] = Vector2.new(0, u46.AbsoluteSize.Y * (v70 - 1) + v71)
                    })
                    local v72
                    if u18 == u44 then
                        v72 = nil
                    else
                        v72 = u44
                    end
                    u18 = v72
                    u19:Fire()
                    if not u69 then
                        u14:SetPriceLabel(u67.Robux_Buy.Price, u45.PurchaseID, ":robux::value:")
                        u69 = true
                    end
                end)
            end)
            local function u78() --[[Anonymous function at line 362]]
                --[[
                Upvalues:
                    [1] = u7
                    [2] = u44
                    [3] = u47
                    [4] = u67
                    [5] = u11
                    [6] = u45
                --]]
                local v73 = u7:GetData()
                if v73 then
                    v73 = v73.SeedStock.Stocks[u44]
                end
                local v74 = v73 and v73.Stock or 0
                local v75 = ("X%* Stock"):format(v74)
                u47.Stock_Text.Text = v75
                u67.Sheckles_Buy.In_Stock.Visible = v74 > 0
                u67.Sheckles_Buy.No_Stock.Visible = v74 <= 0
                u67.Sheckles_Buy.HoverImage = v74 > 0 and "rbxassetid://71551639169723" or "rbxassetid://138411009141674"
                u67.Sheckles_Buy.Image = v74 > 0 and "rbxassetid://96160773850314" or "rbxassetid://104713419928195"
                u47.Cost_Text.Text = v74 <= 0 and "NO STOCK" or u11.Comma(u45.Price) .. "\194\162"
                local v76 = u47.Cost_Text
                local v77
                if v74 <= 0 then
                    v77 = Color3.fromRGB(255, 0, 0)
                else
                    v77 = Color3.fromRGB(0, 255, 0)
                end
                v76.TextColor3 = v77
            end
            task.spawn(u78)
            task.spawn(function() --[[Anonymous function at line 382]]
                --[[
                Upvalues:
                    [1] = u7
                    [2] = u78
                --]]
                local v79 = u7:GetPathSignal("SeedStock/@")
                if v79 then
                    v79:Connect(u78)
                end
                local v80 = u7:GetPathSignal("SeedStock")
                if v80 then
                    v80:Connect(u78)
                end
            end)
            if u8:IsHiddenFromUpdate(u45.SeedName) then
                u47.Visible = false
                task.delay(u8:GetRemainingTimeUntilUpdate(), function() --[[Anonymous function at line 397]]
                    --[[
                    Upvalues:
                        [1] = u47
                    --]]
                    u47.Visible = true
                end)
            end
        end
    end
    table.sort(u43, function(p81, p82) --[[Anonymous function at line 403]]
        local v83 = p81.LayoutOrder
        local v84 = p82.LayoutOrder
        if v83 == v84 then
            return p81.Name < p82.Name
        else
            return v83 < v84
        end
    end)
end
function v26.Start(_) --[[Anonymous function at line 413]]
    --[[
    Upvalues:
        [1] = u6
        [2] = u5
        [3] = u14
        [4] = u85
    --]]
    u6:UsePopupAnims(u5)
    u5.Frame.Frame.ExitButton.Activated:Connect(function() --[[Anonymous function at line 416]]
        --[[
        Upvalues:
            [1] = u6
            [2] = u5
        --]]
        u6:Close(u5)
    end)
    u5.Frame.Frame.Restock.Activated:Connect(function() --[[Anonymous function at line 420]]
        --[[
        Upvalues:
            [1] = u14
        --]]
        u14:PromptPurchase(3248683091, Enum.InfoType.Product)
    end)
    u85()
end
task.spawn(v26.Start, v26)
return v26