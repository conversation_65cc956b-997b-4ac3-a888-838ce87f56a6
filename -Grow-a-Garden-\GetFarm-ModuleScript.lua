-- Full Path: -Grow-a-Garden-\\GetFarm-ModuleScript.lua
game:GetService("Players")
return function(p1) --[[Function name: GetFarm, line 3]]
    if p1 then
        for _, v2 in workspace.Farm:GetChildren() do
            local v3 = v2:FindFirstChild("Important")
            if v3 then
                local v4 = v3:FindFirstChild("Data")
                if v4 and v4.Owner.Value == p1.Name then
                    return v2
                end
            end
        end
    end
end