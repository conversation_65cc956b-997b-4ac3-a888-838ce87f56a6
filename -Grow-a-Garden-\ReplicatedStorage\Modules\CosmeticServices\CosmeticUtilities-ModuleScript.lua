-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\CosmeticServices\CosmeticUtilities-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
local u2 = {}
local u3 = require(v1.Modules.GetFarm)
local u4 = require(v1.Code.Manhattan2D)
local u5 = require(v1.Modules.GetCornersOfPart)
function u2.IsWithinFarm(_, p6, p7) --[[Anonymous function at line 8]]
    --[[
    Upvalues:
        [1] = u3
        [2] = u4
    --]]
    local v8 = u3(p6)
    if v8 then
        return u4(p7.Position, v8.PetArea)
    end
end
function u2.IsModelInFarm(_, p9, p10, p11) --[[Anonymous function at line 14]]
    --[[
    Upvalues:
        [1] = u5
        [2] = u2
    --]]
    local v12 = p11.PrimaryPart
    if v12 then
        local v13 = v12:Clone()
        v13.CFrame = p10 * CFrame.new(0, v13.Size.Y / 2, 0)
        local v14 = u5(v13)
        v13:Destroy()
        for _, v15 in v14 do
            if not u2:IsWithinFarm(p9, v15) then
                return false
            end
        end
        return true
    end
end
return u2