-- Full Path: -Grow-a-Garden-\\Sheckles-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
require(v1.Data.QuestData.Types)
local u2 = require(v1.Comma_Module)
return {
    ["Type"] = "Sheckles",
    ["Display"] = function(_, p3) --[[Function name: Display, line 13]]
        --[[
        Upvalues:
            [1] = u2
        --]]
        return ("+%*\194\162"):format((u2.Comma(p3.Data.Amount)))
    end,
    ["Give"] = function(_, p4, p5) --[[Function name: Give, line 17]]
        local v6 = game:GetService("ServerScriptService")
        return require(v6.Modules.CurrencyService):Add(p4, p5.Data.Amount)
    end,
    ["Use"] = function(p7, p8) --[[Function name: Use, line 25]]
        return {
            ["Type"] = p7.Type,
            ["Data"] = p8
        }
    end
}