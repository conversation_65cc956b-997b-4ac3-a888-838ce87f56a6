-- Full Path: -Grow-a-Garden-\\Menu-ModuleScript.lua
return function(u1) --[[Anonymous function at line 1]]
    local u2 = Instance.new("ScrollingFrame")
    u2.Name = "Menu"
    u2.BackgroundTransparency = 1
    u2.Visible = true
    u2.ZIndex = 1
    u2.Size = UDim2.fromScale(1, 1)
    u2.ClipsDescendants = true
    u2.TopImage = ""
    u2.BottomImage = ""
    u2.HorizontalScrollBarInset = Enum.ScrollBarInset.Always
    u2.CanvasSize = UDim2.new(0, 0, 1, -1)
    u2.ScrollingEnabled = true
    u2.ScrollingDirection = Enum.ScrollingDirection.X
    u2.ZIndex = 20
    u2.ScrollBarThickness = 3
    u2.ScrollBarImageColor3 = Color3.fromRGB(255, 255, 255)
    u2.ScrollBarImageTransparency = 0.8
    u2.BorderSizePixel = 0
    u2.Selectable = false
    local u3 = require(u1.iconModule)
    local u4 = u3.container.TopbarStandard:FindFirstChild("UIListLayout", true):Clone()
    u4.Name = "MenuUIListLayout"
    u4.VerticalAlignment = Enum.VerticalAlignment.Center
    u4.Parent = u2
    local v5 = Instance.new("Frame")
    v5.Name = "MenuGap"
    v5.BackgroundTransparency = 1
    v5.Visible = false
    v5.AnchorPoint = Vector2.new(0, 0.5)
    v5.ZIndex = 5
    v5.Parent = u2
    local u6 = false
    local u7 = require(script.Parent.Parent.Features.Themes)
    local function v32() --[[Anonymous function at line 39]]
        --[[
        Upvalues:
            [1] = u1
            [2] = u6
            [3] = u2
            [4] = u7
            [5] = u4
        --]]
        local u8 = u1.menuJanitor
        local v9 = #u1.menuIcons
        if u6 then
            if v9 <= 0 then
                u8:clean()
                u6 = false
            end
        else
            u6 = true
            u8:add(u1.toggled:Connect(function() --[[Anonymous function at line 53]]
                --[[
                Upvalues:
                    [1] = u1
                --]]
                if #u1.menuIcons > 0 then
                    u1.updateSize:Fire()
                end
            end))
            local _, u10 = u1:modifyTheme({
                { "Menu", "Active", true }
            })
            task.defer(function() --[[Anonymous function at line 63]]
                --[[
                Upvalues:
                    [1] = u8
                    [2] = u1
                    [3] = u10
                --]]
                u8:add(function() --[[Anonymous function at line 64]]
                    --[[
                    Upvalues:
                        [1] = u1
                        [2] = u10
                    --]]
                    u1:removeModification(u10)
                end)
            end)
            local u11 = u2.AbsoluteCanvasSize.X
            local function v14() --[[Anonymous function at line 73]]
                --[[
                Upvalues:
                    [1] = u1
                    [2] = u2
                    [3] = u11
                --]]
                if u1.alignment == "Right" then
                    local v12 = u2.AbsoluteCanvasSize.X
                    local v13 = u11 - v12
                    u11 = v12
                    u2.CanvasPosition = Vector2.new(u2.CanvasPosition.X - v13, 0)
                end
            end
            u8:add(u1.selected:Connect(v14))
            u8:add(u2:GetPropertyChangedSignal("AbsoluteCanvasSize"):Connect(v14))
            local v15 = u1:getStateGroup()
            if u7.getThemeValue(v15, "IconImage", "Image", "Deselected") == u7.getThemeValue(v15, "IconImage", "Image", "Selected") then
                local v16 = Font.new("rbxasset://fonts/families/FredokaOne.json", Enum.FontWeight.Light, Enum.FontStyle.Normal)
                u1:removeModificationWith("IconLabel", "Text", "Viewing")
                u1:removeModificationWith("IconLabel", "Image", "Viewing")
                u1:modifyTheme({
                    {
                        "IconLabel",
                        "FontFace",
                        v16,
                        "Selected"
                    },
                    {
                        "IconLabel",
                        "Text",
                        "X",
                        "Selected"
                    },
                    {
                        "IconLabel",
                        "TextSize",
                        20,
                        "Selected"
                    },
                    {
                        "IconLabel",
                        "TextStrokeTransparency",
                        0.8,
                        "Selected"
                    },
                    {
                        "IconImage",
                        "Image",
                        "",
                        "Selected"
                    }
                })
            end
            local u17 = u1:getInstance("IconSpot")
            local u18 = u1:getInstance("MenuGap")
            local function v19() --[[Anonymous function at line 105]]
                --[[
                Upvalues:
                    [1] = u1
                    [2] = u17
                    [3] = u18
                --]]
                if u1.alignment == "Right" then
                    u17.LayoutOrder = 99999
                    u18.LayoutOrder = 99998
                else
                    u17.LayoutOrder = -99999
                    u18.LayoutOrder = -99998
                end
            end
            u8:add(u1.alignmentChanged:Connect(v19))
            if u1.alignment == "Right" then
                u17.LayoutOrder = 99999
                u18.LayoutOrder = 99998
            else
                u17.LayoutOrder = -99999
                u18.LayoutOrder = -99998
            end
            u2:GetAttributeChangedSignal("MenuCanvasWidth"):Connect(function() --[[Anonymous function at line 120]]
                --[[
                Upvalues:
                    [1] = u2
                --]]
                local v20 = u2:GetAttribute("MenuCanvasWidth")
                local v21 = u2.CanvasSize.Y
                u2.CanvasSize = UDim2.new(0, v20, v21.Scale, v21.Offset)
            end)
            u8:add(u1.updateMenu:Connect(function() --[[Anonymous function at line 125]]
                --[[
                Upvalues:
                    [1] = u2
                    [2] = u4
                --]]
                local v22 = u2:GetAttribute("MaxIcons")
                if not v22 then
                    return
                end
                local v23 = {}
                for _, v24 in pairs(u2:GetChildren()) do
                    if v24:GetAttribute("WidgetUID") and v24.Visible then
                        local v25 = { v24, v24.AbsolutePosition.X }
                        table.insert(v23, v25)
                    end
                end
                table.sort(v23, function(p26, p27) --[[Anonymous function at line 137]]
                    return p26[2] < p27[2]
                end)
                local v28 = 0
                for v29 = 1, v22 do
                    local v30 = v23[v29]
                    if not v30 then
                        break
                    end
                    v28 = v28 + (v30[1].AbsoluteSize.X + u4.Padding.Offset)
                end
                u2:SetAttribute("MenuWidth", v28)
            end))
            local function v31() --[[Anonymous function at line 152]]
                --[[
                Upvalues:
                    [1] = u1
                --]]
                task.delay(0.1, function() --[[Anonymous function at line 153]]
                    --[[
                    Upvalues:
                        [1] = u1
                    --]]
                    u1.startMenuUpdate:Fire()
                end)
            end
            local _ = u1:getInstance("IconButton").AbsoluteSize.X
            u8:add(u2.ChildAdded:Connect(v31))
            u8:add(u2.ChildRemoved:Connect(v31))
            u8:add(u2:GetAttributeChangedSignal("MaxIcons"):Connect(v31))
            u8:add(u2:GetAttributeChangedSignal("MaxWidth"):Connect(v31))
            task.delay(0.1, function() --[[Anonymous function at line 153]]
                --[[
                Upvalues:
                    [1] = u1
                --]]
                u1.startMenuUpdate:Fire()
            end)
        end
    end
    u1.menuChildAdded:Connect(v32)
    u1.menuSet:Connect(function(p33) --[[Anonymous function at line 167]]
        --[[
        Upvalues:
            [1] = u1
            [2] = u3
        --]]
        for _, v34 in pairs(u1.menuIcons) do
            u3.getIconByUID(v34):destroy()
        end
        local _ = #p33
        if type(p33) == "table" then
            for _, v35 in pairs(p33) do
                v35:joinMenu(u1)
            end
        end
    end)
    return u2
end