-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\ByteNet\dataTypes\unknown-ModuleScript.lua
local v1 = require(script.Parent.Parent.process.bufferWriter)
local u2 = require(script.Parent.Parent.process.readRefs)
require(script.Parent.Parent.types)
local u3 = v1.reference
local u4 = v1.alloc
return function() --[[Anonymous function at line 8]]
    --[[
    Upvalues:
        [1] = u4
        [2] = u3
        [3] = u2
    --]]
    return {
        ["write"] = function(p5) --[[Function name: write, line 10]]
            --[[
            Upvalues:
                [1] = u4
                [2] = u3
            --]]
            u4(1)
            u3(p5)
        end,
        ["read"] = function(p6, p7) --[[Function name: read, line 15]]
            --[[
            Upvalues:
                [1] = u2
            --]]
            local v8 = u2.get()
            if v8 then
                return v8[buffer.readu8(p6, p7)], 1
            else
                return nil, 1
            end
        end
    }
end