-- Full Path: -Grow-a-Garden-\\ItemCollect-LocalScript.lua
local u1 = require(game.ReplicatedStorage.Data.GearData)
task.spawn(function() --[[Anonymous function at line 50]]
    while true do
        game:GetService("RunService").RenderStepped:Wait()
        local v2 = {}
        local v3 = {}
        for _, v4 in game.CollectionService:GetTagged("CollectionVisual") do
            if v4.Value.Value == nil or not v4.Value.Value:IsDescendantOf(workspace) then
                v4:Destroy()
            else
                local v5 = tick() * 3.141592653589793
                local v6 = (math.sin(v5) + 1) / 2
                local v7 = CFrame.new(v4.Value.Value.CFrame.Position) * CFrame.new(0, v6 * 1 + 0.5, 0)
                table.insert(v2, v7)
                table.insert(v3, v4)
            end
        end
        workspace:BulkMoveTo(v3, v2, Enum.BulkMoveMode.FireCFrameChanged)
    end
end)
local function v13(p8) --[[Anonymous function at line 2]]
    --[[
    Upvalues:
        [1] = u1
    --]]
    local v9 = p8:GetAttribute("RewardType")
    if p8:GetAttribute("Owner") == nil or p8:GetAttribute("Owner") == game.Players.LocalPlayer.Name then
        local u10 = game.ReplicatedStorage.RewardDropVisual:Clone()
        u10.Value.Value = p8
        for _, v11 in u10.BillboardGui:GetChildren() do
            v11.Visible = false
        end
        if v9 == "Coins" then
            if p8:GetAttribute("SheckleRain") then
                u10.Trail.Color = ColorSequence.new(Color3.fromRGB(255, 204, 0))
            end
            u10.BillboardGui.Coins.Visible = true
        elseif v9 == "Seeds" then
            u10.BillboardGui.Seeds.SeedColour.ImageColor3 = game.ReplicatedStorage.Seed_Models:FindFirstChild(p8:GetAttribute("KeySeed")).Color
            u10.BillboardGui.Seeds.Visible = true
        elseif v9 == "SeedPack" then
            task.spawn(function() --[[Anonymous function at line 26]]
                --[[
                Upvalues:
                    [1] = u10
                --]]
                while u10.Value.Value and u10.Value.Value:IsDescendantOf(workspace) do
                    local v12 = tick() % 2 / 2
                    u10.BillboardGui.Seeds.SeedColour.ImageColor3 = Color3.fromHSV(v12, 1, 1)
                    task.wait()
                end
            end)
            u10.BillboardGui.Seeds.Visible = true
        elseif v9 == "Items" then
            if u1[p8:GetAttribute("GearName")] then
                u10.BillboardGui.Gear.Image = u1[p8:GetAttribute("GearName")].Asset
            end
            u10.BillboardGui.Gear.Visible = true
        end
        u10.CFrame = CFrame.new(p8.CFrame.p) * CFrame.new(0, 0.5, 0)
        u10.Parent = workspace
        u10:AddTag("CollectionVisual")
    end
end
for _, v14 in game.CollectionService:GetTagged("Collectable") do
    v13(v14)
end
game.CollectionService:GetInstanceAddedSignal("Collectable"):Connect(v13)
local function u20(p15) --[[Anonymous function at line 80]]
    --[[
    Upvalues:
        [1] = u1
    --]]
    local v16 = p15.RewardType
    local u17 = game.ReplicatedStorage.RewardDropVisual:Clone()
    for _, v18 in u17.BillboardGui:GetChildren() do
        v18.Visible = false
    end
    if v16 == "Coins" then
        u17.BillboardGui.Coins.Visible = true
        return u17
    end
    if v16 == "Seeds" then
        u17.BillboardGui.Seeds.SeedColour.ImageColor3 = game.ReplicatedStorage.Seed_Models:FindFirstChild(p15.KeySeed).Color
        u17.BillboardGui.Seeds.Visible = true
        return u17
    end
    if v16 ~= "SeedPack" then
        if v16 == "Items" then
            if u1[p15.GearName] then
                u17.BillboardGui.Gear.Image = u1[p15.GearName].Asset
            end
            u17.BillboardGui.Gear.Visible = true
        end
        return u17
    end
    task.spawn(function() --[[Anonymous function at line 97]]
        --[[
        Upvalues:
            [1] = u17
        --]]
        while u17 and u17:IsDescendantOf(workspace) do
            local v19 = tick() % 2 / 2
            u17.BillboardGui.Seeds.SeedColour.ImageColor3 = Color3.fromHSV(v19, 1, 1)
            task.wait()
        end
    end)
    u17.BillboardGui.Seeds.Visible = true
    return u17
end
local u21 = require(game.ReplicatedStorage.Code.Bezier)
game.ReplicatedStorage.GameEvents.FireDrop.OnClientEvent:Connect(function(p22, p23) --[[Anonymous function at line 119]]
    --[[
    Upvalues:
        [1] = u20
        [2] = u21
    --]]
    local v24 = u20(p22)
    v24.CFrame = p23
    v24.Parent = workspace
    local v25 = 0
    while v25 < 0.5 do
        v25 = v25 + game:GetService("RunService").Heartbeat:Wait()
        local v26 = game.Players.LocalPlayer.Character.PrimaryPart.Position
        local v27 = (p23.p + v26) / 2
        local v28 = p23.p.Y + 7
        local v29 = v27 + Vector3.new(0, v28, 0)
        local v30 = u21.new(p23.Position, v29, v26):Get((game.TweenService:GetValue(v25 / 0.5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out)))
        v24.CFrame = CFrame.new(v30)
    end
    v24:Destroy()
    local v31 = game.Workspace.Terrain.PopEffect:Clone()
    v31.Parent = workspace.Terrain
    v31.CFrame = CFrame.new(game.Players.LocalPlayer.Character.PrimaryPart.Position)
    for _, v32 in v31:GetChildren() do
        v32:Emit(1)
    end
    game.Debris:AddItem(v31, 2)
    game.SoundService:PlayLocalSound(game.SoundService.Rainbow)
end)