-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Data_Module-ModuleScript.lua
local u1 = {}
local v2 = game:GetService("DataStoreService")
game:GetService("ReplicatedStorage")
local v3 = game:GetService("ServerStorage")
local v4 = game:GetService("ServerScriptService")
local v5 = game:GetService("ReplicatedStorage")
local u6 = game:GetService("HttpService")
local v7 = v5:WaitForChild("Modules")
local u8 = require(v7:WaitForChild("GetFarm"))
local u9 = require(game.ReplicatedStorage.Modules.MutationHandler)
require(v4.Modules.InventoryService)
v3:WaitForChild("Tools")
require(v5.Modules.DumpTable)
local u10 = require(v5.Modules.Remotes)
v2:GetDataStore("Inventory_22")
require(v5.Give_Seed)
require(v5.Give_Seed)
local u11 = require(v4.Modules.DataService)
local u12 = require(v4.Modules.GardenBadgeAwarder)
function u1.Setup(u13) --[[Anonymous function at line 48]]
    --[[
    Upvalues:
        [1] = u11
        [2] = u10
    --]]
    task.delay(0, function() --[[Anonymous function at line 49]]
        --[[
        Upvalues:
            [1] = u13
            [2] = u11
            [3] = u10
        --]]
        if u13 then
            if u13.UserId then
                local u14 = u11:GetPlayerDataAsync(u13)
                if not u13.Character then
                    u13.CharacterAdded:Wait()
                end
                if u14 then
                    if u14.Sheckles == 20 and u14.FirstTimeUser then
                        u10.Tutorial.Start.sendTo(nil, u13)
                        u13:SetAttribute("FirstTimePlayer", true)
                        task.delay(20, function() --[[Anonymous function at line 64]]
                            --[[
                            Upvalues:
                                [1] = u14
                            --]]
                            u14.FirstTimeUser = false
                        end)
                    end
                    u13:SetAttribute("Grow_Amount", DateTime.now().UnixTimestamp - u14.LastSaveTime)
                    u13:SetAttribute("Setup_Finished", true)
                    u13:SetAttribute("Finished_Loading", true)
                end
            else
                return
            end
        else
            return
        end
    end)
end
function u1.AddPlantedObject(p15, p16, p17, p18, p19, p20) --[[Anonymous function at line 78]]
    --[[
    Upvalues:
        [1] = u11
        [2] = u12
        [3] = u1
    --]]
    local v21 = u11:GetPlayerDataAsync(p15, 10)
    if v21 then
        for _, v22 in v21.PlantedObjects do
            if v22[1] == p16 and v22[2] == p17 then
                return
            end
        end
        local v23 = p20:ToObjectSpace(p18)
        task.spawn(u12.Plant, u12, p15, p16)
        local v24 = v23.X
        local v25 = v23.Y
        local v26 = v23.Z
        local v27 = Vector3.new(v24, v25, v26)
        local _ = p20 * CFrame.new(v27.X, v27.Y, v27.Z)
        local v28 = v21.PlantedObjects
        local v29 = {
            p16,
            p17,
            { v27.X, v27.Y, v27.Z },
            0,
            p19.Variant.Value,
            false
        }
        table.insert(v28, v29)
        u1.Save(p15)
    end
end
function u1.Update_Plant_CFrame(p30, p31, p32) --[[Anonymous function at line 108]]
    --[[
    Upvalues:
        [1] = u11
        [2] = u8
    --]]
    local v33 = u11:GetPlayerDataAsync(p30)
    if v33 then
        local v34 = u8(p30)
        if v34 then
            for _, v35 in v33.PlantedObjects do
                if v35[1] == p31.Name and v35[2] == p31.Item_Seed.Value then
                    local v36 = v34.Owner_Tag.CFrame:ToObjectSpace(p32)
                    local v37 = v35[3][2]
                    local _, v38, _ = p32:ToOrientation()
                    v35[3] = {
                        v36.X,
                        v37,
                        v36.Z,
                        v38
                    }
                    return
                end
            end
        end
    else
        return
    end
end
function u1.RemovePlantedObject(p39, p40, p41, p42) --[[Anonymous function at line 132]]
    --[[
    Upvalues:
        [1] = u11
        [2] = u1
    --]]
    local v43 = u11:GetPlayerDataAsync(p39, 10)
    if not v43 then
        return
    end
    if p42 and v43.SavedFruit[p42] then
        v43.SavedFruit[p42] = nil
    end
    for v44, v45 in v43.PlantedObjects do
        if v45[1] == p40 and v45[2] == p41 then
            table.remove(v43.PlantedObjects, v44)
            break
        end
    end
    u1.Save(p39)
end
function u1.SaveFruit(p46, p47, p48, p49) --[[Anonymous function at line 152]]
    --[[
    Upvalues:
        [1] = u11
        [2] = u9
        [3] = u6
    --]]
    if p46 then
        if p47 then
            if p47:HasTag("PlantGenerated") then
                local v50 = p49 or u11:GetPlayerDataAsync(p46, 10)
                if v50 then
                    local v51 = p47.Name
                    if not v51 or v51 == "" then
                        return warn((("Didn\'t save fruit %*: No Name"):format(p47)))
                    end
                    local v52 = p47:FindFirstChild("Item_Seed")
                    if not v52 then
                        return warn((("Didn\'t save fruit %*: No Item_Seed attribute"):format(p47)))
                    end
                    local v53 = p47:FindFirstChild("Grow")
                    if not v53 then
                        return warn((("Didn\'t save fruit %*: No Grow folder!"):format(p47)))
                    end
                    local v54 = v53:FindFirstChild("Age")
                    if not v54 then
                        return warn((("Didn\'t save fruit %*: No Age attribute!"):format(p47)))
                    end
                    local v55 = p47:GetAttribute("WeightMulti") or 1
                    local v56 = p47:FindFirstChild("Variant")
                    if not v56 then
                        return warn((("Didn\'t save fruit %*: No Variant attribute!"):format(p47)))
                    end
                    local v57 = u9:GetMutationsAsString(p47) or ""
                    local v58 = p47:GetAttribute("UUID")
                    if not v58 or v58 == "" then
                        v58 = u6:GenerateGUID()
                        p47:SetAttribute("UUID", v58)
                    end
                    local v59 = p47:GetAttribute("FruitSpawnIndex")
                    local v60 = p47:GetAttribute("Favorited") or false
                    local v61 = p48 or p47:FindFirstAncestorWhichIsA("Model")
                    if not v61 then
                        return warn((("Didn\'t save fruit %*: No parent plant!"):format(p47)))
                    end
                    local v62 = v61.Name
                    if not v62 or v62 == "" then
                        return warn((("Didn\'t save fruit %*: No parentType!"):format(p47)))
                    end
                    local v63 = v61:FindFirstChild("Item_Seed")
                    if not v63 then
                        return warn((("Didn\'t save fruit %*: No Item_Seed attribute!"):format(p47)))
                    end
                    local v64 = {
                        ["ParentType"] = v62,
                        ["ParentSeed"] = v63.Value,
                        ["FruitType"] = v51,
                        ["FruitSeed"] = v52.Value,
                        ["FruitAge"] = v54.Value,
                        ["FruitWeightMulti"] = v55,
                        ["FruitVariant"] = v56.Value,
                        ["FruitMutationString"] = v57,
                        ["FruitFavorited"] = v60,
                        ["FruitSpawnIndex"] = v59
                    }
                    v50.SavedFruit[v58] = v64
                    return v58
                end
            end
        else
            return warn("Data_Module.SaveFruit | Fruit is nil!")
        end
    else
        return warn("Data_Module.SaveFruit | Player is nil!")
    end
end
local u65 = {}
local function u75(p66, p67) --[[Anonymous function at line 259]]
    --[[
    Upvalues:
        [1] = u8
        [2] = u65
    --]]
    local v68 = u8(p66)
    if v68 then
        local v69 = p67[1]
        local v70 = p67[2]
        local v71 = u65[p66]
        if not v71 then
            v71 = {}
            u65[p66] = v71
        end
        local v72 = v71[v69]
        if not v72 then
            v72 = {}
            v71[v69] = v72
        end
        local v73 = v72[v70]
        if v73 then
            return v73
        end
        for _, v74 in v68.Important.Plants_Physical:GetChildren() do
            if v74.Name == v69 and (v70 and v74:FindFirstChild("Item_Seed").Value == v70) then
                v72[v70] = v74
                return v74
            end
        end
        return v73
    end
end
function u1.Save(p76) --[[Anonymous function at line 286]]
    --[[
    Upvalues:
        [1] = u11
        [2] = u8
        [3] = u75
        [4] = u9
        [5] = u1
    --]]
    if p76 then
        if p76:GetAttribute("Setup_Finished") then
            local v77 = u11:GetPlayerDataAsync(p76)
            if v77 then
                local v78 = os.clock()
                local v79 = os.clock()
                local v80 = v78 - math.floor(v79)
                v77.LastSaveTime = os.time() + v80
                if u8(p76) then
                    local v81 = {}
                    for _, v82 in v77.PlantedObjects do
                        v81[v82[1] .. v82[2]] = true
                        local v83 = u75(p76, v82)
                        if v83 then
                            local v84 = v83:FindFirstChild("Grow")
                            if v84 then
                                local v85 = v83:FindFirstChild("Variant")
                                if v85 then
                                    local v86 = v84:FindFirstChild("Age")
                                    if v86 then
                                        v82[4] = v86.Value
                                        v82[5] = v85.Value
                                        v82[6] = u9:GetMutationsAsString(v83)
                                        v82[7] = v83:GetAttribute("WeightMulti")
                                        v82[8] = v83:GetAttribute("Favorited") or false
                                        local v87 = v83:FindFirstChild("Fruits")
                                        if v87 then
                                            for _, v88 in v87:GetChildren() do
                                                u1.SaveFruit(p76, v88, v83, v77)
                                            end
                                        end
                                    end
                                end
                            end
                        end
                    end
                    for v89, v90 in v77.SavedFruit do
                        if not v81[v90.ParentType .. v90.ParentSeed] then
                            v77.SavedFruit[v89] = nil
                        end
                    end
                end
                if #v77.PlantedObjects > 0 then
                    v77.LastSaveTime = DateTime.now().UnixTimestamp
                end
            else
                return
            end
        else
            return
        end
    else
        return
    end
end
task.spawn(function() --[[Anonymous function at line 346]]
    --[[
    Upvalues:
        [1] = u1
    --]]
    while true do
        task.wait(1)
        for _, u91 in game.Players:GetPlayers() do
            xpcall(function() --[[Anonymous function at line 350]]
                --[[
                Upvalues:
                    [1] = u1
                    [2] = u91
                --]]
                u1.Save(u91)
            end, warn)
        end
    end
end)
game.Players.PlayerRemoving:Connect(function(p92) --[[Anonymous function at line 357]]
    --[[
    Upvalues:
        [1] = u65
    --]]
    u65[p92] = nil
end)
return u1