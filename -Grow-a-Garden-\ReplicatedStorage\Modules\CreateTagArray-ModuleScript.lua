-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\CreateTagArray-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
local u2 = require(v1.Modules.CreateTagHandler)
return function(p3) --[[Function name: CreateTagArray, line 5]]
    --[[
    Upvalues:
        [1] = u2
    --]]
    local u4 = {}
    u2({
        ["Tag"] = p3,
        ["OnInstanceAdded"] = function(p5) --[[Function name: OnInstanceAdded, line 9]]
            --[[
            Upvalues:
                [1] = u4
            --]]
            if not table.find(u4, p5) then
                local v6 = u4
                table.insert(v6, p5)
            end
        end,
        ["OnInstanceRemoved"] = function(p7) --[[Function name: OnInstanceRemoved, line 13]]
            --[[
            Upvalues:
                [1] = u4
            --]]
            local v8 = table.find(u4, p7)
            if v8 then
                table.remove(u4, v8)
            end
        end
    })
    return u4
end