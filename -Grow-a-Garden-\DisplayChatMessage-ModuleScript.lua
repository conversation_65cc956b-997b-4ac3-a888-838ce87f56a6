-- Full Path: -Grow-a-Garden-\\DisplayChatMessage-ModuleScript.lua
local u1 = game:GetService("TextChatService")
game:GetService("ReplicatedStorage"):WaitF<PERSON><PERSON>hild("GameEvents"):Wait<PERSON><PERSON><PERSON>hild("DisplayChatMessage").OnClientEvent:Connect(function(p2) --[[Anonymous function at line 12]]
    --[[
    Upvalues:
        [1] = u1
    --]]
    u1.TextChannels.RBXGeneral:DisplaySystemMessage(p2)
end)
return function(p3) --[[Function name: DisplayChatMessage, line 8]]
    --[[
    Upvalues:
        [1] = u1
    --]]
    u1.TextChannels.RBXGeneral:DisplaySystemMessage(p3)
end