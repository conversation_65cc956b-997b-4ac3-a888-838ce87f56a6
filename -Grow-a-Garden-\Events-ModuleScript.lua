-- Full Path: -Grow-a-Garden-\\Events-ModuleScript.lua
local u1 = {
    ["ProcessEventsInterval"] = 8,
    ["GameKey"] = "",
    ["SecretKey"] = "",
    ["Build"] = "",
    ["_availableResourceCurrencies"] = {},
    ["_availableResourceItemTypes"] = {}
}
local u2 = require(script.Parent.Store)
local u3 = require(script.Parent.Logger)
local u4 = require(script.Parent.Version)
local u5 = require(script.Parent.Validation)
local u6 = require(script.Parent.Threading)
local u7 = require(script.Parent.HttpApi)
local u8 = require(script.Parent.Utilities)
local u9 = require(script.Parent.GAResourceFlowType)
local u10 = require(script.Parent.GAProgressionStatus)
local u11 = require(script.Parent.GAErrorSeverity)
local u12 = game:GetService("HttpService")
local function u16(p13, p14) --[[Anonymous function at line 33]]
    --[[
    Upvalues:
        [1] = u2
    --]]
    if p14 and p13 then
        local v15 = u2:GetPlayerDataFromCache(p13)
        if v15 and (v15.CurrentCustomDimension01 and #v15.CurrentCustomDimension01 > 0) then
            p14.custom_01 = v15.CurrentCustomDimension01
        end
        if v15 and (v15.CurrentCustomDimension02 and #v15.CurrentCustomDimension02 > 0) then
            p14.custom_02 = v15.CurrentCustomDimension02
        end
        if v15 and (v15.CurrentCustomDimension03 and #v15.CurrentCustomDimension03 > 0) then
            p14.custom_03 = v15.CurrentCustomDimension03
        end
    end
end
local u17 = u12:GenerateGUID(false):lower()
local function u27(p18) --[[Anonymous function at line 79]]
    --[[
    Upvalues:
        [1] = u2
        [2] = u17
        [3] = u5
        [4] = u4
        [5] = u8
        [6] = u1
    --]]
    local v19, v20
    if p18 then
        v19 = u2:GetPlayerDataFromCache(p18)
        v20 = p18
    else
        v19 = {
            ["OS"] = "uwp_desktop 0.0.0",
            ["Platform"] = "uwp_desktop",
            ["SessionID"] = u17,
            ["Sessions"] = 1,
            ["CustomUserId"] = "Server"
        }
        v20 = "DummyId"
    end
    local v21 = {
        ["v"] = 2,
        ["user_id"] = tostring(v20) .. v19.CustomUserId
    }
    local v22
    if p18 then
        local v23 = u2:GetPlayerDataFromCache(p18)
        v22 = os.time()
        local v24 = v22 + v23.ClientServerTimeOffset
        if u5:validateClientTs(v24) then
            v22 = v24
        end
    else
        v22 = os.time()
    end
    v21.client_ts = v22
    v21.sdk_version = "roblox " .. u4.SdkVersion
    v21.os_version = v19.OS
    v21.manufacturer = "unknown"
    v21.device = "unknown"
    v21.platform = v19.Platform
    v21.session_id = v19.SessionID
    v21.session_num = v19.Sessions
    if u8:isStringNullOrEmpty(v19.CountryCode) then
        v21.country_code = "unknown"
    else
        v21.country_code = v19.CountryCode
    end
    if u5:validateBuild(u1.Build) then
        v21.build = u1.Build
    end
    if v19.Configurations then
        local v25 = v19.Configurations
        local v26 = 0
        for _, _ in pairs(v25) do
            v26 = v26 + 1
        end
        if v26 > 0 then
            v21.configurations = v19.Configurations
        end
    end
    if not u8:isStringNullOrEmpty(v19.AbId) then
        v21.ab_id = v19.AbId
    end
    if not u8:isStringNullOrEmpty(v19.AbVariantId) then
        v21.ab_variant_id = v19.AbVariantId
    end
    return v21
end
local function u32(p28, p29) --[[Anonymous function at line 146]]
    --[[
    Upvalues:
        [1] = u27
        [2] = u12
        [3] = u3
        [4] = u2
    --]]
    local v30 = u27(p28)
    for v31 in pairs(p29) do
        v30[v31] = p29[v31]
    end
    u3:ii("Event added to queue: " .. u12:JSONEncode(v30))
    u2.EventsQueue[#u2.EventsQueue + 1] = v30
end
local function u39() --[[Anonymous function at line 165]]
    --[[
    Upvalues:
        [1] = u2
        [2] = u3
    --]]
    if #u2.EventsQueue <= 500 then
        local v33 = u2.EventsQueue
        u2.EventsQueue = {}
        return v33
    end
    u3:w(("More than %d events queued! Sending %d."):format(500, 500))
    if #u2.EventsQueue > 2000 then
        u3:w(("DROPPING EVENTS: More than %d events queued!"):format(2000))
    end
    local v34 = table.create(500)
    for v35 = 1, 500 do
        v34[v35] = u2.EventsQueue[v35]
    end
    local v36 = #u2.EventsQueue
    for v37 = 1, math.min(2000, v36) do
        u2.EventsQueue[v37] = u2.EventsQueue[v37 + 500]
    end
    for v38 = 2001, v36 do
        u2.EventsQueue[v38] = nil
    end
    return v34
end
local function u53() --[[Anonymous function at line 198]]
    --[[
    Upvalues:
        [1] = u39
        [2] = u3
        [3] = u7
        [4] = u1
        [5] = u2
    --]]
    local v40 = u39()
    if #v40 == 0 then
        u3:i("Event queue: No events to send")
        return
    end
    local v41 = u3
    local v42 = #v40
    v41:i("Event queue: Sending " .. tostring(v42) .. " events.")
    local v43 = u7:sendEventsInArray(u1.GameKey, u1.SecretKey, v40)
    local v44 = v43.statusCode
    local v45 = v43.body
    if v44 == u7.EGAHTTPApiResponse.Ok and v45 then
        local v46 = u3
        local v47 = #v40
        v46:i("Event queue: " .. tostring(v47) .. " events sent.")
        return
    end
    if v44 == u7.EGAHTTPApiResponse.NoResponse then
        u3:w("Event queue: Failed to send events to collector - Retrying next time")
        for _, v48 in pairs(v40) do
            if #u2.EventsQueue >= 2000 then
                ::l11::
                return
            end
            u2.EventsQueue[#u2.EventsQueue + 1] = v48
        end
        return
    else
        if v44 == u7.EGAHTTPApiResponse.BadRequest and v45 then
            local v49 = u3
            local v50 = #v40
            local v51 = tostring(v50)
            local v52 = #v45
            v49:w("Event queue: " .. v51 .. " events sent. " .. tostring(v52) .. " events failed GA server validation.")
            return
        end
        u3:w("Event queue: Failed to send events.")
        goto l11
    end
end
function u1.processEventQueue(_) --[[Anonymous function at line 235]]
    --[[
    Upvalues:
        [1] = u53
        [2] = u6
        [3] = u1
    --]]
    u53()
    u6:scheduleTimer(u1.ProcessEventsInterval, function() --[[Anonymous function at line 237]]
        --[[
        Upvalues:
            [1] = u1
        --]]
        u1:processEventQueue()
    end)
end
function u1.setBuild(p54, p55) --[[Anonymous function at line 242]]
    --[[
    Upvalues:
        [1] = u5
        [2] = u3
    --]]
    if u5:validateBuild(p55) then
        p54.Build = p55
        u3:i("Set build version: " .. p55)
    else
        u3:w("Validation fail - configure build: Cannot be null, empty or above 32 length. String: " .. p55)
    end
end
function u1.setAvailableResourceCurrencies(p56, p57) --[[Anonymous function at line 252]]
    --[[
    Upvalues:
        [1] = u5
        [2] = u3
    --]]
    if u5:validateResourceCurrencies(p57) then
        p56._availableResourceCurrencies = p57
        u3:i("Set available resource currencies: (" .. table.concat(p57, ", ") .. ")")
    end
end
function u1.setAvailableResourceItemTypes(p58, p59) --[[Anonymous function at line 261]]
    --[[
    Upvalues:
        [1] = u5
        [2] = u3
    --]]
    if u5:validateResourceCurrencies(p59) then
        p58._availableResourceItemTypes = p59
        u3:i("Set available resource item types: (" .. table.concat(p59, ", ") .. ")")
    end
end
function u1.addSessionStartEvent(_, p60, p61) --[[Anonymous function at line 270]]
    --[[
    Upvalues:
        [1] = u2
        [2] = u16
        [3] = u32
        [4] = u3
        [5] = u53
    --]]
    local v62 = u2:GetPlayerDataFromCache(p60)
    if p61 then
        v62.Sessions = p61.Sessions
    else
        local v63 = {
            ["category"] = "user"
        }
        v62.Sessions = v62.Sessions + 1
        u16(p60, v63)
        u32(p60, v63)
        u3:i("Add SESSION START event")
        u53()
    end
end
function u1.addSessionEndEvent(_, p64) --[[Anonymous function at line 296]]
    --[[
    Upvalues:
        [1] = u2
        [2] = u5
        [3] = u3
        [4] = u16
        [5] = u32
        [6] = u53
    --]]
    local v65 = u2:GetPlayerDataFromCache(p64)
    local v66 = v65.SessionStart
    local v67
    if p64 then
        local v68 = u2:GetPlayerDataFromCache(p64)
        v67 = os.time()
        local v69 = v67 + v68.ClientServerTimeOffset
        if u5:validateClientTs(v69) then
            v67 = v69
        end
    else
        v67 = os.time()
    end
    local v70 = (v67 == nil or v66 == nil) and 0 or v67 - v66
    if v70 < 0 then
        u3:w("Session length was calculated to be less then 0. Should not be possible. Resetting to 0.")
        v70 = 0
    end
    local v71 = {
        ["category"] = "session_end",
        ["length"] = v70
    }
    u16(p64, v71)
    u32(p64, v71)
    v65.SessionStart = 0
    u3:i("Add SESSION END event.")
    u53()
end
function u1.addBusinessEvent(_, p72, p73, p74, p75, p76, p77) --[[Anonymous function at line 330]]
    --[[
    Upvalues:
        [1] = u5
        [2] = u2
        [3] = u8
        [4] = u16
        [5] = u3
        [6] = u32
    --]]
    if u5:validateBusinessEvent(p73, p74, p77, p75, p76) then
        local v78 = {}
        local v79 = u2:GetPlayerDataFromCache(p72)
        v79.Transactions = v79.Transactions + 1
        v78.event_id = p75 .. ":" .. p76
        v78.category = "business"
        v78.currency = p73
        v78.amount = p74
        v78.transaction_num = v79.Transactions
        if not u8:isStringNullOrEmpty(p77) then
            v78.cart_type = p77
        end
        u16(p72, v78)
        u3:i("Add BUSINESS event: {currency:" .. p73 .. ", amount:" .. tostring(p74) .. ", itemType:" .. p75 .. ", itemId:" .. p76 .. ", cartType:" .. p77 .. "}")
        u32(p72, v78)
    end
end
function u1.addResourceEvent(p80, p81, p82, p83, p84, p85, p86) --[[Anonymous function at line 365]]
    --[[
    Upvalues:
        [1] = u5
        [2] = u9
        [3] = u16
        [4] = u3
        [5] = u32
    --]]
    if u5:validateResourceEvent(u9, p82, p83, p84, p85, p86, p80._availableResourceCurrencies, p80._availableResourceItemTypes) then
        if p82 == u9.Sink then
            p84 = -1 * p84
        end
        local v87 = {
            ["event_id"] = u9[p82] .. ":" .. p83 .. ":" .. p85 .. ":" .. p86,
            ["category"] = "resource",
            ["amount"] = p84
        }
        u16(p81, v87)
        u3:i("Add RESOURCE event: {currency:" .. p83 .. ", amount:" .. tostring(p84) .. ", itemType:" .. p85 .. ", itemId:" .. p86 .. "}")
        u32(p81, v87)
    end
end
function u1.addProgressionEvent(_, p88, p89, p90, p91, p92, p93) --[[Anonymous function at line 395]]
    --[[
    Upvalues:
        [1] = u5
        [2] = u10
        [3] = u8
        [4] = u2
        [5] = u16
        [6] = u3
        [7] = u32
    --]]
    if u5:validateProgressionEvent(u10, p89, p90, p91, p92) then
        local v94 = {}
        local v95
        if u8:isStringNullOrEmpty(p91) then
            v95 = p90
        elseif u8:isStringNullOrEmpty(p92) then
            v95 = p90 .. ":" .. p91
        else
            v95 = p90 .. ":" .. p91 .. ":" .. p92
        end
        local v96 = u10[p89]
        v94.category = "progression"
        v94.event_id = v96 .. ":" .. v95
        local v97 = 0
        if p93 ~= nil and p89 ~= u10.Start then
            v94.score = p93
        end
        local v98 = u2:GetPlayerDataFromCache(p88)
        if p89 == u10.Fail then
            local v99 = v98.ProgressionTries[v95] or 0
            v98.ProgressionTries[v95] = v99 + 1
        end
        if p89 == u10.Complete then
            local v100 = v98.ProgressionTries[v95] or 0
            v98.ProgressionTries[v95] = v100 + 1
            v97 = v98.ProgressionTries[v95]
            v94.attempt_num = v97
            v98.ProgressionTries[v95] = 0
        end
        u16(p88, v94)
        u3:i("Add PROGRESSION event: {status:" .. v96 .. ", progression01:" .. p90 .. ", progression02:" .. (u8:isStringNullOrEmpty(p91) and "" or p91) .. ", progression03:" .. (u8:isStringNullOrEmpty(p92) and "" or p92) .. ", score:" .. tostring(p93) .. ", attempt:" .. tostring(v97) .. "}")
        u32(p88, v94)
    end
end
function u1.addDesignEvent(_, p101, p102, p103) --[[Anonymous function at line 471]]
    --[[
    Upvalues:
        [1] = u5
        [2] = u16
        [3] = u3
        [4] = u32
    --]]
    if u5:validateDesignEvent(p102) then
        local v104 = {
            ["category"] = "design",
            ["event_id"] = p102
        }
        if p103 ~= nil then
            v104.value = p103
        end
        u16(p101, v104)
        u3:i("Add DESIGN event: {eventId:" .. p102 .. ", value:" .. tostring(p103) .. "}")
        u32(p101, v104)
    end
end
function u1.addErrorEvent(_, p105, p106, p107) --[[Anonymous function at line 498]]
    --[[
    Upvalues:
        [1] = u5
        [2] = u11
        [3] = u16
        [4] = u8
        [5] = u3
        [6] = u32
    --]]
    if u5:validateErrorEvent(u11, p106, p107) then
        local v108 = {}
        local v109 = u11[p106]
        v108.category = "error"
        v108.severity = v109
        v108.message = p107
        u16(p105, v108)
        u3:i("Add ERROR event: {severity:" .. v109 .. ", message:" .. (u8:isStringNullOrEmpty(p107) and "" or p107) .. "}")
        u32(p105, v108)
    end
end
function u1.addSdkErrorEvent(_, p110, p111, p112, p113, p114, p115) --[[Anonymous function at line 528]]
    --[[
    Upvalues:
        [1] = u8
        [2] = u3
        [3] = u32
    --]]
    local v116 = {
        ["category"] = "sdk_error",
        ["error_category"] = p111,
        ["error_area"] = p112,
        ["error_action"] = p113
    }
    if not u8:isStringNullOrEmpty(p114) then
        v116.error_parameter = p114
    end
    if not u8:isStringNullOrEmpty(p115) then
        v116.reason = p115
    end
    u3:i("Add SDK ERROR event: {error_category:" .. p111 .. ", error_area:" .. p112 .. ", error_action:" .. p113 .. "}")
    u32(p110, v116)
end
return u1