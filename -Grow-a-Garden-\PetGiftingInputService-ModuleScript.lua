-- Full Path: -Grow-a-Garden-\\PetGiftingInputService-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
local u2 = game:GetService("Players")
local u3 = u2.LocalPlayer
local u4 = u3.Character or u3.CharacterAdded:Wait()
u3.CharacterAdded:Connect(function(p5) --[[Anonymous function at line 7]]
    --[[
    Upvalues:
        [1] = u4
    --]]
    u4 = p5
end)
local u6 = require(v1.Modules.PetServices.PetGiftingService)
require(v1.Modules.PetServices.PetUtilities)
local u7 = require(v1.Modules.PetServices.ActivePetsService)
local u8 = require(v1.Data.PetRegistry).PetConfig.PET_GIFTING_CONFIG.MINIMUM_DISTANCE_FOR_GIFTING
local function u16() --[[Anonymous function at line 22]]
    --[[
    Upvalues:
        [1] = u7
        [2] = u3
        [3] = u2
        [4] = u4
        [5] = u8
    --]]
    u7:GetClientPetState(u3.Name)
    local v9 = nil
    local v10 = (1 / 0)
    local v11 = u2:GetPlayers()
    local v12 = table.find(v11, u3)
    if v12 then
        table.remove(v11, v12)
    end
    for _, v13 in v11 do
        local v14 = v13.Character
        if v14 then
            local v15 = (v14:GetPivot().Position - u4:GetPivot().Position).Magnitude
            if v10 > v15 and u8 > v15 then
                v9 = v14
                v10 = v15
            end
        end
    end
    return v9
end
local u17 = Instance.new("ProximityPrompt")
u17.Exclusivity = Enum.ProximityPromptExclusivity.AlwaysShow
u17.KeyboardKeyCode = Enum.KeyCode.E
u17.RequiresLineOfSight = false
u17.HoldDuration = 0.5
u17.MaxActivationDistance = (1 / 0)
local u18 = nil
u17.Triggered:Connect(function() --[[Anonymous function at line 58]]
    --[[
    Upvalues:
        [1] = u18
        [2] = u2
        [3] = u6
    --]]
    if not u18 then
        return warn("No Closest")
    end
    local v19 = u2:GetPlayerFromCharacter(u18)
    if not v19 then
        return warn("No player")
    end
    u6:GivePet(v19)
end)
require(v1.Data.DecimalNumberFormat)
task.spawn(function() --[[Anonymous function at line 67]]
    --[[
    Upvalues:
        [1] = u4
        [2] = u17
        [3] = u16
        [4] = u18
    --]]
    while true do
        while true do
            task.wait(0.5)
            local v20 = u4:FindFirstChildWhichIsA("Tool")
            if not v20 then
                break
            end
            if v20:GetAttribute("ItemType") == "Pet" then
                if v20:GetAttribute("Favorite") == true then
                    u17.Enabled = false
                else
                    local v21 = u16()
                    u18 = v21
                    if v21 then
                        u17.ObjectText = v21.Name
                        u17.ActionText = ("Gift Pet %*"):format(v20.Name)
                        u17.Enabled = true
                        u17.Parent = v21.PrimaryPart or v21:FindFirstChildWhichIsA("BasePart", true)
                    else
                        u17.Enabled = false
                    end
                end
            else
                u17.Enabled = false
            end
        end
        u17.Enabled = false
    end
end)
return {}