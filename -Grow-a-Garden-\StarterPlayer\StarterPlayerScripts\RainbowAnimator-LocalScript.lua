-- Full Path: -Grow-a-Garden-\StarterPlayer\StarterPlayerScripts\RainbowAnimator-LocalScript.lua
local u1 = game:GetService("CollectionService")
game:GetService("RunService")
game:GetService("ReplicatedStorage"):WaitForChild("Modules")
task.spawn(function() --[[Anonymous function at line 11]]
    --[[
    Upvalues:
        [1] = u1
    --]]
    while true do
        local v2 = Color3.fromHSV(os.clock() % 10 / 10, 1, 1)
        for _, v3 in u1:GetTagged("RainbowPart") do
            v3.Color = v2
        end
        task.wait(0.1)
    end
end)