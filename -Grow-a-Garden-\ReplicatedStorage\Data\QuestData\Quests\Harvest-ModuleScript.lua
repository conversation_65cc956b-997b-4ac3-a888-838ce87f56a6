-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Data\QuestData\Quests\Harvest-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
require(v1.Data.QuestData.Types)
return {
    ["Type"] = "Harvest",
    ["Display"] = function(_, p2, p3, p4) --[[Function name: Display, line 10]]
        assert(p4, "Quest Harvest doesn\'t have an argument.")
        return {
            ["Title"] = ("Harvest %* %*"):format(p3, p4[1]),
            ["Bar"] = ("%*/%*"):format(p2, p3)
        }
    end,
    ["Use"] = function(p5, p6) --[[Function name: Use, line 19]]
        local v7 = p6.Arguments
        assert(v7, "Plant quests needs arguments.")
        local v8 = table.clone(p6)
        v8.Type = p5.Type
        return v8
    end
}