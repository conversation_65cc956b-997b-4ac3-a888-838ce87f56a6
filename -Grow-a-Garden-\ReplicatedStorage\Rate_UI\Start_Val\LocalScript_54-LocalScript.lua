-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Rate_UI\Start_Val\LocalScript_54-LocalScript.lua
local v1 = game:GetService("TweenService")
local v2 = TweenInfo.new(1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out, 0, false, 0)
local v3 = TweenInfo.new(0.07, Enum.EasingStyle.Quad, Enum.EasingDirection.Out, 0, false, 0)
local u4 = script.Parent.Parent.CanvasGroup.TextButton.Hold_Down_Val
local u5 = v1:Create(u4, v2, {
    ["Size"] = UDim2.new(1, 0, 1, 0)
})
local u6 = v1:Create(u4, v3, {
    ["BackgroundTransparency"] = 0.5
})
local u7 = time()
script.Parent:GetPropertyChangedSignal("Value"):Connect(function() --[[Anonymous function at line 8]]
    --[[
    Upvalues:
        [1] = u7
        [2] = u5
        [3] = u4
        [4] = u6
    --]]
    u7 = time()
    if script.Parent.Value == true then
        u5:Cancel()
        u4.Size = UDim2.new(0, 0, 1, 0)
        u4.BackgroundTransparency = 1
        u5:Play()
        u6:Play()
        u7 = time()
    else
        u5:Cancel()
        u6:Cancel()
        u4.BackgroundTransparency = 1
    end
end)
local u8 = game.ReplicatedStorage.GameEvents.Send_Rating
local _ = game.Players.LocalPlayer.PlayerGui.Gradient_UI.Green_VAL
local _ = game.Players.LocalPlayer.PlayerGui.Gradient_UI.Red_VAL
local _ = game.SoundService.Error
u5.Completed:Connect(function() --[[Anonymous function at line 27]]
    --[[
    Upvalues:
        [1] = u7
        [2] = u8
    --]]
    if time() - u7 >= 0.95 then
        script.Parent.Parent.Show_Val.Value = false
        local v9 = game.Players:FindFirstChild(script.Parent.Parent.PLR_1.Value)
        if v9 and v9.Character then
            local v10 = v9.Character:FindFirstChild("Show_The_Val")
            local v11 = v9.Character:FindFirstChild("Character_Highlight_Click")
            if v10 and v11 then
                v10.Value = false
                v11.Value = false
            end
        end
        u8:FireServer(script.Parent.Parent.PLR_1.Value, script.Parent.Parent.Rating_Val.Value)
    end
end)