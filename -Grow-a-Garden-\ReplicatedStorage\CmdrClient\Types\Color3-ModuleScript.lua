-- Full Path: -Grow-a-Garden-\ReplicatedStorage\CmdrClient\Types\Color3-ModuleScript.lua
local u1 = require(script.Parent.Parent.Shared.Util)
local u4 = u1.MakeSequenceType({
    ["Prefixes"] = "# hexColor3 ! brickColor3",
    ["ValidateEach"] = function(p2, p3) --[[Function name: ValidateEach, line 5]]
        if p2 == nil then
            return false, ("Invalid or missing number at position %d in Color3 type."):format(p3)
        elseif p2 < 0 or p2 > 255 then
            return false, ("Number out of acceptable range 0-255 at position %d in Color3 type."):format(p3)
        elseif p2 % 1 == 0 then
            return true
        else
            return false, ("Number is not an integer at position %d in Color3 type."):format(p3)
        end
    end,
    ["TransformEach"] = tonumber,
    ["Constructor"] = Color3.fromRGB,
    ["Length"] = 3
})
local function u6(p5) --[[Anonymous function at line 21]]
    if #p5 == 1 then
        p5 = p5 .. p5
    end
    return tonumber(p5, 16)
end
local u15 = {
    ["Transform"] = function(p7) --[[Function name: Transform, line 30]]
        --[[
        Upvalues:
            [1] = u1
            [2] = u6
        --]]
        local v8, v9, v10 = p7:match("^#?(%x%x?)(%x%x?)(%x%x?)$")
        return u1.Each(u6, v8, v9, v10)
    end,
    ["Validate"] = function(p11, p12, p13) --[[Function name: Validate, line 35]]
        local v14
        if p11 == nil or p12 == nil then
            v14 = false
        else
            v14 = p13 ~= nil
        end
        return v14, "Invalid hex color"
    end,
    ["Parse"] = function(...) --[[Function name: Parse, line 39]]
        return Color3.fromRGB(...)
    end
}
return function(p16) --[[Anonymous function at line 44]]
    --[[
    Upvalues:
        [1] = u4
        [2] = u1
        [3] = u15
    --]]
    p16:RegisterType("color3", u4)
    p16:RegisterType("color3s", u1.MakeListableType(u4, {
        ["Prefixes"] = "# hexColor3s ! brickColor3s"
    }))
    p16:RegisterType("hexColor3", u15)
    p16:RegisterType("hexColor3s", u1.MakeListableType(u15))
end