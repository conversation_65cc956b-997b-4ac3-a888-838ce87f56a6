-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\Icon\Elements\Caption-ModuleScript.lua
return function(u1) --[[Anonymous function at line 1]]
    local u2 = u1:getInstance("ClickRegion")
    local u3 = Instance.new("CanvasGroup")
    u3.Name = "Caption"
    u3.AnchorPoint = Vector2.new(0.5, 0)
    u3.BackgroundTransparency = 1
    u3.BorderSizePixel = 0
    u3.GroupTransparency = 1
    u3.Position = UDim2.fromOffset(0, 0)
    u3.Visible = true
    u3.ZIndex = 30
    u3.Parent = u2
    local u4 = Instance.new("Frame")
    u4.Name = "Box"
    u4.AutomaticSize = Enum.AutomaticSize.XY
    u4.BackgroundColor3 = Color3.fromRGB(101, 102, 104)
    u4.Position = UDim2.fromOffset(4, 7)
    u4.ZIndex = 12
    u4.Parent = u3
    local v5 = Instance.new("TextLabel")
    v5.Name = "Header"
    v5.FontFace = Font.new("rbxasset://fonts/families/GothamSSm.json", Enum.FontWeight.Medium, Enum.FontStyle.Normal)
    v5.Text = "Caption"
    v5.TextColor3 = Color3.fromRGB(255, 255, 255)
    v5.TextSize = 14
    v5.TextTruncate = Enum.TextTruncate.None
    v5.TextWrapped = false
    v5.TextXAlignment = Enum.TextXAlignment.Left
    v5.AutomaticSize = Enum.AutomaticSize.X
    v5.BackgroundTransparency = 1
    v5.LayoutOrder = 1
    v5.Size = UDim2.fromOffset(0, 16)
    v5.ZIndex = 18
    v5.Parent = u4
    local v6 = Instance.new("UIListLayout")
    v6.Name = "Layout"
    v6.Padding = UDim.new(0, 8)
    v6.SortOrder = Enum.SortOrder.LayoutOrder
    v6.Parent = u4
    local v7 = Instance.new("UICorner")
    v7.Name = "CaptionCorner"
    v7.Parent = u4
    local v8 = Instance.new("UIPadding")
    v8.Name = "Padding"
    v8.PaddingBottom = UDim.new(0, 12)
    v8.PaddingLeft = UDim.new(0, 12)
    v8.PaddingRight = UDim.new(0, 12)
    v8.PaddingTop = UDim.new(0, 12)
    v8.Parent = u4
    local u9 = Instance.new("Frame")
    u9.Name = "Hotkeys"
    u9.AutomaticSize = Enum.AutomaticSize.Y
    u9.BackgroundTransparency = 1
    u9.LayoutOrder = 3
    u9.Size = UDim2.fromScale(1, 0)
    u9.Visible = false
    u9.Parent = u4
    local v10 = Instance.new("UIListLayout")
    v10.Name = "Layout1"
    v10.Padding = UDim.new(0, 6)
    v10.FillDirection = Enum.FillDirection.Vertical
    v10.HorizontalAlignment = Enum.HorizontalAlignment.Center
    v10.HorizontalFlex = Enum.UIFlexAlignment.None
    v10.ItemLineAlignment = Enum.ItemLineAlignment.Automatic
    v10.VerticalFlex = Enum.UIFlexAlignment.None
    v10.SortOrder = Enum.SortOrder.LayoutOrder
    v10.Parent = u9
    local v11 = Instance.new("ImageLabel")
    v11.Name = "Key1"
    v11.Image = "rbxasset://textures/ui/Controls/key_single.png"
    v11.ImageTransparency = 0.7
    v11.ScaleType = Enum.ScaleType.Slice
    v11.SliceCenter = Rect.new(5, 5, 23, 24)
    v11.AutomaticSize = Enum.AutomaticSize.X
    v11.BackgroundTransparency = 1
    v11.LayoutOrder = 1
    v11.Size = UDim2.fromOffset(0, 30)
    v11.ZIndex = 15
    v11.Parent = u9
    local v12 = Instance.new("UIPadding")
    v12.Name = "Inset"
    v12.PaddingLeft = UDim.new(0, 8)
    v12.PaddingRight = UDim.new(0, 8)
    v12.Parent = v11
    local u13 = Instance.new("TextLabel")
    u13.AutoLocalize = false
    u13.Name = "LabelContent"
    u13.FontFace = Font.new("rbxasset://fonts/families/GothamSSm.json", Enum.FontWeight.Medium, Enum.FontStyle.Normal)
    u13.Text = ""
    u13.TextColor3 = Color3.fromRGB(189, 190, 190)
    u13.TextSize = 14
    u13.AutomaticSize = Enum.AutomaticSize.X
    u13.BackgroundTransparency = 1
    u13.Position = UDim2.fromOffset(0, -1)
    u13.Size = UDim2.fromScale(1, 1)
    u13.ZIndex = 16
    u13.Parent = v11
    local u14 = Instance.new("ImageLabel")
    u14.Name = "Caret"
    u14.Image = "rbxasset://LuaPackages/Packages/_Index/UIBlox/UIBlox/AppImageAtlas/img_set_1x_1.png"
    u14.ImageColor3 = Color3.fromRGB(101, 102, 104)
    u14.ImageRectOffset = Vector2.new(260, 440)
    u14.ImageRectSize = Vector2.new(16, 8)
    u14.AnchorPoint = Vector2.new(0, 0.5)
    u14.BackgroundTransparency = 1
    u14.Position = UDim2.new(0, 0, 0, 4)
    u14.Rotation = 180
    u14.Size = UDim2.fromOffset(16, 8)
    u14.ZIndex = 12
    u14.Parent = u3
    local u15 = Instance.new("ImageLabel")
    u15.Name = "DropShadow"
    u15.Image = "rbxasset://LuaPackages/Packages/_Index/UIBlox/UIBlox/AppImageAtlas/img_set_1x_1.png"
    u15.ImageColor3 = Color3.fromRGB(0, 0, 0)
    u15.ImageRectOffset = Vector2.new(217, 486)
    u15.ImageRectSize = Vector2.new(25, 25)
    u15.ImageTransparency = 0.45
    u15.ScaleType = Enum.ScaleType.Slice
    u15.SliceCenter = Rect.new(12, 12, 13, 13)
    u15.BackgroundTransparency = 1
    u15.Position = UDim2.fromOffset(0, 5)
    u15.Size = UDim2.new(1, 0, 0, 48)
    u15.Parent = u3
    u4:GetPropertyChangedSignal("AbsoluteSize"):Connect(function() --[[Anonymous function at line 147]]
        --[[
        Upvalues:
            [1] = u15
            [2] = u4
        --]]
        u15.Size = UDim2.new(1, 0, 0, u4.AbsoluteSize.Y + 8)
    end)
    local v16 = u1.captionJanitor
    local _, u17 = u1:clipOutside(u3)
    u17.AutomaticSize = Enum.AutomaticSize.None
    v16:add(u3:GetPropertyChangedSignal("AbsoluteSize"):Connect(function() --[[Function name: matchSize, line 157]]
        --[[
        Upvalues:
            [1] = u3
            [2] = u17
        --]]
        local v18 = u3.AbsoluteSize
        u17.Size = UDim2.fromOffset(v18.X, v18.Y)
    end))
    local v19 = u3.AbsoluteSize
    u17.Size = UDim2.fromOffset(v19.X, v19.Y)
    local u20 = false
    local u21 = u3.Box.Header
    local u22 = game:GetService("UserInputService")
    local function v27(p23) --[[Anonymous function at line 170]]
        --[[
        Upvalues:
            [1] = u22
            [2] = u3
            [3] = u1
            [4] = u21
            [5] = u13
            [6] = u9
        --]]
        local v24 = u22.KeyboardEnabled
        local v25 = u3:GetAttribute("CaptionText") or ""
        local v26 = v25 == "_hotkey_"
        if v24 or not v26 then
            u21.Text = v25
            u21.Visible = not v26
            if p23 then
                u13.Text = p23.Name
                u9.Visible = true
            end
            if not v24 then
                u9.Visible = false
            end
        else
            u1:setCaption()
        end
    end
    u3:GetAttributeChangedSignal("CaptionText"):Connect(v27)
    local v28 = Enum.EasingStyle.Quad
    local u29 = TweenInfo.new(0.2, v28, Enum.EasingDirection.In)
    local u30 = TweenInfo.new(0.2, v28, Enum.EasingDirection.Out)
    local u31 = game:GetService("TweenService")
    local u32 = game:GetService("RunService")
    local function u47(p33) --[[Anonymous function at line 203]]
        --[[
        Upvalues:
            [1] = u20
            [2] = u14
            [3] = u3
            [4] = u2
            [5] = u17
            [6] = u29
            [7] = u30
            [8] = u31
            [9] = u32
        --]]
        if u20 then
            if p33 == nil then
                p33 = u20
            end
            local v34 = not p33
            if v34 == nil then
                v34 = u20
            end
            local v35 = UDim2.new(0.5, 0, 1, v34 and 10 or 2)
            local v36
            if p33 == nil then
                v36 = u20
            else
                v36 = p33
            end
            local v37 = UDim2.new(0.5, 0, 1, v36 and 10 or 2)
            if p33 then
                local v38 = u14.Position.Y.Offset
                u14.Position = UDim2.fromOffset(0, v38)
                u3.AutomaticSize = Enum.AutomaticSize.XY
                u3.Size = UDim2.fromOffset(32, 53)
            else
                local v39 = u3.AbsoluteSize
                u3.AutomaticSize = Enum.AutomaticSize.Y
                u3.Size = UDim2.fromOffset(v39.X, v39.Y)
            end
            local u40 = nil
            local function v44() --[[Anonymous function at line 232]]
                --[[
                Upvalues:
                    [1] = u2
                    [2] = u3
                    [3] = u14
                    [4] = u40
                --]]
                local v41 = u2.AbsolutePosition.X - u3.AbsolutePosition.X + u2.AbsoluteSize.X / 2 - u14.AbsoluteSize.X / 2
                local v42 = u14.Position.Y.Offset
                local v43 = UDim2.fromOffset(v41, v42)
                if u40 ~= v41 then
                    u40 = v41
                    u14.Position = UDim2.fromOffset(0, v42)
                    task.wait()
                end
                u14.Position = v43
            end
            u17.Position = v35
            v44()
            local v45 = u31:Create(u17, p33 and u29 or u30, {
                ["Position"] = v37
            })
            local u46 = u32.Heartbeat:Connect(v44)
            v45:Play()
            v45.Completed:Once(function() --[[Anonymous function at line 255]]
                --[[
                Upvalues:
                    [1] = u46
                --]]
                u46:Disconnect()
            end)
        end
    end
    v16:add(u2:GetPropertyChangedSignal("AbsoluteSize"):Connect(function() --[[Anonymous function at line 260]]
        --[[
        Upvalues:
            [1] = u47
        --]]
        u47()
    end))
    u47(false)
    v16:add(u1.toggleKeyAdded:Connect(v27))
    for v48, _ in pairs(u1.bindedToggleKeys) do
        local v49 = u22.KeyboardEnabled
        local v50 = u3:GetAttribute("CaptionText") or ""
        local v51 = v50 == "_hotkey_"
        if v49 or not v51 then
            u21.Text = v50
            u21.Visible = not v51
            if v48 then
                u13.Text = v48.Name
                u9.Visible = true
            end
            if not v49 then
                u9.Visible = false
            end
        else
            u1:setCaption()
        end
        break
    end
    v16:add(u1.fakeToggleKeyChanged:Connect(v27))
    local v52 = u1.fakeToggleKey
    if v52 then
        local v53 = u22.KeyboardEnabled
        local v54 = u3:GetAttribute("CaptionText") or ""
        local v55 = v54 == "_hotkey_"
        if v53 or not v55 then
            u21.Text = v54
            u21.Visible = not v55
            if v52 then
                u13.Text = v52.Name
                u9.Visible = true
            end
            if not v53 then
                u9.Visible = false
            end
        else
            u1:setCaption()
        end
    end
    local function u61(p56) --[[Anonymous function at line 276]]
        --[[
        Upvalues:
            [1] = u20
            [2] = u1
            [3] = u29
            [4] = u30
            [5] = u31
            [6] = u3
            [7] = u47
            [8] = u22
            [9] = u21
            [10] = u9
        --]]
        if u20 == p56 then
            return
        else
            local v57 = u1.joinedFrame
            if v57 and string.match(v57.Name, "Dropdown") then
                p56 = false
            end
            u20 = p56
            u31:Create(u3, p56 and u29 or u30, {
                ["GroupTransparency"] = p56 and 0 or 1
            }):Play()
            u47()
            local v58 = u22.KeyboardEnabled
            local v59 = u3:GetAttribute("CaptionText") or ""
            local v60 = v59 == "_hotkey_"
            if v58 or not v60 then
                u21.Text = v59
                u21.Visible = not v60
                if not v58 then
                    u9.Visible = false
                end
            else
                u1:setCaption()
            end
        end
    end
    local u62 = require(u1.iconModule)
    v16:add(u1.stateChanged:Connect(function(p63) --[[Anonymous function at line 298]]
        --[[
        Upvalues:
            [1] = u62
            [2] = u1
            [3] = u61
        --]]
        if p63 == "Viewing" then
            local v64 = u62.captionLastClosedClock
            local v65 = (v64 and os.clock() - v64 or 999) < 0.3 and 0 or 0.5
            task.delay(v65, function() --[[Anonymous function at line 303]]
                --[[
                Upvalues:
                    [1] = u1
                    [2] = u61
                --]]
                if u1.activeState == "Viewing" then
                    u61(true)
                end
            end)
        else
            u62.captionLastClosedClock = os.clock()
            u61(false)
        end
    end))
    return u3
end