-- Full Path: -Grow-a-Garden-\StarterPlayer\StarterPlayerScripts\CustomPack\Loader\Main\Utility-ModuleScript.lua
local u1 = Color3.fromRGB(0, 162, 255)
local u2 = Color3.fromRGB(78, 84, 96)
local u3 = Color3.fromRGB(204, 204, 204)
local u4 = Color3.fromRGB(255, 255, 255)
local u5 = Color3.fromRGB(150, 150, 150)
local u6 = game:GetService("HttpService")
local u7 = game:GetService("UserInputService")
local u8 = game:GetService("GuiService")
local u9 = game:GetService("RunService")
local u10 = game.Players.LocalPlayer.PlayerGui
u10:WaitForChild("BackpackGui")
local u11 = game:GetService("ContextActionService")
local u12 = game:GetService("VRService")
local v13, v14 = pcall(function() --[[Anonymous function at line 39]]
    return false
end)
local u15 = v13 and v14
local u22 = {
    ["Create"] = function(u16) --[[Function name: Create, line 48]]
        return function(p17) --[[Anonymous function at line 49]]
            --[[
            Upvalues:
                [1] = u16
            --]]
            local v18 = Instance.new(u16)
            local v19 = nil
            for v20, v21 in pairs(p17) do
                if type(v20) == "number" then
                    v21.Parent = v18
                elseif v20 == "Parent" then
                    v19 = v21
                else
                    v18[v20] = v21
                end
            end
            if v19 then
                v18.Parent = v19
            end
            return v18
        end
    end
}
local u23 = {}
setmetatable(u23, {
    ["__mode"] = "k"
})
local u24 = u22.Create("ImageLabel")({
    ["Image"] = "",
    ["BackgroundTransparency"] = 1
})
function clamp(p25, p26, p27)
    local v28 = math.min(p26, p27)
    return math.max(p25, v28)
end
function ClampVector2(p29, p30, p31)
    return Vector2.new(clamp(p29.x, p30.x, p31.x), clamp(p29.y, p30.y, p31.y))
end
local function u36(p32, p33, p34, p35) --[[Anonymous function at line 90]]
    if p35 <= p32 then
        return p33 + p34
    else
        return p34 * p32 / p35 + p33
    end
end
local function u42(p37, p38, p39, p40) --[[Anonymous function at line 98]]
    if p40 <= p37 then
        return p38 + p39
    end
    local v41 = p37 / p40
    return p38 - p39 * v41 * (v41 - 2)
end
local function u48(p43, p44, p45, p46) --[[Anonymous function at line 107]]
    if p46 <= p43 then
        return p44 + p45
    else
        local v47 = p43 / p46
        if v47 < 0.5 then
            return 2 * p45 * v47 * v47 + p44
        else
            return p44 + p45 * (2 * (2 - v47) * v47 - 1)
        end
    end
end
function PropertyTweener(u49, u50, u51, u52, u53, u54, u55)
    --[[
    Upvalues:
        [1] = u9
    --]]
    local u56 = {
        ["StartTime"] = tick()
    }
    u56.EndTime = u56.StartTime + u53
    u56.Cancelled = false
    local u57 = false
    local u58 = 0
    u49[u50] = u54(0, u51, u52 - u51, u53)
    coroutine.wrap(function() --[[Anonymous function at line 141]]
        --[[
        Upvalues:
            [1] = u56
            [2] = u49
            [3] = u50
            [4] = u54
            [5] = u51
            [6] = u52
            [7] = u53
            [8] = u58
            [9] = u9
            [10] = u57
            [11] = u55
        --]]
        local v59 = tick()
        while v59 < u56.EndTime and u49 do
            if u56.Cancelled then
                return
            end
            u49[u50] = u54(v59 - u56.StartTime, u51, u52 - u51, u53)
            u58 = clamp(0, 1, (v59 - u56.StartTime) / u53)
            u9.RenderStepped:wait()
            v59 = tick()
        end
        if u56.Cancelled == false and u49 then
            if u49 then
                u49[u50] = u54(1, u51, u52 - u51, 1)
            end
            u57 = true
            u58 = 1
            if u55 then
                u55()
            end
        end
    end)()
    function u56.GetFinal(_) --[[Anonymous function at line 157]]
        --[[
        Upvalues:
            [1] = u52
        --]]
        return u52
    end
    function u56.GetPercentComplete(_) --[[Anonymous function at line 161]]
        --[[
        Upvalues:
            [1] = u58
        --]]
        return u58
    end
    function u56.IsFinished(_) --[[Anonymous function at line 165]]
        --[[
        Upvalues:
            [1] = u57
        --]]
        return u57
    end
    function u56.Finish(p60) --[[Anonymous function at line 169]]
        --[[
        Upvalues:
            [1] = u57
            [2] = u49
            [3] = u50
            [4] = u54
            [5] = u51
            [6] = u52
            [7] = u58
            [8] = u55
        --]]
        if not u57 then
            p60:Cancel()
            if u49 then
                u49[u50] = u54(1, u51, u52 - u51, 1)
            end
            u57 = true
            u58 = 1
            if u55 then
                u55()
            end
        end
    end
    function u56.Cancel(_) --[[Anonymous function at line 176]]
        --[[
        Upvalues:
            [1] = u56
        --]]
        u56.Cancelled = true
    end
    return u56
end
local function u70() --[[Anonymous function at line 185]]
    local v61 = {}
    local u62 = Instance.new("BindableEvent")
    local u63 = nil
    local u64 = nil
    function v61.fire(_, ...) --[[Anonymous function at line 193]]
        --[[
        Upvalues:
            [1] = u63
            [2] = u64
            [3] = u62
        --]]
        u63 = { ... }
        u64 = select("#", ...)
        u62:Fire()
    end
    function v61.connect(_, u65) --[[Anonymous function at line 199]]
        --[[
        Upvalues:
            [1] = u62
            [2] = u63
            [3] = u64
        --]]
        if not u65 then
            error("connect(nil)", 2)
        end
        return u62.Event:Connect(function() --[[Anonymous function at line 201]]
            --[[
            Upvalues:
                [1] = u65
                [2] = u63
                [3] = u64
            --]]
            local v66 = u63
            local v67 = u64
            u65(unpack(v66, 1, v67))
        end)
    end
    function v61.wait(_) --[[Anonymous function at line 206]]
        --[[
        Upvalues:
            [1] = u62
            [2] = u63
            [3] = u64
        --]]
        u62.Event:wait()
        if not u63 then
            error("Missing arg data, likely due to :TweenSize/Position corrupting threadrefs.")
        end
        local v68 = u63
        local v69 = u64
        return unpack(v68, 1, v69)
    end
    return v61
end
local function u71() --[[Anonymous function at line 217]]
    while not workspace.CurrentCamera do
        workspace.Changed:wait()
    end
    while workspace.CurrentCamera.ViewportSize == Vector2.new(0, 0) or workspace.CurrentCamera.ViewportSize == Vector2.new(1, 1) do
        workspace.CurrentCamera.Changed:wait()
    end
    return workspace.CurrentCamera.ViewportSize
end
local function u78(u72, u73, u74, u75) --[[Anonymous function at line 340]]
    local function v76() --[[Anonymous function at line 341]]
        --[[
        Upvalues:
            [1] = u72
            [2] = u74
            [3] = u73
        --]]
        if u72.Active then
            u74(u73)
        end
    end
    local function v77() --[[Anonymous function at line 346]]
        --[[
        Upvalues:
            [1] = u72
            [2] = u75
            [3] = u73
        --]]
        if u72.Active then
            u75(u73)
        end
    end
    u72.MouseEnter:Connect(v77)
    u72.SelectionGained:Connect(v77)
    u72.MouseLeave:Connect(v76)
    u72.SelectionLost:Connect(v76)
    u74(u73)
end
local function u83(p79, p80) --[[Anonymous function at line 360]]
    --[[
    Upvalues:
        [1] = u23
        [2] = u71
    --]]
    u23[p79] = p80
    local v81 = u71()
    local v82 = u71()
    p80(v81, v82.Y > v82.X)
end
local u84 = {
    [Enum.UserInputType.Gamepad1] = true,
    [Enum.UserInputType.Gamepad2] = true,
    [Enum.UserInputType.Gamepad3] = true,
    [Enum.UserInputType.Gamepad4] = true,
    [Enum.UserInputType.Gamepad5] = true,
    [Enum.UserInputType.Gamepad6] = true,
    [Enum.UserInputType.Gamepad7] = true,
    [Enum.UserInputType.Gamepad8] = true
}
local function u105(p85, p86, u87, u88, u89) --[[Anonymous function at line 376]]
    --[[
    Upvalues:
        [1] = u22
        [2] = u84
        [3] = u7
        [4] = u8
        [5] = u12
    --]]
    local v90 = u22.Create("ImageLabel")({
        ["Image"] = "",
        ["BackgroundTransparency"] = 1
    })
    local u91 = u22.Create("ImageButton")({
        ["Name"] = p85 .. "Button",
        ["Image"] = "rbxasset://textures/ui/Settings/MenuBarAssets/MenuButton.png",
        ["ScaleType"] = Enum.ScaleType.Slice,
        ["SliceCenter"] = Rect.new(8, 6, 46, 44),
        ["AutoButtonColor"] = false,
        ["BackgroundTransparency"] = 1,
        ["Size"] = p86,
        ["ZIndex"] = 2,
        ["SelectionImageObject"] = v90
    })
    u22.Create("BoolValue")({
        ["Name"] = "Enabled",
        ["Parent"] = u91,
        ["Value"] = true
    })
    if u87 then
        u91.MouseButton1Click:Connect(function() --[[Anonymous function at line 404]]
            --[[
            Upvalues:
                [1] = u87
                [2] = u84
                [3] = u7
            --]]
            u87(u84[u7:GetLastInputType()] or false)
        end)
    end
    local u92 = nil
    local function v94(p93) --[[Anonymous function at line 414]]
        --[[
        Upvalues:
            [1] = u92
        --]]
        u92 = p93
    end
    u91.InputBegan:Connect(function(p95) --[[Anonymous function at line 443]]
        --[[
        Upvalues:
            [1] = u91
            [2] = u89
            [3] = u88
            [4] = u92
        --]]
        if u91.Selectable and (p95.UserInputType == Enum.UserInputType.MouseMovement or p95.UserInputType == Enum.UserInputType.Touch) then
            local v96 = u89
            if v96 == nil and u88 then
                v96 = u88.HubRef
            end
            if v96 and v96.Active or v96 == nil then
                u91.Image = "rbxasset://textures/ui/Settings/MenuBarAssets/MenuButtonSelected.png"
                local v97 = u91
                if u92 then
                    v97 = u92
                end
                if v96 then
                    v96:ScrollToFrame(v97)
                end
            end
        end
    end)
    u91.InputEnded:Connect(function(p98) --[[Anonymous function at line 448]]
        --[[
        Upvalues:
            [1] = u91
            [2] = u8
        --]]
        if u91.Selectable and u8.SelectedCoreObject ~= u91 and (p98.UserInputType == Enum.UserInputType.MouseMovement or p98.UserInputType == Enum.UserInputType.Touch) then
            u91.Image = "rbxasset://textures/ui/Settings/MenuBarAssets/MenuButton.png"
        end
    end)
    u91.SelectionGained:Connect(function() --[[Anonymous function at line 455]]
        --[[
        Upvalues:
            [1] = u89
            [2] = u88
            [3] = u91
            [4] = u92
        --]]
        local v99 = u89
        if v99 == nil and u88 then
            v99 = u88.HubRef
        end
        if v99 and v99.Active or v99 == nil then
            u91.Image = "rbxasset://textures/ui/Settings/MenuBarAssets/MenuButtonSelected.png"
            local v100 = u91
            if u92 then
                v100 = u92
            end
            if v99 then
                v99:ScrollToFrame(v100)
            end
        end
    end)
    u91.SelectionLost:Connect(function() --[[Anonymous function at line 458]]
        --[[
        Upvalues:
            [1] = u91
        --]]
        u91.Image = "rbxasset://textures/ui/Settings/MenuBarAssets/MenuButton.png"
    end)
    u8.Changed:Connect(function(p101) --[[Anonymous function at line 462]]
        --[[
        Upvalues:
            [1] = u12
            [2] = u7
            [3] = u8
            [4] = u91
            [5] = u89
            [6] = u88
            [7] = u92
        --]]
        if p101 == "SelectedCoreObject" then
            local v102
            if u12.VREnabled then
                v102 = false
            else
                v102 = (not u7.TouchEnabled or u7.GamepadEnabled) and true or false
            end
            if v102 then
                if u8.SelectedCoreObject == nil or u8.SelectedCoreObject ~= u91 then
                    u91.Image = "rbxasset://textures/ui/Settings/MenuBarAssets/MenuButton.png"
                elseif u91.Selectable then
                    local v103 = u89
                    if v103 == nil and u88 then
                        v103 = u88.HubRef
                    end
                    if v103 and v103.Active or v103 == nil then
                        u91.Image = "rbxasset://textures/ui/Settings/MenuBarAssets/MenuButtonSelected.png"
                        local v104 = u91
                        if u92 then
                            v104 = u92
                        end
                        if v103 then
                            v103:ScrollToFrame(v104)
                        end
                    end
                end
            else
                return
            end
        else
            return
        end
    end)
    return u91, v94
end
local function u118(p106, p107, p108, p109, p110, p111) --[[Anonymous function at line 479]]
    --[[
    Upvalues:
        [1] = u105
        [2] = u22
        [3] = u71
        [4] = u7
    --]]
    local v112, v113 = u105(p106, p108, p109, p110, p111)
    local v114 = u22.Create("TextLabel")({
        ["Name"] = p106 .. "TextLabel",
        ["BackgroundTransparency"] = 1,
        ["BorderSizePixel"] = 0,
        ["Size"] = UDim2.new(1, 0, 1, -8),
        ["Position"] = UDim2.new(0, 0, 0, 0),
        ["TextColor3"] = Color3.fromRGB(255, 255, 255),
        ["TextYAlignment"] = Enum.TextYAlignment.Center,
        ["Font"] = Enum.Font.SourceSansBold,
        ["TextSize"] = 24,
        ["Text"] = p107,
        ["TextScaled"] = true,
        ["TextWrapped"] = true,
        ["ZIndex"] = 2,
        ["Parent"] = v112
    })
    local v115 = Instance.new("UITextSizeConstraint", v114)
    local v116 = u71()
    local v117 = u7.TouchEnabled
    if v117 then
        v117 = v116.Y < 500 and true or v116.X < 700
    end
    if v117 then
        v114.TextSize = 18
    elseif false then
        v114.TextSize = 36
    end
    v115.MaxTextSize = v114.TextSize
    return v112, v114, v113
end
local function u128(p119, p120, p121, p122, p123, p124, p125) --[[Anonymous function at line 511]]
    --[[
    Upvalues:
        [1] = u105
        [2] = u22
    --]]
    local v126, v127 = u105(p119, p121, p123, p124, p125)
    return v126, u22.Create("ImageLabel")({
        ["Name"] = p119 .. "ImageLabel",
        ["BackgroundTransparency"] = 1,
        ["BorderSizePixel"] = 0,
        ["Size"] = p122,
        ["Position"] = UDim2.new(0.5, 0, 0.5, 0),
        ["AnchorPoint"] = Vector2.new(0.5, 0.5),
        ["Image"] = p120,
        ["ZIndex"] = 2,
        ["Parent"] = v126
    }), v127
end
local function u139(p129, p130, p131, p132, p133, p134) --[[Anonymous function at line 530]]
    --[[
    Upvalues:
        [1] = u118
        [2] = u22
    --]]
    local v135, v136, v137 = u118(p130, p131, p132, p133, p129, p134)
    local v138 = u22.Create("Frame")({
        ["Name"] = p130 .. "Row",
        ["BackgroundTransparency"] = 1,
        ["Size"] = UDim2.new(1, 0, p132.Y.Scale, p132.Y.Offset),
        ["Parent"] = p129.Page
    })
    v135.Parent = v138
    v135.AnchorPoint = Vector2.new(1, 0)
    v135.Position = UDim2.new(1, -20, 0, 0)
    return v138, v135, v136, v137
end
local function u204(p140, u141, u142) --[[Anonymous function at line 545]]
    --[[
    Upvalues:
        [1] = u6
        [2] = u22
        [3] = u10
        [4] = u12
        [5] = u7
        [6] = u8
        [7] = u11
        [8] = u118
    --]]
    local u143 = Color3.fromRGB(178, 178, 178)
    local u144 = Color3.fromRGB(229, 229, 229)
    local u145 = Color3.fromRGB(255, 255, 255)
    local u146 = nil
    local u147 = {
        ["CurrentIndex"] = nil
    }
    Instance.new("BindableEvent").Name = "IndexChanged"
    if type(p140) ~= "table" then
        error("CreateDropDown dropDownStringTable (first arg) is not a table", 2)
        return u147
    end
    local u148 = Instance.new("BindableEvent")
    u148.Name = "IndexChanged"
    local u149 = true
    local u150 = u6:GenerateGUID(false)
    local u151 = nil
    local u152 = p140
    local u153 = u22.Create("ImageButton")({
        ["Name"] = "DropDownFullscreenFrame",
        ["BackgroundTransparency"] = 0.2,
        ["BorderSizePixel"] = 0,
        ["Size"] = UDim2.new(1, 0, 1, 0),
        ["BackgroundColor3"] = Color3.fromRGB(0, 0, 0),
        ["ZIndex"] = 10,
        ["Active"] = true,
        ["Visible"] = false,
        ["Selectable"] = false,
        ["AutoButtonColor"] = false,
        ["Parent"] = u10.RobloxGui
    })
    local function v155(p154) --[[Anonymous function at line 592]]
        --[[
        Upvalues:
            [1] = u12
            [2] = u10
            [3] = u153
            [4] = u147
            [5] = u152
        --]]
        if p154 == "VREnabled" then
            if u12.VREnabled then
                u153.Parent = require(u10.RobloxGui.Modules.VR.Panel3D).Get("SettingsMenu"):GetGUI()
                u153.BackgroundTransparency = 1
            else
                u153.Parent = u10.RobloxGui
                u153.BackgroundTransparency = 0.2
            end
            if u147.UpdateDropDownList then
                u147:UpdateDropDownList(u152)
            end
        end
    end
    u12.Changed:Connect(v155)
    v155("VREnabled")
    local u156 = u22.Create("ImageLabel")({
        ["Name"] = "DropDownSelectionFrame",
        ["Image"] = "rbxasset://textures/ui/Settings/MenuBarAssets/MenuButton.png",
        ["ScaleType"] = Enum.ScaleType.Slice,
        ["SliceCenter"] = Rect.new(8, 6, 46, 44),
        ["BackgroundTransparency"] = 1,
        ["Size"] = UDim2.new(0.6, 0, 0.9, 0),
        ["Position"] = UDim2.new(0.5, 0, 0.5, 0),
        ["AnchorPoint"] = Vector2.new(0.5, 0.5),
        ["ZIndex"] = 10,
        ["Parent"] = u153
    })
    local u157 = u22.Create("ScrollingFrame")({
        ["Name"] = "DropDownScrollingFrame",
        ["BackgroundTransparency"] = 1,
        ["BorderSizePixel"] = 0,
        ["Size"] = UDim2.new(1, -20, 1, -25),
        ["Position"] = UDim2.new(0, 10, 0, 10),
        ["ZIndex"] = 10,
        ["Parent"] = u156
    })
    local u158 = nil
    local u159 = false
    local function u163(p160, p161) --[[Anonymous function at line 640]]
        --[[
        Upvalues:
            [1] = u147
            [2] = u149
            [3] = u142
            [4] = u153
            [5] = u12
            [6] = u7
            [7] = u8
            [8] = u146
            [9] = u158
            [10] = u11
            [11] = u150
            [12] = u151
            [13] = u159
            [14] = u10
        --]]
        if p160 == nil or p161 == Enum.UserInputState.Begin then
            u147.DropDownFrame.Selectable = u149
            u142:SetActive(true)
            if u153.Visible then
                local v162
                if u12.VREnabled then
                    v162 = false
                else
                    v162 = (not u7.TouchEnabled or u7.GamepadEnabled) and true or false
                end
                if v162 then
                    u8.SelectedCoreObject = u146
                end
            end
            u153.Visible = false
            if u158 then
                u158:Disconnect()
            end
            u11:UnbindAction(u150 .. "Action")
            u11:UnbindAction(u150 .. "FreezeAction")
            u151.Value = u149
            u159 = false
            if u12.VREnabled then
                require(u10.RobloxGui.Modules.VR.Panel3D).Get("SettingsMenu"):SetSubpanelDepth(u153, 0)
            end
        end
    end
    local function u164() --[[Anonymous function at line 664]] end
    local function v166() --[[Anonymous function at line 666]]
        --[[
        Upvalues:
            [1] = u149
            [2] = u147
            [3] = u159
            [4] = u153
            [5] = u12
            [6] = u10
            [7] = u146
            [8] = u8
            [9] = u158
            [10] = u145
            [11] = u144
            [12] = u143
            [13] = u11
            [14] = u150
            [15] = u164
            [16] = u163
            [17] = u142
            [18] = u151
        --]]
        if u149 then
            u147.DropDownFrame.Selectable = false
            u159 = true
            u153.Visible = true
            if u12.VREnabled then
                require(u10.RobloxGui.Modules.VR.Panel3D).Get("SettingsMenu"):SetSubpanelDepth(u153, 0.5)
            end
            u146 = u147.DropDownFrame
            if u147.CurrentIndex and u147.CurrentIndex > 0 then
                u8.SelectedCoreObject = u147.Selections[u147.CurrentIndex]
            end
            u158 = u8:GetPropertyChangedSignal("SelectedCoreObject"):Connect(function() --[[Anonymous function at line 683]]
                --[[
                Upvalues:
                    [1] = u147
                    [2] = u8
                    [3] = u145
                    [4] = u12
                    [5] = u144
                    [6] = u143
                --]]
                for v165 = 1, #u147.Selections do
                    if u8.SelectedCoreObject == u147.Selections[v165] then
                        u147.Selections[v165].TextColor3 = u145
                    else
                        u147.Selections[v165].TextColor3 = u12.VREnabled and u144 or u143
                    end
                end
            end)
            u11:BindActionAtPriority(u150 .. "FreezeAction", u164, false, Enum.ContextActionPriority.High.Value, Enum.UserInputType.Keyboard, Enum.UserInputType.Gamepad1)
            u11:BindActionAtPriority(u150 .. "Action", u163, false, Enum.ContextActionPriority.High.Value, Enum.KeyCode.ButtonB, Enum.KeyCode.Escape)
            u142:SetActive(false)
            u151.Value = false
        end
    end
    u147.DropDownFrame = u118("DropDownFrame", "Choose One", UDim2.new(0.6, 0, 0, 50), v166, nil, u142)
    u147.DropDownFrame.Position = UDim2.new(1, 0, 0.5, 0)
    u147.DropDownFrame.AnchorPoint = Vector2.new(1, 0.5)
    u151 = u147.DropDownFrame.Enabled
    local u167 = u147.DropDownFrame.DropDownFrameTextLabel
    u167.Position = UDim2.new(0, 15, 0, 0)
    u167.Size = UDim2.new(1, -50, 1, -8)
    u167.ClipsDescendants = true
    u167.TextXAlignment = Enum.TextXAlignment.Left
    local u168 = u22.Create("ImageLabel")({
        ["Name"] = "DropDownImage",
        ["Image"] = "rbxasset://textures/ui/Settings/DropDown/DropDown.png",
        ["BackgroundTransparency"] = 1,
        ["AnchorPoint"] = Vector2.new(1, 0.5),
        ["Size"] = UDim2.new(0, 15, 0, 10),
        ["Position"] = UDim2.new(1, -12, 0.5, 0),
        ["ZIndex"] = 2,
        ["Parent"] = u147.DropDownFrame
    })
    u147.DropDownImage = u168
    local function u173(p169) --[[Anonymous function at line 727]]
        --[[
        Upvalues:
            [1] = u147
            [2] = u167
            [3] = u148
        --]]
        local v170 = false
        for v171, v172 in pairs(u147.Selections) do
            if v171 == p169 then
                u167.Text = v172.Text
                u147.CurrentIndex = v171
                v170 = true
            end
        end
        if v170 then
            u148:Fire(p169)
        end
    end
    local function u178(p174) --[[Anonymous function at line 743]]
        --[[
        Upvalues:
            [1] = u147
            [2] = u167
            [3] = u148
        --]]
        local v175 = false
        for v176, v177 in pairs(u147.Selections) do
            if v177.Text == p174 then
                u167.Text = v177.Text
                u147.CurrentIndex = v176
                v175 = true
            end
        end
        if v175 then
            u148:Fire(u147.CurrentIndex)
        end
        return v175
    end
    local _ = false
    u147.IndexChanged = u148.Event
    function u147.SetSelectionIndex(_, p179) --[[Anonymous function at line 797]]
        --[[
        Upvalues:
            [1] = u173
        --]]
        u173(p179)
    end
    function u147.SetSelectionByValue(_, p180) --[[Anonymous function at line 801]]
        --[[
        Upvalues:
            [1] = u178
        --]]
        return u178(p180)
    end
    function u147.ResetSelectionIndex(_) --[[Anonymous function at line 805]]
        --[[
        Upvalues:
            [1] = u147
            [2] = u167
            [3] = u163
        --]]
        u147.CurrentIndex = nil
        u167.Text = "Choose One"
        u163()
    end
    function u147.GetSelectedIndex(_) --[[Anonymous function at line 811]]
        --[[
        Upvalues:
            [1] = u147
        --]]
        return u147.CurrentIndex
    end
    function u147.SetZIndex(_, p181) --[[Anonymous function at line 815]]
        --[[
        Upvalues:
            [1] = u147
            [2] = u168
            [3] = u167
        --]]
        u147.DropDownFrame.ZIndex = p181
        u168.ZIndex = p181
        u167.ZIndex = p181
    end
    function u147.SetInteractable(_, p182) --[[Anonymous function at line 821]]
        --[[
        Upvalues:
            [1] = u149
            [2] = u147
            [3] = u163
            [4] = u12
            [5] = u151
            [6] = u159
        --]]
        u149 = p182
        u147.DropDownFrame.Selectable = u149
        if u149 then
            u147.DropDownFrame.DropDownFrameTextLabel.TextTransparency = 0
            u147.DropDownFrame.ImageTransparency = 0
            u147.DropDownImage.ImageTransparency = 0
            if not u12.VREnabled then
                u147:SetZIndex(2)
            end
        else
            u163()
            if u12.VREnabled then
                u147.DropDownFrame.DropDownFrameTextLabel.TextTransparency = 0.5
                u147.DropDownFrame.ImageTransparency = 0.5
                u147.DropDownImage.ImageTransparency = 0.5
            else
                u147.DropDownFrame.DropDownFrameTextLabel.TextTransparency = 0
                u147.DropDownFrame.ImageTransparency = 0
                u147.DropDownImage.ImageTransparency = 0
            end
            if not u12.VREnabled then
                u147:SetZIndex(1)
            end
        end
        local v183 = u151
        if p182 then
            p182 = not u159
        end
        v183.Value = p182
    end
    function u147.UpdateDropDownList(_, p184) --[[Anonymous function at line 842]]
        --[[
        Upvalues:
            [1] = u152
            [2] = u147
            [3] = u12
            [4] = u22
            [5] = u144
            [6] = u143
            [7] = u157
            [8] = u141
            [9] = u167
            [10] = u145
            [11] = u163
            [12] = u148
            [13] = u7
            [14] = u8
            [15] = u150
            [16] = u153
            [17] = u156
        --]]
        u152 = p184
        if u147.Selections then
            for v185 = 1, #u147.Selections do
                u147.Selections[v185]:Destroy()
            end
        end
        u147.Selections = {}
        u147.SelectionInfo = {}
        local v186 = u12.VREnabled
        local v187 = v186 and Enum.Font.SourceSansBold or Enum.Font.SourceSans
        local v188 = v186 and 70 or 50
        local v189 = v188 + 1
        local v190 = v186 and 36 or 24
        local u191 = v186 and 600 or 400
        for u192, v193 in pairs(p184) do
            local v194 = u22.Create("Frame")({
                ["BackgroundTransparency"] = 0.7,
                ["BorderSizePixel"] = 0,
                ["Size"] = UDim2.new(1, 0, 1, 0)
            })
            local u195 = u22.Create("TextButton")({
                ["Name"] = "Selection" .. tostring(u192),
                ["BackgroundTransparency"] = 1,
                ["BorderSizePixel"] = 0,
                ["AutoButtonColor"] = false,
                ["Size"] = UDim2.new(1, -28, 0, v188),
                ["Position"] = UDim2.new(0, 14, 0, (u192 - 1) * v189),
                ["TextColor3"] = u12.VREnabled and u144 or u143,
                ["Font"] = v187,
                ["TextSize"] = v190,
                ["Text"] = v193,
                ["ZIndex"] = 10,
                ["SelectionImageObject"] = v194,
                ["Parent"] = u157
            })
            if u192 == u141 then
                u147.CurrentIndex = u192
                u167.Text = v193
                u195.TextColor3 = u145
            elseif not u141 and u192 == 1 then
                u195.TextColor3 = u145
            end
            local function v196() --[[Anonymous function at line 896]]
                --[[
                Upvalues:
                    [1] = u167
                    [2] = u195
                    [3] = u163
                    [4] = u147
                    [5] = u192
                    [6] = u148
                --]]
                u167.Text = u195.Text
                u163()
                u147.CurrentIndex = u192
                u148:Fire(u192)
            end
            u195.MouseButton1Click:Connect(v196)
            u195.MouseEnter:Connect(function() --[[Anonymous function at line 905]]
                --[[
                Upvalues:
                    [1] = u12
                    [2] = u7
                    [3] = u8
                    [4] = u195
                --]]
                local v197
                if u12.VREnabled then
                    v197 = false
                else
                    v197 = (not u7.TouchEnabled or u7.GamepadEnabled) and true or false
                end
                if v197 then
                    u8.SelectedCoreObject = u195
                end
            end)
            u147.Selections[u192] = u195
            u147.SelectionInfo[u195] = {
                ["Clicked"] = v196
            }
        end
        u8:RemoveSelectionGroup(u150)
        local v198 = u8
        local v199 = u150
        local v200 = u147.Selections
        v198:AddSelectionTuple(v199, unpack(v200))
        u157.CanvasSize = UDim2.new(1, -20, 0, #p184 * v189)
        local function u201() --[[Anonymous function at line 920]]
            --[[
            Upvalues:
                [1] = u157
                [2] = u153
                [3] = u156
                [4] = u191
            --]]
            if u157.CanvasSize.Y.Offset < u153.AbsoluteSize.Y - 10 then
                u156.Size = UDim2.new(0, u191, 0, u157.CanvasSize.Y.Offset + 25)
            else
                u156.Size = UDim2.new(0, u191, 0.9, 0)
            end
        end
        u153.Changed:Connect(function(p202) --[[Anonymous function at line 929]]
            --[[
            Upvalues:
                [1] = u201
            --]]
            if p202 == "AbsoluteSize" then
                u201()
            end
        end)
        u201()
    end
    u147:UpdateDropDownList(p140)
    u153.MouseButton1Click:Connect(u163)
    u142.PoppedMenu:Connect(function(p203) --[[Anonymous function at line 942]]
        --[[
        Upvalues:
            [1] = u153
            [2] = u163
        --]]
        if p203 == u153 then
            u163()
        end
    end)
    return u147
end
local function u269(p205, u206) --[[Anonymous function at line 952]]
    --[[
    Upvalues:
        [1] = u22
        [2] = u24
        [3] = u3
        [4] = u7
        [5] = u4
        [6] = u78
        [7] = u12
        [8] = u8
        [9] = u42
        [10] = u5
        [11] = u23
        [12] = u71
    --]]
    local u207 = 0
    local u208 = {
        ["HubRef"] = nil
    }
    if type(p205) ~= "table" then
        error("CreateSelector selectionStringTable (first arg) is not a table", 2)
        return u208
    end
    local u209 = Instance.new("BindableEvent")
    u209.Name = "IndexChanged"
    local u210 = true
    u208.CurrentIndex = 0
    u208.SelectorFrame = u22.Create("ImageButton")({
        ["Name"] = "Selector",
        ["Image"] = "",
        ["AutoButtonColor"] = false,
        ["NextSelectionLeft"] = u208.SelectorFrame,
        ["NextSelectionRight"] = u208.SelectorFrame,
        ["BackgroundTransparency"] = 1,
        ["Size"] = UDim2.new(0.6, 0, 0, 50),
        ["Position"] = UDim2.new(1, 0, 0.5, 0),
        ["AnchorPoint"] = Vector2.new(1, 0.5),
        ["ZIndex"] = 2,
        ["SelectionImageObject"] = u24
    })
    local u211 = u22.Create("ImageButton")({
        ["Name"] = "LeftButton",
        ["BackgroundTransparency"] = 1,
        ["AnchorPoint"] = Vector2.new(0, 0.5),
        ["Position"] = UDim2.new(0, 0, 0.5, 0),
        ["Size"] = UDim2.new(0, 50, 0, 50),
        ["Image"] = "",
        ["ZIndex"] = 3,
        ["Selectable"] = false,
        ["SelectionImageObject"] = u24,
        ["Parent"] = u208.SelectorFrame
    })
    local u212 = u22.Create("ImageButton")({
        ["Name"] = "RightButton",
        ["BackgroundTransparency"] = 1,
        ["AnchorPoint"] = Vector2.new(1, 0.5),
        ["Position"] = UDim2.new(1, 0, 0.5, 0),
        ["Size"] = UDim2.new(0, 50, 0, 50),
        ["Image"] = "",
        ["ZIndex"] = 3,
        ["Selectable"] = false,
        ["SelectionImageObject"] = u24,
        ["Parent"] = u208.SelectorFrame
    })
    local u213 = u22.Create("ImageLabel")({
        ["Name"] = "LeftButton",
        ["BackgroundTransparency"] = 1,
        ["AnchorPoint"] = Vector2.new(0.5, 0.5),
        ["Position"] = UDim2.new(0.5, 0, 0.5, 0),
        ["Size"] = UDim2.new(0, 18, 0, 30),
        ["Image"] = "rbxasset://textures/ui/Settings/Slider/Left.png",
        ["ImageColor3"] = u3,
        ["ZIndex"] = 4,
        ["Parent"] = u211
    })
    local u214 = u22.Create("ImageLabel")({
        ["Name"] = "RightButton",
        ["BackgroundTransparency"] = 1,
        ["AnchorPoint"] = Vector2.new(0.5, 0.5),
        ["Position"] = UDim2.new(0.5, 0, 0.5, 0),
        ["Size"] = UDim2.new(0, 18, 0, 30),
        ["Image"] = "rbxasset://textures/ui/Settings/Slider/Right.png",
        ["ImageColor3"] = u3,
        ["ZIndex"] = 4,
        ["Parent"] = u212
    })
    if not u7.TouchEnabled then
        local function v216(p215) --[[Anonymous function at line 1043]]
            --[[
            Upvalues:
                [1] = u3
            --]]
            p215.ImageColor3 = u3
        end
        local function v218(p217) --[[Anonymous function at line 1044]]
            --[[
            Upvalues:
                [1] = u4
            --]]
            p217.ImageColor3 = u4
        end
        u78(u211, u213, v216, v218)
        u78(u212, u214, v216, v218)
    end
    u208.Selections = {}
    local u219 = {}
    local u220 = {}
    local u221 = u22.Create("ImageButton")({
        ["Name"] = "AutoSelectButton",
        ["BackgroundTransparency"] = 1,
        ["Image"] = "",
        ["Position"] = UDim2.new(0, u211.Size.X.Offset, 0, 0),
        ["Size"] = UDim2.new(1, u211.Size.X.Offset * -2, 1, 0),
        ["Parent"] = u208.SelectorFrame,
        ["ZIndex"] = 2,
        ["SelectionImageObject"] = u24
    })
    u221.MouseButton1Click:Connect(function() --[[Anonymous function at line 1064]]
        --[[
        Upvalues:
            [1] = u210
            [2] = u208
            [3] = u12
            [4] = u7
            [5] = u8
        --]]
        if u210 then
            if #u208.Selections > 1 then
                local v222 = u208.CurrentIndex + 1
                u208:SetSelectionIndex(#u208.Selections < v222 and 1 or v222)
                local v223
                if u12.VREnabled then
                    v223 = false
                else
                    v223 = (not u7.TouchEnabled or u7.GamepadEnabled) and true or false
                end
                if v223 then
                    u8.SelectedCoreObject = u208.SelectorFrame
                end
            end
        else
            return
        end
    end)
    u220[u221] = true
    local function u231(p224, p225) --[[Anonymous function at line 1079]]
        --[[
        Upvalues:
            [1] = u208
            [2] = u211
            [3] = u219
            [4] = u42
            [5] = u209
        --]]
        for v226, v227 in pairs(u208.Selections) do
            local v228 = v226 == p224
            local v229 = UDim2.new(0, u211.Size.X.Offset, 0, 0)
            local v230 = UDim2.new(0, u211.Size.X.Offset * p225 * 3, 0, 0)
            if u219[v227] then
                v230 = UDim2.new(0, u211.Size.X.Offset * -p225 * 3, 0, 0)
            end
            if v230.X.Offset < 0 then
                v230 = UDim2.new(0, v230.X.Offset + v227.AbsoluteSize.X / 4, 0, 0)
            end
            if v228 then
                u219[v227] = true
                v227.Position = v230
                v227.Visible = true
                PropertyTweener(v227, "TextTransparency", 1, 0, 0.165, u42)
                if v227:IsDescendantOf(game) then
                    v227:TweenPosition(v229, Enum.EasingDirection.In, Enum.EasingStyle.Quad, 0.15, true)
                else
                    v227.Position = v229
                end
                u208.CurrentIndex = v226
                u209:Fire(p224)
            elseif u219[v227] then
                u219[v227] = false
                PropertyTweener(v227, "TextTransparency", 0, 1, 0.165, u42)
                if v227:IsDescendantOf(game) then
                    v227:TweenPosition(v230, Enum.EasingDirection.Out, Enum.EasingStyle.Quad, 0.135, true)
                else
                    v227.Position = UDim2.new(v230)
                end
            end
        end
    end
    local function u237(p232, p233) --[[Anonymous function at line 1118]]
        --[[
        Upvalues:
            [1] = u210
            [2] = u12
            [3] = u7
            [4] = u8
            [5] = u208
            [6] = u231
        --]]
        if u210 then
            if p232 == nil or (p232.UserInputType == Enum.UserInputType.MouseButton1 or (p232.UserInputType == Enum.UserInputType.Gamepad1 or (p232.UserInputType == Enum.UserInputType.Gamepad2 or (p232.UserInputType == Enum.UserInputType.Gamepad3 or (p232.UserInputType == Enum.UserInputType.Gamepad4 or p232.UserInputType == Enum.UserInputType.Keyboard))))) then
                local v234
                if u12.VREnabled then
                    v234 = false
                else
                    v234 = (not u7.TouchEnabled or u7.GamepadEnabled) and true or false
                end
                if v234 then
                    u8.SelectedCoreObject = u208.SelectorFrame
                end
                local v235 = p233 + u208.CurrentIndex
                local v236 = u208.CurrentIndex < v235 and 1 or -1
                u231(#u208.Selections < v235 and 1 or (v235 < 1 and #u208.Selections or v235), v236)
            end
        else
            return
        end
    end
    local u238 = nil
    u208.IndexChanged = u209.Event
    function u208.SetSelectionIndex(_, p239) --[[Anonymous function at line 1174]]
        --[[
        Upvalues:
            [1] = u231
        --]]
        u231(p239, 1)
    end
    function u208.GetSelectedIndex(_) --[[Anonymous function at line 1178]]
        --[[
        Upvalues:
            [1] = u208
        --]]
        return u208.CurrentIndex
    end
    function u208.SetZIndex(_, p240) --[[Anonymous function at line 1182]]
        --[[
        Upvalues:
            [1] = u211
            [2] = u212
            [3] = u213
            [4] = u214
            [5] = u208
        --]]
        u211.ZIndex = p240
        u212.ZIndex = p240
        u213.ZIndex = p240
        u214.ZIndex = p240
        for v241 = 1, #u208.Selections do
            u208.Selections[v241].ZIndex = p240
        end
    end
    function u208.SetInteractable(_, p242) --[[Anonymous function at line 1193]]
        --[[
        Upvalues:
            [1] = u210
            [2] = u208
            [3] = u211
            [4] = u212
            [5] = u213
            [6] = u5
            [7] = u214
            [8] = u3
        --]]
        u210 = p242
        u208.SelectorFrame.Selectable = u210
        u211.Active = u210
        u212.Active = u210
        if u210 then
            for _, v243 in pairs(u208.Selections) do
                v243.TextColor3 = Color3.fromRGB(255, 255, 255)
            end
            u213.ImageColor3 = u3
            u214.ImageColor3 = u3
        else
            for _, v244 in pairs(u208.Selections) do
                v244.TextColor3 = Color3.fromRGB(49, 49, 49)
            end
            u213.ImageColor3 = u5
            u214.ImageColor3 = u5
        end
    end
    function u208.UpdateOptions(_, p245) --[[Anonymous function at line 1215]]
        --[[
        Upvalues:
            [1] = u208
            [2] = u219
            [3] = u22
            [4] = u211
            [5] = u206
            [6] = u212
        --]]
        for _, v246 in pairs(u208.Selections) do
            v246:Destroy()
        end
        u219 = {}
        u208.Selections = {}
        for v247, v248 in pairs(p245) do
            local v249 = u22.Create("TextLabel")({
                ["Name"] = "Selection" .. tostring(v247),
                ["BackgroundTransparency"] = 1,
                ["BorderSizePixel"] = 0,
                ["Size"] = UDim2.new(1, u211.Size.X.Offset * -2, 1, 0),
                ["Position"] = UDim2.new(1, 0, 0, 0),
                ["TextColor3"] = Color3.fromRGB(255, 255, 255),
                ["TextYAlignment"] = Enum.TextYAlignment.Center,
                ["TextTransparency"] = 0.5,
                ["Font"] = Enum.Font.SourceSans,
                ["TextSize"] = 24,
                ["Text"] = v248,
                ["ZIndex"] = 2,
                ["Visible"] = false,
                ["Parent"] = u208.SelectorFrame
            })
            if false then
                v249.TextSize = 36
            end
            if v247 == u206 then
                u208.CurrentIndex = v247
                v249.Position = UDim2.new(0, u211.Size.X.Offset, 0, 0)
                v249.Visible = true
                u219[v249] = true
            else
                u219[v249] = false
            end
            u208.Selections[v247] = v249
        end
        local v250 = #u208.Selections > 1
        u211.Visible = v250
        u212.Visible = v250
    end
    local function v253(p251) --[[Anonymous function at line 1264]]
        --[[
        Upvalues:
            [1] = u12
            [2] = u211
            [3] = u212
            [4] = u221
        --]]
        if p251 == "VREnabled" then
            local v252 = u12.VREnabled
            u211.Selectable = v252
            u212.Selectable = v252
            u221.Selectable = v252
        end
    end
    u12.Changed:Connect(v253)
    local v254 = u12.VREnabled
    u211.Selectable = v254
    u212.Selectable = v254
    u221.Selectable = v254
    u211.InputBegan:Connect(function(p255) --[[Anonymous function at line 1276]]
        --[[
        Upvalues:
            [1] = u237
        --]]
        if p255.UserInputType == Enum.UserInputType.Touch then
            u237(nil, -1)
        end
    end)
    u211.MouseButton1Click:Connect(function() --[[Anonymous function at line 1281]]
        --[[
        Upvalues:
            [1] = u7
            [2] = u237
        --]]
        if not u7.TouchEnabled then
            u237(nil, -1)
        end
    end)
    u212.InputBegan:Connect(function(p256) --[[Anonymous function at line 1286]]
        --[[
        Upvalues:
            [1] = u237
        --]]
        if p256.UserInputType == Enum.UserInputType.Touch then
            u237(nil, 1)
        end
    end)
    u212.MouseButton1Click:Connect(function() --[[Anonymous function at line 1291]]
        --[[
        Upvalues:
            [1] = u7
            [2] = u237
        --]]
        if not u7.TouchEnabled then
            u237(nil, 1)
        end
    end)
    local u257 = true
    u208:UpdateOptions(p205)
    u7.InputBegan:Connect(function(p258) --[[Anonymous function at line 1300]]
        --[[
        Upvalues:
            [1] = u210
            [2] = u257
            [3] = u8
            [4] = u208
            [5] = u237
        --]]
        if u210 then
            if u257 then
                if p258.UserInputType == Enum.UserInputType.Gamepad1 or p258.UserInputType == Enum.UserInputType.Keyboard then
                    if u8.SelectedCoreObject == u208.SelectorFrame then
                        if p258.KeyCode == Enum.KeyCode.DPadLeft or (p258.KeyCode == Enum.KeyCode.Left or p258.KeyCode == Enum.KeyCode.A) then
                            u237(p258, -1)
                        elseif p258.KeyCode == Enum.KeyCode.DPadRight or (p258.KeyCode == Enum.KeyCode.Right or p258.KeyCode == Enum.KeyCode.D) then
                            u237(p258, 1)
                        end
                    else
                        return
                    end
                else
                    return
                end
            else
                return
            end
        else
            return
        end
    end)
    u7.InputChanged:Connect(function(p259) --[[Anonymous function at line 1314]]
        --[[
        Upvalues:
            [1] = u210
            [2] = u257
            [3] = u207
            [4] = u8
            [5] = u208
            [6] = u237
        --]]
        if u210 then
            if u257 then
                if p259.UserInputType == Enum.UserInputType.Gamepad1 then
                    local v260 = u8.SelectedCoreObject
                    if v260 and v260:IsDescendantOf(u208.SelectorFrame.Parent) then
                        if p259.KeyCode == Enum.KeyCode.Thumbstick1 then
                            if p259.Position.X > 0.8 and (p259.Delta.X > 0 and u207 ~= 1) then
                                u207 = 1
                                u237(p259, u207)
                                return
                            elseif p259.Position.X < -0.8 and (p259.Delta.X < 0 and u207 ~= -1) then
                                u207 = -1
                                u237(p259, u207)
                            else
                                local v261 = p259.Position.X
                                if math.abs(v261) < 0.8 then
                                    u207 = 0
                                end
                            end
                        else
                            return
                        end
                    else
                        return
                    end
                else
                    return
                end
            else
                u207 = 0
                return
            end
        else
            return
        end
    end)
    u208.SelectorFrame.AncestryChanged:Connect(function(_, p262) --[[Anonymous function at line 1337]]
        --[[
        Upvalues:
            [1] = u257
            [2] = u238
            [3] = u8
            [4] = u208
            [5] = u220
            [6] = u12
        --]]
        u257 = p262
        if u257 then
            u238 = u8:GetPropertyChangedSignal("SelectedCoreObject"):Connect(function() --[[Anonymous function at line 1150]]
                --[[
                Upvalues:
                    [1] = u208
                    [2] = u8
                    [3] = u220
                    [4] = u12
                --]]
                if #u208.Selections <= 0 then
                    return
                elseif u8.SelectedCoreObject == u208.SelectorFrame then
                    u208.Selections[u208.CurrentIndex].TextTransparency = 0
                    return
                elseif u8.SelectedCoreObject == nil or not u220[u8.SelectedCoreObject] then
                    u208.Selections[u208.CurrentIndex].TextTransparency = 0.5
                    return
                elseif u12.VREnabled then
                    u208.Selections[u208.CurrentIndex].TextTransparency = 0
                else
                    u8.SelectedCoreObject = u208.SelectorFrame
                end
            end)
        elseif u238 then
            u238:Disconnect()
            return
        end
    end)
    local function v266(_, p263) --[[Anonymous function at line 1346]]
        --[[
        Upvalues:
            [1] = u208
        --]]
        local v264 = p263 and 16 or 24
        for _, v265 in pairs(u208.Selections) do
            v265.TextSize = v264
        end
    end
    u23[u208.SelectorFrame] = v266
    local v267 = u71()
    local v268 = u71()
    v266(v267, v268.Y > v268.X)
    u238 = u8:GetPropertyChangedSignal("SelectedCoreObject"):Connect(function() --[[Anonymous function at line 1150]]
        --[[
        Upvalues:
            [1] = u208
            [2] = u8
            [3] = u220
            [4] = u12
        --]]
        if #u208.Selections <= 0 then
            return
        elseif u8.SelectedCoreObject == u208.SelectorFrame then
            u208.Selections[u208.CurrentIndex].TextTransparency = 0
            return
        elseif u8.SelectedCoreObject == nil or not u220[u8.SelectedCoreObject] then
            u208.Selections[u208.CurrentIndex].TextTransparency = 0.5
            return
        elseif u12.VREnabled then
            u208.Selections[u208.CurrentIndex].TextTransparency = 0
        else
            u8.SelectedCoreObject = u208.SelectorFrame
        end
    end)
    return u208
end
local function u289(p270, p271, u272, u273, p274) --[[Anonymous function at line 1365]]
    --[[
    Upvalues:
        [1] = u10
        [2] = u12
        [3] = u22
        [4] = u6
        [5] = u11
        [6] = u8
        [7] = u118
        [8] = u7
    --]]
    local u275 = u10.RobloxGui
    if not u275:FindFirstChild("AlertViewFullScreen") then
        local u276 = nil
        local function v279(p277) --[[Anonymous function at line 1374]]
            --[[
            Upvalues:
                [1] = u12
                [2] = u10
                [3] = u275
                [4] = u276
            --]]
            if p277 == "VREnabled" then
                local v278 = nil
                if u12.VREnabled then
                    v278 = require(u10.RobloxGui.Modules.VR.Panel3D).Get("SettingsMenu")
                    u275 = v278:GetGUI()
                else
                    u275 = u10.RobloxGui
                end
                if u276 and u276.Parent ~= nil then
                    u276.Parent = u275
                    if u12.VREnabled then
                        v278:SetSubpanelDepth(u276, 0.5)
                    end
                end
            end
        end
        local u280 = u12.Changed:Connect(v279)
        Color3.fromRGB(59, 166, 241)
        Color3.fromRGB(255, 255, 255)
        u276 = u22.Create("ImageLabel")({
            ["Name"] = "AlertViewBacking",
            ["Image"] = "rbxasset://textures/ui/Settings/MenuBarAssets/MenuButton.png",
            ["ScaleType"] = Enum.ScaleType.Slice,
            ["SliceCenter"] = Rect.new(8, 6, 46, 44),
            ["BackgroundTransparency"] = 1,
            ["ImageTransparency"] = 1,
            ["Size"] = UDim2.new(0, 400, 0, 350),
            ["Position"] = UDim2.new(0.5, -200, 0.5, -175),
            ["ZIndex"] = 9,
            ["Parent"] = u275
        })
        v279("VREnabled")
        if p274 or u12.VREnabled then
            u276.ImageTransparency = 0
        else
            u276.Size = UDim2.new(0.8, 0, 0, 350)
            u276.Position = UDim2.new(0.1, 0, 0.1, 0)
        end
        if u10.RobloxGui.AbsoluteSize.Y <= u276.Size.Y.Offset then
            u276.Size = UDim2.new(u276.Size.X.Scale, u276.Size.X.Offset, u276.Size.Y.Scale, u10.RobloxGui.AbsoluteSize.Y)
            u276.Position = UDim2.new(u276.Position.X.Scale, -u276.Size.X.Offset / 2, 0.5, -u276.Size.Y.Offset / 2)
        end
        u22.Create("TextLabel")({
            ["Name"] = "AlertViewText",
            ["BackgroundTransparency"] = 1,
            ["Size"] = UDim2.new(0.95, 0, 0.6, 0),
            ["Position"] = UDim2.new(0.025, 0, 0.05, 0),
            ["Font"] = Enum.Font.SourceSansBold,
            ["TextSize"] = 36,
            ["Text"] = p270,
            ["TextWrapped"] = true,
            ["TextColor3"] = Color3.fromRGB(255, 255, 255),
            ["TextXAlignment"] = Enum.TextXAlignment.Center,
            ["TextYAlignment"] = Enum.TextYAlignment.Center,
            ["ZIndex"] = 10,
            ["Parent"] = u276
        })
        u22.Create("ImageLabel")({
            ["Image"] = "",
            ["BackgroundTransparency"] = 1
        })
        local u281 = u6:GenerateGUID(false)
        local function v283(_, p282) --[[Anonymous function at line 1449]]
            --[[
            Upvalues:
                [1] = u12
                [2] = u276
                [3] = u10
                [4] = u273
                [5] = u11
                [6] = u281
                [7] = u8
                [8] = u272
                [9] = u280
            --]]
            if u12.VREnabled and (p282 == Enum.UserInputState.Begin or p282 == Enum.UserInputState.Cancel) then
                return
            elseif u276 then
                if u12.VREnabled then
                    require(u10.RobloxGui.Modules.VR.Panel3D).Get("SettingsMenu"):SetSubpanelDepth(u276, 0)
                end
                u276:Destroy()
                u276 = nil
                if u273 then
                    u273()
                end
                u11:UnbindAction(u281)
                u8.SelectedCoreObject = nil
                if u272 then
                    u272:ShowBar()
                end
                if u280 then
                    u280:Disconnect()
                end
            end
        end
        local v284 = UDim2.new(1, -20, 0, 60)
        local v285 = UDim2.new(0, 10, 0.65, 0)
        if not p274 then
            v284 = UDim2.new(0, 200, 0, 50)
            v285 = UDim2.new(0.5, -100, 0.65, 0)
        end
        local v286, v287 = u118("AlertViewButton", p271, v284, v283)
        v286.Position = v285
        v286.NextSelectionLeft = v286
        v286.NextSelectionRight = v286
        v286.NextSelectionUp = v286
        v286.NextSelectionDown = v286
        v286.ZIndex = 9
        v287.ZIndex = v286.ZIndex
        v286.Parent = u276
        local v288
        if u12.VREnabled then
            v288 = false
        else
            v288 = (not u7.TouchEnabled or u7.GamepadEnabled) and true or false
        end
        if v288 then
            u8.SelectedCoreObject = v286
        end
        u8.SelectedCoreObject = v286
        u11:BindActionAtPriority(u281, v283, false, Enum.ContextActionPriority.High.Value, Enum.KeyCode.Escape, Enum.KeyCode.ButtonB, Enum.KeyCode.ButtonA)
        if u272 and not u12.VREnabled then
            u272:HideBar()
            u272.Pages.CurrentPage:Hide(1, 1)
        end
    end
end
local function u376(p290, p291, u292) --[[Anonymous function at line 1506]]
    --[[
    Upvalues:
        [1] = u6
        [2] = u22
        [3] = u24
        [4] = u7
        [5] = u4
        [6] = u3
        [7] = u4
        [8] = u78
        [9] = u71
        [10] = u1
        [11] = u2
        [12] = u12
        [13] = u8
        [14] = u9
    --]]
    local u293 = {}
    local u294 = tonumber(p290)
    local u295 = p291
    local u296 = 0
    local u297 = nil
    local u298 = true
    local u299 = u6:GenerateGUID(false)
    if u294 > 0 then
        local u300 = Instance.new("BindableEvent")
        u300.Name = "ValueChanged"
        u293.SliderFrame = u22.Create("ImageButton")({
            ["Name"] = "Slider",
            ["Image"] = "",
            ["AutoButtonColor"] = false,
            ["NextSelectionLeft"] = u293.SliderFrame,
            ["NextSelectionRight"] = u293.SliderFrame,
            ["BackgroundTransparency"] = 1,
            ["Size"] = UDim2.new(0.6, 0, 0, 50),
            ["Position"] = UDim2.new(1, 0, 0.5, 0),
            ["AnchorPoint"] = Vector2.new(1, 0.5),
            ["SelectionImageObject"] = u24,
            ["ZIndex"] = 2
        })
        u293.StepsContainer = u22.Create("Frame")({
            ["Name"] = "StepsContainer",
            ["Position"] = UDim2.new(0.5, 0, 0.5, 0),
            ["Size"] = UDim2.new(1, -100, 1, 0),
            ["AnchorPoint"] = Vector2.new(0.5, 0.5),
            ["BackgroundTransparency"] = 1,
            ["Parent"] = u293.SliderFrame
        })
        local u301 = u22.Create("ImageButton")({
            ["Name"] = "LeftButton",
            ["BackgroundTransparency"] = 1,
            ["AnchorPoint"] = Vector2.new(0, 0.5),
            ["Position"] = UDim2.new(0, 0, 0.5, 0),
            ["Size"] = UDim2.new(0, 50, 0, 50),
            ["Image"] = "",
            ["ZIndex"] = 3,
            ["Selectable"] = false,
            ["SelectionImageObject"] = u24,
            ["Active"] = true,
            ["Parent"] = u293.SliderFrame
        })
        local u302 = u22.Create("ImageButton")({
            ["Name"] = "RightButton",
            ["BackgroundTransparency"] = 1,
            ["AnchorPoint"] = Vector2.new(1, 0.5),
            ["Position"] = UDim2.new(1, 0, 0.5, 0),
            ["Size"] = UDim2.new(0, 50, 0, 50),
            ["Image"] = "",
            ["ZIndex"] = 3,
            ["Selectable"] = false,
            ["SelectionImageObject"] = u24,
            ["Active"] = true,
            ["Parent"] = u293.SliderFrame
        })
        local u303 = u22.Create("ImageLabel")({
            ["Name"] = "LeftButton",
            ["BackgroundTransparency"] = 1,
            ["AnchorPoint"] = Vector2.new(0.5, 0.5),
            ["Position"] = UDim2.new(0.5, 0, 0.5, 0),
            ["Size"] = UDim2.new(0, 30, 0, 30),
            ["Image"] = "rbxasset://textures/ui/Settings/Slider/Less.png",
            ["ZIndex"] = 4,
            ["Parent"] = u301,
            ["ImageColor3"] = u7.TouchEnabled and u4 or u3
        })
        local u304 = u22.Create("ImageLabel")({
            ["Name"] = "RightButton",
            ["BackgroundTransparency"] = 1,
            ["AnchorPoint"] = Vector2.new(0.5, 0.5),
            ["Position"] = UDim2.new(0.5, 0, 0.5, 0),
            ["Size"] = UDim2.new(0, 30, 0, 30),
            ["Image"] = "rbxasset://textures/ui/Settings/Slider/More.png",
            ["ZIndex"] = 4,
            ["Parent"] = u302,
            ["ImageColor3"] = u7.TouchEnabled and u4 or u3
        })
        if not u7.TouchEnabled then
            local function v306(p305) --[[Anonymous function at line 1615]]
                --[[
                Upvalues:
                    [1] = u3
                --]]
                p305.ImageColor3 = u3
            end
            local function v308(p307) --[[Anonymous function at line 1616]]
                --[[
                Upvalues:
                    [1] = u4
                --]]
                p307.ImageColor3 = u4
            end
            u78(u301, u303, v306, v308)
            u78(u302, u304, v306, v308)
        end
        u293.Steps = {}
        local v309 = u71()
        local v310 = u7.TouchEnabled
        if v310 then
            v310 = v309.Y < 500 and true or v309.X < 700
        end
        local v311 = 1 / u294
        for v312 = 1, u294 do
            local v313 = u22.Create("ImageButton")({
                ["Name"] = "Step" .. tostring(v312),
                ["BackgroundColor3"] = u1,
                ["BackgroundTransparency"] = 0.36,
                ["BorderSizePixel"] = 0,
                ["AutoButtonColor"] = false,
                ["Active"] = false,
                ["AnchorPoint"] = Vector2.new(0, 0.5),
                ["Position"] = UDim2.new((v312 - 1) * v311, 2, 0.5, 0),
                ["Size"] = UDim2.new(v311, -4, 0.48, 0),
                ["Image"] = "",
                ["ZIndex"] = 3,
                ["Selectable"] = false,
                ["ImageTransparency"] = 0.36,
                ["Parent"] = u293.StepsContainer,
                ["SelectionImageObject"] = u24
            })
            if u295 < v312 then
                v313.BackgroundColor3 = u2
            end
            if v312 == 1 or v312 == u294 then
                v313.BackgroundTransparency = 1
                v313.ScaleType = Enum.ScaleType.Slice
                v313.SliceCenter = Rect.new(3, 3, 32, 21)
                if v312 <= u295 then
                    if v312 == 1 then
                        v313.Image = "rbxasset://textures/ui/Settings/Slider/SelectedBarLeft.png"
                    else
                        v313.Image = "rbxasset://textures/ui/Settings/Slider/SelectedBarRight.png"
                    end
                elseif v312 == 1 then
                    v313.Image = "rbxasset://textures/ui/Settings/Slider/BarLeft.png"
                else
                    v313.Image = "rbxasset://textures/ui/Settings/Slider/BarRight.png"
                end
            end
            u293.Steps[#u293.Steps + 1] = v313
        end
        local function u315() --[[Anonymous function at line 1680]]
            --[[
            Upvalues:
                [1] = u294
                [2] = u293
                [3] = u2
            --]]
            for v314 = 1, u294 do
                u293.Steps[v314].BackgroundColor3 = u2
                if v314 == 1 then
                    u293.Steps[v314].Image = "rbxasset://textures/ui/Settings/Slider/BarLeft.png"
                elseif v314 == u294 then
                    u293.Steps[v314].Image = "rbxasset://textures/ui/Settings/Slider/BarRight.png"
                end
            end
        end
        local function u317() --[[Anonymous function at line 1690]]
            --[[
            Upvalues:
                [1] = u294
                [2] = u295
                [3] = u293
                [4] = u1
            --]]
            for v316 = 1, u294 do
                if u295 < v316 then
                    break
                end
                u293.Steps[v316].BackgroundColor3 = u1
                if v316 == 1 then
                    u293.Steps[v316].Image = "rbxasset://textures/ui/Settings/Slider/SelectedBarLeft.png"
                elseif v316 == u294 then
                    u293.Steps[v316].Image = "rbxasset://textures/ui/Settings/Slider/SelectedBarRight.png"
                end
            end
        end
        local function u319(p318) --[[Anonymous function at line 1711]]
            --[[
            Upvalues:
                [1] = u292
                [2] = u301
                [3] = u302
                [4] = u294
                [5] = u295
                [6] = u315
                [7] = u317
                [8] = u297
                [9] = u300
            --]]
            if not u292 then
                u292 = 0
            end
            u301.Visible = true
            u302.Visible = true
            if p318 <= u292 then
                p318 = u292
                u301.Visible = false
            end
            if u294 <= p318 then
                p318 = u294
                u302.Visible = false
            end
            if u295 ~= p318 then
                u295 = p318
                u315()
                u317()
                u297 = tick()
                u300:Fire(u295)
            end
        end
        local function u334(p320, p321, p322) --[[Anonymous function at line 1741]]
            --[[
            Upvalues:
                [1] = u298
                [2] = u12
                [3] = u7
                [4] = u8
                [5] = u293
                [6] = u296
                [7] = u295
                [8] = u294
                [9] = u319
            --]]
            if u298 then
                if p320 == nil then
                    return
                else
                    local v323
                    if p320 then
                        if p320.UserInputType == Enum.UserInputType.MouseButton1 or p320.UserInputType == Enum.UserInputType.Touch then
                            v323 = true
                        elseif p320.UserInputType == Enum.UserInputType.Gamepad1 then
                            v323 = p320.KeyCode == Enum.KeyCode.ButtonA
                        else
                            v323 = false
                        end
                    else
                        v323 = false
                    end
                    if v323 then
                        local v324
                        if u12.VREnabled then
                            v324 = false
                        else
                            v324 = (not u7.TouchEnabled or u7.GamepadEnabled) and true or false
                        end
                        if v324 and not u12.VREnabled then
                            u8.SelectedCoreObject = u293.SliderFrame
                        end
                        if u12.VREnabled then
                            u296 = 0
                        elseif p322 then
                            u296 = p321 - u295
                        else
                            u296 = 0
                            local u325 = nil
                            local u331 = u7.InputChanged:Connect(function(p326) --[[Anonymous function at line 1761]]
                                --[[
                                Upvalues:
                                    [1] = u294
                                    [2] = u293
                                    [3] = u319
                                --]]
                                if p326.UserInputType == Enum.UserInputType.MouseMovement then
                                    local v327 = p326.Position.X
                                    for v328 = 1, u294 do
                                        local v329 = u293.Steps[v328].AbsolutePosition.X
                                        local v330 = u293.Steps[v328].AbsoluteSize.X
                                        if v329 <= v327 and v327 <= v329 + v330 then
                                            u319(v328)
                                            return
                                        end
                                        if v328 == 1 and v327 < v329 then
                                            u319(0)
                                            return
                                        end
                                        if v328 == u294 and v329 <= v327 then
                                            u319(v328)
                                            return
                                        end
                                    end
                                end
                            end)
                            u325 = u7.InputEnded:Connect(function(p332) --[[Anonymous function at line 1780]]
                                --[[
                                Upvalues:
                                    [1] = u296
                                    [2] = u325
                                    [3] = u331
                                --]]
                                local v333
                                if p332 then
                                    if p332.UserInputType == Enum.UserInputType.MouseButton1 or p332.UserInputType == Enum.UserInputType.Touch then
                                        v333 = true
                                    elseif p332.UserInputType == Enum.UserInputType.Gamepad1 then
                                        v333 = p332.KeyCode == Enum.KeyCode.ButtonA
                                    else
                                        v333 = false
                                    end
                                else
                                    v333 = false
                                end
                                if v333 then
                                    u296 = 0
                                    u325:Disconnect()
                                    u331:Disconnect()
                                end
                            end)
                        end
                        u319(p321)
                    end
                end
            else
                return
            end
        end
        u293.ValueChanged = u300.Event
        function u293.SetValue(_, p335) --[[Anonymous function at line 1809]]
            --[[
            Upvalues:
                [1] = u319
            --]]
            u319(p335)
        end
        function u293.GetValue(_) --[[Anonymous function at line 1813]]
            --[[
            Upvalues:
                [1] = u295
            --]]
            return u295
        end
        function u293.SetInteractable(_, p336) --[[Anonymous function at line 1817]]
            --[[
            Upvalues:
                [1] = u296
                [2] = u298
                [3] = u293
                [4] = u315
                [5] = u317
            --]]
            u296 = 0
            u298 = p336
            u293.SliderFrame.Selectable = p336
            if u298 then
                u317()
            else
                u315()
            end
        end
        function u293.SetZIndex(_, p337) --[[Anonymous function at line 1828]]
            --[[
            Upvalues:
                [1] = u301
                [2] = u302
                [3] = u303
                [4] = u304
                [5] = u293
            --]]
            u301.ZIndex = p337
            u302.ZIndex = p337
            u303.ZIndex = p337
            u304.ZIndex = p337
            for v338 = 1, #u293.Steps do
                u293.Steps[v338].ZIndex = p337
            end
        end
        function u293.SetMinStep(_, p339) --[[Anonymous function at line 1839]]
            --[[
            Upvalues:
                [1] = u294
                [2] = u292
                [3] = u295
                [4] = u301
                [5] = u302
            --]]
            if p339 >= 0 and p339 <= u294 then
                u292 = p339
            end
            if u295 <= u292 then
                u295 = u292
                u301.Visible = false
            end
            if u294 <= u295 then
                u295 = u294
                u302.Visible = false
            end
        end
        u301.InputBegan:Connect(function(p340) --[[Anonymous function at line 1856]]
            --[[
            Upvalues:
                [1] = u334
                [2] = u295
            --]]
            u334(p340, u295 - 1, true)
        end)
        u301.InputEnded:Connect(function(p341) --[[Anonymous function at line 1857]]
            --[[
            Upvalues:
                [1] = u298
                [2] = u296
            --]]
            if u298 then
                local v342
                if p341 then
                    if p341.UserInputType == Enum.UserInputType.MouseButton1 or p341.UserInputType == Enum.UserInputType.Touch then
                        v342 = true
                    elseif p341.UserInputType == Enum.UserInputType.Gamepad1 then
                        v342 = p341.KeyCode == Enum.KeyCode.ButtonA
                    else
                        v342 = false
                    end
                else
                    v342 = false
                end
                if v342 then
                    u296 = 0
                end
            else
                return
            end
        end)
        u302.InputBegan:Connect(function(p343) --[[Anonymous function at line 1858]]
            --[[
            Upvalues:
                [1] = u334
                [2] = u295
            --]]
            u334(p343, u295 + 1, true)
        end)
        u302.InputEnded:Connect(function(p344) --[[Anonymous function at line 1859]]
            --[[
            Upvalues:
                [1] = u298
                [2] = u296
            --]]
            if u298 then
                local v345
                if p344 then
                    if p344.UserInputType == Enum.UserInputType.MouseButton1 or p344.UserInputType == Enum.UserInputType.Touch then
                        v345 = true
                    elseif p344.UserInputType == Enum.UserInputType.Gamepad1 then
                        v345 = p344.KeyCode == Enum.KeyCode.ButtonA
                    else
                        v345 = false
                    end
                else
                    v345 = false
                end
                if v345 then
                    u296 = 0
                end
            else
                return
            end
        end)
        local function v349(p346) --[[Anonymous function at line 1861]]
            --[[
            Upvalues:
                [1] = u12
                [2] = u301
                [3] = u298
                [4] = u302
                [5] = u293
                [6] = u294
            --]]
            if p346 == "VREnabled" then
                if u12.VREnabled then
                    u301.Selectable = u298
                    u302.Selectable = u298
                    u293.SliderFrame.Selectable = u298
                    for v347 = 1, u294 do
                        u293.Steps[v347].Selectable = u298
                        u293.Steps[v347].Active = u298
                    end
                else
                    u301.Selectable = false
                    u302.Selectable = false
                    u293.SliderFrame.Selectable = u298
                    for v348 = 1, u294 do
                        u293.Steps[v348].Selectable = false
                        u293.Steps[v348].Active = false
                    end
                end
            else
                return
            end
        end
        u12.Changed:Connect(v349)
        v349("VREnabled")
        local function u352(p350) --[[Anonymous function at line 1701]]
            --[[
            Upvalues:
                [1] = u294
                [2] = u293
            --]]
            for v351 = 1, u294 do
                if v351 == 1 or v351 == u294 then
                    u293.Steps[v351].ImageTransparency = p350
                else
                    u293.Steps[v351].BackgroundTransparency = p350
                end
            end
        end
        for u353 = 1, u294 do
            u293.Steps[u353].InputBegan:Connect(function(p354) --[[Anonymous function at line 1888]]
                --[[
                Upvalues:
                    [1] = u334
                    [2] = u353
                --]]
                u334(p354, u353)
            end)
            u293.Steps[u353].InputEnded:Connect(function(p355) --[[Anonymous function at line 1891]]
                --[[
                Upvalues:
                    [1] = u298
                    [2] = u296
                --]]
                if u298 then
                    local v356
                    if p355 then
                        if p355.UserInputType == Enum.UserInputType.MouseButton1 or p355.UserInputType == Enum.UserInputType.Touch then
                            v356 = true
                        elseif p355.UserInputType == Enum.UserInputType.Gamepad1 then
                            v356 = p355.KeyCode == Enum.KeyCode.ButtonA
                        else
                            v356 = false
                        end
                    else
                        v356 = false
                    end
                    if v356 then
                        u296 = 0
                    end
                else
                    return
                end
            end)
        end
        u293.SliderFrame.InputBegan:Connect(function(p357) --[[Anonymous function at line 1895]]
            --[[
            Upvalues:
                [1] = u12
                [2] = u8
                [3] = u293
                [4] = u334
                [5] = u295
            --]]
            if u12.VREnabled then
                local v358 = u8.SelectedCoreObject
                if not (v358 and v358:IsDescendantOf(u293.SliderFrame.Parent)) then
                    return
                end
            end
            u334(p357, u295)
        end)
        u293.SliderFrame.InputEnded:Connect(function(p359) --[[Anonymous function at line 1902]]
            --[[
            Upvalues:
                [1] = u12
                [2] = u8
                [3] = u293
                [4] = u298
                [5] = u296
            --]]
            if u12.VREnabled then
                local v360 = u8.SelectedCoreObject
                if not (v360 and v360:IsDescendantOf(u293.SliderFrame.Parent)) then
                    return
                end
            end
            if u298 then
                local v361
                if p359 then
                    if p359.UserInputType == Enum.UserInputType.MouseButton1 or p359.UserInputType == Enum.UserInputType.Touch then
                        v361 = true
                    elseif p359.UserInputType == Enum.UserInputType.Gamepad1 then
                        v361 = p359.KeyCode == Enum.KeyCode.ButtonA
                    else
                        v361 = false
                    end
                else
                    v361 = false
                end
                if v361 then
                    u296 = 0
                end
            else
                return
            end
        end)
        local function u362() --[[Anonymous function at line 1911]]
            --[[
            Upvalues:
                [1] = u297
                [2] = u319
                [3] = u295
                [4] = u296
            --]]
            if u297 ~= nil then
                if tick() - u297 >= 0.2 then
                    u319(u295 + u296)
                end
            end
        end
        local u363 = true
        local u364 = {
            [Enum.KeyCode.Thumbstick1] = true,
            [Enum.KeyCode.DPadLeft] = -1,
            [Enum.KeyCode.DPadRight] = 1,
            [Enum.KeyCode.Left] = -1,
            [Enum.KeyCode.Right] = 1,
            [Enum.KeyCode.A] = -1,
            [Enum.KeyCode.D] = 1,
            [Enum.KeyCode.ButtonA] = true
        }
        u7.InputBegan:Connect(function(p365) --[[Anonymous function at line 1935]]
            --[[
            Upvalues:
                [1] = u298
                [2] = u363
                [3] = u8
                [4] = u293
                [5] = u364
                [6] = u296
                [7] = u319
                [8] = u295
            --]]
            if u298 then
                if u363 then
                    if p365.UserInputType == Enum.UserInputType.Gamepad1 or p365.UserInputType == Enum.UserInputType.Keyboard then
                        local v366 = u8.SelectedCoreObject
                        if v366 and v366:IsDescendantOf(u293.SliderFrame.Parent) then
                            if u364[p365.KeyCode] == -1 then
                                u296 = -1
                                u319(u295 - 1)
                            elseif u364[p365.KeyCode] == 1 then
                                u296 = 1
                                u319(u295 + 1)
                            end
                        else
                            return
                        end
                    else
                        return
                    end
                else
                    return
                end
            else
                return
            end
        end)
        u7.InputEnded:Connect(function(p367) --[[Anonymous function at line 1952]]
            --[[
            Upvalues:
                [1] = u298
                [2] = u8
                [3] = u293
                [4] = u364
                [5] = u296
            --]]
            if u298 then
                if p367.UserInputType == Enum.UserInputType.Gamepad1 or p367.UserInputType == Enum.UserInputType.Keyboard then
                    local v368 = u8.SelectedCoreObject
                    if v368 and v368:IsDescendantOf(u293.SliderFrame.Parent) then
                        if u364[p367.KeyCode] then
                            u296 = 0
                        end
                    end
                else
                    return
                end
            else
                return
            end
        end)
        u7.InputChanged:Connect(function(p369) --[[Anonymous function at line 1964]]
            --[[
            Upvalues:
                [1] = u298
                [2] = u296
                [3] = u363
                [4] = u8
                [5] = u293
                [6] = u319
                [7] = u295
            --]]
            if u298 then
                if u363 then
                    if p369.UserInputType == Enum.UserInputType.Gamepad1 then
                        local v370 = u8.SelectedCoreObject
                        if v370 and v370:IsDescendantOf(u293.SliderFrame.Parent) then
                            if p369.KeyCode == Enum.KeyCode.Thumbstick1 then
                                if p369.Position.X > 0.8 and (p369.Delta.X > 0 and u296 ~= 1) then
                                    u296 = 1
                                    u319(u295 + 1)
                                    return
                                elseif p369.Position.X < -0.8 and (p369.Delta.X < 0 and u296 ~= -1) then
                                    u296 = -1
                                    u319(u295 - 1)
                                else
                                    local v371 = p369.Position.X
                                    if math.abs(v371) < 0.8 then
                                        u296 = 0
                                    end
                                end
                            else
                                return
                            end
                        else
                            return
                        end
                    else
                        return
                    end
                else
                    u296 = 0
                    return
                end
            else
                u296 = 0
                return
            end
        end)
        local u372 = false
        u8.Changed:Connect(function(p373) --[[Anonymous function at line 1991]]
            --[[
            Upvalues:
                [1] = u8
                [2] = u293
                [3] = u352
                [4] = u372
                [5] = u297
                [6] = u9
                [7] = u299
                [8] = u362
            --]]
            if p373 == "SelectedCoreObject" then
                local v374 = u8.SelectedCoreObject
                if v374 then
                    v374 = v374:IsDescendantOf(u293.SliderFrame.Parent)
                end
                if v374 then
                    u352(0)
                    if not u372 then
                        u372 = true
                        u297 = tick()
                        u9:BindToRenderStep(u299, Enum.RenderPriority.Input.Value + 1, u362)
                        return
                    end
                else
                    u352(0.36)
                    if u372 then
                        u372 = false
                        u9:UnbindFromRenderStep(u299)
                    end
                end
            end
        end)
        u293.SliderFrame.AncestryChanged:Connect(function(_, p375) --[[Anonymous function at line 2012]]
            --[[
            Upvalues:
                [1] = u363
            --]]
            u363 = p375
        end)
        u319(u295)
        return u293
    end
    error("CreateNewSlider failed because numOfSteps (first arg) is 0 or negative, please supply a positive integer", 2)
end
local u377 = 50
local u378 = {}
local function u440(u379, u380, p381, p382, p383, p384) --[[Anonymous function at line 2025]]
    --[[
    Upvalues:
        [1] = u378
        [2] = u22
        [3] = u377
        [4] = u24
        [5] = u15
        [6] = u71
        [7] = u23
        [8] = u376
        [9] = u269
        [10] = u204
        [11] = u12
        [12] = u7
        [13] = u8
        [14] = u10
    --]]
    local v385 = p381 ~= "TextBox"
    local v386 = not u378[u379] and 0 or u378[u379]
    local u387 = u22.Create("ImageButton")({
        ["Name"] = u380 .. "Frame",
        ["BackgroundTransparency"] = 1,
        ["BorderSizePixel"] = 0,
        ["Image"] = "rbxasset://textures/ui/VR/rectBackgroundWhite.png",
        ["ScaleType"] = Enum.ScaleType.Slice,
        ["SliceCenter"] = Rect.new(2, 2, 18, 18),
        ["ImageTransparency"] = 1,
        ["Active"] = false,
        ["AutoButtonColor"] = false,
        ["Size"] = UDim2.new(1, 0, 0, u377),
        ["Position"] = UDim2.new(0, 0, 0, v386),
        ["ZIndex"] = 2,
        ["Selectable"] = false,
        ["SelectionImageObject"] = u24,
        ["Parent"] = u379.Page
    })
    u387.ImageColor3 = u387.BackgroundColor3
    if u387 and p384 then
        u387.Position = UDim2.new(u387.Position.X.Scale, u387.Position.X.Offset, u387.Position.Y.Scale, u387.Position.Y.Offset + p384)
    end
    local u388 = u22.Create("TextLabel")({
        ["Name"] = u380 .. "Label",
        ["Text"] = u380,
        ["Font"] = Enum.Font.SourceSansBold,
        ["TextSize"] = 16,
        ["TextColor3"] = Color3.fromRGB(255, 255, 255),
        ["TextXAlignment"] = Enum.TextXAlignment.Left,
        ["BackgroundTransparency"] = 1,
        ["Size"] = UDim2.new(0, 200, 1, 0),
        ["Position"] = UDim2.new(0, 10, 0, 0),
        ["ZIndex"] = 2,
        ["Parent"] = u387
    })
    local u389 = Instance.new("UITextSizeConstraint")
    if u15 then
        u388.Size = UDim2.new(0.35, 0, 1, 0)
        u388.TextScaled = true
        u388.TextWrapped = true
        u389.Parent = u388
        u389.MaxTextSize = 16
    end
    if not v385 then
        u388.Text = ""
    end
    local function v391(_, p390) --[[Anonymous function at line 2088]]
        --[[
        Upvalues:
            [1] = u388
            [2] = u389
        --]]
        if p390 then
            u388.TextSize = 16
        else
            u388.TextSize = 24
        end
        u389.MaxTextSize = u388.TextSize
    end
    u71()
    local v392 = u71()
    if v392.Y > v392.X then
        u388.TextSize = 16
    else
        u388.TextSize = 24
    end
    u389.MaxTextSize = u388.TextSize
    u23[u387] = v391
    local v393 = u71()
    local v394 = u71()
    v391(v393, v394.Y > v394.X)
    local u395 = nil
    local u396 = nil
    if p381 == "Slider" then
        u396 = u376(p382, p383)
        u396.SliderFrame.Parent = u387
        u395 = u396.SliderFrame
    elseif p381 == "Selector" then
        u396 = u269(p382, p383)
        u396.SelectorFrame.Parent = u387
        u395 = u396.SelectorFrame
    elseif p381 == "DropDown" then
        u396 = u204(p382, p383, u379.HubRef)
        u396.DropDownFrame.Parent = u387
        u395 = u396.DropDownFrame
    elseif p381 == "TextBox" then
        local u397 = false
        local u398 = false
        local v399 = u22.Create("ImageLabel")({
            ["Image"] = "",
            ["BackgroundTransparency"] = 1
        })
        u396 = {
            ["HubRef"] = nil
        }
        local u400 = u22.Create("TextBox")({
            ["AnchorPoint"] = Vector2.new(1, 0.5),
            ["Size"] = UDim2.new(0.6, 0, 1, 0),
            ["Position"] = UDim2.new(1, 0, 0.5, 0),
            ["Text"] = u380,
            ["TextColor3"] = Color3.fromRGB(49, 49, 49),
            ["BackgroundTransparency"] = 0.5,
            ["BorderSizePixel"] = 0,
            ["TextYAlignment"] = Enum.TextYAlignment.Top,
            ["TextXAlignment"] = Enum.TextXAlignment.Left,
            ["TextWrapped"] = true,
            ["Font"] = Enum.Font.SourceSans,
            ["TextSize"] = 24,
            ["ZIndex"] = 2,
            ["SelectionImageObject"] = v399,
            ["ClearTextOnFocus"] = false,
            ["Parent"] = u387
        })
        u395 = u400
        u400.Focused:Connect(function() --[[Anonymous function at line 2146]]
            --[[
            Upvalues:
                [1] = u12
                [2] = u7
                [3] = u8
                [4] = u400
                [5] = u380
            --]]
            local v401
            if u12.VREnabled then
                v401 = false
            else
                v401 = (not u7.TouchEnabled or u7.GamepadEnabled) and true or false
            end
            if v401 then
                u8.SelectedCoreObject = u400
            end
            if u400.Text == u380 then
                u400.Text = ""
            end
        end)
        u400.FocusLost:Connect(function(_, _) --[[Anonymous function at line 2155]]
            --[[
            Upvalues:
                [1] = u398
            --]]
            u398 = false
        end)
        if p384 then
            u400.Position = UDim2.new(u400.Position.X.Scale, u400.Position.X.Offset, u400.Position.Y.Scale, u400.Position.Y.Offset + p384)
        end
        u395.SelectionGained:Connect(function() --[[Anonymous function at line 2163]]
            --[[
            Upvalues:
                [1] = u12
                [2] = u7
                [3] = u400
                [4] = u396
                [5] = u395
            --]]
            local v402
            if u12.VREnabled then
                v402 = false
            else
                v402 = (not u7.TouchEnabled or u7.GamepadEnabled) and true or false
            end
            if v402 then
                u400.BackgroundTransparency = 0.1
                if u396.HubRef then
                    u396.HubRef:ScrollToFrame(u395)
                end
            end
        end)
        u395.SelectionLost:Connect(function() --[[Anonymous function at line 2172]]
            --[[
            Upvalues:
                [1] = u12
                [2] = u7
                [3] = u400
            --]]
            local v403
            if u12.VREnabled then
                v403 = false
            else
                v403 = (not u7.TouchEnabled or u7.GamepadEnabled) and true or false
            end
            if v403 then
                u400.BackgroundTransparency = 0.5
            end
        end)
        local function v407() --[[Anonymous function at line 2178]]
            --[[
            Upvalues:
                [1] = u10
                [2] = u395
                [3] = u12
                [4] = u7
                [5] = u379
                [6] = u8
                [7] = u397
            --]]
            local v404 = u10.RobloxGui:FindFirstChild("DropDownFullscreenFrame")
            if not (v404 and v404.Visible) then
                local v405 = u395
                if v405 and (v405.Visible and v405.ZIndex > 1) then
                    local v406
                    if u12.VREnabled then
                        v406 = false
                    else
                        v406 = (not u7.TouchEnabled or u7.GamepadEnabled) and true or false
                    end
                    if v406 and u379.Active then
                        u8.SelectedCoreObject = v405
                        u397 = true
                    end
                end
            end
        end
        local function v409(p408) --[[Anonymous function at line 2189]]
            --[[
            Upvalues:
                [1] = u8
                [2] = u395
                [3] = u398
                [4] = u400
            --]]
            if p408.UserInputState == Enum.UserInputState.Begin and (p408.KeyCode == Enum.KeyCode.Return and u8.SelectedCoreObject == u395) then
                u398 = true
                u400:CaptureFocus()
            end
        end
        u400.MouseEnter:Connect(v407)
        u7.InputBegan:Connect(v409)
    elseif p381 == "TextEntry" then
        local u410 = false
        local u411 = false
        local v412 = u22.Create("ImageLabel")({
            ["Image"] = "",
            ["BackgroundTransparency"] = 1
        })
        u396 = {
            ["HubRef"] = nil
        }
        local u413 = u22.Create("TextBox")({
            ["AnchorPoint"] = Vector2.new(1, 0.5),
            ["Size"] = UDim2.new(0.4, -10, 0, 40),
            ["Position"] = UDim2.new(1, 0, 0.5, 0),
            ["Text"] = u380,
            ["TextColor3"] = Color3.fromRGB(178, 178, 178),
            ["BackgroundTransparency"] = 1,
            ["BorderSizePixel"] = 0,
            ["TextYAlignment"] = Enum.TextYAlignment.Center,
            ["TextXAlignment"] = Enum.TextXAlignment.Center,
            ["TextWrapped"] = false,
            ["Font"] = Enum.Font.SourceSans,
            ["TextSize"] = 24,
            ["ZIndex"] = 2,
            ["SelectionImageObject"] = v412,
            ["ClearTextOnFocus"] = false,
            ["Parent"] = u387
        })
        u395 = u413
        u413.Focused:Connect(function() --[[Anonymous function at line 2236]]
            --[[
            Upvalues:
                [1] = u12
                [2] = u7
                [3] = u8
                [4] = u413
                [5] = u380
            --]]
            local v414
            if u12.VREnabled then
                v414 = false
            else
                v414 = (not u7.TouchEnabled or u7.GamepadEnabled) and true or false
            end
            if v414 then
                u8.SelectedCoreObject = u413
            end
            if u413.Text == u380 then
                u413.Text = ""
            end
        end)
        u413.FocusLost:Connect(function(_, _) --[[Anonymous function at line 2245]]
            --[[
            Upvalues:
                [1] = u411
            --]]
            u411 = false
        end)
        if p384 then
            u413.Position = UDim2.new(u413.Position.X.Scale, u413.Position.X.Offset, u413.Position.Y.Scale, u413.Position.Y.Offset + p384)
        end
        u395.SelectionGained:Connect(function() --[[Anonymous function at line 2253]]
            --[[
            Upvalues:
                [1] = u12
                [2] = u7
                [3] = u413
                [4] = u396
                [5] = u395
            --]]
            local v415
            if u12.VREnabled then
                v415 = false
            else
                v415 = (not u7.TouchEnabled or u7.GamepadEnabled) and true or false
            end
            if v415 then
                u413.BackgroundTransparency = 0.8
                if u396.HubRef then
                    u396.HubRef:ScrollToFrame(u395)
                end
            end
        end)
        u395.SelectionLost:Connect(function() --[[Anonymous function at line 2262]]
            --[[
            Upvalues:
                [1] = u12
                [2] = u7
                [3] = u413
            --]]
            local v416
            if u12.VREnabled then
                v416 = false
            else
                v416 = (not u7.TouchEnabled or u7.GamepadEnabled) and true or false
            end
            if v416 then
                u413.BackgroundTransparency = 1
            end
        end)
        local function v420() --[[Anonymous function at line 2268]]
            --[[
            Upvalues:
                [1] = u10
                [2] = u395
                [3] = u12
                [4] = u7
                [5] = u379
                [6] = u8
                [7] = u410
            --]]
            local v417 = u10.RobloxGui:FindFirstChild("DropDownFullscreenFrame")
            if not (v417 and v417.Visible) then
                local v418 = u395
                if v418 and (v418.Visible and v418.ZIndex > 1) then
                    local v419
                    if u12.VREnabled then
                        v419 = false
                    else
                        v419 = (not u7.TouchEnabled or u7.GamepadEnabled) and true or false
                    end
                    if v419 and u379.Active then
                        u8.SelectedCoreObject = v418
                        u410 = true
                    end
                end
            end
        end
        local function v422(p421) --[[Anonymous function at line 2279]]
            --[[
            Upvalues:
                [1] = u8
                [2] = u395
                [3] = u411
                [4] = u413
            --]]
            if p421.UserInputState == Enum.UserInputState.Begin and (p421.KeyCode == Enum.KeyCode.Return and u8.SelectedCoreObject == u395) then
                u411 = true
                u413:CaptureFocus()
            end
        end
        u387.MouseEnter:Connect(v420)
        function u396.SetZIndex(_, p423) --[[Anonymous function at line 2291]]
            --[[
            Upvalues:
                [1] = u413
            --]]
            u413.ZIndex = p423
        end
        function u396.SetInteractable(_, p424) --[[Anonymous function at line 2295]]
            --[[
            Upvalues:
                [1] = u413
            --]]
            u413.Selectable = p424
            if p424 then
                u413.TextColor3 = Color3.fromRGB(178, 178, 178)
                u413.ZIndex = 2
            else
                u413.TextColor3 = Color3.fromRGB(49, 49, 49)
                u413.ZIndex = 1
            end
        end
        function u396.SetValue(_, p425) --[[Anonymous function at line 2306]]
            --[[
            Upvalues:
                [1] = u413
            --]]
            u413.Text = p425
        end
        local u426 = Instance.new("BindableEvent")
        u426.Name = "ValueChanged"
        u413.FocusLost:Connect(function() --[[Anonymous function at line 2313]]
            --[[
            Upvalues:
                [1] = u426
                [2] = u413
            --]]
            u426:Fire(u413.Text)
        end)
        u396.ValueChanged = u426.Event
        u7.InputBegan:Connect(v422)
    end
    u396.Name = u380 .. "ValueChanger"
    local v427 = v386 + u377
    if p384 then
        v427 = v427 + p384
    end
    u378[u379] = v427
    if v385 then
        local function v431() --[[Anonymous function at line 2332]]
            --[[
            Upvalues:
                [1] = u10
                [2] = u396
                [3] = u12
                [4] = u7
                [5] = u379
                [6] = u8
            --]]
            local v428 = u10.RobloxGui:FindFirstChild("DropDownFullscreenFrame")
            if not (v428 and v428.Visible) then
                local v429 = u396.SliderFrame or u396.SliderFrame or u396.DropDownFrame or u396.SelectorFrame
                if v429 and (v429.Visible and v429.ZIndex > 1) then
                    local v430
                    if u12.VREnabled then
                        v430 = false
                    else
                        v430 = (not u7.TouchEnabled or u7.GamepadEnabled) and true or false
                    end
                    if v430 and u379.Active then
                        u8.SelectedCoreObject = v429
                    end
                end
            end
        end
        u387.MouseEnter:Connect(v431)
        local function v435(p432) --[[Anonymous function at line 2354]]
            --[[
            Upvalues:
                [1] = u12
                [2] = u387
                [3] = u395
                [4] = u8
            --]]
            if p432 == "VREnabled" then
                if u12.VREnabled then
                    u387.Selectable = true
                    u387.Active = true
                    u395.Active = true
                    u8.Changed:Connect(function(p433) --[[Anonymous function at line 2360]]
                        --[[
                        Upvalues:
                            [1] = u8
                            [2] = u387
                        --]]
                        if p433 == "SelectedCoreObject" then
                            local v434 = u8.SelectedCoreObject
                            if v434 and (v434 == u387 or v434:IsDescendantOf(u387)) then
                                u387.ImageTransparency = 0.5
                                u387.BackgroundTransparency = 1
                                return
                            end
                            u387.ImageTransparency = 1
                            u387.BackgroundTransparency = 1
                        end
                    end)
                    return
                end
                u387.Selectable = false
                u387.Active = false
            end
        end
        u12.Changed:Connect(v435)
        if u12.VREnabled then
            u387.Selectable = true
            u387.Active = true
            u395.Active = true
            u8.Changed:Connect(function(p436) --[[Anonymous function at line 2360]]
                --[[
                Upvalues:
                    [1] = u8
                    [2] = u387
                --]]
                if p436 == "SelectedCoreObject" then
                    local v437 = u8.SelectedCoreObject
                    if v437 and (v437 == u387 or v437:IsDescendantOf(u387)) then
                        u387.ImageTransparency = 0.5
                        u387.BackgroundTransparency = 1
                        return
                    end
                    u387.ImageTransparency = 1
                    u387.BackgroundTransparency = 1
                end
            end)
        else
            u387.Selectable = false
            u387.Active = false
        end
        u395.SelectionGained:Connect(function() --[[Anonymous function at line 2381]]
            --[[
            Upvalues:
                [1] = u12
                [2] = u7
                [3] = u387
                [4] = u396
            --]]
            local v438
            if u12.VREnabled then
                v438 = false
            else
                v438 = (not u7.TouchEnabled or u7.GamepadEnabled) and true or false
            end
            if v438 then
                if u12.VREnabled then
                    u387.ImageTransparency = 0.5
                    u387.BackgroundTransparency = 1
                else
                    u387.ImageTransparency = 1
                    u387.BackgroundTransparency = 0.5
                end
                if u396.HubRef then
                    u396.HubRef:ScrollToFrame(u387)
                end
            end
        end)
        u395.SelectionLost:Connect(function() --[[Anonymous function at line 2396]]
            --[[
            Upvalues:
                [1] = u12
                [2] = u7
                [3] = u387
            --]]
            local v439
            if u12.VREnabled then
                v439 = false
            else
                v439 = (not u7.TouchEnabled or u7.GamepadEnabled) and true or false
            end
            if v439 then
                u387.ImageTransparency = 1
                u387.BackgroundTransparency = 1
            end
        end)
    end
    u379:AddRow(u387, u388, u396, p384, false)
    u396.Selection = u395
    return u387, u388, u396
end
local function u454(p441, p442, p443, p444) --[[Anonymous function at line 2411]]
    --[[
    Upvalues:
        [1] = u378
        [2] = u22
        [3] = u377
        [4] = u24
        [5] = u23
        [6] = u71
        [7] = u8
        [8] = u12
    --]]
    local v445 = not u378[p441] and 0 or u378[p441]
    local u446 = u22.Create("ImageButton")({
        ["Name"] = p442 .. "Frame",
        ["BackgroundTransparency"] = 1,
        ["BorderSizePixel"] = 0,
        ["Image"] = "rbxasset://textures/ui/VR/rectBackgroundWhite.png",
        ["ScaleType"] = Enum.ScaleType.Slice,
        ["SliceCenter"] = Rect.new(10, 10, 10, 10),
        ["ImageTransparency"] = 1,
        ["Active"] = false,
        ["AutoButtonColor"] = false,
        ["Size"] = UDim2.new(1, 0, 0, u377),
        ["Position"] = UDim2.new(0, 0, 0, v445),
        ["ZIndex"] = 2,
        ["Selectable"] = false,
        ["SelectionImageObject"] = u24,
        ["Parent"] = p441.Page
    })
    u446.ImageColor3 = u446.BackgroundColor3
    u446.SelectionGained:Connect(function() --[[Anonymous function at line 2437]]
        --[[
        Upvalues:
            [1] = u446
        --]]
        u446.BackgroundTransparency = 0.5
    end)
    u446.SelectionLost:Connect(function() --[[Anonymous function at line 2440]]
        --[[
        Upvalues:
            [1] = u446
        --]]
        u446.BackgroundTransparency = 1
    end)
    local u447 = u22.Create("TextLabel")({
        ["Name"] = p442 .. "Label",
        ["Text"] = p442,
        ["Font"] = Enum.Font.SourceSansBold,
        ["TextSize"] = 16,
        ["TextColor3"] = Color3.fromRGB(255, 255, 255),
        ["TextXAlignment"] = Enum.TextXAlignment.Left,
        ["BackgroundTransparency"] = 1,
        ["Size"] = UDim2.new(0, 200, 1, 0),
        ["Position"] = UDim2.new(0, 10, 0, 0),
        ["ZIndex"] = 2,
        ["Parent"] = u446
    })
    local function v449(_, p448) --[[Anonymous function at line 2458]]
        --[[
        Upvalues:
            [1] = u447
        --]]
        if p448 then
            u447.TextSize = 16
        else
            u447.TextSize = 24
        end
    end
    u23[u446] = v449
    local v450 = u71()
    local v451 = u71()
    v449(v450, v451.Y > v451.X)
    if p444 then
        u446.Position = UDim2.new(u446.Position.X.Scale, u446.Position.X.Offset, u446.Position.Y.Scale, u446.Position.Y.Offset + p444)
    end
    local v452 = v445 + u377
    if p444 then
        v452 = v452 + p444
    end
    u378[p441] = v452
    local function v453() --[[Anonymous function at line 2479]]
        --[[
        Upvalues:
            [1] = u446
            [2] = u8
        --]]
        if u446.Visible then
            u8.SelectedCoreObject = u446
        end
    end
    u446.MouseEnter:Connect(v453)
    p443.SelectionImageObject = u24
    p443.SelectionGained:Connect(function() --[[Anonymous function at line 2488]]
        --[[
        Upvalues:
            [1] = u12
            [2] = u446
        --]]
        if u12.VREnabled then
            u446.ImageTransparency = 0.5
            u446.BackgroundTransparency = 1
        else
            u446.ImageTransparency = 1
            u446.BackgroundTransparency = 0.5
        end
    end)
    p443.SelectionLost:Connect(function() --[[Anonymous function at line 2497]]
        --[[
        Upvalues:
            [1] = u446
        --]]
        u446.ImageTransparency = 1
        u446.BackgroundTransparency = 1
    end)
    p443.Parent = u446
    p441:AddRow(u446, u447, p443, p444, true)
    return u446
end
local u528 = {
    ["Create"] = function(_, u455) --[[Function name: Create, line 2511]]
        return function(p456) --[[Anonymous function at line 2512]]
            --[[
            Upvalues:
                [1] = u455
            --]]
            local v457 = Instance.new(u455)
            local v458 = nil
            for v459, v460 in pairs(p456) do
                if type(v459) == "number" then
                    v460.Parent = v457
                elseif v459 == "Parent" then
                    v458 = v460
                else
                    v457[v459] = v460
                end
            end
            if v458 then
                v457.Parent = v458
            end
            return v457
        end
    end,
    ["RayPlaneIntersection"] = function(_, p461, p462, p463) --[[Function name: RayPlaneIntersection, line 2533]]
        local v464 = p462.unit
        local v465 = p461.Unit
        local v466 = v464:Dot(v465.Direction)
        if v466 == 0 then
            return nil
        else
            local v467 = v464:Dot(p463 - v465.Origin) / v466
            if v467 < 0 then
                return nil
            else
                return v465.Origin + v465.Direction * v467
            end
        end
    end,
    ["GetEaseLinear"] = function(_) --[[Function name: GetEaseLinear, line 2551]]
        --[[
        Upvalues:
            [1] = u36
        --]]
        return u36
    end,
    ["GetEaseOutQuad"] = function(_) --[[Function name: GetEaseOutQuad, line 2554]]
        --[[
        Upvalues:
            [1] = u42
        --]]
        return u42
    end,
    ["GetEaseInOutQuad"] = function(_) --[[Function name: GetEaseInOutQuad, line 2557]]
        --[[
        Upvalues:
            [1] = u48
        --]]
        return u48
    end,
    ["CreateNewSlider"] = function(_, p468, p469, p470) --[[Function name: CreateNewSlider, line 2561]]
        --[[
        Upvalues:
            [1] = u376
        --]]
        return u376(p468, p469, p470)
    end,
    ["CreateNewSelector"] = function(_, p471, p472) --[[Function name: CreateNewSelector, line 2565]]
        --[[
        Upvalues:
            [1] = u269
        --]]
        return u269(p471, p472)
    end,
    ["CreateNewDropDown"] = function(_, p473, p474) --[[Function name: CreateNewDropDown, line 2569]]
        --[[
        Upvalues:
            [1] = u204
        --]]
        return u204(p473, p474, nil)
    end,
    ["AddNewRow"] = function(_, p475, p476, p477, p478, p479, p480) --[[Function name: AddNewRow, line 2573]]
        --[[
        Upvalues:
            [1] = u440
        --]]
        return u440(p475, p476, p477, p478, p479, p480)
    end,
    ["AddNewRowObject"] = function(_, p481, p482, p483, p484) --[[Function name: AddNewRowObject, line 2577]]
        --[[
        Upvalues:
            [1] = u454
        --]]
        return u454(p481, p482, p483, p484)
    end,
    ["ShowAlert"] = function(_, p485, p486, p487, p488, p489) --[[Function name: ShowAlert, line 2581]]
        --[[
        Upvalues:
            [1] = u289
        --]]
        u289(p485, p486, p487, p488, p489)
    end,
    ["IsSmallTouchScreen"] = function(_) --[[Function name: IsSmallTouchScreen, line 2585]]
        --[[
        Upvalues:
            [1] = u71
            [2] = u7
        --]]
        local v490 = u71()
        local v491 = u7.TouchEnabled
        if v491 then
            v491 = v490.Y < 500 and true or v490.X < 700
        end
        return v491
    end,
    ["IsPortrait"] = function(_) --[[Function name: IsPortrait, line 2589]]
        --[[
        Upvalues:
            [1] = u71
        --]]
        local v492 = u71()
        return v492.Y > v492.X
    end,
    ["MakeStyledButton"] = function(_, p493, p494, p495, p496, p497, p498) --[[Function name: MakeStyledButton, line 2593]]
        --[[
        Upvalues:
            [1] = u118
        --]]
        return u118(p493, p494, p495, p496, p497, p498)
    end,
    ["MakeStyledImageButton"] = function(_, p499, p500, p501, p502, p503, p504, p505) --[[Function name: MakeStyledImageButton, line 2597]]
        --[[
        Upvalues:
            [1] = u128
        --]]
        return u128(p499, p500, p501, p502, p503, p504, p505)
    end,
    ["AddButtonRow"] = function(_, p506, p507, p508, p509, p510, p511) --[[Function name: AddButtonRow, line 2601]]
        --[[
        Upvalues:
            [1] = u139
        --]]
        return u139(p506, p507, p508, p509, p510, p511)
    end,
    ["CreateSignal"] = function(_) --[[Function name: CreateSignal, line 2605]]
        --[[
        Upvalues:
            [1] = u70
        --]]
        return u70()
    end,
    ["UsesSelectedObject"] = function(_) --[[Function name: UsesSelectedObject, line 2609]]
        --[[
        Upvalues:
            [1] = u12
            [2] = u7
        --]]
        if u12.VREnabled then
            return false
        else
            return (not u7.TouchEnabled or u7.GamepadEnabled) and true or false
        end
    end,
    ["TweenProperty"] = function(_, p512, p513, p514, p515, p516, p517, p518) --[[Function name: TweenProperty, line 2613]]
        return PropertyTweener(p512, p513, p514, p515, p516, p517, p518)
    end,
    ["OnResized"] = function(_, p519, p520) --[[Function name: OnResized, line 2617]]
        --[[
        Upvalues:
            [1] = u83
        --]]
        return u83(p519, p520)
    end,
    ["FireOnResized"] = function(_) --[[Function name: FireOnResized, line 2621]]
        --[[
        Upvalues:
            [1] = u71
            [2] = u528
            [3] = u23
        --]]
        local v521 = u71()
        local v522 = u528:IsPortrait()
        for _, v523 in pairs(u23) do
            v523(v521, v522)
        end
    end,
    ["Lerp"] = function(_, p524, p525, p526) --[[Function name: Lerp, line 2632]]
        return (1 - p524) * p525 + p524 * p526
    end,
    ["Round"] = function(_, p527) --[[Function name: Round, line 2637]]
        return p527 % 1 >= 0.5 and math.ceil(p527) or math.floor(p527)
    end
}
return u528