-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\ABTestExperiments\Experiments\TutorialExperiment-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
require(v1.Modules.ABTestExperiments.ABTestTypes)
local function v4(p2, p3) --[[Anonymous function at line 8]]
    p2:<PERSON><PERSON><PERSON><PERSON>bute("AB_TutorialVariant", p3)
end
local v5 = {
    ["RemoteConfig"] = "TutorialVariant",
    ["Disabled"] = false,
    ["DefaultState"] = "Variant1"
}
local v6 = {
    ["Variant1"] = {
        ["Client"] = v4
    },
    ["Variant2"] = {
        ["Client"] = v4
    },
    ["Variant3"] = {
        ["Client"] = v4
    }
}
v5.States = v6
return v5