-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\NightQuestUIController\ItemRewarder-ModuleScript.lua
local v1 = {}
local u2 = require(game.ReplicatedStorage.Code.Particle2D)
local function u43(u3, u4, _) --[[Anonymous function at line 5]]
    --[[
    Upvalues:
        [1] = u2
    --]]
    if not u3:GetAttribute("Unlocking") then
        u3:SetAttribute("Unlocking", true)
        u3.Icon.UIScale.Scale = 1.2
        game.TweenService:Create(u3.Icon.UIScale, TweenInfo.new(0.3 * u4, Enum.EasingStyle.Back, Enum.EasingDirection.In), {
            ["Scale"] = 0
        }):Play()
        task.wait(0.3 * u4)
        game.TweenService:Create(u3.Icon, TweenInfo.new(0.5 * u4, Enum.EasingStyle.Back, Enum.EasingDirection.Out), {
            ["ImageTransparency"] = 1
        }):Play()
        game.TweenService:Create(u3.Frame.Circle, TweenInfo.new(0.3 * u4), {
            ["ImageTransparency"] = 0
        }):Play()
        local u5 = Random.new(os.time())
        task.spawn(function() --[[Anonymous function at line 27]]
            --[[
            Upvalues:
                [1] = u5
                [2] = u3
                [3] = u4
                [4] = u2
            --]]
            for _ = 1, 25 do
                local v6 = u5:NextNumber(0, 359)
                local v7 = u5:NextInteger(10, 15)
                local u8 = u5:NextInteger(1, 0) == 1 and script.Sparkle:Clone() or script.Sparkle2:Clone()
                local v9 = u5:NextNumber(-50, 50)
                local v10 = math.rad(v6)
                local v11 = v9 * math.cos(v10)
                local v12 = math.rad(v6)
                local v13 = v9 * math.sin(v12)
                u8.Position = UDim2.new(0.5, v11, 0.5, v13)
                u8.Size = UDim2.new(0, v7, 0, v7)
                u8.Parent = u3
                local u14 = u5:NextNumber(0, 0.5)
                task.spawn(function() --[[Anonymous function at line 44]]
                    --[[
                    Upvalues:
                        [1] = u8
                        [2] = u4
                        [3] = u14
                    --]]
                    local v15 = {
                        ["ImageTransparency"] = u14
                    }
                    game.TweenService:Create(u8, TweenInfo.new(1 * u4), v15):Play()
                    task.wait(1 * u4)
                    game.TweenService:Create(u8, TweenInfo.new(1 * u4), {
                        ["ImageTransparency"] = 1
                    }):Play()
                end)
                local v16 = UDim2.new(0, 500 * u5:NextNumber(-1, 1), 0, 700 * u5:NextInteger(-1, 1))
                local v17 = UDim2.new(0, 500 * u5:NextNumber(-1, 1), 0, 700)
                u2.createParticle(u8, 3, v16, 45, v17, UDim2.new(0, 5, 0, 5), 0, 0)
                task.wait(0.05)
            end
        end)
        game.TweenService:Create(u3.Frame.Circle.UIScale, TweenInfo.new(0.3 * u4, Enum.EasingStyle.Back, Enum.EasingDirection.Out), {
            ["Scale"] = 1.4
        }):Play()
        task.delay(0.3 * u4, function() --[[Anonymous function at line 63]]
            --[[
            Upvalues:
                [1] = u3
                [2] = u4
            --]]
            game.TweenService:Create(u3.Frame.Circle, TweenInfo.new(0.9 * u4, Enum.EasingStyle.Back, Enum.EasingDirection.Out), {
                ["ImageTransparency"] = 1
            }):Play()
        end)
        game.TweenService:Create(u3.Icon, TweenInfo.new(0.5 * u4, Enum.EasingStyle.Back, Enum.EasingDirection.Out), {
            ["ImageTransparency"] = 0
        }):Play()
        for _ = 1, 3 do
            local v18 = u5:NextNumber(0, 359)
            local v19 = u5:NextInteger(30, 45)
            local u20 = script.Sparkle:Clone()
            local v21 = (u5:NextNumber(0, 1) == 0 and -1 or 1) * 50
            local v22 = math.rad(v18)
            local v23 = v21 * math.cos(v22)
            local v24 = math.rad(v18)
            local v25 = v21 * math.sin(v24)
            u20.Position = UDim2.new(0.5, v23, 0.5, v25)
            u20.Size = UDim2.new(0, v19, 0, v19)
            u20.Parent = u3
            local u26 = u5:NextNumber(0, 0.5)
            task.spawn(function() --[[Anonymous function at line 85]]
                --[[
                Upvalues:
                    [1] = u20
                    [2] = u26
                --]]
                local v27 = {
                    ["ImageTransparency"] = u26
                }
                game.TweenService:Create(u20, TweenInfo.new(0.1), v27):Play()
                task.wait(1)
                game.TweenService:Create(u20, TweenInfo.new(1), {
                    ["ImageTransparency"] = 1
                }):Play()
            end)
            local v28 = UDim2.new(0, 1600 * u5:NextNumber(-1, 1), 0, 1600 * u5:NextNumber(-1, 1))
            local v29 = UDim2.new(0, 0, 0, 150)
            u2.createParticle(u20, 3, v28, 45, v29, UDim2.new(0, 5, 0, 5), 0, 0, nil, 0.5)
        end
        game.TweenService:Create(u3.Icon.UIScale, TweenInfo.new(0.5 * u4, Enum.EasingStyle.Back, Enum.EasingDirection.Out), {
            ["Scale"] = 1.1
        }):Play()
        game.TweenService:Create(u3.Frame.Shockwave1, TweenInfo.new(1), {
            ["ImageTransparency"] = 1
        }):Play()
        game.TweenService:Create(u3.Frame.Shockwave1, TweenInfo.new(1, Enum.EasingStyle.Quad, Enum.EasingDirection.InOut, 0, true), {
            ["ImageColor3"] = Color3.fromRGB(255, 255, 255)
        }):Play()
        game.TweenService:Create(u3.Frame.Shockwave1.UIScale, TweenInfo.new(1), {
            ["Scale"] = 2
        }):Play()
        game.TweenService:Create(u3.Frame.Shockwave2, TweenInfo.new(1), {
            ["ImageTransparency"] = 1
        }):Play()
        game.TweenService:Create(u3.Frame.Shockwave2.UIScale, TweenInfo.new(1), {
            ["Scale"] = 2
        }):Play()
        game.TweenService:Create(u3.Frame.Shockwave2, TweenInfo.new(1, Enum.EasingStyle.Quad, Enum.EasingDirection.InOut, 0, true), {
            ["ImageColor3"] = Color3.fromRGB(255, 255, 255)
        }):Play()
        u3.Icon.ImageTransparency = 0
        u3.Icon.UIGradient.Color = ColorSequence.new(Color3.fromRGB(255, 255, 255))
        game.TweenService:Create(u3.Frame.Sunburst2, TweenInfo.new(0.3), {
            ["ImageTransparency"] = 0
        }):Play()
        game.TweenService:Create(u3.Frame.Sunburst, TweenInfo.new(0.3), {
            ["ImageTransparency"] = 0
        }):Play()
        u3.Frame.Position = UDim2.new(0.5, 0, 0.5, 0)
        u3.MouseEnter:Connect(function() --[[Anonymous function at line 127]]
            --[[
            Upvalues:
                [1] = u3
            --]]
            game.TweenService:Create(u3.UIScale, TweenInfo.new(0.25), {
                ["Scale"] = 1.2
            }):Play()
        end)
        u3.MouseLeave:Connect(function() --[[Anonymous function at line 130]]
            --[[
            Upvalues:
                [1] = u3
            --]]
            game.TweenService:Create(u3.UIScale, TweenInfo.new(0.25), {
                ["Scale"] = 1
            }):Play()
        end)
        game.TweenService:Create(u3.TextLabel, TweenInfo.new(0.4), {
            ["TextTransparency"] = 0
        }):Play()
        game.TweenService:Create(u3.TextLabel.UIStroke, TweenInfo.new(0.4), {
            ["Transparency"] = 0
        }):Play()
        task.delay(0.6, function() --[[Anonymous function at line 136]]
            --[[
            Upvalues:
                [1] = u5
                [2] = u3
                [3] = u2
            --]]
            local v30 = 8
            while v30 > 0 do
                local v31 = u5:NextNumber(0, 359)
                local v32 = u5:NextInteger(10, 15)
                local u33 = script.Sparkle:Clone()
                local v34 = u5:NextNumber(-50, 50)
                local v35 = math.rad(v31)
                local v36 = v34 * math.cos(v35)
                local v37 = math.rad(v31)
                local v38 = v34 * math.sin(v37)
                u33.Position = UDim2.new(0.5, v36, 0.5, v38)
                u33.Size = UDim2.new(0, v32, 0, v32)
                u33.Parent = u3
                local u39 = u5:NextNumber(0, 0.5)
                task.spawn(function() --[[Anonymous function at line 157]]
                    --[[
                    Upvalues:
                        [1] = u33
                        [2] = u39
                    --]]
                    local v40 = {
                        ["ImageTransparency"] = u39
                    }
                    game.TweenService:Create(u33, TweenInfo.new(1), v40):Play()
                    task.wait(2)
                    game.TweenService:Create(u33, TweenInfo.new(1), {
                        ["ImageTransparency"] = 1
                    }):Play()
                end)
                local v41 = UDim2.new(0, 0, 0, 0)
                local v42 = UDim2.new(0, 0, 0, 150)
                u2.createParticle(u33, 3, v41, (u5:NextInteger(1, 0) == 0 and -1 or 1) * u5:NextNumber(20, 45), v42, UDim2.new(0, 5, 0, 5), 0, 0, nil, 0)
                v30 = v30 - 1
                task.wait(0.4)
            end
        end)
    end
end
task.spawn(function() --[[Anonymous function at line 173]]
    while true do
        local v44 = game:GetService("RunService").Heartbeat:Wait()
        for _, v45 in game.CollectionService:GetTagged("SpinTemplate") do
            local v46 = v45:GetAttribute("Speed") or -90
            local v47 = v45:GetAttribute("Direction") or 1
            v45.Rotation = v45.Rotation + v46 * v47 * v44
        end
    end
end)
local u48 = false
function v1.UnlockItems(p49) --[[Anonymous function at line 186]]
    --[[
    Upvalues:
        [1] = u48
        [2] = u43
    --]]
    if not u48 then
        u48 = true
        local u50 = script.ScreenGui:Clone()
        local v51 = u50:WaitForChild("UnlockedEmotes")
        v51.Visible = true
        u50.Parent = game.Players.LocalPlayer.PlayerGui
        game.TweenService:Create(v51, TweenInfo.new(0.4), {
            ["Transparency"] = 0.4
        }):Play()
        v51.MouseButton1Down:Connect(function() --[[Anonymous function at line 201]]
            --[[
            Upvalues:
                [1] = u48
                [2] = u50
            --]]
            u48 = false
            u50:Destroy()
        end)
        v51:WaitForChild("ClickToContinue").MouseButton1Down:Connect(function() --[[Anonymous function at line 206]]
            --[[
            Upvalues:
                [1] = u48
                [2] = u50
            --]]
            u48 = false
            u50:Destroy()
        end)
        task.wait(0.4)
        v51:WaitForChild("Title").Position = UDim2.new(0.5, 0, 0, 30)
        game.TweenService:Create(v51.Title, TweenInfo.new(0.6), {
            ["TextTransparency"] = 0,
            ["Position"] = UDim2.new(0.5, 0, 0, 36)
        }):Play()
        game.TweenService:Create(v51.Title.UIStroke, TweenInfo.new(0.6), {
            ["Transparency"] = 0
        }):Play()
        local v52 = v51:WaitForChild("EmoteHolder"):WaitForChild("Scroller"):WaitForChild("Template")
        for v53, v54 in p49 do
            local u55 = v52:Clone()
            u55.Parent = v51.EmoteHolder.Scroller
            u55.LayoutOrder = v53
            u55.Visible = true
            u55.TextLabel.Text = "Test " .. v53
            u55.Icon.Image = v54.RewardIcon
            u55.TextLabel.Text = v54.RewardName
            task.spawn(function() --[[Anonymous function at line 231]]
                --[[
                Upvalues:
                    [1] = u43
                    [2] = u55
                --]]
                u43(u55, 1)
            end)
            task.wait(0.3)
        end
        game.TweenService:Create(v51.ClickToContinue, TweenInfo.new(0.6), {
            ["TextTransparency"] = 0,
            ["Position"] = UDim2.new(0.5, 0, 1, -30)
        }):Play()
        game.TweenService:Create(v51.ClickToContinue.UIStroke, TweenInfo.new(0.6), {
            ["Transparency"] = 0
        }):Play()
    end
end
return v1