-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\ExpansionController-ModuleScript.lua
local u1 = game:GetService("ReplicatedStorage")
game:GetService("RunService")
local u2 = game:GetService("Players").LocalPlayer
local u3 = require(u1.Modules.MarketController)
local u4 = require(u1.Modules.UpdateService)
local u5 = require(u1.Modules.GetFarmAsync)
local u6 = require(u1.Modules.DataService)
local u7 = require(u1.Modules.NumberUtil)
require(u1.Modules.Observers)
local u8 = require(u1.Data.Expansions)
local u9 = require(u1.Comma_Module)
local u10 = require(u1.Modules.Remotes)
local u11 = require(u1.Modules.Trove)
local u44 = {
    ["GetNextExpansions"] = function(_) --[[Function name: GetNextExpansions, line 21]]
        --[[
        Upvalues:
            [1] = u6
            [2] = u4
            [3] = u8
        --]]
        local v12 = u6:GetData()
        if not v12 then
            return {}
        end
        if u4:IsHiddenFromUpdate("Expansions") then
            return {}
        end
        local v13 = v12.ExpansionsData.Unlocked
        local v14 = {}
        for v15, v16 in u8 do
            if not table.find(v13, v15) then
                local v17 = v16.Requires
                local v18 = true
                if v17 then
                    for _, v19 in v17 do
                        if not table.find(v13, v19) then
                            v18 = false
                            break
                        end
                    end
                end
                if v18 then
                    table.insert(v14, v15)
                end
            end
        end
        return v14
    end,
    ["Start"] = function(_) --[[Function name: Start, line 58]]
        --[[
        Upvalues:
            [1] = u5
            [2] = u2
            [3] = u6
            [4] = u4
            [5] = u44
            [6] = u8
            [7] = u1
            [8] = u11
            [9] = u10
            [10] = u3
            [11] = u9
            [12] = u7
        --]]
        local u20 = u5(u2)
        if u20 then
            local u21 = u6:GetData()
            if u21 then
                local u22 = {}
                local function u42() --[[Anonymous function at line 72]]
                    --[[
                    Upvalues:
                        [1] = u21
                        [2] = u4
                        [3] = u20
                        [4] = u44
                        [5] = u22
                        [6] = u8
                        [7] = u1
                        [8] = u11
                        [9] = u10
                        [10] = u3
                        [11] = u9
                        [12] = u7
                    --]]
                    if u21.ExpansionsData.CanSeeExpansions then
                        if not u4:IsHiddenFromUpdate("Expansions") then
                            local v23 = u20.Center_Point:GetPivot()
                            local v24 = u44:GetNextExpansions()
                            for v25, v26 in table.clone(u22) do
                                if not table.find(v24, v25) then
                                    v26.Trove:Destroy()
                                    u22[v25] = nil
                                end
                            end
                            for _, u27 in v24 do
                                if not u22[u27] then
                                    local u28 = u8[u27]
                                    local v29 = u1.Assets.Expansions.Models:FindFirstChild((tostring(u27)))
                                    local v30
                                    if v29 then
                                        v30 = v29:FindFirstChild("Expand")
                                    else
                                        v30 = v29
                                    end
                                    if v30 then
                                        local v31 = v29:GetPivot():ToObjectSpace(v30:GetPivot())
                                        local v32 = u11.new()
                                        local u33 = v32:Clone(u1.Assets.Expansions.ExpandSign)
                                        u22[u27] = {
                                            ["Model"] = u33,
                                            ["Trove"] = v32
                                        }
                                        u33.Name = "ExpansionSign"
                                        u33:PivotTo(v23:ToWorldSpace(v31))
                                        u33.Parent = u20
                                        v32:Add(u33.ProximityPrompt.Triggered:Connect(function(_) --[[Anonymous function at line 120]]
                                            --[[
                                            Upvalues:
                                                [1] = u28
                                                [2] = u21
                                                [3] = u27
                                                [4] = u10
                                            --]]
                                            if u28.Timer then
                                                local v34 = nil
                                                for _, v35 in u21.ExpansionsData.ExpansionTimers do
                                                    if v35.Expansion == u27 then
                                                        v34 = v35
                                                    end
                                                end
                                                if v34 and workspace:GetServerTimeNow() < v34.Timer then
                                                    u10.Expansions.SkipTimer.send(u27)
                                                    return
                                                end
                                            end
                                            u10.Expansions.Expand.send(u27)
                                        end))
                                        local u36 = true
                                        v32:Add(function() --[[Anonymous function at line 140]]
                                            --[[
                                            Upvalues:
                                                [1] = u36
                                            --]]
                                            u36 = false
                                        end)
                                        local u37 = nil
                                        local u38 = u33.SurfaceGui.TextLabel
                                        v32:Add(task.spawn(function() --[[Anonymous function at line 147]]
                                            --[[
                                            Upvalues:
                                                [1] = u36
                                                [2] = u21
                                                [3] = u27
                                                [4] = u37
                                                [5] = u38
                                                [6] = u33
                                                [7] = u3
                                                [8] = u28
                                                [9] = u9
                                                [10] = u7
                                            --]]
                                            while u36 do
                                                local v39 = nil
                                                for _, v40 in u21.ExpansionsData.ExpansionTimers do
                                                    if v40.Expansion == u27 then
                                                        v39 = v40
                                                    end
                                                end
                                                local v41 = (v39 and v39.Timer or 0) - workspace:GetServerTimeNow()
                                                if v41 <= 0 then
                                                    if u37 ~= true then
                                                        u37 = true
                                                        u38.Text = "Expand Garden"
                                                        u33.ProximityPrompt.ActionText = "Expand"
                                                        u3:RemovePriceLabel(u33.ProximityPrompt)
                                                        if u28.Price then
                                                            u33.ProximityPrompt.ObjectText = u9.Comma(u28.Price) .. "\194\162"
                                                        elseif u28.ProductId then
                                                            u3:SetPriceLabel(u33.ProximityPrompt, u28.ProductId, ":robux::value:")
                                                        end
                                                    end
                                                elseif v41 > 0 then
                                                    if u37 ~= false then
                                                        u37 = false
                                                        u33.ProximityPrompt.ActionText = "Skip Timer"
                                                        u3:RemovePriceLabel(u33.ProximityPrompt)
                                                        if u28.TimerProductId then
                                                            u3:SetPriceLabel(u33.ProximityPrompt, u28.TimerProductId, ":robux::value:")
                                                        elseif u28.Price then
                                                            u33.ProximityPrompt.ObjectText = ""
                                                        end
                                                    end
                                                    u38.Text = ("Expand in\n%*"):format((u7.compactFormat(v41)))
                                                end
                                                task.wait(1)
                                            end
                                        end))
                                    end
                                end
                            end
                        end
                    else
                        return
                    end
                end
                xpcall(u42, warn)
                local v43 = u6
                assert(v43:GetPathSignal("ExpansionsData/@")):Connect(u42)
                task.spawn(function() --[[Anonymous function at line 199]]
                    --[[
                    Upvalues:
                        [1] = u4
                        [2] = u42
                    --]]
                    while u4:IsHiddenFromUpdate("Expansions") do
                        task.wait(1)
                    end
                    u42()
                end)
            end
        else
            return
        end
    end
}
task.spawn(u44.Start, u44)
return u44