-- Full Path: -Grow-a-Garden-\\Seed-ModuleScript.lua
local u1 = game:GetService("ReplicatedStorage")
require(u1.Data.QuestData.Types)
return {
    ["Type"] = "Seed",
    ["Display"] = function(_, p2) --[[Function name: Display, line 13]]
        local v3 = p2.Data.Amount
        return ("+%* %* Seed%*"):format(v3, p2.Data.Seed, v3 > 1 and "s" or "")
    end,
    ["Give"] = function(_, p4, p5) --[[Function name: Give, line 18]]
        --[[
        Upvalues:
            [1] = u1
        --]]
        game:GetService("ServerScriptService")
        local v6 = require(u1.Give_Seed)
        for _ = 1, p5.Data.Amount do
            task.spawn(v6.Give_Seed, p4, p5.Data.Seed)
        end
        return true
    end,
    ["Use"] = function(p7, p8) --[[Function name: Use, line 30]]
        return {
            ["Type"] = p7.Type,
            ["Data"] = p8
        }
    end
}