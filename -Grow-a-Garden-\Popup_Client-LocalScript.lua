-- Full Path: -Grow-a-Garden-\\Popup_Client-LocalScript.lua
local u1 = require(game.ReplicatedStorage.Field_Of_View_Module)
local v2 = game:GetService("TweenService")
local v3 = TweenInfo.new(0.5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out, 0, false, 0)
local v4 = TweenInfo.new(3, Enum.EasingStyle.Quad, Enum.EasingDirection.Out, 0, false, 0)
local v5 = game.Players.LocalPlayer.PlayerGui:WaitForChild("Pop_Effect"):WaitForChild("Frame")
local u6 = v2:Create(v5, v3, {
    ["BackgroundTransparency"] = 0
})
local u7 = v2:Create(v5, v4, {
    ["BackgroundTransparency"] = 1
})
game.ReplicatedStorage.GameEvents.Pop_Effect.OnClientEvent:Connect(function() --[[Anonymous function at line 8]]
    --[[
    Upvalues:
        [1] = u1
        [2] = u6
        [3] = u7
    --]]
    u1.Change_FOV(120, 0.5)
    u6:Play()
    task.wait(1)
    u1.Change_FOV(70, 4)
    task.wait(1.25)
    u7:Play()
end)