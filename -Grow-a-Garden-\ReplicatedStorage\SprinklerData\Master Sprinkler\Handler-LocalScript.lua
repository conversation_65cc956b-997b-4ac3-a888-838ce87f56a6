-- Full Path: -Grow-a-Garden-\ReplicatedStorage\SprinklerData\Master Sprinkler\Handler-LocalScript.lua
local v1 = game:GetService("Players")
local v2 = game:GetService("UserInputService")
local u3 = game:GetService("ReplicatedStorage")
local u4 = v1.LocalPlayer
local u5 = u4.PlayerGui
local u6 = u4:GetMouse()
local u7 = u5.ConfirmSprinkler
local u8 = require(u3.Modules.EffectController.Libraries.BoatTween)
local u9 = RaycastParams.new()
local v10 = {}
for _, v11 in workspace.Farm:GetChildren() do
    for _, v12 in v11.Important.Plant_Locations:GetChildren() do
        table.insert(v10, v12)
    end
end
u9.FilterDescendantsInstances = v10
u9.FilterType = Enum.RaycastFilterType.Include
local u13 = os.clock()
local u14 = u6.Hit
local u15 = script.Parent
local u16 = false
u15.Equipped:Connect(function() --[[Anonymous function at line 31]]
    --[[
    Upvalues:
        [1] = u16
        [2] = u7
        [3] = u15
        [4] = u13
        [5] = u3
        [6] = u6
        [7] = u9
        [8] = u8
    --]]
    u16 = true
    u7:FindFirstChild("PlacingLabel", true).Text = ("Place %*?"):format(u15.Name)
    u13 = os.clock()
    task.spawn(function() --[[Anonymous function at line 39]]
        --[[
        Upvalues:
            [1] = u3
            [2] = u15
            [3] = u16
            [4] = u6
            [5] = u9
            [6] = u8
        --]]
        local v17 = script.SprinklerVisualization:Clone()
        v17.Parent = workspace.Visuals
        v17.Range.Enabled = true
        v17.OuterRange.Enabled = true
        local v18 = u3.ObjectModels[u15.Name]:Clone()
        v18.Parent = workspace.Visuals
        v18:PivotTo(CFrame.new(0, -55, 0))
        for _, v19 in v18:GetDescendants() do
            if v19:IsA("BasePart") then
                v19.Transparency = 0.5
            end
        end
        local v20 = v17.Size
        v17.Size = Vector3.new(0, 0, 0)
        local v21 = false
        local v22 = nil
        local v23 = nil
        while u16 do
            local v24 = u6.Hit
            local v25 = workspace:Raycast(v24.Position + Vector3.new(0, 10, 0), Vector3.new(-0, -20, -0), u9)
            if v25 then
                local v26 = v17.OuterRange.Image
                v26.Rotation = v26.Rotation + 0.2
                if not v21 then
                    if v22 then
                        v22:Stop()
                    end
                    v23 = u8:Create(v17, {
                        ["Time"] = 0.3,
                        ["EasingStyle"] = "Smoother",
                        ["EasingDirection"] = "In",
                        ["StepType"] = "Heartbeat",
                        ["Goal"] = {
                            ["Size"] = v20
                        }
                    })
                    v23:Play()
                    v21 = true
                end
                v18:PivotTo(CFrame.new(v25.Position) * CFrame.new(0, 0.5, 0))
                v17:PivotTo(CFrame.new(v25.Position) * CFrame.new(0, v17.Size.Y / 2, 0))
            elseif v21 then
                if v23 then
                    v23:Stop()
                end
                v22 = u8:Create(v17, {
                    ["Time"] = 0.3,
                    ["EasingStyle"] = "Smoother",
                    ["EasingDirection"] = "Out",
                    ["StepType"] = "Heartbeat",
                    ["Goal"] = {
                        ["Size"] = Vector3.new(0, 0, 0)
                    }
                })
                v18:PivotTo(v18:GetPivot() * CFrame.new(0, -10, 0))
                v22:Play()
                v21 = false
            end
            task.wait()
        end
        v18:Destroy()
        v17:Destroy()
    end)
end)
u15.Unequipped:Connect(function() --[[Anonymous function at line 128]]
    --[[
    Upvalues:
        [1] = u16
    --]]
    u16 = false
end)
local u27 = u3.GameEvents.Sprinkler_RE
local u28 = nil
local function u34(p29) --[[Anonymous function at line 147]]
    --[[
    Upvalues:
        [1] = u5
        [2] = u6
        [3] = u9
        [4] = u3
        [5] = u4
    --]]
    for _, v30 in u5:GetGuiObjectsAtPosition(u6.X, u6.Y) do
        if v30.BackgroundTransparency ~= 1 and v30.Visible then
            v33 = true
            ::l5::
            if not v33 then
                local v31 = workspace:Raycast(p29.Position + Vector3.new(0, 10, 0), Vector3.new(-0, -20, -0), u9)
                if not (v31 and (v31.Instance and v31.Position)) then
                    require(u3.Modules.Notification):CreateNotification("You can only place this in plots!")
                    return false
                end
                local v32 = u4.Character
                if v32 then
                    v32 = v32:FindFirstChild("HumanoidRootPart")
                end
                if not v32 then
                    return false
                end
                if (v32.Position - v31.Position).Magnitude <= 50 then
                    return v31
                end
                require(u3.Modules.Notification):CreateNotification("Too far away! Try getting closer.")
                return false
            end
            return
        end
    end
    local v33 = nil
    goto l5
end
local v35 = u7:FindFirstChild("Confirm", true)
local v36 = u7:FindFirstChild("Cancel", true)
v35.MouseButton1Click:Connect(function() --[[Anonymous function at line 197]]
    --[[
    Upvalues:
        [1] = u7
        [2] = u14
        [3] = u15
        [4] = u16
        [5] = u34
        [6] = u28
        [7] = u27
    --]]
    u7.Enabled = false
    local v37 = u14
    if u15 then
        if u16 then
            if u34(v37) then
                u28:Disconnect()
                u16 = false
                u27:FireServer(u15, v37)
            end
        else
            return
        end
    else
        return
    end
end)
v36.MouseButton1Click:Connect(function() --[[Anonymous function at line 203]]
    --[[
    Upvalues:
        [1] = u13
        [2] = u7
    --]]
    u13 = os.clock()
    u7.Enabled = false
end)
if v2.TouchEnabled then
    u28 = v2.TouchTapInWorld:Connect(function() --[[Anonymous function at line 210]]
        --[[
        Upvalues:
            [1] = u16
            [2] = u13
            [3] = u34
            [4] = u6
            [5] = u14
            [6] = u7
        --]]
        if u16 and (os.clock() >= u13 + 0.1 and u34(u6.Hit)) then
            u13 = os.clock()
            u14 = u6.Hit
            u7.Enabled = true
        end
    end)
else
    u28 = v2.InputBegan:Connect(function(p38) --[[Anonymous function at line 219]]
        --[[
        Upvalues:
            [1] = u6
            [2] = u15
            [3] = u16
            [4] = u34
            [5] = u28
            [6] = u27
        --]]
        if p38.UserInputType == Enum.UserInputType.MouseButton1 or p38.KeyCode == Enum.KeyCode.ButtonR2 then
            local v39 = u6.Hit
            if u15 then
                if u16 then
                    if u34(v39) then
                        u28:Disconnect()
                        u16 = false
                        u27:FireServer(u15, v39)
                    end
                else
                    return
                end
            else
                return
            end
        else
            return
        end
    end)
end