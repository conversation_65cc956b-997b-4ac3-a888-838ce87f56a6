-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Code\BodyShaker-ModuleScript.lua
local v1 = {}
require(game.ReplicatedStorage.Code.SolveIK)
local u2 = game.Players.LocalPlayer
local u3 = {}
local u4 = false
local function u29(p5) --[[Anonymous function at line 9]]
    --[[
    Upvalues:
        [1] = u2
        [2] = u4
    --]]
    local v6 = p5 or u2.Character
    repeat
        task.wait()
    until v6:IsDescendantOf(workspace)
    if not v6:GetAttribute("Shaking") then
        v6:SetAttribute("Shaking", true)
        local v7 = v6:WaitFor<PERSON>hild("Torso")
        local v8 = v7:WaitFor<PERSON>hild("Neck")
        local v9 = v7:Wait<PERSON><PERSON><PERSON>hild("Left Shoulder")
        local v10 = v7:Wait<PERSON><PERSON><PERSON>hild("Right Shoulder")
        local v11 = v7:WaitForChild("Left Hip")
        local v12 = v7:WaitForChild("Right Hip")
        local v13 = v9.C0
        local v14 = v10.C0
        local v15 = v11.C0
        local v16 = v12.C0
        local v17 = v8.C0
        local v18 = 0
        while v6:IsDescendantOf(workspace) and u4 do
            task.wait(0)
            v18 = v18 + 1
            local v19 = CFrame.Angles
            local v20 = math.sin(v18) * 1
            v9.C0 = v13 * v19(0, math.rad(v20), 0)
            local v21 = CFrame.Angles
            local v22 = math.sin(v18) * -1
            v10.C0 = v14 * v21(0, math.rad(v22), 0)
            local v23 = CFrame.Angles
            local v24 = math.sin(v18) * -1
            v12.C0 = v16 * v23(0, math.rad(v24), 0)
            local v25 = CFrame.Angles
            local v26 = math.sin(v18) * 1
            v11.C0 = v15 * v25(0, math.rad(v26), 0)
            local v27 = CFrame.Angles
            local v28 = math.sin(v18) * 1
            v8.C0 = v17 * v27(0, 0, (math.rad(v28)))
        end
        v9.C0 = v13
        v10.C0 = v14
        v11.C0 = v15
        v12.C0 = v16
        v8.C0 = v17
        v6:SetAttribute("Shaking", false)
    end
end
function v1.EnableShaking() --[[Anonymous function at line 65]]
    --[[
    Upvalues:
        [1] = u2
        [2] = u29
        [3] = u3
        [4] = u4
    --]]
    if u2.Character then
        task.spawn(u29)
    end
    local v30 = u3
    local v31 = u2.CharacterAdded
    local v32 = u29
    table.insert(v30, v31:Connect(v32))
    u4 = true
end
function v1.DisableShaking() --[[Anonymous function at line 74]]
    --[[
    Upvalues:
        [1] = u3
        [2] = u4
    --]]
    for _, v33 in u3 do
        v33:Disconnect()
    end
    u4 = false
end
return v1