-- Full Path: -Grow-a-Garden-\\GameTranslator-ModuleScript.lua
game:GetService("LocalizationService")
game:GetService("Players")
game.Players.LocalPlayer.PlayerGui:WaitFor<PERSON>hild("BackpackGui")
local _ = nil
local _ = nil
local _ = nil
local _ = nil
local v1 = Instance.new("BindableEvent")
local _ = {}
return {
    ["LocaleChanged"] = v1.Event,
    ["TranslateGameText"] = function(_, _, p2) --[[Function name: TranslateGameText, line 102]]
        return p2
    end,
    ["TranslateAndRegister"] = function(_, _, _, p3) --[[Function name: TranslateAndRegister, line 125]]
        return p3
    end
}