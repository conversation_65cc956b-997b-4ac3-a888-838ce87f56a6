-- Full Path: -Grow-a-Garden-\\SeedSackUIController-ModuleScript.lua
local v1 = game:GetService("Players")
local u2 = game:GetService("ReplicatedStorage")
game:GetService("ServerScriptService")
local u3 = game:GetService("MarketplaceService")
local u4 = game:GetService("PolicyService")
local u5 = game:GetService("RunService")
u2:WaitF<PERSON><PERSON>hild("GameEvents"):WaitForChild("SeedPackGiverEvent")
require(u2.Data.SeedGiverQuestProgression)
local u6 = require(u2.Modules.MarketController)
local u7 = require(u2.Modules.GiftController)
local u8 = require(u2.Modules.GuiController)
require(u2.Modules.DataService)
local u9 = require(u2.Data.SeedPackData)
local u10 = require(u2.Modules.RetryPcall)
local u11 = require(u2.Comma_Module)
local u12 = require(u2.Data.SeedData)
local u13 = require(u2.Data.PetRegistry.PetEggs)
local u14 = v1.LocalPlayer
local u15 = u14.PlayerGui:WaitForChild("FreeSeedSack_UI")
local u16 = u15.Frame
local u17 = u16.Btns
local v18 = workspace:FindFirstChild("NightEvent")
if not v18 then
    return {}
end
local u19 = v18:FindFirstChild("Night Pack Interact", true):FindFirstChild("ProximityPrompt", true)
if not u19 then
    warn("Pack Proximity Prompt not found")
end
local u20 = v18:FindFirstChild("Night Egg Interact", true):FindFirstChild("ProximityPrompt", true)
if not u20 then
    warn("Egg Proximity Prompt not found")
end
local u21 = false
local u22 = {
    ["DisplayPetSackUI"] = function(_) --[[Function name: DisplayPetSackUI, line 42]]
        --[[
        Upvalues:
            [1] = u21
            [2] = u8
            [3] = u15
            [4] = u16
        --]]
        if u21 then
            u8:Open(u15)
            u16.PremSeeds.Visible = false
            u16.PremEggs.Visible = true
        end
    end,
    ["DisplaySeedSackUI"] = function(_) --[[Function name: DisplaySeedSackUI, line 52]]
        --[[
        Upvalues:
            [1] = u21
            [2] = u8
            [3] = u15
            [4] = u16
        --]]
        if u21 then
            u8:Open(u15)
            u16.PremSeeds.Visible = true
            u16.PremEggs.Visible = false
        end
    end
}
u19.Triggered:Connect(function() --[[Anonymous function at line 62]]
    --[[
    Upvalues:
        [1] = u21
        [2] = u22
    --]]
    if u21 then
        u22:DisplaySeedSackUI()
    end
end)
u20.Triggered:Connect(function() --[[Anonymous function at line 70]]
    --[[
    Upvalues:
        [1] = u21
        [2] = u22
    --]]
    if u21 then
        u22:DisplayPetSackUI()
    end
end)
u15:GetPropertyChangedSignal("Enabled"):Connect(function() --[[Function name: updateProxPrompts, line 79]]
    --[[
    Upvalues:
        [1] = u15
        [2] = u19
        [3] = u20
    --]]
    local v23 = u15.Enabled
    u19.Enabled = not v23
    u20.Enabled = not v23
end)
function u22.Start(_) --[[Anonymous function at line 86]]
    --[[
    Upvalues:
        [1] = u8
        [2] = u15
        [3] = u17
        [4] = u22
        [5] = u16
        [6] = u9
        [7] = u12
        [8] = u5
        [9] = u2
        [10] = u13
        [11] = u6
        [12] = u10
        [13] = u3
        [14] = u11
        [15] = u7
        [16] = u4
        [17] = u14
        [18] = u21
    --]]
    local u24 = u8:GetStateForGui(u15)
    u8:UsePopupAnims(u15)
    u17.Seeds.Activated:Connect(function() --[[Anonymous function at line 90]]
        --[[
        Upvalues:
            [1] = u22
        --]]
        u22:DisplaySeedSackUI()
    end)
    u17.Eggs.Activated:Connect(function() --[[Anonymous function at line 94]]
        --[[
        Upvalues:
            [1] = u22
        --]]
        u22:DisplayPetSackUI()
    end)
    u16.ExitButton.Activated:Connect(function() --[[Anonymous function at line 98]]
        --[[
        Upvalues:
            [1] = u8
            [2] = u15
        --]]
        u8:Close(u15)
    end)
    local function v47(p25, p26) --[[Anonymous function at line 153]]
        --[[
        Upvalues:
            [1] = u2
            [2] = u24
            [3] = u5
        --]]
        local v27 = require(u2.Data.PetRegistry).PetList
        local v28 = {}
        for v29, v30 in p26.RarityData.Items do
            local v31 = {
                ["Name"] = v29,
                ["Item"] = v30,
                ["NormalizedOdd"] = v30.NormalizedOdd
            }
            table.insert(v28, v31)
        end
        table.sort(v28, function(p32, p33) --[[Anonymous function at line 166]]
            return p32.NormalizedOdd < p33.NormalizedOdd
        end)
        for v34, v35 in v28 do
            local v36 = v34 <= 1
            local v37 = v34 <= 3
            local v38
            if v36 then
                v38 = p25.UIListLayout.TemplateRainbow
            elseif v37 then
                v38 = p25.UIListLayout.TemplateBigger
            else
                v38 = p25.UIListLayout.TemplateSmaller
            end
            local u39 = v38:Clone()
            u39.LayoutOrder = 100 / v35.NormalizedOdd
            u39.Title.Text = v35.Name
            local v40 = u39.Odds
            local v41 = v35.NormalizedOdd + 0.5
            v40.Text = ("%*%%"):format((math.floor(v41)))
            local v42 = v27[v35.Name]
            if v42 and v42.Icon then
                u39.Vector.Image = v42.Icon
                u39.Vector.Visible = true
            else
                u39.Vector.Visible = false
            end
            if v36 then
                local u43 = nil
                u24.Visible:Listen(function(p44) --[[Anonymous function at line 193]]
                    --[[
                    Upvalues:
                        [1] = u43
                        [2] = u5
                        [3] = u39
                    --]]
                    if p44 and not u43 then
                        u43 = u5.PostSimulation:Connect(function() --[[Anonymous function at line 196]]
                            --[[
                            Upvalues:
                                [1] = u39
                            --]]
                            local v45 = Color3.fromHSV(os.clock() % 5 / 5, 1, 1)
                            u39.Odds.TextColor3 = v45
                            u39.Title.TextColor3 = v45
                        end)
                    elseif not p44 and u43 then
                        u43:Disconnect()
                        u43 = nil
                    end
                end)
            elseif v37 then
                local v46 = Color3.fromRGB(255, 204, 1)
                u39.Odds.TextColor3 = v46
                u39.Title.TextColor3 = v46
            end
            u39.Parent = p25
        end
    end
    (function(p48, p49) --[[Function name: renderSeedRewards, line 103]]
        --[[
        Upvalues:
            [1] = u9
            [2] = u12
            [3] = u24
            [4] = u5
        --]]
        for v50, v51 in p49.Items do
            local v52
            if p49 == u9.Packs.SeedSackBasic then
                v52 = v50 >= 7
            else
                v52 = v50 >= 6
            end
            local v53
            if p49 == u9.Packs.SeedSackBasic then
                v53 = v50 >= 5
            else
                v53 = v50 >= 4
            end
            local v54
            if v52 then
                v54 = p48.UIListLayout.TemplateRainbow
            elseif v53 then
                v54 = p48.UIListLayout.TemplateBigger
            else
                v54 = p48.UIListLayout.TemplateSmaller
            end
            local u55 = v54:Clone()
            u55.LayoutOrder = v50
            local v56 = u55.Odds
            local v57
            if v51.RemoveChance then
                v57 = ""
            elseif v51.HideChance then
                v57 = "???"
            else
                local v58 = v51.Chance * 100
                v57 = ("%*%%"):format(math.floor(v58) / 100)
            end
            v56.Text = v57
            u55.Title.Text = u9:GetTextDisplayForItem(v51)
            if v51.Icon then
                u55.Vector.Image = v51.Icon
                u55.Vector.Visible = true
            else
                local v59 = u12[v51.RewardId]
                if v59 and v59.Asset then
                    u55.Vector.Visible = true
                    u55.Vector.Image = v59.Asset
                end
            end
            if v52 then
                local u60 = nil
                u24.Visible:Listen(function(p61) --[[Anonymous function at line 130]]
                    --[[
                    Upvalues:
                        [1] = u60
                        [2] = u5
                        [3] = u55
                    --]]
                    if p61 then
                        if not u60 then
                            u60 = u5.PostSimulation:Connect(function() --[[Anonymous function at line 134]]
                                --[[
                                Upvalues:
                                    [1] = u55
                                --]]
                                u55.Odds.TextColor3 = Color3.fromHSV(os.clock() % 5 / 5, 1, 1)
                            end)
                            return
                        end
                    elseif u60 then
                        u60:Disconnect()
                        u60 = nil
                    end
                end)
            elseif v53 then
                u55.Odds.TextColor3 = Color3.fromRGB(255, 204, 1)
            end
            u55.Parent = p48
        end
    end)(u16.PremSeeds.Items, u9.Packs.NightPremium)
    v47(u16.PremEggs.Items, u13["Premium Night Egg"])
    u6:SetPriceLabel(u16.PremSeeds.Frame.Buy1.Cost, 3282157387)
    u16.PremSeeds.Frame.Buy1.Activated:Connect(function() --[[Anonymous function at line 222]]
        --[[
        Upvalues:
            [1] = u6
        --]]
        u6:PromptPurchase(3282157387, Enum.InfoType.Product)
    end)
    u6:SetPriceLabel(u16.PremSeeds.Frame.Buy3.Cost, 3282157419)
    u16.PremSeeds.Frame.Buy3.Activated:Connect(function() --[[Anonymous function at line 226]]
        --[[
        Upvalues:
            [1] = u6
        --]]
        u6:PromptPurchase(3282157419, Enum.InfoType.Product)
    end)
    u6:SetPriceLabel(u16.PremSeeds.Frame.Buy10.Cost, 3282157462)
    u16.PremSeeds.Frame.Buy10.Activated:Connect(function() --[[Anonymous function at line 230]]
        --[[
        Upvalues:
            [1] = u6
        --]]
        u6:PromptPurchase(3282157462, Enum.InfoType.Product)
    end)
    task.spawn(function() --[[Anonymous function at line 238]]
        --[[
        Upvalues:
            [1] = u16
            [2] = u10
            [3] = u3
            [4] = u11
        --]]
        u16.PremSeeds.Frame.Buy10.Tag.Visible = false
        local v62, v63 = u10(30, 12, function() --[[Anonymous function at line 241]]
            --[[
            Upvalues:
                [1] = u3
            --]]
            return u3:GetProductInfo(3282157387, Enum.InfoType.Product)
        end)
        if v62 then
            u16.PremSeeds.Frame.Buy10.Tag.Text = u11.Comma(v63.PriceInRobux * 10)
            u16.PremSeeds.Frame.Buy10.Tag.Visible = true
        end
    end)
    u16.PremSeeds.Frame.Gift.Activated:Connect(function() --[[Anonymous function at line 253]]
        --[[
        Upvalues:
            [1] = u7
        --]]
        u7:PromptGift("10 Premium Night Seeds")
    end)
    u6:SetPriceLabel(u16.PremEggs.Frame.Buy1.Cost, 3282157151)
    u16.PremEggs.Frame.Buy1.Activated:Connect(function() --[[Anonymous function at line 259]]
        --[[
        Upvalues:
            [1] = u6
        --]]
        u6:PromptPurchase(3282157151, Enum.InfoType.Product)
    end)
    u6:SetPriceLabel(u16.PremEggs.Frame.Buy3.Cost, 3282157274)
    u16.PremEggs.Frame.Buy3.Activated:Connect(function() --[[Anonymous function at line 263]]
        --[[
        Upvalues:
            [1] = u6
        --]]
        u6:PromptPurchase(3282157274, Enum.InfoType.Product)
    end)
    u6:SetPriceLabel(u16.PremEggs.Frame.Buy10.Cost, 3282157312)
    u16.PremEggs.Frame.Buy10.Activated:Connect(function() --[[Anonymous function at line 267]]
        --[[
        Upvalues:
            [1] = u6
        --]]
        u6:PromptPurchase(3282157312, Enum.InfoType.Product)
    end)
    task.spawn(function() --[[Anonymous function at line 271]]
        --[[
        Upvalues:
            [1] = u16
            [2] = u10
            [3] = u3
            [4] = u11
        --]]
        u16.PremEggs.Frame.Buy10.Tag.Visible = false
        local v64, v65 = u10(30, 12, function() --[[Anonymous function at line 274]]
            --[[
            Upvalues:
                [1] = u3
            --]]
            return u3:GetProductInfo(3282157151, Enum.InfoType.Product)
        end)
        if v64 then
            u16.PremEggs.Frame.Buy10.Tag.Text = u11.Comma(v65.PriceInRobux * 10)
            u16.PremEggs.Frame.Buy10.Tag.Visible = true
        end
    end)
    u16.PremEggs.Frame.Gift.Activated:Connect(function() --[[Anonymous function at line 286]]
        --[[
        Upvalues:
            [1] = u7
        --]]
        u7:PromptGift("10 Premium Night Eggs")
    end)
    task.spawn(function() --[[Anonymous function at line 290]]
        --[[
        Upvalues:
            [1] = u10
            [2] = u4
            [3] = u14
            [4] = u21
            [5] = u17
        --]]
        local v66, v67 = u10(10, 23, function() --[[Anonymous function at line 291]]
            --[[
            Upvalues:
                [1] = u4
                [2] = u14
            --]]
            return u4:GetPolicyInfoForPlayerAsync(u14)
        end)
        if v66 then
            if not v67.ArePaidRandomItemsRestricted then
                u21 = true
                u17.Seeds.Visible = true
                u17.Eggs.Visible = true
            end
        else
            return
        end
    end)
end
task.spawn(u22.Start, u22)
return u22