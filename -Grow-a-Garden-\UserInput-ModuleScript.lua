-- Full Path: -Grow-a-Garden-\\UserInput-ModuleScript.lua
local u1 = require(script.Parent.Parent.Shared.Util)
local u2 = Enum.UserInputType:GetEnumItems()
for _, v3 in pairs(Enum.KeyCode:GetEnumItems()) do
    u2[#u2 + 1] = v3
end
local u8 = {
    ["Transform"] = function(p4) --[[Function name: Transform, line 10]]
        --[[
        Upvalues:
            [1] = u1
            [2] = u2
        --]]
        return u1.MakeFuzzyFinder(u2)(p4)
    end,
    ["Validate"] = function(p5) --[[Function name: Validate, line 16]]
        return #p5 > 0
    end,
    ["Autocomplete"] = function(p6) --[[Function name: Autocomplete, line 20]]
        --[[
        Upvalues:
            [1] = u1
        --]]
        return u1.GetNames(p6)
    end,
    ["Parse"] = function(p7) --[[Function name: Parse, line 24]]
        return p7[1]
    end
}
return function(p9) --[[Anonymous function at line 29]]
    --[[
    Upvalues:
        [1] = u8
        [2] = u1
    --]]
    p9:RegisterType("userInput", u8)
    p9:RegisterType("userInputs", u1.MakeListableType(u8))
end