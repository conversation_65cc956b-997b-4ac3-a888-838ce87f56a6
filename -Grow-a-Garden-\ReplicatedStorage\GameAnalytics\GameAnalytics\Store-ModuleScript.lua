-- Full Path: -Grow-a-Garden-\ReplicatedStorage\GameAnalytics\GameAnalytics\Store-ModuleScript.lua
local u1 = game:GetService("DataStoreService")
local u2 = game:GetService("RunService")
local u3 = require(script.DataStoreQueue)
local u21 = {
    ["PlayerDS"] = u2:IsStudio() and {} or u1:GetDataStore("GA_PlayerDS_1.0.0"),
    ["AutoSaveData"] = 180,
    ["BasePlayerData"] = {
        ["Sessions"] = 0,
        ["Transactions"] = 0,
        ["ProgressionTries"] = {},
        ["CurrentCustomDimension01"] = "",
        ["CurrentCustomDimension02"] = "",
        ["CurrentCustomDimension03"] = "",
        ["ConfigsHash"] = "",
        ["AbId"] = "",
        ["AbVariantId"] = "",
        ["InitAuthorized"] = false,
        ["SdkConfig"] = {},
        ["ClientServerTimeOffset"] = 0,
        ["Configurations"] = {},
        ["RemoteConfigsIsReady"] = false,
        ["PlayerTeleporting"] = false,
        ["OwnedGamepasses"] = nil,
        ["CountryCode"] = "",
        ["CustomUserId"] = ""
    },
    ["DataToSave"] = {
        "Sessions",
        "Transactions",
        "ProgressionTries",
        "CurrentCustomDimension01",
        "CurrentCustomDimension02",
        "CurrentCustomDimension03",
        "OwnedGamepasses"
    },
    ["PlayerCache"] = {},
    ["EventsQueue"] = {},
    ["DataStoreQueue"] = u3,
    ["GetPlayerData"] = function(_, p4) --[[Function name: GetPlayerData, line 45]]
        --[[
        Upvalues:
            [1] = u3
            [2] = u2
            [3] = u21
        --]]
        local u5 = p4.UserId
        local v6, v7 = u3.AddRequest(u5, function() --[[Anonymous function at line 47]]
            --[[
            Upvalues:
                [1] = u2
                [2] = u21
                [3] = u5
            --]]
            return u2:IsStudio() and {} or (u21.PlayerDS:GetAsync(u5) or {})
        end, 7)
        return not v6 and {} or v7
    end,
    ["GetPlayerDataFromCache"] = function(_, p8) --[[Function name: GetPlayerDataFromCache, line 57]]
        --[[
        Upvalues:
            [1] = u21
        --]]
        return u21.PlayerCache[tonumber(p8)] or u21.PlayerCache[tostring(p8)]
    end,
    ["GetErrorDataStore"] = function(_, u9) --[[Function name: GetErrorDataStore, line 66]]
        --[[
        Upvalues:
            [1] = u2
            [2] = u1
        --]]
        local u10 = nil
        return not pcall(function() --[[Anonymous function at line 68]]
            --[[
            Upvalues:
                [1] = u10
                [2] = u2
                [3] = u1
                [4] = u9
            --]]
            u10 = u2:IsStudio() and {} or u1:GetDataStore("GA_ErrorDS_1.0.0", u9)
        end) and {} or u10
    end,
    ["SavePlayerData"] = function(_, p11) --[[Function name: SavePlayerData, line 79]]
        --[[
        Upvalues:
            [1] = u21
            [2] = u2
            [3] = u3
        --]]
        local v12 = u21:GetPlayerDataFromCache(p11.UserId)
        local u13 = {}
        if v12 then
            for _, v14 in pairs(u21.DataToSave) do
                u13[v14] = v12[v14]
            end
            local u15 = p11.UserId
            if not u2:IsStudio() then
                u3.AddRequest(u15, function() --[[Anonymous function at line 96]]
                    --[[
                    Upvalues:
                        [1] = u21
                        [2] = u15
                        [3] = u13
                    --]]
                    return u21.PlayerDS:SetAsync(u15, u13)
                end, 7)
            end
        end
    end,
    ["IncrementErrorCount"] = function(_, u16, u17, u18) --[[Function name: IncrementErrorCount, line 102]]
        --[[
        Upvalues:
            [1] = u2
            [2] = u3
        --]]
        if u17 then
            local v19
            if u2:IsStudio() then
                v19 = 0
            else
                local v20
                v20, v19 = u3.AddRequest(u17, function() --[[Anonymous function at line 111]]
                    --[[
                    Upvalues:
                        [1] = u16
                        [2] = u17
                        [3] = u18
                    --]]
                    return u16:IncrementAsync(u17, u18)
                end, 7)
                _ = v20
            end
            return v19
        end
    end
}
return u21