-- Full Path: -Grow-a-Garden-\ReplicatedStorage\CullThrottle\Utilities\CameraCache-ModuleScript.lua
local u66 = {
    ["_connections"] = {},
    ["init"] = function(p1, p2) --[[Function name: init, line 13]]
        --[[
        Upvalues:
            [1] = u66
        --]]
        p1:cleanup()
        p1.Object = p2
        p1.CFrame = p1.Object.CFrame
        p1.Position = p1.CFrame.Position
        p1.FieldOfView = p1.Object.FieldOfView
        local v3 = p1.FieldOfView / 2
        local v4 = math.rad(v3)
        p1.HalfTanFOV = math.tan(v4)
        p1.ViewportSize = p1.Object.ViewportSize
        p1.AspectRatio = p1.ViewportSize.X / p1.ViewportSize.Y
        p1._connections.CFrameChanged = u66.Object:GetPropertyChangedSignal("CFrame"):Connect(function() --[[Anonymous function at line 24]]
            --[[
            Upvalues:
                [1] = u66
            --]]
            u66.CFrame = u66.Object.CFrame
            u66.Position = u66.CFrame.Position
        end)
        p1._connections.FieldOfViewChanged = u66.Object:GetPropertyChangedSignal("FieldOfView"):Connect(function() --[[Anonymous function at line 28]]
            --[[
            Upvalues:
                [1] = u66
            --]]
            u66.FieldOfView = u66.Object.FieldOfView
            local v5 = u66
            local v6 = u66.FieldOfView / 2
            local v7 = math.rad(v6)
            v5.HalfTanFOV = math.tan(v7)
        end)
        p1._connections.ViewportSizeChanged = u66.Object:GetPropertyChangedSignal("ViewportSize"):Connect(function() --[[Anonymous function at line 34]]
            --[[
            Upvalues:
                [1] = u66
            --]]
            u66.ViewportSize = u66.Object.ViewportSize
            u66.AspectRatio = u66.ViewportSize.X / u66.ViewportSize.Y
        end)
    end,
    ["cleanup"] = function(p8) --[[Function name: cleanup, line 40]]
        for _, v9 in p8._connections do
            v9:Disconnect()
        end
        p8._connections = {}
    end,
    ["getPlanesAndBounds"] = function(p10, p11) --[[Function name: getPlanesAndBounds, line 47]]
        --[[
        Upvalues:
            [1] = u66
        --]]
        local v12 = u66.CFrame
        local v13 = u66.Position
        local v14 = u66.HalfTanFOV * p10
        local v15 = v14 * u66.AspectRatio
        local v16 = v12 * CFrame.new(0, 0, -p10)
        local v17 = -v15
        local v18 = v16 * Vector3.new(v17, v14, 0)
        local v19 = v16 * Vector3.new(v15, v14, 0)
        local v20 = -v15
        local v21 = -v14
        local v22 = v16 * Vector3.new(v20, v21, 0)
        local v23 = -v14
        local v24 = v16 * Vector3.new(v15, v23, 0)
        local v25 = v12.UpVector
        local v26 = v12.RightVector
        local v27 = v25:Cross(v13 - v24).Unit
        local v28 = {
            v13,
            v25:Cross(v22 - v13).Unit,
            v13,
            v27,
            v13,
            v26:Cross(v19 - v13).Unit,
            v13,
            v26:Cross(v13 - v24).Unit,
            v16.Position,
            v12.LookVector
        }
        local v29 = v13.X
        local v30 = v18.X
        local v31 = v22.X
        local v32 = v19.X
        local v33 = v24.X
        local v34 = math.min(v29, v30, v31, v32, v33) // p11
        local v35 = v13.Y
        local v36 = v18.Y
        local v37 = v22.Y
        local v38 = v19.Y
        local v39 = v24.Y
        local v40 = math.min(v35, v36, v37, v38, v39) // p11
        local v41 = v13.Z
        local v42 = v18.Z
        local v43 = v22.Z
        local v44 = v19.Z
        local v45 = v24.Z
        local v46 = math.min(v41, v42, v43, v44, v45) // p11
        local v47 = Vector3.new(v34, v40, v46)
        local v48 = v13.X
        local v49 = v18.X
        local v50 = v22.X
        local v51 = v19.X
        local v52 = v24.X
        local v53 = math.max(v48, v49, v50, v51, v52) // p11
        local v54 = v13.Y
        local v55 = v18.Y
        local v56 = v22.Y
        local v57 = v19.Y
        local v58 = v24.Y
        local v59 = math.max(v54, v55, v56, v57, v58) // p11
        local v60 = v13.Z
        local v61 = v18.Z
        local v62 = v22.Z
        local v63 = v19.Z
        local v64 = v24.Z
        local v65 = math.max(v60, v61, v62, v63, v64) // p11
        return v28, v47, Vector3.new(v53, v59, v65)
    end
}
u66:init(workspace.CurrentCamera)
workspace:GetPropertyChangedSignal("CurrentCamera"):Connect(function() --[[Anonymous function at line 100]]
    --[[
    Upvalues:
        [1] = u66
    --]]
    u66:init(workspace.CurrentCamera)
end)
return u66