-- Full Path: -Grow-a-Garden-\StarterPlayer\StarterPlayerScripts\Preload-LocalScript.lua
local function v2() --[[Anonymous function at line 1]]
    if workspace:GetAttribute("PreloadMonsterMash") then
        print("Attempting Preload")
        task.wait(Random.new():NextNumber(10, 120))
        local v1 = Instance.new("Sound")
        v1.SoundId = "rbxassetid://95780928979580"
        game:GetService("ContentProvider"):PreloadAsync({ v1 })
    end
end
v2()
game:GetService("Workspace"):GetAttributeChangedSignal("PreloadMonsterMash"):Connect(v2)