-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\CosmeticServices\UserInterface\CosmeticActionUserInterfaceService-ModuleScript.lua
local v1 = game:GetService("Players")
local u2 = game:GetService("TweenService")
local v3 = game:GetService("ReplicatedStorage")
local u4 = game:GetService("CollectionService")
local u5 = game:GetService("UserInputService")
local u6 = v1.LocalPlayer:WaitForChild("PlayerGui"):WaitForChild("CosmeticUI"):WaitForChild("ActionMenu")
local v7 = require(v3.Data.CosmeticRegistry)
local u8 = require(v3.Modules.WaitForDescendant)
local u9 = require(v3.Modules.GetWorldToPosition)
local u10 = require(v3.Modules.GetUIElementWithTag)
local u11 = require(v3.Modules.CosmeticServices.UserInterface.CosmeticInventoryUserInterfaceService)
require(v3.Modules.PlaySound)
local v12 = script:WaitForChild("Actions")
local u13 = v7.DefaultActions
local u14 = v7.ActionRegistry
local u15 = u6:WaitForChild("ICON_INSERTION_POINT")
local u16 = u15:WaitForChild("ICON_TEMPLATE")
u16.Visible = false
local u17 = u6:WaitForChild("UIScale")
local u18 = 1
u18 = u5.TouchEnabled and 2.5 or 1
u5:GetPropertyChangedSignal("TouchEnabled"):Connect(function() --[[Anonymous function at line 42]]
    --[[
    Upvalues:
        [1] = u18
        [2] = u5
    --]]
    u18 = u5.TouchEnabled and 2.5 or 1
end)
local u19 = u18
local u20 = {
    ["Target"] = nil,
    ["CurrentState"] = false,
    ["MovementStates"] = {}
}
local u21 = {}
for _, v22 in v12:GetChildren() do
    local v23 = require(v22)
    u21[v22.Name] = v23(u20)
end
local function u39(p24) --[[Anonymous function at line 99]]
    --[[
    Upvalues:
        [1] = u14
        [2] = u16
        [3] = u15
        [4] = u8
        [5] = u2
        [6] = u20
        [7] = u21
    --]]
    for _, v25 in p24 do
        local v26 = u14[v25]
        if v26 then
            local u27 = u16:Clone()
            u27:AddTag("ActionButton")
            u27:SetAttribute("Action", v25)
            u27.Parent = u15
            u8(u27, "IMAGE_ICON").Image = v26.Image
            u27.LayoutOrder = v26.LayoutOrder
            u27.Visible = true
            u27.Name = "NewTemplate"
            local v28 = u27:WaitForChild("SENSOR")
            local u29 = u27:WaitForChild("Vignette")
            local u30 = u27:WaitForChild("UIStroke")
            local function u32(p31) --[[Anonymous function at line 118]]
                --[[
                Upvalues:
                    [1] = u2
                    [2] = u29
                    [3] = u30
                --]]
                u2:Create(u29, TweenInfo.new(0.2), {
                    ["BackgroundTransparency"] = p31 and 0.5 or 1
                }):Play()
                u2:Create(u30, TweenInfo.new(0.2), {
                    ["Color"] = p31 and Color3.fromRGB(255, 157, 0) or Color3.fromRGB(61, 32, 19),
                    ["Thickness"] = p31 and 3 or 1.55
                }):Play()
            end
            v28.MouseLeave:Connect(function() --[[Anonymous function at line 130]]
                --[[
                Upvalues:
                    [1] = u32
                --]]
                u32(false)
            end)
            v28.MouseEnter:Connect(function() --[[Anonymous function at line 134]]
                --[[
                Upvalues:
                    [1] = u32
                --]]
                u32(true)
            end)
            v28.MouseButton1Down:Connect(function() --[[Anonymous function at line 138]]
                --[[
                Upvalues:
                    [1] = u32
                    [2] = u20
                    [3] = u21
                    [4] = u27
                --]]
                u32(false)
                local v33 = u20.CurrentButton
                if v33 then
                    u20.CurrentButton = nil
                    local v34 = v33:GetAttribute("Action")
                    local v35 = u21[v34]
                    if v35 then
                        v35.InputEnded()
                    else
                        warn(v34, "Has no action handler!")
                    end
                end
                u20.CurrentButton = u27
                local v36 = u20.CurrentButton
                if v36 then
                    local v37 = v36:GetAttribute("Action")
                    local v38 = u21[v37]
                    if v38 then
                        v38.InputBegan()
                    else
                        warn(v37, "Has no action handler!")
                    end
                else
                    return
                end
            end)
            v28.MouseButton1Up:Connect(function() --[[Anonymous function at line 145]]
                --[[
                Upvalues:
                    [1] = u32
                --]]
                u32(true)
            end)
            u27.Parent = u15
        else
            warn(v25, "Has no action data!")
        end
    end
end
function u20.SetTarget(_, p40) --[[Anonymous function at line 153]]
    --[[
    Upvalues:
        [1] = u20
        [2] = u4
        [3] = u39
        [4] = u13
    --]]
    u20.Target = p40
    for _, v41 in u4:GetTagged("ActionButton") do
        v41:Destroy()
    end
    u39(u13)
end
function u20.Toggle(_, p42) --[[Anonymous function at line 164]]
    --[[
    Upvalues:
        [1] = u20
        [2] = u2
        [3] = u17
        [4] = u19
    --]]
    if u20.CurrentState ~= p42 then
        u20.CurrentState = p42
        local v43 = {
            ["Scale"] = p42 and u19 or 0
        }
        u2:Create(u17, TweenInfo.new(0.2), v43):Play()
    end
end
function u20.HardDisable(_) --[[Anonymous function at line 173]]
    --[[
    Upvalues:
        [1] = u20
        [2] = u21
    --]]
    u20:Toggle(false)
    u20.Target = nil
    local v44 = u20.CurrentButton
    if v44 then
        u20.CurrentButton = nil
        local v45 = v44:GetAttribute("Action")
        local v46 = u21[v45]
        if v46 then
            v46.InputEnded()
        else
            warn(v45, "Has no action handler!")
        end
    else
        return
    end
end
u20:Toggle(false)
task.spawn(function() --[[Anonymous function at line 181]]
    --[[
    Upvalues:
        [1] = u11
        [2] = u20
        [3] = u9
        [4] = u6
    --]]
    while true do
        task.wait(0.004166666666666667)
        local v47 = (u11.State and true or false) and ((u20.Target and true or false) and u20.Target)
        if v47 then
            v47 = not u20.Target:IsDescendantOf(workspace)
        end
        if v47 then
            u20:HardDisable()
        else
            local v48 = u20.Target
            if v48 then
                local v49, v50 = u9(v48:GetPivot().Position)
                u6.Position = v49
                u6.Visible = v50
            end
        end
    end
end)
local u58 = {
    [Enum.UserInputType.MouseButton1] = {
        ["Activate"] = function() --[[Anonymous function at line 216]]
            --[[
            Upvalues:
                [1] = u10
                [2] = u20
            --]]
            if not u10("ActionButton") then
                u20:HardDisable()
            end
        end
    },
    [Enum.KeyCode.ButtonR2] = { function() --[[Anonymous function at line 224]]
            --[[
            Upvalues:
                [1] = u20
                [2] = u21
                [3] = u10
            --]]
            local v51 = u20.CurrentButton
            if v51 then
                u20.CurrentButton = nil
                local v52 = v51:GetAttribute("Action")
                local v53 = u21[v52]
                if v53 then
                    v53.InputEnded()
                else
                    warn(v52, "Has no action handler!")
                end
            end
            local v54 = u10("ActionButton")
            if v54 then
                u20.CurrentButton = v54
                local v55 = u20.CurrentButton
                if v55 then
                    local v56 = v55:GetAttribute("Action")
                    local v57 = u21[v56]
                    if v57 then
                        v57.InputBegan()
                    else
                        warn(v56, "Has no action handler!")
                    end
                else
                    return
                end
            else
                u20:HardDisable()
                return
            end
        end }
}
local u65 = {
    [Enum.UserInputType.MouseButton1] = {
        ["Deactivate"] = function() --[[Anonymous function at line 232]]
            --[[
            Upvalues:
                [1] = u20
                [2] = u21
            --]]
            local v59 = u20.CurrentButton
            if v59 then
                u20.CurrentButton = nil
                local v60 = v59:GetAttribute("Action")
                local v61 = u21[v60]
                if v61 then
                    v61.InputEnded()
                else
                    warn(v60, "Has no action handler!")
                end
            else
                return
            end
        end
    },
    [Enum.KeyCode.ButtonR2] = { function() --[[Anonymous function at line 237]]
            --[[
            Upvalues:
                [1] = u20
                [2] = u21
            --]]
            local v62 = u20.CurrentButton
            if v62 then
                u20.CurrentButton = nil
                local v63 = v62:GetAttribute("Action")
                local v64 = u21[v63]
                if v64 then
                    v64.InputEnded()
                else
                    warn(v63, "Has no action handler!")
                end
            else
                return
            end
        end }
}
u5.TouchEnded:Connect(function() --[[Anonymous function at line 247]]
    --[[
    Upvalues:
        [1] = u20
        [2] = u21
    --]]
    local v66 = u20.CurrentButton
    if v66 then
        u20.CurrentButton = nil
        local v67 = v66:GetAttribute("Action")
        local v68 = u21[v67]
        if v68 then
            v68.InputEnded()
        else
            warn(v67, "Has no action handler!")
        end
    else
        return
    end
end)
u5.InputBegan:Connect(function(u69, _) --[[Anonymous function at line 251]]
    --[[
    Upvalues:
        [1] = u58
    --]]
    local v70 = u58[u69.KeyCode] or u58[u69.UserInputType]
    if v70 then
        for _, u71 in v70 do
            task.spawn(function() --[[Anonymous function at line 255]]
                --[[
                Upvalues:
                    [1] = u71
                    [2] = u69
                --]]
                u71(u69)
            end)
        end
    end
end)
u5.InputEnded:Connect(function(u72, _) --[[Anonymous function at line 262]]
    --[[
    Upvalues:
        [1] = u65
    --]]
    local v73 = u65[u72.KeyCode] or u65[u72.UserInputType]
    if v73 then
        for _, u74 in v73 do
            task.spawn(function() --[[Anonymous function at line 266]]
                --[[
                Upvalues:
                    [1] = u74
                    [2] = u72
                --]]
                u74(u72)
            end)
        end
    end
end)
return u20