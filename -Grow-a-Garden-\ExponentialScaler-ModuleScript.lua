-- Full Path: -Grow-a-Garden-\\ExponentialScaler-ModuleScript.lua
local u1 = {}
u1.__index = u1
function u1.new(p2) --[[Anonymous function at line 4]]
    --[[
    Upvalues:
        [1] = u1
    --]]
    local v3 = u1
    local v4 = setmetatable({}, v3)
    v4.BaseCost = p2.BaseCost or 100
    v4.ScalingFactor = p2.ScalingFactor or 1.2
    return v4
end
function u1.GetCost(p5, p6) --[[Anonymous function at line 11]]
    local v7 = p5.BaseCost
    local v8 = p5.ScalingFactor
    return v7 * math.pow(p6, v8)
end
return u1