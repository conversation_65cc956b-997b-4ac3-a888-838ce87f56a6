-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\PetServices\PetEggRenderer-ModuleScript.lua
local u1 = game:GetService("ReplicatedStorage")
game:GetService("ServerScriptService")
game:GetService("ServerScriptService")
game:GetService("RunService")
local _ = u1.Assets.SFX
local _ = u1.Assets.VFX
require(u1.Modules.TweenModel)
local v2 = require(u1.Modules.CreateTagHandler)
local v3 = u1:WaitForChild("Assets")
local u4 = v3:WaitForChild("Models"):WaitForChild("EggModels")
local u5 = require(u1.Data.PetRegistry)
local _ = u5.PetList
local _ = v3.SFX
local _ = v3.VFX
game:GetService("TweenService")
local u6 = u1.GameEvents.PetEggService
local v7 = u1.GameEvents.PetSkipped
local v8 = u1.GameEvents.EggReadyToHatch_RE
local u9 = {}
local u10 = {}
local u11 = {}
local u12 = {}
for _, v13 in script.EggEffects:GetChildren() do
    u9[v13.Name] = require(v13)
end
local u14 = game.Players.LocalPlayer
local function u22(p15, p16) --[[Anonymous function at line 51]]
    --[[
    Upvalues:
        [1] = u10
        [2] = u11
        [3] = u9
    --]]
    if p15 then
        local v17 = u10[p15]
        if v17 then
            local v18 = v17.Asset
            if v18 then
                local v19 = p15:GetAttribute("OBJECT_UUID")
                if v19 then
                    if v18:GetAttribute("DoingHatch") then
                        return
                    else
                        v18:SetAttribute("DoingHatch", true)
                        v18.Parent = workspace.Visuals
                        local v20 = u11[v19]
                        local v21 = p15:GetAttribute("EggName")
                        if u9[v21] then
                            u9[v21](v18, v20, p16)
                            return
                        elseif v21 == "Rare Egg" or (v21 == "Legendary Egg" or v21 == "Mythical Egg") then
                            u9.Rare(v18, v20, p16)
                        else
                            u9.Normal(v18, v20, p16)
                        end
                    end
                else
                    return warn("DoHatchAnim | No egg uuid!")
                end
            else
                warn("DoHatchAnim | No asset!")
                return
            end
        else
            warn("DoHatchAnim | No state data for server egg")
            return
        end
    else
        warn("DoHatchAnim | No server egg!")
        return
    end
end
local function u28(p23, p24) --[[Anonymous function at line 82]]
    --[[
    Upvalues:
        [1] = u10
        [2] = u22
    --]]
    if p23 then
        local v25 = u10[p23]
        if v25 then
            local v26 = v25.Asset
            if v26 then
                local v27 = v26:FindFirstChild("ProximityPrompt")
                if not v27 then
                    return warn((("PetEggRender:RenderEgg | No Proximity Prompt found for %*"):format(v26)))
                end
                v27:Destroy()
                u22(p23, p24)
            else
                warn("ActivateEgg | No asset!")
            end
        else
            warn("ActivateEgg | No state data for server egg")
            return
        end
    else
        warn("ActivateEgg | No server egg!")
        return
    end
end
local function u35(p29) --[[Anonymous function at line 99]]
    --[[
    Upvalues:
        [1] = u14
        [2] = u10
    --]]
    if p29:GetAttribute("OWNER") == u14.Name then
        local v30 = p29:GetAttribute("TimeToHatch")
        local v31 = u10[p29]
        if v31 then
            local v32 = v31.Asset
            if v32 then
                local v33 = v32:FindFirstChild("ProximityPrompt")
                local v34 = v32:FindFirstChild("SkipPrompt")
                if v33 then
                    if v34 then
                        if v30 <= 0 then
                            v33.Enabled = true
                            v34.Enabled = false
                        else
                            v33.Enabled = false
                            v34.Enabled = true
                        end
                    else
                        return warn((("CheckEggTimer | No Skip Prompt found for %*"):format(v32)))
                    end
                else
                    return warn((("CheckEggTimer | No Proximity Prompt found for %*"):format(v32)))
                end
            else
                warn("CheckEggTimer | No asset!")
                return
            end
        else
            warn("CheckEggTimer | No state data for server egg")
            return
        end
    else
        return
    end
end
function u12.RenderEgg(_, u36) --[[Anonymous function at line 126]]
    --[[
    Upvalues:
        [1] = u4
        [2] = u5
        [3] = u35
        [4] = u10
        [5] = u14
        [6] = u1
        [7] = u6
    --]]
    if not u36:GetAttribute("READY") then
        u36:GetAttributeChangedSignal("READY"):Wait()
    end
    local v37 = u36:GetAttribute("EggName")
    if not v37 then
        local v38 = 0
        repeat
            task.wait(0.5)
            v37 = u36:GetAttribute("EggName")
            print("tryting to eggname for", u36:GetFullName(), v37)
            v38 = v38 + 1
        until v38 >= 5 or v37
    end
    if v37 then
        local v39 = u4:FindFirstChild(v37)
        if v39 then
            local u40 = v39:Clone()
            u40.PrimaryPart.Anchored = true
            local u41 = u5.PetEggs[v37].HatchTime
            local v42 = 1 - 0.5 * (u36:GetAttribute("TimeToHatch") / u41)
            u40:ScaleTo((math.clamp(v42, 0.2, 1)))
            workspace.Terrain.EggParticle.WorldCFrame = u36:GetPivot()
            for _, v43 in workspace.Terrain.EggParticle:GetDescendants() do
                v43:Emit(v43:GetAttribute("EmitCount"))
            end
            local u44 = Vector3.new(0, 1.5, 0)
            local u45 = 0
            task.spawn(function() --[[Anonymous function at line 173]]
                --[[
                Upvalues:
                    [1] = u45
                    [2] = u44
                    [3] = u40
                    [4] = u36
                --]]
                while u45 < 0.25 do
                    u45 = u45 + game:GetService("RunService").Heartbeat:Wait()
                    u44 = (Vector3.new(0, 3, 0)):Lerp(Vector3.new(0, 0, 0), (game.TweenService:GetValue(u45 / 0.25, Enum.EasingStyle.Quad, Enum.EasingDirection.Out)))
                    local v46 = u40:GetExtentsSize().Y
                    u40:PivotTo(u36:GetPivot() * CFrame.new(0, u44.Y + v46 * 0.5, 0))
                end
            end)
            u36:GetAttributeChangedSignal("TimeToHatch"):Connect(function() --[[Anonymous function at line 186]]
                --[[
                Upvalues:
                    [1] = u40
                    [2] = u36
                    [3] = u41
                    [4] = u44
                    [5] = u35
                --]]
                local v47 = u40
                local v48 = 1 - 0.5 * (u36:GetAttribute("TimeToHatch") / u41)
                v47:ScaleTo((math.clamp(v48, 0.2, 1)))
                local v49 = u40:GetExtentsSize().Y
                u40:PivotTo(u36:GetPivot() * CFrame.new(0, u44.Y + v49 * 0.5, 0))
                u35(u36)
            end)
            u10[u36] = {
                ["Asset"] = u40
            }
            local v50 = u40:GetExtentsSize().Y
            u40:PivotTo(u36:GetPivot() * CFrame.new(0, u44.Y + v50 * 0.4, 0))
            u40.Parent = u36
            if u36:GetAttribute("OWNER") == u14.Name then
                local v51 = u1.ProximityPrompt:Clone()
                if not v51 then
                    return warn("PetEggRender:RenderEgg | No Proximity Prompt found in ReplicatedStorage!")
                end
                v51.Parent = u40
                v51.ActionText = "Hatch!"
                v51.HoldDuration = 1
                v51.Enabled = false
                v51.Triggered:Connect(function() --[[Anonymous function at line 214]]
                    --[[
                    Upvalues:
                        [1] = u6
                        [2] = u36
                    --]]
                    u6:FireServer("HatchPet", u36)
                end)
                local v52 = u1.ProximityPrompt:Clone()
                if not v52 then
                    return warn("PetEggRender:RenderEgg | No Proximity Prompt found in ReplicatedStorage!")
                end
                v52.Parent = u40
                v52.ActionText = "Skip Growth"
                v52.Name = "SkipPrompt"
                v52.HoldDuration = 1
                v52.Enabled = false
                v52.Triggered:Connect(function() --[[Anonymous function at line 224]]
                    --[[
                    Upvalues:
                        [1] = u6
                        [2] = u36
                    --]]
                    u6:FireServer("AuthorisePurchase", u36)
                end)
                u35(u36)
            end
        else
            return warn((("Could not find egg model associated with %*"):format(v37)))
        end
    else
        return
    end
end
function u12.DerenderEgg(_, p53) --[[Anonymous function at line 233]]
    --[[
    Upvalues:
        [1] = u10
    --]]
    if p53 then
        local v54 = u10[p53]
        if v54 then
            local v55 = v54.Asset
            if v55 then
                if not v55:GetAttribute("DoingHatch") then
                    v55:Destroy()
                end
            else
                warn("PetEggRender:DerenderEgg | No asset to derender")
            end
        else
            warn("PetEggRender:DerenderEgg | No state data for server egg")
            return
        end
    else
        warn("PetEggRender:DerenderEgg | No server egg to derender")
        return
    end
end
v2({
    ["Tag"] = "PetEggServer",
    ["OnInstanceAdded"] = function(p56) --[[Function name: OnInstanceAdded, line 251]]
        --[[
        Upvalues:
            [1] = u12
        --]]
        if p56:IsDescendantOf(workspace) then
            u12:RenderEgg(p56)
        end
    end,
    ["OnInstanceRemoved"] = function(p57) --[[Function name: OnInstanceRemoved, line 255]]
        --[[
        Upvalues:
            [1] = u12
        --]]
        u12:DerenderEgg(p57)
    end
})
v8.OnClientEvent:Connect(function(p58, p59) --[[Anonymous function at line 260]]
    --[[
    Upvalues:
        [1] = u11
    --]]
    u11[p59] = p58
end)
u6.OnClientEvent:Connect(function(p60, p61) --[[Anonymous function at line 264]]
    --[[
    Upvalues:
        [1] = u28
    --]]
    u28(p60, p61)
end)
local u62 = require(script.SkipEgg)
v7.OnClientEvent:Connect(function(p63) --[[Anonymous function at line 269]]
    --[[
    Upvalues:
        [1] = u62
    --]]
    u62(p63)
end)
return u12