-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\ProximityPromptController-ModuleScript.lua
local u1 = game:GetService("ProximityPromptService")
local v2 = {}
local u3 = {}
function v2.AddDisabler(_, p4) --[[Anonymous function at line 17]]
    --[[
    Upvalues:
        [1] = u3
        [2] = u1
    --]]
    if not table.find(u3, p4) then
        local v5 = u3
        table.insert(v5, p4)
        u1.Enabled = #u3 == 0
    end
end
function v2.RemoveDisabler(_, p6) --[[Anonymous function at line 29]]
    --[[
    Upvalues:
        [1] = u3
        [2] = u1
    --]]
    local v7 = table.find(u3, p6)
    if v7 then
        table.remove(u3, v7)
        u1.Enabled = #u3 == 0
    end
end
return v2