-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\Icon\Utility-ModuleScript.lua
local u1 = {}
local u2 = game:GetService("Players").LocalPlayer
function u1.createStagger(p3, u4, u5) --[[Anonymous function at line 13]]
    local u6 = false
    local u7 = false
    local u8 = (not p3 or p3 == 0) and 0.01 or p3
    local function u12(...) --[[Anonymous function at line 29]]
        --[[
        Upvalues:
            [1] = u6
            [2] = u7
            [3] = u5
            [4] = u8
            [5] = u4
            [6] = u12
        --]]
        if u6 then
            u7 = true
        else
            local u9 = table.pack(...)
            u6 = true
            u7 = false
            task.spawn(function() --[[Anonymous function at line 37]]
                --[[
                Upvalues:
                    [1] = u5
                    [2] = u8
                    [3] = u4
                    [4] = u9
                --]]
                if u5 then
                    task.wait(u8)
                end
                local v10 = u9
                u4(table.unpack(v10))
            end)
            task.delay(u8, function() --[[Anonymous function at line 43]]
                --[[
                Upvalues:
                    [1] = u6
                    [2] = u7
                    [3] = u12
                    [4] = u9
                --]]
                u6 = false
                if u7 then
                    local v11 = u9
                    u12(table.unpack(v11))
                end
            end)
        end
    end
    return u12
end
function u1.round(p13) --[[Anonymous function at line 55]]
    local v14 = p13 + 0.5
    return math.floor(v14)
end
function u1.reverseTable(p15) --[[Anonymous function at line 60]]
    local v16 = #p15 / 2
    for v17 = 1, math.floor(v16) do
        local v18 = #p15 - v17 + 1
        local v19 = p15[v18]
        local v20 = p15[v17]
        p15[v17] = v19
        p15[v18] = v20
    end
end
function u1.copyTable(p21) --[[Anonymous function at line 67]]
    --[[
    Upvalues:
        [1] = u1
    --]]
    local v22 = type(p21) == "table"
    assert(v22, "First argument must be a table")
    local v23 = table.create(#p21)
    for v24, v25 in pairs(p21) do
        if type(v25) == "table" then
            v23[v24] = u1.copyTable(v25)
        else
            v23[v24] = v25
        end
    end
    return v23
end
local u26 = {
    "a",
    "b",
    "c",
    "d",
    "e",
    "f",
    "g",
    "h",
    "i",
    "j",
    "k",
    "l",
    "m",
    "n",
    "o",
    "p",
    "q",
    "r",
    "s",
    "t",
    "u",
    "v",
    "w",
    "x",
    "y",
    "z",
    "A",
    "B",
    "C",
    "D",
    "E",
    "F",
    "G",
    "H",
    "I",
    "J",
    "K",
    "L",
    "M",
    "N",
    "O",
    "P",
    "Q",
    "R",
    "S",
    "T",
    "U",
    "V",
    "W",
    "X",
    "Y",
    "Z",
    "1",
    "2",
    "3",
    "4",
    "5",
    "6",
    "7",
    "8",
    "9",
    "0",
    "<",
    ">",
    "?",
    "@",
    "{",
    "}",
    "[",
    "]",
    "!",
    "(",
    ")",
    "=",
    "+",
    "~",
    "#"
}
function u1.generateUID(p27) --[[Anonymous function at line 82]]
    --[[
    Upvalues:
        [1] = u26
    --]]
    local v28 = u26
    local v29 = #v28
    local v30 = ""
    for _ = 1, p27 or 8 do
        v30 = v30 .. v28[math.random(1, v29)]
    end
    return v30
end
local u31 = {}
function u1.setVisible(u32, p33, p34) --[[Anonymous function at line 95]]
    --[[
    Upvalues:
        [1] = u31
    --]]
    local v35 = u31[u32]
    if not v35 then
        v35 = {}
        u31[u32] = v35
        u32.Destroying:Once(function() --[[Anonymous function at line 104]]
            --[[
            Upvalues:
                [1] = u31
                [2] = u32
            --]]
            u31[u32] = nil
        end)
    end
    if p33 then
        v35[p34] = nil
    else
        v35[p34] = true
    end
    if p33 then
        for _, _ in pairs(v35) do
            p33 = false
            break
        end
    end
    u32.Visible = p33
end
function u1.formatStateName(p36) --[[Anonymous function at line 123]]
    return string.upper((string.sub(p36, 1, 1))) .. string.lower((string.sub(p36, 2)))
end
function u1.localPlayerRespawned(p37) --[[Anonymous function at line 127]]
    --[[
    Upvalues:
        [1] = u2
    --]]
    u2.CharacterRemoving:Connect(p37)
end
function u1.getClippedContainer(p38) --[[Anonymous function at line 137]]
    local v39 = p38:FindFirstChild("ClippedContainer")
    if not v39 then
        v39 = Instance.new("Folder")
        v39.Name = "ClippedContainer"
        v39.Parent = p38
    end
    return v39
end
local u40 = require(script.Parent.Packages.Janitor)
local u41 = game:GetService("GuiService")
function u1.clipOutside(u42, u43) --[[Anonymous function at line 151]]
    --[[
    Upvalues:
        [1] = u40
        [2] = u1
        [3] = u41
    --]]
    local u44 = u42.janitor:add(u40.new())
    u43.Destroying:Once(function() --[[Anonymous function at line 153]]
        --[[
        Upvalues:
            [1] = u44
        --]]
        u44:Destroy()
    end)
    u42.janitor:add(u43)
    local u45 = u43.Parent
    local u46 = u44:add(Instance.new("Frame"))
    u46:SetAttribute("IsAClippedClone", true)
    u46.Name = u43.Name
    u46.AnchorPoint = u43.AnchorPoint
    u46.Size = u43.Size
    u46.Position = u43.Position
    u46.BackgroundTransparency = 1
    u46.LayoutOrder = u43.LayoutOrder
    u46.Parent = u45
    local v47 = Instance.new("ObjectValue")
    v47.Name = "OriginalInstance"
    v47.Value = u43
    v47.Parent = u46
    local v48 = v47:Clone()
    u43:SetAttribute("HasAClippedClone", true)
    v48.Name = "ClippedClone"
    v48.Value = u46
    v48.Parent = u43
    local u49 = nil
    local function v51() --[[Anonymous function at line 181]]
        --[[
        Upvalues:
            [1] = u45
            [2] = u49
            [3] = u43
            [4] = u1
        --]]
        local v50 = u45:FindFirstAncestorWhichIsA("ScreenGui")
        if not string.match(v50.Name, "Clipped") then
            v50 = v50.Parent[v50.Name .. "Clipped"]
        end
        u49 = v50
        u43.AnchorPoint = Vector2.new(0, 0)
        u43.Parent = u1.getClippedContainer(u49)
    end
    u44:add(u42.alignmentChanged:Connect(v51))
    v51()
    local u52 = u49
    for _, v53 in pairs(u43:GetChildren()) do
        if v53:IsA("UIAspectRatioConstraint") then
            v53:Clone().Parent = u46
        end
    end
    local u54 = u42.widget
    local u55 = false
    local u56 = u43:GetAttribute("IgnoreVisibilityUpdater")
    local function v58() --[[Anonymous function at line 203]]
        --[[
        Upvalues:
            [1] = u56
            [2] = u54
            [3] = u55
            [4] = u1
            [5] = u43
        --]]
        if not u56 then
            local v57 = u54.Visible
            if u55 then
                v57 = false
            end
            u1.setVisible(u43, v57, "ClipHandler")
        end
    end
    u44:add(u54:GetPropertyChangedSignal("Visible"):Connect(v58))
    local u59 = nil
    local u60 = require(u42.iconModule)
    local function u74() --[[Anonymous function at line 218]]
        --[[
        Upvalues:
            [1] = u42
            [2] = u43
            [3] = u60
            [4] = u55
            [5] = u56
            [6] = u54
            [7] = u1
            [8] = u59
            [9] = u74
            [10] = u44
        --]]
        task.defer(function() --[[Anonymous function at line 220]]
            --[[
            Upvalues:
                [1] = u42
                [2] = u43
                [3] = u60
                [4] = u55
                [5] = u56
                [6] = u54
                [7] = u1
                [8] = u59
                [9] = u74
                [10] = u44
            --]]
            local v61 = nil
            local v62 = u42.UID
            local v63
            if u43:GetAttribute("ClipToJoinedParent") then
                v63 = v62
                for _ = 1, 10 do
                    local v64 = u60.getIconByUID(v62)
                    if not v64 then
                        break
                    end
                    local v65 = v64.joinedFrame
                    v62 = v64.parentIconUID
                    if not v65 then
                        break
                    end
                    v61 = v65
                end
            else
                v63 = v62
            end
            if v61 then
                local v66 = u43.AbsolutePosition
                local v67 = u43.AbsoluteSize / 2
                local v68 = v61.AbsolutePosition
                local v69 = v61.AbsoluteSize
                local v70 = v66 + v67
                local v71 = v70.X < v68.X or (v70.X > v68.X + v69.X or (v70.Y < v68.Y or v70.Y > v68.Y + v69.Y))
                if v71 ~= u55 then
                    u55 = v71
                    if not u56 then
                        local v72 = u54.Visible
                        if u55 then
                            v72 = false
                        end
                        u1.setVisible(u43, v72, "ClipHandler")
                    end
                end
                if v61:IsA("ScrollingFrame") and u59 ~= v61 then
                    u59 = v61
                    u44:add(v61:GetPropertyChangedSignal("AbsoluteWindowSize"):Connect(function() --[[Anonymous function at line 262]]
                        --[[
                        Upvalues:
                            [1] = u74
                        --]]
                        u74()
                    end), "Disconnect", "TrackUtilityScroller-" .. v63)
                end
                return
            else
                u55 = false
                if not u56 then
                    local v73 = u54.Visible
                    if u55 then
                        v73 = false
                    end
                    u1.setVisible(u43, v73, "ClipHandler")
                end
            end
        end)
    end
    local u75 = workspace.CurrentCamera
    local u76 = u43:GetAttribute("AdditionalOffsetX") or 0
    local function v105(u77) --[[Anonymous function at line 272]]
        --[[
        Upvalues:
            [1] = u46
            [2] = u75
            [3] = u43
            [4] = u41
            [5] = u52
            [6] = u42
            [7] = u76
            [8] = u60
            [9] = u55
            [10] = u56
            [11] = u54
            [12] = u1
            [13] = u59
            [14] = u74
            [15] = u44
        --]]
        local u78 = "Absolute" .. u77
        local function v102() --[[Anonymous function at line 274]]
            --[[
            Upvalues:
                [1] = u46
                [2] = u78
                [3] = u77
                [4] = u75
                [5] = u43
                [6] = u41
                [7] = u52
                [8] = u42
                [9] = u76
                [10] = u60
                [11] = u55
                [12] = u56
                [13] = u54
                [14] = u1
                [15] = u59
                [16] = u74
                [17] = u44
            --]]
            local v79 = u46[u78]
            local v80 = UDim2.fromOffset(v79.X, v79.Y)
            if u77 == "Position" then
                local v81 = u75.ViewportSize.X - u43.AbsoluteSize.X - 4
                local v82 = v80.X.Offset
                if v82 < 4 then
                    v81 = 4
                elseif v81 >= v82 then
                    v81 = v82
                end
                local v83 = UDim2.fromOffset(v81, v80.Y.Offset)
                local v84 = u41.TopbarInset
                local v85 = workspace.CurrentCamera.ViewportSize.X
                local v86 = u52.AbsoluteSize.X
                local v87 = u52.AbsolutePosition.X
                local _ = v87 - v84.Min.X
                if not u42.isOldTopbar then
                    v87 = v85 - v86 - 0
                end
                local v88 = v87 - u76
                v80 = v83 + UDim2.fromOffset(-v88, v84.Height)
                task.defer(function() --[[Anonymous function at line 220]]
                    --[[
                    Upvalues:
                        [1] = u42
                        [2] = u43
                        [3] = u60
                        [4] = u55
                        [5] = u56
                        [6] = u54
                        [7] = u1
                        [8] = u59
                        [9] = u74
                        [10] = u44
                    --]]
                    local v89 = nil
                    local v90 = u42.UID
                    local v91
                    if u43:GetAttribute("ClipToJoinedParent") then
                        v91 = v90
                        for _ = 1, 10 do
                            local v92 = u60.getIconByUID(v90)
                            if not v92 then
                                break
                            end
                            local v93 = v92.joinedFrame
                            v90 = v92.parentIconUID
                            if not v93 then
                                break
                            end
                            v89 = v93
                        end
                    else
                        v91 = v90
                    end
                    if v89 then
                        local v94 = u43.AbsolutePosition
                        local v95 = u43.AbsoluteSize / 2
                        local v96 = v89.AbsolutePosition
                        local v97 = v89.AbsoluteSize
                        local v98 = v94 + v95
                        local v99 = v98.X < v96.X or (v98.X > v96.X + v97.X or (v98.Y < v96.Y or v98.Y > v96.Y + v97.Y))
                        if v99 ~= u55 then
                            u55 = v99
                            if not u56 then
                                local v100 = u54.Visible
                                if u55 then
                                    v100 = false
                                end
                                u1.setVisible(u43, v100, "ClipHandler")
                            end
                        end
                        if v89:IsA("ScrollingFrame") and u59 ~= v89 then
                            u59 = v89
                            u44:add(v89:GetPropertyChangedSignal("AbsoluteWindowSize"):Connect(function() --[[Anonymous function at line 262]]
                                --[[
                                Upvalues:
                                    [1] = u74
                                --]]
                                u74()
                            end), "Disconnect", "TrackUtilityScroller-" .. v91)
                        end
                        return
                    else
                        u55 = false
                        if not u56 then
                            local v101 = u54.Visible
                            if u55 then
                                v101 = false
                            end
                            u1.setVisible(u43, v101, "ClipHandler")
                        end
                    end
                end)
            end
            u43[u77] = v80
        end
        local v103 = u1.createStagger(0.01, v102)
        u44:add(u46:GetPropertyChangedSignal(u78):Connect(v103))
        local v104 = u1.createStagger(0.5, v102, true)
        u44:add(u46:GetPropertyChangedSignal(u78):Connect(v104))
    end
    task.delay(0.1, u74)
    task.defer(function() --[[Anonymous function at line 220]]
        --[[
        Upvalues:
            [1] = u42
            [2] = u43
            [3] = u60
            [4] = u55
            [5] = u56
            [6] = u54
            [7] = u1
            [8] = u59
            [9] = u74
            [10] = u44
        --]]
        local v106 = nil
        local v107 = u42.UID
        local v108
        if u43:GetAttribute("ClipToJoinedParent") then
            v108 = v107
            for _ = 1, 10 do
                local v109 = u60.getIconByUID(v107)
                if not v109 then
                    break
                end
                local v110 = v109.joinedFrame
                v107 = v109.parentIconUID
                if not v110 then
                    break
                end
                v106 = v110
            end
        else
            v108 = v107
        end
        if v106 then
            local v111 = u43.AbsolutePosition
            local v112 = u43.AbsoluteSize / 2
            local v113 = v106.AbsolutePosition
            local v114 = v106.AbsoluteSize
            local v115 = v111 + v112
            local v116 = v115.X < v113.X or (v115.X > v113.X + v114.X or (v115.Y < v113.Y or v115.Y > v113.Y + v114.Y))
            if v116 ~= u55 then
                u55 = v116
                if not u56 then
                    local v117 = u54.Visible
                    if u55 then
                        v117 = false
                    end
                    u1.setVisible(u43, v117, "ClipHandler")
                end
            end
            if v106:IsA("ScrollingFrame") and u59 ~= v106 then
                u59 = v106
                u44:add(v106:GetPropertyChangedSignal("AbsoluteWindowSize"):Connect(function() --[[Anonymous function at line 262]]
                    --[[
                    Upvalues:
                        [1] = u74
                    --]]
                    u74()
                end), "Disconnect", "TrackUtilityScroller-" .. v108)
            end
            return
        else
            u55 = false
            if not u56 then
                local v118 = u54.Visible
                if u55 then
                    v118 = false
                end
                u1.setVisible(u43, v118, "ClipHandler")
            end
        end
    end)
    if not u56 then
        local v119 = u54.Visible
        if u55 then
            v119 = false
        end
        u1.setVisible(u43, v119, "ClipHandler")
    end
    v105("Position")
    u44:add(u43:GetPropertyChangedSignal("Visible"):Connect(function() --[[Anonymous function at line 333]] end))
    if u43:GetAttribute("TrackCloneSize") then
        v105("Size")
    else
        u44:add(u43:GetPropertyChangedSignal("AbsoluteSize"):Connect(function() --[[Anonymous function at line 343]]
            --[[
            Upvalues:
                [1] = u43
                [2] = u46
            --]]
            local v120 = u43.AbsoluteSize
            u46.Size = UDim2.fromOffset(v120.X, v120.Y)
        end))
    end
    return u46
end
function u1.joinFeature(u121, u122, u123, p124) --[[Anonymous function at line 352]]
    local v125 = u121.joinJanitor
    v125:clean()
    if p124 then
        u121.parentIconUID = u122.UID
        u121.joinedFrame = p124
        v125:add(u122.alignmentChanged:Connect(function() --[[Function name: updateAlignent, line 363]]
            --[[
            Upvalues:
                [1] = u122
                [2] = u121
            --]]
            local v126 = u122.alignment
            u121:setAlignment(v126 == "Center" and "Left" or v126, true)
        end))
        local v127 = u122.alignment
        u121:setAlignment(v127 == "Center" and "Left" or v127, true)
        u121:modifyTheme({ "IconButton", "BackgroundTransparency", 1 }, "JoinModification")
        u121:modifyTheme({ "ClickRegion", "Active", false }, "JoinModification")
        if u122.childModifications then
            task.defer(function() --[[Anonymous function at line 378]]
                --[[
                Upvalues:
                    [1] = u121
                    [2] = u122
                --]]
                u121:modifyTheme(u122.childModifications, u122.childModificationsUID)
            end)
        end
        local u128 = u121:getInstance("ClickRegion")
        local function v129() --[[Anonymous function at line 384]]
            --[[
            Upvalues:
                [1] = u128
                [2] = u122
            --]]
            u128.Selectable = u122.isSelected
        end
        v125:add(u122.toggled:Connect(v129))
        task.defer(v129)
        v125:add(function() --[[Anonymous function at line 389]]
            --[[
            Upvalues:
                [1] = u128
            --]]
            u128.Selectable = true
        end)
        local u130 = u121.UID
        table.insert(u123, u130)
        u122:autoDeselect(false)
        u122.childIconsDict[u130] = true
        if not u122.isEnabled then
            u122:setEnabled(true)
        end
        u121.joinedParent:Fire(u122)
        v125:add(function() --[[Anonymous function at line 407]]
            --[[
            Upvalues:
                [1] = u121
                [2] = u123
                [3] = u130
                [4] = u122
            --]]
            if not u121.joinedFrame then
                return
            end
            for v131, v132 in pairs(u123) do
                if v132 == u130 then
                    table.remove(u123, v131)
                    break
                end
            end
            local v133 = require(u121.iconModule).getIconByUID(u121.parentIconUID)
            if v133 then
                u121:setAlignment(u121.originalAlignment)
                u121.parentIconUID = false
                u121.joinedFrame = false
                u121:setBehaviour("IconButton", "BackgroundTransparency", nil, true)
                u121:removeModification("JoinModification")
                local v134 = true
                local v135 = v133.childIconsDict
                v135[u130] = nil
                for _, _ in pairs(v135) do
                    v134 = false
                    break
                end
                if v134 and not v133.isAnOverflow then
                    v133:setEnabled(false)
                end
                local v136 = u122.alignment
                u121:setAlignment(v136 == "Center" and "Left" or v136, true)
            end
        end)
    else
        u121:leave()
    end
end
return u1