-- Full Path: -Grow-a-Garden-\StarterPlayer\StarterPlayerScripts\PlayerModule\ControlModule\TouchJump-ModuleScript.lua
game:GetService("Players")
local u1 = game:GetService("GuiService")
local v2 = script.Parent.Parent:Wait<PERSON><PERSON><PERSON>hild("CommonUtils")
local u3 = require(v2:<PERSON><PERSON><PERSON><PERSON>hild("ConnectionUtil"))
local u4 = require(v2:<PERSON><PERSON><PERSON><PERSON>hild("CharacterUtil"))
local u5 = {
    ["HUMANOID_STATE_ENABLED_CHANGED"] = "HUMANOID_STATE_ENABLED_CHANGED",
    ["HUMANOID_JUMP_POWER"] = "HUMANOID_JUMP_POWER",
    ["HUMANOID"] = "HUMANOID",
    ["JUMP_INPUT_ENDED"] = "JUMP_INPUT_ENDED",
    ["MENU_OPENED"] = "MENU_OPENED"
}
local u6 = require(script.Parent:Wait<PERSON><PERSON><PERSON>hild("BaseCharacterController"))
local u7 = setmetatable({}, u6)
u7.__index = u7
function u7.new() --[[Anonymous function at line 50]]
    --[[
    Upvalues:
        [1] = u6
        [2] = u7
        [3] = u3
    --]]
    local v8 = u6.new()
    local v9 = u7
    local v10 = setmetatable(v8, v9)
    v10.parentUIFrame = nil
    v10.jumpButton = nil
    v10.externallyEnabled = false
    v10.isJumping = false
    v10._active = false
    v10._connectionUtil = u3.new()
    return v10
end
function u7._reset(p11) --[[Anonymous function at line 64]]
    p11.isJumping = false
    p11.touchObject = nil
    if p11.jumpButton then
        p11.jumpButton.ImageRectOffset = Vector2.new(1, 146)
    end
end
function u7.EnableButton(u12, p13) --[[Anonymous function at line 74]]
    --[[
    Upvalues:
        [1] = u5
        [2] = u1
    --]]
    if p13 == u12._active then
        u12:_reset()
    else
        if p13 then
            if not u12.jumpButton then
                u12:Create()
            end
            u12.jumpButton.Visible = true
            u12._connectionUtil:trackConnection(u5.JUMP_INPUT_ENDED, u12.jumpButton.InputEnded:Connect(function(p14) --[[Anonymous function at line 90]]
                --[[
                Upvalues:
                    [1] = u12
                --]]
                if p14 == u12.touchObject then
                    u12:_reset()
                end
            end))
            u12._connectionUtil:trackConnection(u5.MENU_OPENED, u1.MenuOpened:Connect(function() --[[Anonymous function at line 100]]
                --[[
                Upvalues:
                    [1] = u12
                --]]
                if u12.touchObject then
                    u12:_reset()
                end
            end))
        else
            if u12.jumpButton then
                u12.jumpButton.Visible = false
            end
            u12._connectionUtil:disconnect(u5.JUMP_INPUT_ENDED)
            u12._connectionUtil:disconnect(u5.MENU_OPENED)
        end
        u12:_reset()
        u12._active = p13
    end
end
function u7.UpdateEnabled(p15) --[[Anonymous function at line 117]]
    --[[
    Upvalues:
        [1] = u4
    --]]
    local v16 = u4.getChild("Humanoid", "Humanoid")
    if v16 and (p15.externallyEnabled and (v16.JumpPower > 0 and v16:GetStateEnabled(Enum.HumanoidStateType.Jumping))) then
        p15:EnableButton(true)
    else
        p15:EnableButton(false)
    end
end
function u7._setupConfigurations(u17) --[[Anonymous function at line 126]]
    --[[
    Upvalues:
        [1] = u4
        [2] = u5
    --]]
    local function u18() --[[Anonymous function at line 127]]
        --[[
        Upvalues:
            [1] = u17
        --]]
        u17:UpdateEnabled()
    end
    local v22 = u4.onChild("Humanoid", "Humanoid", function(p19) --[[Anonymous function at line 132]]
        --[[
        Upvalues:
            [1] = u17
            [2] = u5
            [3] = u18
        --]]
        u17:UpdateEnabled()
        u17._connectionUtil:trackConnection(u5.HUMANOID_JUMP_POWER, p19:GetPropertyChangedSignal("JumpPower"):Connect(u18))
        u17._connectionUtil:trackConnection(u5.HUMANOID_STATE_ENABLED_CHANGED, p19.StateEnabledChanged:Connect(function(p20, p21) --[[Anonymous function at line 140]]
            --[[
            Upvalues:
                [1] = u17
            --]]
            if p20 == Enum.HumanoidStateType.Jumping and p21 ~= u17._active then
                u17:UpdateEnabled()
            end
        end))
    end)
    u17._connectionUtil:trackConnection(u5.HUMANOID, v22)
end
function u7.Enable(p23, p24, p25) --[[Anonymous function at line 152]]
    if p25 then
        p23.parentUIFrame = p25
    end
    if p23.externallyEnabled == p24 then
        return
    else
        p23.externallyEnabled = p24
        p23:UpdateEnabled()
        if p24 then
            p23:_setupConfigurations()
        else
            p23._connectionUtil:disconnectAll()
        end
    end
end
function u7.Create(u26) --[[Anonymous function at line 169]]
    if u26.parentUIFrame then
        if u26.jumpButton then
            u26.jumpButton:Destroy()
            u26.jumpButton = nil
        end
        if u26.absoluteSizeChangedConn then
            u26.absoluteSizeChangedConn:Disconnect()
            u26.absoluteSizeChangedConn = nil
        end
        u26.jumpButton = Instance.new("ImageButton")
        u26.jumpButton.Name = "JumpButton"
        u26.jumpButton.Visible = false
        u26.jumpButton.BackgroundTransparency = 1
        u26.jumpButton.Image = "rbxasset://textures/ui/Input/TouchControlsSheetV2.png"
        u26.jumpButton.ImageRectOffset = Vector2.new(1, 146)
        u26.jumpButton.ImageRectSize = Vector2.new(144, 144)
        local function v31() --[[Anonymous function at line 192]]
            --[[
            Upvalues:
                [1] = u26
            --]]
            local v27 = u26.parentUIFrame.AbsoluteSize.x
            local v28 = u26.parentUIFrame.AbsoluteSize.y
            local v29 = math.min(v27, v28) <= 500
            local v30 = v29 and 70 or 120
            u26.jumpButton.Size = UDim2.new(0, v30, 0, v30)
            u26.jumpButton.Position = v29 and UDim2.new(1, -(v30 * 1.5 - 10), 1, -v30 - 20) or UDim2.new(1, -(v30 * 1.5 - 10), 1, -v30 * 1.75)
        end
        v31()
        u26.absoluteSizeChangedConn = u26.parentUIFrame:GetPropertyChangedSignal("AbsoluteSize"):Connect(v31)
        u26.touchObject = nil
        u26.jumpButton.InputBegan:connect(function(p32) --[[Anonymous function at line 206]]
            --[[
            Upvalues:
                [1] = u26
            --]]
            if not u26.touchObject and (p32.UserInputType == Enum.UserInputType.Touch and p32.UserInputState == Enum.UserInputState.Begin) then
                u26.touchObject = p32
                u26.jumpButton.ImageRectOffset = Vector2.new(146, 146)
                u26.isJumping = true
            end
        end)
        u26.jumpButton.Parent = u26.parentUIFrame
    end
end
return u7