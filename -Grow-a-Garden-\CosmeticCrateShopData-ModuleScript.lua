-- Full Path: -Grow-a-Garden-\\CosmeticCrateShopData-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
require(v1.Modules.Chalk)
local v2 = {
    ["Sign Crate"] = {
        ["CrateName"] = "Sign Crate",
        ["CrateRarity"] = "Rare",
        ["StockChance"] = 1,
        ["Price"] = 55000000,
        ["PurchaseID"] = 3290108854
    },
    ["Common Gnome Crate"] = {
        ["CrateName"] = "Common Gnome Crate",
        ["CrateRarity"] = "Rare",
        ["StockChance"] = 6,
        ["Price"] = 55500000,
        ["PurchaseID"] = 3290108955
    },
    ["Fun Crate"] = {
        ["CrateName"] = "Fun Crate",
        ["CrateRarity"] = "Legendary",
        ["StockChance"] = 30,
        ["Price"] = 88000000,
        ["PurchaseID"] = 3290109194
    },
    ["Farmers Gnome Crate"] = {
        ["CrateName"] = "Farmers Gnome Crate",
        ["CrateRarity"] = "Legendary",
        ["StockChance"] = 30,
        ["Price"] = 88500000,
        ["PurchaseID"] = 3290109302
    },
    ["Classic Gnome Crate"] = {
        ["CrateName"] = "Classic Gnome Crate",
        ["CrateRarity"] = "Mythical",
        ["StockChance"] = 60,
        ["Price"] = 113000000,
        ["PurchaseID"] = 3290109380
    },
    ["Statue Crate"] = {
        ["CrateName"] = "Statue Crate",
        ["CrateRarity"] = "Mythical",
        ["StockChance"] = 60,
        ["Price"] = 115000000,
        ["PurchaseID"] = 3290109444
    },
    ["Twilight Crate"] = {
        ["CrateName"] = "Twilight Crate",
        ["CrateRarity"] = "Mythical",
        ["StockChance"] = 60,
        ["Price"] = 120000000,
        ["PurchaseID"] = 3292233505
    },
    ["Bloodmoon Crate"] = {
        ["CrateName"] = "Bloodmoon Crate",
        ["CrateRarity"] = "Mythical",
        ["StockChance"] = 60,
        ["Price"] = 120000000,
        ["PurchaseID"] = 3292233680
    }
}
return v2