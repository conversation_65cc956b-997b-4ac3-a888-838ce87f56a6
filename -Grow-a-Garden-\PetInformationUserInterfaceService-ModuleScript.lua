-- Full Path: -Grow-a-Garden-\\PetInformationUserInterfaceService-ModuleScript.lua
game:GetService("Selection")
local v1 = game:GetService("Players")
local v2 = game:GetService("ReplicatedStorage")
local u3 = game:GetService("TweenService")
game:GetService("CollectionService")
local v4 = game:GetService("RunService")
game:GetService("UserInputService")
game:GetService("GuiService")
local u5 = workspace.CurrentCamera
workspace:GetPropertyChangedSignal("CurrentCamera"):Connect(function() --[[Anonymous function at line 12]]
    --[[
    Upvalues:
        [1] = u5
    --]]
    u5 = workspace.CurrentCamera
end)
local u6 = require(v2.Modules.PetServices.ActivePetsService)
local v7 = require(v2.Modules.WaitForDescendant)
local v8 = require(v2.Data.PetRegistry)
local u9 = require(v2.Modules.PetServices.PetUtilities)
local u10 = v8.PetList
local u11 = v8.PetConfig.XP_CONFIG.MAX_LEVEL
local u12 = v1.LocalPlayer:WaitForChild("PlayerGui"):WaitForChild("PetUI"):WaitForChild("PetInformationUI")
local u13 = v7(u12, "CanvasGroup")
local u14 = v7(u12, "DROP_SHADOW")
local u15 = v7(u12, "PET_TEXT")
local u16 = v7(u12, "LEVEL_TEXT")
local u17 = v7(u12, "HUNGER_BAR")
local u18 = v7(u12, "LEVEL_PROGRESS_BAR")
local u19 = v7(u12, "LEVEL_ICON")
local u20 = v7(u12, "LEVEL_MAXED_TEXT")
local u21 = v7(u12, "CANVAS_STROKE")
local u40 = {
    ["Active"] = false,
    ["Target"] = nil,
    ["SetTarget"] = function(_, p22) --[[Function name: SetTarget, line 50]]
        --[[
        Upvalues:
            [1] = u40
            [2] = u6
            [3] = u15
            [4] = u16
        --]]
        u40.Target = p22
        if p22 then
            local v23 = u6:GetPetDataFromPetObject(p22)
            if v23 then
                local v24 = v23.PetType
                local v25 = v23.PetData
                local v26 = v25.Name
                if v26 == "" then
                    v26 = nil
                end
                u15.Text = v26 or v24
                u16.Text = ("Age %*"):format(v25.Level)
                u40:Update(0)
            end
        else
            return
        end
    end,
    ["Update"] = function(_, p27) --[[Function name: Update, line 69]]
        --[[
        Upvalues:
            [1] = u40
            [2] = u6
            [3] = u10
            [4] = u3
            [5] = u17
            [6] = u9
            [7] = u11
            [8] = u18
            [9] = u20
            [10] = u19
        --]]
        local v28 = u40.Target
        if v28 then
            local v29 = u6:GetPetDataFromPetObject(v28)
            if v29 then
                local v30 = v29.PetType
                local v31 = v29.PetData
                local v32 = u10[v30].DefaultHunger
                local v33 = v31.Hunger / v32
                u3:Create(u17, TweenInfo.new(p27 or 0.2), {
                    ["Size"] = UDim2.fromScale(v33, 1)
                }):Play()
                local v34 = v31.Level
                local v35 = u9:GetCurrentLevelXPCost(v34)
                local v36 = u11 <= v34
                local v37 = v36 and 1 or (v31.LevelProgress or 0) / v35
                u3:Create(u18, TweenInfo.new(p27 or 0.2), {
                    ["Size"] = UDim2.fromScale(v37, 1)
                }):Play()
                u20.Visible = v36
                u19.Visible = not v36
            end
        else
            return
        end
    end,
    ["Toggle"] = function(_, p38) --[[Function name: Toggle, line 103]]
        --[[
        Upvalues:
            [1] = u40
            [2] = u3
            [3] = u13
            [4] = u14
            [5] = u21
        --]]
        if u40.Active ~= p38 then
            u40.Active = p38
            local v39 = u3:Create(u13, TweenInfo.new(0.15), {
                ["GroupTransparency"] = p38 and 0 or 1
            })
            u3:Create(u14, TweenInfo.new(0.15), {
                ["ImageTransparency"] = p38 and 0.6 or 1
            })
            v39:Play()
            u3:Create(u21, TweenInfo.new(0.15), {
                ["Transparency"] = p38 and 0 or 1
            }):Play()
            if not p38 then
                u40.Target = nil
            end
        end
    end
}
v4.RenderStepped:Connect(function() --[[Anonymous function at line 136]]
    --[[
    Upvalues:
        [1] = u40
        [2] = u12
        [3] = u5
    --]]
    if u40.Active then
        if u40.Target then
            local v41 = u12
            local v42 = u40.Target.Position + Vector3.new(0, 5, 0)
            local v43
            if v42 then
                local v44 = u5:WorldToScreenPoint(v42)
                v43 = UDim2.fromOffset(v44.X, v44.Y)
            else
                v43 = nil
            end
            v41.Position = v43
        else
            u40:Toggle(false)
        end
    else
        return
    end
end)
task.spawn(function() --[[Anonymous function at line 142]]
    --[[
    Upvalues:
        [1] = u40
    --]]
    while true do
        task.wait(1)
        u40:Update()
    end
end)
return u40