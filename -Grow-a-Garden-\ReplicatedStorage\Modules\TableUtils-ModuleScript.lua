-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\TableUtils-ModuleScript.lua
local u5 = {
    ["DeepCopy"] = function(_, p1) --[[Function name: DeepCopy, line 8]]
        --[[
        Upvalues:
            [1] = u5
        --]]
        local v2 = table.clone(p1)
        for v3, v4 in v2 do
            if type(v4) == "table" then
                v2[v3] = u5:DeepCopy(v4)
            end
        end
        return v2
    end
}
return u5