-- Full Path: -Grow-a-Garden-\ReplicatedStorage\CmdrClient\Types\PetType-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
game:GetService("ServerStorage")
local u2 = require(script.Parent.Parent.Shared.Util)
require(v1.Data.PetEggData)
local u3 = {}
for v4 in require(v1.Data.PetRegistry).PetList do
    table.insert(u3, v4)
end
local u9 = {
    ["Transform"] = function(p5) --[[Function name: Transform, line 20]]
        --[[
        Upvalues:
            [1] = u2
            [2] = u3
        --]]
        return u2.MakeFuzzyFinder(u3)(p5)
    end,
    ["Validate"] = function(p6) --[[Function name: Validate, line 26]]
        return #p6 > 0, "No Pet could be found"
    end,
    ["Autocomplete"] = function(p7) --[[Function name: Autocomplete, line 30]]
        --[[
        Upvalues:
            [1] = u2
        --]]
        return u2.GetNames(p7)
    end,
    ["Parse"] = function(p8) --[[Function name: Parse, line 34]]
        return p8[1]
    end,
    ["Default"] = function(_) --[[Function name: Default, line 38]]
        --[[
        Upvalues:
            [1] = u3
        --]]
        return u3[1]
    end,
    ["ArgumentOperatorAliases"] = {
        ["me"] = ".",
        ["all"] = "*",
        ["others"] = "**",
        ["random"] = "?"
    }
}
return function(p10) --[[Anonymous function at line 50]]
    --[[
    Upvalues:
        [1] = u9
    --]]
    p10:RegisterType("pettype", u9)
end