-- Full Path: -Grow-a-Garden-\\Music-LocalScript.lua
task.wait(1)
local u1 = game.Players.LocalPlayer:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("Current_Biome")
local u2 = game:GetService("TweenService")
local u3 = {}
for _, v4 in pairs(game.SoundService.Music_Folder:GetChildren()) do
    local v5 = { v4, v4:GetChildren()[1] }
    table.insert(u3, v5)
end
for _, v6 in pairs(u3) do
    for _, v7 in pairs(v6[1]:GetChildren()) do
        v7.Volume = 0
    end
end
local u8 = nil
for _, u9 in pairs(u3) do
    task.spawn(function() --[[Anonymous function at line 15]]
        --[[
        Upvalues:
            [1] = u9
        --]]
        while true do
            local v10 = u9[1]:GetChildren()[math.random(1, #u9[1]:GetChildren())]
            u9[2] = v10
            task.wait(v10.TimeLength)
        end
    end)
end
local u11 = nil
function Turn_Off(p12, p13)
    --[[
    Upvalues:
        [1] = u2
    --]]
    u2:Create(p12, TweenInfo.new(p13, Enum.EasingStyle.Quad, Enum.EasingDirection.Out, 0, false, 0), {
        ["Volume"] = 0
    }):Play()
end
function Turn_On(p14, p15)
    --[[
    Upvalues:
        [1] = u2
    --]]
    u2:Create(p14, TweenInfo.new(p15, Enum.EasingStyle.Quad, Enum.EasingDirection.Out, 0, false, 0), {
        ["Volume"] = 0.15
    }):Play()
end
function UPD()
    --[[
    Upvalues:
        [1] = u3
        [2] = u11
        [3] = u8
    --]]
    task.spawn(function() --[[Anonymous function at line 35]]
        --[[
        Upvalues:
            [1] = u3
            [2] = u11
            [3] = u8
        --]]
        local v16 = false
        while v16 == false do
            task.wait()
            for _, v17 in pairs(game.SoundService.Music_Folder:GetDescendants()) do
                if v17:IsA("Sound") and v17.Playing == true then
                    v16 = true
                end
            end
            local v18 = nil
            for _, v19 in pairs(u3) do
                if v19[1] == u11 then
                    v18 = v19[2]
                end
            end
            if u8 ~= v18 and u8 ~= nil then
                Turn_Off(u8, 1)
            end
            u8 = v18
            if v18 then
                v18.Playing = true
                Turn_On(v18, 1)
            end
        end
    end)
end
UPD()
function ok()
    --[[
    Upvalues:
        [1] = u3
        [2] = u1
        [3] = u11
    --]]
    if game.ReplicatedStorage.Night_Time.Value == false then
        for _, v20 in pairs(u3) do
            if v20[1].Name == u1.Value then
                u11 = v20[1]
            end
        end
        UPD()
    end
end
ok()
u1:GetPropertyChangedSignal("Value"):Connect(function() --[[Anonymous function at line 75]]
    ok()
end)
game.ReplicatedStorage.Night_Time:GetPropertyChangedSignal("Value"):Connect(function() --[[Anonymous function at line 78]]
    --[[
    Upvalues:
        [1] = u3
        [2] = u11
        [3] = u8
        [4] = u1
    --]]
    if game.ReplicatedStorage.Night_Time.Value == true then
        for _, v21 in pairs(u3) do
            if v21[1].Name ~= "Night_Time" then
                for _, v22 in pairs(v21[1]:GetChildren()) do
                    Turn_Off(v22, 7)
                end
            end
        end
        for _, v23 in pairs(u3) do
            if v23[1].Name == "Night_Time" then
                u11 = v23[1]
            end
        end
        local v24 = nil
        for _, v25 in pairs(u3) do
            if v25[1] == u11 then
                v24 = v25[2]
            end
        end
        if u8 ~= v24 and u8 ~= nil then
            Turn_Off(u8, 7)
        end
        u8 = v24
        if v24 then
            v24.Playing = true
            Turn_On(v24, 7)
            return
        end
    else
        for _, v26 in pairs(u3) do
            if v26[1].Name == u1.Value then
                u11 = v26[1]
            end
        end
        local v27 = nil
        for _, v28 in pairs(u3) do
            if v28[1] == u11 then
                v27 = v28[2]
            end
        end
        if u8 ~= v27 and u8 ~= nil then
            Turn_Off(u8, 7)
        end
        u8 = v27
        if v27 then
            v27.Playing = true
            Turn_On(v27, 7)
        end
    end
end)
workspace:GetAttributeChangedSignal("DiscoEvent"):Connect(function() --[[Anonymous function at line 129]]
    --[[
    Upvalues:
        [1] = u2
    --]]
    local v29 = workspace:GetAttribute("DiscoEvent")
    local v30 = game:GetService("SoundService")
    local u31 = v30.Disco
    local v32 = TweenInfo.new(1, Enum.EasingStyle.Linear, Enum.EasingDirection.InOut)
    if v29 then
        u31.Volume = 0
        u31:Play()
        u2:Create(u31, v32, {
            ["Volume"] = 0.2
        }):Play()
        u2:Create(v30.Music, v32, {
            ["Volume"] = 0
        }):Play()
    else
        u2:Create(u31, v32, {
            ["Volume"] = 0
        }):Play()
        u2:Create(v30.Music, v32, {
            ["Volume"] = 1
        }):Play()
        task.delay(1, function() --[[Anonymous function at line 146]]
            --[[
            Upvalues:
                [1] = u31
            --]]
            u31:Stop()
        end)
    end
end)