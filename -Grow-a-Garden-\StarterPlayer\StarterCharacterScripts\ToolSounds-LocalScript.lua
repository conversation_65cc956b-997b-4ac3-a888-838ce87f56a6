-- Full Path: -Grow-a-Garden-\StarterPlayer\StarterCharacterScripts\ToolSounds-LocalScript.lua
local v1 = game.Players.LocalPlayer.Character
local v2 = game:GetService("ReplicatedStorage")
local u3 = game:GetService("SoundService")
local u4 = {}
local u5 = os.clock()
local u6 = v1:FindFirstChild("Animator", true):LoadAnimation(script.TwoHandedFruitAnimation)
local function u11(p7, p8) --[[Anonymous function at line 14]]
    --[[
    Upvalues:
        [1] = u5
    --]]
    u5 = os.clock()
    local v9 = p7:Clone()
    local v10 = Instance.new("Part")
    v10.Parent = workspace
    v10.CanCollide = false
    v10.CanQuery = false
    v10.Anchored = true
    v10.Position = p8.Position
    v10.Transparency = 1
    v9.Parent = v10
    v9:Play()
    game.Debris:AddItem(v10, v9.TimeLength)
end
v1.ChildAdded:Connect(function(u12) --[[Anonymous function at line 36]]
    --[[
    Upvalues:
        [1] = u6
        [2] = u11
        [3] = u4
    --]]
    if u12:IsA("Tool") then
        local u13 = u12:WaitForChild("Handle")
        if u13 then
            u4[u12] = { u12.Equipped:Connect(function() --[[Anonymous function at line 46]]
                    --[[
                    Upvalues:
                        [1] = u13
                        [2] = u12
                        [3] = u6
                        [4] = u11
                    --]]
                    local v14 = u13:FindFirstChild("Equip") or script.Equip
                    if u12:HasTag("TwoHandedFruit") then
                        u6:Play()
                    end
                    u11(v14, u13)
                end), (u12.Unequipped:Connect(function() --[[Anonymous function at line 55]]
                    --[[
                    Upvalues:
                        [1] = u13
                        [2] = u12
                        [3] = u6
                        [4] = u11
                    --]]
                    local v15 = u13:FindFirstChild("Unequip") or script.Unequip
                    if u12:HasTag("TwoHandedFruit") then
                        u6:Stop()
                    end
                    u11(v15, u13)
                end)) }
        end
    else
        return
    end
end)
v1.ChildRemoved:Connect(function(p16) --[[Anonymous function at line 72]]
    --[[
    Upvalues:
        [1] = u4
        [2] = u6
    --]]
    if p16:IsA("Tool") then
        if u4[p16] then
            for _, v17 in u4[p16] do
                v17:Disconnect()
            end
            if p16:HasTag("TwoHandedFruit") then
                u6:Stop()
            end
        end
    end
end)
v2.GameEvents.PlaySound.OnClientEvent:Connect(function(p18) --[[Anonymous function at line 86]]
    --[[
    Upvalues:
        [1] = u3
    --]]
    local v19 = u3:FindFirstChild(p18)
    if v19 then
        u3:PlayLocalSound(v19)
    end
end)
local u20 = -1
local u21 = 0
local function v28(p22) --[[Anonymous function at line 98]]
    --[[
    Upvalues:
        [1] = u3
        [2] = u20
        [3] = u21
    --]]
    local v23 = u3:FindFirstChild(p22)
    if v23 then
        local v24 = v23:Clone()
        u20 = u20 + 1
        u20 = u20 % 16
        local u25 = tick()
        u21 = u25
        task.delay(1, function() --[[Anonymous function at line 110]]
            --[[
            Upvalues:
                [1] = u21
                [2] = u25
                [3] = u20
            --]]
            if u21 == u25 then
                u20 = 0
            end
        end)
        local v26 = Instance.new("PitchShiftSoundEffect")
        local v27 = u20 * 0.05
        v26.Octave = math.clamp(v27, 0, 0.8) + 0.7
        v26.Parent = v24
        v24.Parent = workspace
        v24:Play()
        game.Debris:AddItem(v24, 5)
    end
end
v2.GameEvents.PickupEvent.Event:Connect(v28)
v2.GameEvents.PickupSound.OnClientEvent:Connect(v28)