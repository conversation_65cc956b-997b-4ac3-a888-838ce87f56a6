-- Full Path: -Grow-a-Garden-\\Haptic-LocalScript.lua
game:GetService("HapticService")
local v1 = game:GetService("ReplicatedStorage")
local v2 = game:GetService("UserInputService")
local v3 = v1.GameEvents.CollectHaptic
local u4 = v2.TouchEnabled
if u4 then
    u4 = not v2.MouseEnabled
end
v3.OnClientEvent:Connect(function() --[[Anonymous function at line 9]]
    --[[
    Upvalues:
        [1] = u4
    --]]
    if u4 then
        task.spawn(function() --[[Anonymous function at line 18]] end)
    end
end)