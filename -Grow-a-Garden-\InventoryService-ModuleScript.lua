-- Full Path: -Grow-a-Garden-\\InventoryService-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
local u2 = require(v1.Modules.DataService)
local u3 = require(v1.Modules.CountDictionary)
local u14 = {
    ["GetToolData"] = function(_, p4) --[[Function name: GetToolData, line 18]]
        --[[
        Upvalues:
            [1] = u2
        --]]
        if not p4 then
            return warn("InventoryService:GetToolData | Player is Nil!", debug.traceback())
        end
        if not p4:IsA("Tool") then
            return warn(("InventoryService:GetToolData | %* is not of \'Tool\' type!"):format(p4), debug.traceback())
        end
        local v5 = p4:GetAttribute("ITEM_UUID")
        if not v5 then
            return warn(p4, "has no attached ITEM_UUID attribute")
        end
        local v6 = u2:GetData().InventoryData[v5]
        if v6 then
            return v6
        end
    end,
    ["Find"] = function(_, p7, p8) --[[Function name: Find, line 33]]
        --[[
        Upvalues:
            [1] = u2
        --]]
        local v9 = p8 or function() --[[Anonymous function at line 34]]
            return true
        end
        local v10 = {}
        local v11 = u2:GetData()
        if not v11 then
            return v10
        end
        for v12, v13 in v11.InventoryData do
            if v13.ItemType == p7 and v9(v13.ItemData) then
                v10[v12] = v13
            end
        end
        return v10
    end,
    ["IsMaxInventory"] = function(_) --[[Function name: IsMaxInventory, line 52]]
        --[[
        Upvalues:
            [1] = u14
            [2] = u3
        --]]
        return u3((u14:Find("Holdable"))) >= 200
    end
}
return u14