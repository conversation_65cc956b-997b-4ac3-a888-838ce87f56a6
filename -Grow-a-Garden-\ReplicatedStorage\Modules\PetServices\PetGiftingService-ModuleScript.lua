-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\PetServices\PetGiftingService-ModuleScript.lua
local v1 = {}
local u2 = game:GetService("ReplicatedStorage"):Wait<PERSON><PERSON><PERSON>hild("GameEvents"):<PERSON><PERSON><PERSON><PERSON>hil<PERSON>("PetGiftingService")
local u3 = script.Gift_Notification
local u4 = game.Players.LocalPlayer.PlayerGui:WaitForChild("Gift_Notification")
function v1.GivePet(_, p5) --[[Anonymous function at line 12]]
    --[[
    Upvalues:
        [1] = u2
    --]]
    u2:FireServer("GivePet", p5)
end
game.ReplicatedStorage.GameEvents.GiftPet.OnClientEvent:Connect(function(u6, p7, p8) --[[Anonymous function at line 16]]
    --[[
    Upvalues:
        [1] = u3
        [2] = u4
    --]]
    local u9 = u3:Clone()
    u9.Parent = u4.Frame
    u9.Holder.Notification_UI.TextLabel.Text = "Gift from @" .. p8
    u9.Holder.TextLabel.Text = p7
    u9.Holder.Frame.Accept.MouseButton1Click:Connect(function() --[[Anonymous function at line 23]]
        --[[
        Upvalues:
            [1] = u9
            [2] = u6
        --]]
        u9:Destroy()
        game.ReplicatedStorage.GameEvents.AcceptPetGift:FireServer(true, u6)
    end)
    u9.Holder.Frame.Decline.MouseButton1Click:Connect(function() --[[Anonymous function at line 28]]
        --[[
        Upvalues:
            [1] = u9
            [2] = u6
        --]]
        u9:Destroy()
        game.ReplicatedStorage.GameEvents.AcceptPetGift:FireServer(false, u6)
    end)
end)
return v1