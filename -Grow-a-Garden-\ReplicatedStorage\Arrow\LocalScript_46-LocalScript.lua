-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Arrow\LocalScript_46-LocalScript.lua
local u1 = require(game.ReplicatedStorage.Arrow_Module)
local u2 = true
script.Parent.Touched:Connect(function(p3) --[[Anonymous function at line 3]]
    --[[
    Upvalues:
        [1] = u2
        [2] = u1
    --]]
    if p3.Parent.Name == script.Parent.Target_Player.Value and u2 == true then
        u2 = false
        u1.Remove_Arrow(script.Parent)
    end
end)