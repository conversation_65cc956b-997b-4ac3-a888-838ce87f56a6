-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\EventShopUIController-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
game:GetService("TweenService")
game:GetService("RunService")
local u2 = game:GetService("ContentProvider")
local u3 = game:GetService("UserInputService")
local v4 = v1:WaitForChild("GameEvents")
local u5 = game.Players.LocalPlayer.PlayerGui:WaitForChild("EventShop_UI")
local u6 = require(v1.Modules.DataService)
local v7 = require(v1.Modules.Signal)
local u8 = require(v1.Modules.FastTween)
require(v1.Modules.DumpTable)
local u9 = require(v1.Modules.MarketController)
local u10 = require(v1.Modules.GiftController)
local u11 = require(v1.Modules.GuiController)
local u12 = require(v1.Comma_Module)
local u13 = require(v1.Item_Module)
require(v1.Modules.NumberUtil)
require(v1.Data.EventShopConfigData)
local u14 = require(v1.Data.EventShopData)
local u15 = v4.BuyEventShopStock
local u16 = nil
local u17 = v7.new()
local u18 = u5:WaitForChild("Frame"):WaitForChild("ScrollingFrame")
u5:WaitForChild("Frame"):WaitForChild("Frame"):WaitForChild("Timer")
local u19 = u18:WaitForChild("ItemFrame")
u19.Parent = script
local u20 = u18:WaitForChild("ItemPadding")
u20.Parent = u18
local u21 = {}
local v22 = {}
local u23 = game.SoundService.Click
local function u24() --[[Anonymous function at line 62]] end
local function u59() --[[Anonymous function at line 122]]
    --[[
    Upvalues:
        [1] = u24
        [2] = u3
        [3] = u14
        [4] = u2
        [5] = u19
        [6] = u21
        [7] = u13
        [8] = u12
        [9] = u18
        [10] = u20
        [11] = u15
        [12] = u9
        [13] = u23
        [14] = u10
        [15] = u8
        [16] = u16
        [17] = u17
        [18] = u6
    --]]
    task.spawn(u24)
    if not u3.TouchEnabled then
        local v25 = {}
        for _, v26 in u14 do
            if v26.DisplayInShop or v26.ItemType ~= "Seed" then
                local v27 = Instance.new("ImageLabel")
                v27.Image = v26.FruitIcon
                table.insert(v25, v27)
            end
        end
        u2:PreloadAsync(v25)
    end
    local u28 = {}
    for u29, u30 in u14 do
        if u30.DisplayInShop then
            local u31 = u19:Clone()
            u21[u29] = u31
            u31.Name = u29
            u31.LayoutOrder = u30.LayoutOrder * 10
            table.insert(u28, u31)
            local u32 = u31.Main_Frame
            local v33 = u13.Return_Rarity_Data(u30.SeedRarity)
            if v33 then
                u32.Rarity_Text.Text = v33[1]
                local v34 = u32.Rarity_Text.UIStroke
                local v35, v36, v37 = v33[2]:ToHSV()
                v34.Color = Color3.fromHSV(v35, v36, v37 / 2)
                u32.Rarity_BG.ImageColor3 = v33[2]
            end
            u32.Seed_Text.Text = u30.SeedName
            u32.Seed_Text_Shadow.Text = u30.SeedName
            u32.Cost_Text.Text = u12.Comma(u30.Price) .. "\194\162"
            u32.Description_Text.Text = u30.Description
            local v38 = u32:FindFirstChild("ShopItem_Image", true)
            v38.Visible = true
            v38.Image = u30.Asset
            u31.Parent = u18
            local u39 = u20:Clone()
            u39.LayoutOrder = u30.LayoutOrder * 10 + 1
            u39.Name = ("%*_Padding"):format(u29)
            u39.Parent = u18
            local u40 = u31.Frame
            u40.Sheckles_Buy.In_Stock.Cost_Text.Text = u12.Comma(u30.Price) .. "\194\162"
            u40.Sheckles_Buy.Activated:Connect(function() --[[Anonymous function at line 183]]
                --[[
                Upvalues:
                    [1] = u15
                    [2] = u29
                --]]
                u15:FireServer(u29)
            end)
            u40.Robux_Buy.Activated:Connect(function() --[[Anonymous function at line 189]]
                --[[
                Upvalues:
                    [1] = u9
                    [2] = u30
                --]]
                u9:PromptPurchase(u30.PurchaseID, Enum.InfoType.Product)
            end)
            u40.Gift.Visible = u30.GiftPurchaseID ~= nil
            if u30.GiftPurchaseID then
                u40.Gift.Activated:Connect(function() --[[Anonymous function at line 195]]
                    --[[
                    Upvalues:
                        [1] = u23
                        [2] = u10
                        [3] = u30
                    --]]
                    u23.PlaybackSpeed = 1 + math.random(-15, 15) / 100
                    u23.TimePosition = 0
                    u23.Playing = true
                    u10:PromptGiftFromGiftId(u30.GiftPurchaseID)
                end)
            end
            if u30.ItemType == "Seed" then
                local u41 = nil
                local u42 = nil
                u32.MouseEnter:Connect(function() --[[Anonymous function at line 208]]
                    --[[
                    Upvalues:
                        [1] = u41
                        [2] = u8
                        [3] = u31
                        [4] = u42
                        [5] = u30
                    --]]
                    u41 = u8(u31.Main_Frame.CanvasGroup.ShopItem_Image, TweenInfo.new(0.1, Enum.EasingStyle.Linear, Enum.EasingDirection.Out, -1, true, 0), {
                        ["Rotation"] = u31.Main_Frame.CanvasGroup.ShopItem_Image.Rotation + 10
                    })
                    u42 = u8(u31.Main_Frame.CanvasGroup.ShopItem_Image.UIScale, TweenInfo.new(0.5, Enum.EasingStyle.Linear, Enum.EasingDirection.Out, 0, false, 0), {
                        ["Scale"] = 1.5
                    })
                    u42.Completed:Connect(function(p43) --[[Anonymous function at line 224]]
                        --[[
                        Upvalues:
                            [1] = u31
                            [2] = u30
                        --]]
                        if p43 == Enum.PlaybackState.Completed then
                            u31.Main_Frame.CanvasGroup.ShopItem_Image.Image = u30.FruitIcon
                        end
                    end)
                end)
                u32.MouseLeave:Connect(function() --[[Anonymous function at line 231]]
                    --[[
                    Upvalues:
                        [1] = u41
                        [2] = u31
                        [3] = u30
                        [4] = u8
                    --]]
                    if u41 then
                        u41:Cancel()
                    end
                    u31.Main_Frame.CanvasGroup.ShopItem_Image.Rotation = 0
                    u31.Main_Frame.CanvasGroup.ShopItem_Image.Image = u30.Asset
                    u8(u31.Main_Frame.CanvasGroup.ShopItem_Image.UIScale, TweenInfo.new(0.25, Enum.EasingStyle.Linear, Enum.EasingDirection.Out, 0, false, 0), {
                        ["Scale"] = 1
                    })
                end)
            end
            local function v44() --[[Anonymous function at line 252]]
                --[[
                Upvalues:
                    [1] = u16
                    [2] = u29
                    [3] = u40
                    [4] = u8
                    [5] = u39
                --]]
                if u16 == u29 then
                    u40.Visible = true
                    u8(u40, TweenInfo.new(0.25, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
                        ["Position"] = UDim2.fromScale(0.5, 1.3)
                    })
                    u8(u39, TweenInfo.new(0.25, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
                        ["Size"] = UDim2.fromScale(0.13, 0.25)
                    })
                else
                    task.delay(0.25, function() --[[Anonymous function at line 264]]
                        --[[
                        Upvalues:
                            [1] = u40
                        --]]
                        if u40.Position == UDim2.fromScale(0.5, 0.5) then
                            u40.Visible = false
                        end
                    end)
                    u8(u40, TweenInfo.new(0.25, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
                        ["Position"] = UDim2.fromScale(0.5, 0.5)
                    })
                    u8(u39, TweenInfo.new(0.25, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
                        ["Size"] = UDim2.fromScale(0.13, 0.02)
                    })
                end
            end
            u17:Connect(v44)
            task.spawn(v44)
            local u45 = false
            u32.Activated:Connect(function() --[[Anonymous function at line 283]]
                --[[
                Upvalues:
                    [1] = u8
                    [2] = u18
                    [3] = u31
                    [4] = u28
                    [5] = u16
                    [6] = u29
                    [7] = u17
                    [8] = u45
                    [9] = u9
                    [10] = u40
                    [11] = u30
                --]]
                u8(u18, TweenInfo.new(0.35), {
                    ["CanvasPosition"] = Vector2.new(0, u31.AbsoluteSize.Y * (table.find(u28, u31) - 1))
                })
                local v46
                if u16 == u29 then
                    v46 = nil
                else
                    v46 = u29
                end
                u16 = v46
                u17:Fire()
                if not u45 then
                    u9:SetPriceLabel(u40.Robux_Buy.Price, u30.PurchaseID, ":robux::value:")
                    u45 = true
                end
            end)
            local function u52() --[[Anonymous function at line 298]]
                --[[
                Upvalues:
                    [1] = u6
                    [2] = u29
                    [3] = u32
                    [4] = u40
                    [5] = u12
                    [6] = u30
                --]]
                local v47 = u6:GetData()
                if v47 then
                    v47 = v47.EventShopStock.Stocks[u29]
                end
                local v48 = v47 and v47.Stock or 0
                local v49 = ("X%* Stock"):format(v48)
                u32.Stock_Text.Text = v49
                u40.Sheckles_Buy.In_Stock.Visible = v48 > 0
                u40.Sheckles_Buy.No_Stock.Visible = v48 <= 0
                u40.Sheckles_Buy.HoverImage = v48 > 0 and "rbxassetid://71551639169723" or "rbxassetid://138411009141674"
                u40.Sheckles_Buy.Image = v48 > 0 and "rbxassetid://96160773850314" or "rbxassetid://104713419928195"
                u32.Cost_Text.Text = v48 <= 0 and "NO STOCK" or u12.Comma(u30.Price) .. "\194\162"
                local v50 = u32.Cost_Text
                local v51
                if v48 <= 0 then
                    v51 = Color3.fromRGB(255, 0, 0)
                else
                    v51 = Color3.fromRGB(0, 255, 0)
                end
                v50.TextColor3 = v51
            end
            task.spawn(u52)
            task.spawn(function() --[[Anonymous function at line 318]]
                --[[
                Upvalues:
                    [1] = u6
                    [2] = u52
                --]]
                local v53 = u6:GetPathSignal("EventShopStock/@")
                if v53 then
                    v53:Connect(u52)
                end
                local v54 = u6:GetPathSignal("EventShopStock")
                if v54 then
                    v54:Connect(u52)
                end
            end)
        end
    end
    table.sort(u28, function(p55, p56) --[[Anonymous function at line 334]]
        local v57 = p55.LayoutOrder
        local v58 = p56.LayoutOrder
        if v57 == v58 then
            return p55.Name < p56.Name
        else
            return v57 < v58
        end
    end)
end
function v22.Start(_) --[[Anonymous function at line 344]]
    --[[
    Upvalues:
        [1] = u11
        [2] = u5
        [3] = u59
    --]]
    u11:UsePopupAnims(u5)
    u5.Frame.Frame.ExitButton.Activated:Connect(function() --[[Anonymous function at line 347]]
        --[[
        Upvalues:
            [1] = u11
            [2] = u5
        --]]
        u11:Close(u5)
    end)
    u5.Frame.Frame.Restock.Activated:Connect(function() --[[Anonymous function at line 351]] end)
    u59()
end
task.spawn(v22.Start, v22)
return v22