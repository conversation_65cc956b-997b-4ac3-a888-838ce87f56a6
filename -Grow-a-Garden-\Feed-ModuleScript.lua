-- Full Path: -Grow-a-Garden-\\Feed-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
local v2 = game:GetService("Players")
require(v1.Modules.PetServices.PetsService)
local u3 = v2.LocalPlayer
local u4 = require(v1.Modules.PetServices.ActivePetsService)
local u5 = require(v1.Modules.Notification)
return {
    ["Verifier"] = function(p6) --[[Function name: Verifier, line 13]]
        --[[
        Upvalues:
            [1] = u3
        --]]
        return p6:GetAttribute("OWNER") == u3.Name
    end,
    ["Activate"] = function(p7) --[[Function name: Activate, line 18]]
        --[[
        Upvalues:
            [1] = u3
            [2] = u5
            [3] = u4
        --]]
        local v8 = u3.Character
        if v8 then
            local v9 = v8:FindFirstChildWhichIsA("Tool")
            if not v9 then
                return u5:CreateNotification("You are not holding a fruit!")
            end
            if not v9:<PERSON><PERSON><PERSON>("FruitTool") then
                return u5:CreateNotification("You are not holding a fruit!")
            end
            u4:Feed(p7:GetAttribute("UUID"))
        end
    end
}