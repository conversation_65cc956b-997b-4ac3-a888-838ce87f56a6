-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\NumberUtil-ModuleScript.lua
return {
    ["autoColon"] = function(p1) --[[Function name: autoColon, line 3]]
        if p1 >= 86400 then
            return string.format("%02i:%02i:%02i:%02i", p1 / 86400, p1 / 3600 % 24, p1 / 60 % 60, p1 % 60)
        elseif p1 >= 3600 then
            return string.format("%02i:%02i:%02i", p1 / 3600, p1 / 60 % 60, p1 % 60)
        else
            return string.format("%02i:%02i", p1 / 60, p1 % 60)
        end
    end,
    ["toMS"] = function(p2) --[[Function name: toMS, line 13]]
        return string.format("%01i:%02i", p2 / 60, p2 % 60)
    end,
    ["toHMS"] = function(p3) --[[Function name: toHMS, line 17]]
        return string.format("%02i:%02i:%02i", p3 / 3600, p3 / 60 % 60, p3 % 60)
    end,
    ["toHMSExtended"] = function(p4) --[[Function name: toHMSExtended, line 21]]
        local v5 = p4 / 3600
        if v5 < 24 then
            return string.format("%02i:%02i:%02i", v5, p4 / 60 % 60, p4 % 60)
        end
        if v5 < 48 then
            return string.format("1 day, %02i hours", v5 - 24)
        end
        local v6 = v5 / 24
        return string.format("%02i days", v6)
    end,
    ["compactFormat"] = function(p7, p8) --[[Function name: compactFormat, line 33]]
        if p7 >= 86400 then
            local v9 = p7 % 86400 // 3600
            return ("%*d%*"):format(p7 // 86400, (v9 <= 0 or p8) and "" or (" %*h"):format(v9))
        end
        if p7 >= 3600 then
            local v10 = p7 % 3600 // 60
            return ("%*h%*"):format(p7 // 3600, (v10 <= 0 or p8) and "" or (" %*m"):format(v10))
        end
        if p7 < 60 then
            return ("%*s"):format(p7 // 1)
        end
        local v11 = p7 // 1 % 60
        return ("%*m%*"):format(p7 // 60, (v11 <= 0 or p8) and "" or (" %*s"):format(v11))
    end,
    ["FormatCompactPrice"] = function(p12) --[[Function name: FormatCompactPrice, line 48]]
        if p12 >= 1000000 then
            return string.format("%.0fM\194\162", p12 / 1000000)
        elseif p12 >= 1000 then
            return string.format("%.0fK\194\162", p12 / 1000)
        else
            return tostring(p12) .. "\194\162"
        end
    end,
    ["ReverseLerp"] = function(p13, p14, p15) --[[Function name: ReverseLerp, line 58]]
        return (p15 - p13) / (p14 - p13)
    end
}