-- Full Path: -Grow-a-Garden-\\ByteNet-ModuleScript.lua
local v1 = game:GetService("RunService")
local v2 = require(script.process.client)
local v3 = require(script.dataTypes.array)
local v4 = require(script.dataTypes.bool)
local v5 = require(script.dataTypes.buff)
local v6 = require(script.dataTypes.cframe)
local v7 = require(script.dataTypes.float32)
local v8 = require(script.dataTypes.float64)
local v9 = require(script.dataTypes.inst)
local v10 = require(script.dataTypes.int16)
local v11 = require(script.dataTypes.int32)
local v12 = require(script.dataTypes.int8)
local v13 = require(script.dataTypes.map)
local v14 = require(script.dataTypes.nothing)
local v15 = require(script.dataTypes.optional)
local v16 = require(script.dataTypes.string)
local v17 = require(script.dataTypes.struct)
local v18 = require(script.dataTypes.uint16)
local v19 = require(script.dataTypes.uint32)
local v20 = require(script.dataTypes.uint8)
local v21 = require(script.dataTypes.unknown)
local v22 = require(script.dataTypes.vec2)
local v23 = require(script.dataTypes.vec3)
local v24 = require(script.namespaces.namespace)
local v25 = require(script.packets.definePacket)
local v26 = require(script.process.server)
local v27 = require(script.replicated.values)
require(script.types)
v27.start()
if v1:IsServer() then
    v26.start()
else
    v2.start()
end
return table.freeze({
    ["definePacket"] = v25,
    ["defineNamespace"] = v24,
    ["array"] = v3,
    ["bool"] = v4(),
    ["optional"] = v15,
    ["uint8"] = v20(),
    ["uint16"] = v18(),
    ["uint32"] = v19(),
    ["int8"] = v12(),
    ["int16"] = v10(),
    ["int32"] = v11(),
    ["float32"] = v7(),
    ["float64"] = v8(),
    ["cframe"] = v6(),
    ["string"] = v16(),
    ["vec2"] = v22(),
    ["vec3"] = v23(),
    ["buff"] = v5(),
    ["struct"] = v17,
    ["map"] = v13,
    ["inst"] = v9(),
    ["unknown"] = v21(),
    ["nothing"] = v14()
})