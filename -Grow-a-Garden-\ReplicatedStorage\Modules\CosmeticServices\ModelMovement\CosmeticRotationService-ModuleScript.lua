-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\CosmeticServices\ModelMovement\CosmeticRotationService-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
local v2 = game:GetService("RunService")
game:GetService("CollectionService")
local u3 = game:GetService("UserInputService")
require(v1.Modules.GetMouseToWorld)
local u4 = require(v1.Modules.RoundToNearestNumber)
local v5 = require(v1.Data.CosmeticRegistry)
local u6 = require(v1.Modules.CosmeticServices.CosmeticPhyiscalityService)
require(v1.Modules.PlaySound)
local u7 = v5.InputConfig.ROTATION_CONFIG
local u8 = {
    ["CurrentOffset"] = 0,
    ["Target"] = nil,
    ["StartPivot"] = nil,
    ["LastCFrame"] = nil,
    ["Target"] = nil
}
local u9 = workspace.CurrentCamera
workspace:GetPropertyChangedSignal("CurrentCamera"):Connect(function() --[[Anonymous function at line 27]]
    --[[
    Upvalues:
        [1] = u9
    --]]
    u9 = workspace.CurrentCamera
end)
local u10 = 0
function u8.SetTarget(_, p11) --[[Anonymous function at line 33]]
    --[[
    Upvalues:
        [1] = u8
        [2] = u10
        [3] = u3
    --]]
    u8.LastCFrame = nil
    u10 = u3:GetMouseLocation().X
    u8.Target = nil
    u8.CurrentOffset = 0
    local v12 = u8
    local v13
    if p11 then
        v13 = p11.Parent:GetPivot()
    else
        v13 = nil
    end
    v12.StartPivot = v13
    u8.Target = p11
end
local u14 = Instance.new("Sound")
u14.SoundId = "rbxassetid://103926661258479"
u14.Volume = 0.2
u14.Parent = v1
local function u23() --[[Anonymous function at line 48]]
    --[[
    Upvalues:
        [1] = u8
        [2] = u3
        [3] = u10
        [4] = u4
        [5] = u7
        [6] = u14
        [7] = u6
    --]]
    if u8.Target then
        local v15 = u3:GetMouseLocation().X
        local v16 = v15 - u10
        local v17 = u8
        v17.CurrentOffset = v17.CurrentOffset + v16
        u10 = v15
        local v18 = u4(u8.CurrentOffset, u7.ROTATION_ROUNDING_PIXEL) / u7.ROTATION_ROUNDING_PIXEL
        local v19 = u8.StartPivot
        local v20 = v18 * u7.ROTATION_STEP_DEGREE
        local v21 = v19 * CFrame.Angles(0, math.rad(v20), 0)
        local v22 = u8.Target.Parent
        if u8.LastCFrame ~= v21 then
            u8.LastCFrame = v21
            u14.PlaybackSpeed = Random.new():NextNumber(0.95, 1.05)
            u14:Play()
            u6:UpdateCFrame(v22:GetAttribute("CosmeticUUID"), v21)
        end
    else
        return
    end
end
v2.RenderStepped:Connect(function() --[[Anonymous function at line 77]]
    --[[
    Upvalues:
        [1] = u23
    --]]
    u23()
end)
local u26 = {
    [Enum.KeyCode.DPadLeft] = {
        ["Test"] = function() --[[Anonymous function at line 83]]
            --[[
            Upvalues:
                [1] = u8
            --]]
            local v24 = u8
            v24.CurrentOffset = v24.CurrentOffset - 30
        end
    },
    [Enum.KeyCode.DPadRight] = {
        ["Test"] = function() --[[Anonymous function at line 88]]
            --[[
            Upvalues:
                [1] = u8
            --]]
            local v25 = u8
            v25.CurrentOffset = v25.CurrentOffset + 30
        end
    }
}
local u27 = {}
u3.InputBegan:Connect(function(u28, _) --[[Anonymous function at line 106]]
    --[[
    Upvalues:
        [1] = u26
    --]]
    local v29 = u26[u28.KeyCode] or u26[u28.UserInputType]
    if v29 then
        for _, u30 in v29 do
            task.spawn(function() --[[Anonymous function at line 110]]
                --[[
                Upvalues:
                    [1] = u30
                    [2] = u28
                --]]
                u30(u28)
            end)
        end
    end
end)
u3.InputEnded:Connect(function(u31, _) --[[Anonymous function at line 116]]
    --[[
    Upvalues:
        [1] = u27
    --]]
    local v32 = u27[u31.KeyCode] or u27[u31.UserInputType]
    if v32 then
        for _, u33 in v32 do
            task.spawn(function() --[[Anonymous function at line 120]]
                --[[
                Upvalues:
                    [1] = u33
                    [2] = u31
                --]]
                u33(u31)
            end)
        end
    end
end)
return u8