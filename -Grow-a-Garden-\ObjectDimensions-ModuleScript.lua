-- Full Path: -Grow-a-Garden-\\ObjectDimensions-ModuleScript.lua
require(script.Parent.Parent.types)
local u1 = require(script.Parent.CameraCache)
local u68 = {
    ["getScreenSize"] = function(p2, p3) --[[Function name: getScreenSize, line 13]]
        --[[
        Upvalues:
            [1] = u1
        --]]
        return p3 / p2 / u1.HalfTanFOV
    end,
    ["getObjectCFrameSource"] = function(p4) --[[Function name: getObjectCFrameSource, line 23]]
        --[[
        Upvalues:
            [1] = u68
        --]]
        if p4 == workspace then
            return nil, nil
        elseif p4:IsA("BasePart") then
            return p4, "BasePart"
        elseif p4:IsA("Model") then
            return p4, "Model"
        elseif p4:IsA("Bone") then
            return p4, "Bone"
        elseif p4:IsA("Attachment") then
            return p4, "Attachment"
        elseif p4:IsA("Beam") then
            return p4, "Beam"
        elseif p4.Parent then
            return u68.getObjectCFrameSource(p4.Parent)
        else
            return nil, nil
        end
    end,
    ["getObjectBoundingBoxSource"] = function(p5) --[[Function name: getObjectBoundingBoxSource, line 47]]
        --[[
        Upvalues:
            [1] = u68
        --]]
        if p5 == workspace then
            return nil, nil
        elseif p5:IsA("BasePart") then
            return p5, "BasePart"
        elseif p5:IsA("Model") then
            return p5, "Model"
        elseif p5:IsA("Beam") then
            return p5, "Beam"
        elseif p5:IsA("PointLight") or p5:IsA("SpotLight") then
            return p5, "Light"
        elseif p5:IsA("Sound") then
            return p5, "Sound"
        elseif p5.Parent then
            return u68.getObjectBoundingBoxSource(p5.Parent)
        else
            return nil, nil
        end
    end,
    ["getObjectCFrame"] = function(p6) --[[Function name: getObjectCFrame, line 71]]
        local v7 = p6.cframeSource
        local v8 = p6.cframeType
        if v8 == "BasePart" then
            return v7.CFrame
        end
        if v8 == "Model" then
            return v7:GetPivot()
        end
        if v8 == "Bone" then
            return v7.TransformedWorldCFrame
        end
        if v8 == "Attachment" then
            return v7.WorldCFrame
        end
        if v8 ~= "Beam" then
            return nil
        end
        local v9 = v7.Attachment0
        local v10 = v7.Attachment1
        if v9 and v10 then
            return v9.WorldCFrame:Lerp(v10.WorldCFrame, 0.5)
        end
        warn("Cannot determine position of Beam since it does not have attachments")
        return nil
    end,
    ["getObjectBoundingBox"] = function(p11) --[[Function name: getObjectBoundingBox, line 95]]
        local v12 = p11.boundingBoxSource
        local v13 = p11.boundingBoxType
        if v13 == "BasePart" then
            return v12.Size
        elseif v13 == "Model" then
            local _, v14 = v12:GetBoundingBox()
            return v14
        elseif v13 == "Beam" then
            local v15 = v12.Attachment0
            local v16 = v12.Attachment1
            if not (v15 and v16) then
                warn("Cannot determine position of Beam since it does not have attachments")
                return nil
            end
            local v17 = v12.Width0
            local v18 = v12.Width1
            local v19 = math.max(v17, v18)
            local v20 = (v15.WorldPosition - v16.WorldPosition).Magnitude
            return Vector3.new(v19, v19, v20)
        elseif v13 == "Light" then
            return Vector3.new(1, 1, 1) * v12.Range
        elseif v13 == "Sound" then
            return Vector3.new(1, 1, 1) * v12.RollOffMaxDistance
        else
            return nil
        end
    end,
    ["connectCFrameChangeEvent"] = function(u21, u22) --[[Function name: connectCFrameChangeEvent, line 123]]
        --[[
        Upvalues:
            [1] = u68
        --]]
        local v23 = {}
        local u24 = u21.cframeSource
        if u24 then
            local v25 = u21.cframeType
            if v25 == "BasePart" then
                local v26 = u24:GetPropertyChangedSignal("CFrame")
                table.insert(v23, v26:Connect(function() --[[Anonymous function at line 140]]
                    --[[
                    Upvalues:
                        [1] = u22
                        [2] = u24
                    --]]
                    u22(u24.CFrame)
                end))
                return v23
            elseif v25 == "Model" then
                if u24.PrimaryPart then
                    local v27 = u24.PrimaryPart:GetPropertyChangedSignal("CFrame")
                    table.insert(v23, v27:Connect(function() --[[Anonymous function at line 149]]
                        --[[
                        Upvalues:
                            [1] = u22
                            [2] = u24
                        --]]
                        u22(u24:GetPivot())
                    end))
                    return v23
                else
                    local v28 = u24:GetPropertyChangedSignal("WorldPivot")
                    table.insert(v23, v28:Connect(function() --[[Anonymous function at line 156]]
                        --[[
                        Upvalues:
                            [1] = u22
                            [2] = u24
                        --]]
                        u22(u24:GetPivot())
                    end))
                    return v23
                end
            else
                if v25 == "Bone" then
                    local v29 = u24:GetPropertyChangedSignal("TransformedWorldCFrame")
                    table.insert(v23, v29:Connect(function() --[[Anonymous function at line 165]]
                        --[[
                        Upvalues:
                            [1] = u22
                            [2] = u24
                        --]]
                        u22(u24.TransformedWorldCFrame)
                    end))
                    return v23
                end
                if v25 ~= "Attachment" then
                    if v25 == "Beam" then
                        local v30 = u24.Attachment0
                        local v31 = u24.Attachment1
                        if not (v30 and v31) then
                            warn("Cannot determine position of Beam since it does not have attachments")
                            return v23
                        end
                        local v32 = v30:GetPropertyChangedSignal("WorldCFrame")
                        local function v33() --[[Anonymous function at line 187]]
                            --[[
                            Upvalues:
                                [1] = u22
                                [2] = u68
                                [3] = u21
                            --]]
                            u22(u68.getObjectCFrame(u21) or CFrame.identity)
                        end
                        table.insert(v23, v32:Connect(v33))
                        local v34 = v31:GetPropertyChangedSignal("WorldCFrame")
                        local function v35() --[[Anonymous function at line 193]]
                            --[[
                            Upvalues:
                                [1] = u22
                                [2] = u68
                                [3] = u21
                            --]]
                            u22(u68.getObjectCFrame(u21) or CFrame.identity)
                        end
                        table.insert(v23, v34:Connect(v35))
                    end
                    return v23
                end
                local v36 = u24:GetPropertyChangedSignal("WorldCFrame")
                table.insert(v23, v36:Connect(function() --[[Anonymous function at line 173]]
                    --[[
                    Upvalues:
                        [1] = u22
                        [2] = u24
                    --]]
                    u22(u24.WorldCFrame)
                end))
                return v23
            end
        else
            return v23
        end
    end,
    ["connectBoundingBoxChangeEvent"] = function(u37, u38) --[[Function name: connectBoundingBoxChangeEvent, line 202]]
        --[[
        Upvalues:
            [1] = u68
        --]]
        local v39 = {}
        local u40 = u37.boundingBoxSource
        if not u40 then
            return v39
        end
        local v41 = u37.boundingBoxType
        if v41 == "BasePart" then
            local v42 = u40:GetPropertyChangedSignal("Size")
            table.insert(v39, v42:Connect(function() --[[Anonymous function at line 219]]
                --[[
                Upvalues:
                    [1] = u38
                    [2] = u40
                --]]
                u38(u40.Size)
            end))
            return v39
        end
        if v41 == "Model" then
            local v43 = u40.DescendantAdded
            table.insert(v39, v43:Connect(function() --[[Anonymous function at line 229]]
                --[[
                Upvalues:
                    [1] = u40
                    [2] = u38
                --]]
                local _, v44 = u40:GetBoundingBox()
                u38(v44)
            end))
            local v45 = u40.DescendantRemoving
            table.insert(v39, v45:Connect(function() --[[Anonymous function at line 236]]
                --[[
                Upvalues:
                    [1] = u40
                    [2] = u38
                --]]
                local _, v46 = u40:GetBoundingBox()
                u38(v46)
            end))
            return v39
        end
        if v41 ~= "Beam" then
            if v41 ~= "Light" then
                if v41 == "Sound" then
                    local v47 = u40:GetPropertyChangedSignal("RollOffMaxDistance")
                    table.insert(v39, v47:Connect(function() --[[Anonymous function at line 286]]
                        --[[
                        Upvalues:
                            [1] = u38
                            [2] = u40
                        --]]
                        u38(Vector3.new(1, 1, 1) * u40.RollOffMaxDistance)
                    end))
                end
                return v39
            end
            local v48 = u40:GetPropertyChangedSignal("Range")
            table.insert(v39, v48:Connect(function() --[[Anonymous function at line 278]]
                --[[
                Upvalues:
                    [1] = u38
                    [2] = u40
                --]]
                u38(Vector3.new(1, 1, 1) * u40.Range)
            end))
            return v39
        end
        local v49 = u40.Attachment0
        local v50 = u40.Attachment1
        if not (v49 and v50) then
            warn("Cannot determine bounding box of Beam since it does not have attachments")
            return v39
        end
        local v51 = u40:GetPropertyChangedSignal("Width0")
        local function v52() --[[Anonymous function at line 252]]
            --[[
            Upvalues:
                [1] = u38
                [2] = u68
                [3] = u37
            --]]
            u38(u68.getObjectBoundingBox(u37) or Vector3.new(1, 1, 1))
        end
        table.insert(v39, v51:Connect(v52))
        local v53 = u40:GetPropertyChangedSignal("Width1")
        local function v54() --[[Anonymous function at line 258]]
            --[[
            Upvalues:
                [1] = u38
                [2] = u68
                [3] = u37
            --]]
            u38(u68.getObjectBoundingBox(u37) or Vector3.new(1, 1, 1))
        end
        table.insert(v39, v53:Connect(v54))
        local v55 = v49:GetPropertyChangedSignal("WorldPosition")
        local function v56() --[[Anonymous function at line 264]]
            --[[
            Upvalues:
                [1] = u38
                [2] = u68
                [3] = u37
            --]]
            u38(u68.getObjectBoundingBox(u37) or Vector3.new(1, 1, 1))
        end
        table.insert(v39, v55:Connect(v56))
        local v57 = v50:GetPropertyChangedSignal("WorldPosition")
        local function v58() --[[Anonymous function at line 270]]
            --[[
            Upvalues:
                [1] = u38
                [2] = u68
                [3] = u37
            --]]
            u38(u68.getObjectBoundingBox(u37) or Vector3.new(1, 1, 1))
        end
        table.insert(v39, v57:Connect(v58))
        return v39
    end,
    ["subscribeToDimensionChanges"] = function(p59, p60, p61) --[[Function name: subscribeToDimensionChanges, line 295]]
        --[[
        Upvalues:
            [1] = u68
        --]]
        local v62 = u68.connectCFrameChangeEvent(p59, p60)
        local v63 = u68.connectBoundingBoxChangeEvent(p59, p61)
        for _, v64 in v62 do
            local v65 = p59.changeConnections
            table.insert(v65, v64)
        end
        for _, v66 in v63 do
            local v67 = p59.changeConnections
            table.insert(v67, v66)
        end
    end
}
return u68