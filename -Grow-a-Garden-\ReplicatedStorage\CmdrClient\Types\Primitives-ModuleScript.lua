-- Full Path: -Grow-a-Garden-\ReplicatedStorage\CmdrClient\Types\Primitives-ModuleScript.lua
local u1 = require(script.Parent.Parent.Shared.Util)
local u4 = {
    ["Validate"] = function(p2) --[[Function name: Validate, line 4]]
        return p2 ~= nil
    end,
    ["Parse"] = function(p3) --[[Function name: Parse, line 8]]
        return tostring(p3)
    end
}
local u8 = {
    ["Transform"] = function(p5) --[[Function name: Transform, line 14]]
        return tonumber(p5)
    end,
    ["Validate"] = function(p6) --[[Function name: Validate, line 18]]
        return p6 ~= nil
    end,
    ["Parse"] = function(p7) --[[Function name: Parse, line 22]]
        return p7
    end
}
local u13 = {
    ["Transform"] = function(p9) --[[Function name: Transform, line 28]]
        return tonumber(p9)
    end,
    ["Validate"] = function(p10) --[[Function name: Valida<PERSON>, line 32]]
        local v11
        if p10 == nil then
            v11 = false
        else
            v11 = p10 == math.floor(p10)
        end
        return v11, "Only whole numbers are valid."
    end,
    ["Parse"] = function(p12) --[[Function name: Parse, line 36]]
        return p12
    end
}
local u18 = {
    ["Transform"] = function(p14) --[[Function name: Transform, line 42]]
        return tonumber(p14)
    end,
    ["Validate"] = function(p15) --[[Function name: Validate, line 46]]
        local v16
        if p15 == nil or p15 ~= math.floor(p15) then
            v16 = false
        else
            v16 = p15 > 0
        end
        return v16, "Only positive whole numbers are valid."
    end,
    ["Parse"] = function(p17) --[[Function name: Parse, line 50]]
        return p17
    end
}
local u23 = {
    ["Transform"] = function(p19) --[[Function name: Transform, line 56]]
        return tonumber(p19)
    end,
    ["Validate"] = function(p20) --[[Function name: Validate, line 60]]
        local v21
        if p20 == nil or p20 ~= math.floor(p20) then
            v21 = false
        else
            v21 = p20 >= 0
        end
        return v21, "Only non-negative whole numbers are valid."
    end,
    ["Parse"] = function(p22) --[[Function name: Parse, line 64]]
        return p22
    end
}
local u28 = {
    ["Transform"] = function(p24) --[[Function name: Transform, line 70]]
        return tonumber(p24)
    end,
    ["Validate"] = function(p25) --[[Function name: Validate, line 74]]
        local v26
        if p25 == nil or (p25 ~= math.floor(p25) or p25 < 0) then
            v26 = false
        else
            v26 = p25 <= 255
        end
        return v26, "Only bytes are valid."
    end,
    ["Parse"] = function(p27) --[[Function name: Parse, line 78]]
        return p27
    end
}
local u33 = {
    ["Transform"] = function(p29) --[[Function name: Transform, line 84]]
        return tonumber(p29)
    end,
    ["Validate"] = function(p30) --[[Function name: Validate, line 88]]
        local v31
        if p30 == nil or (p30 ~= math.floor(p30) or p30 < 0) then
            v31 = false
        else
            v31 = p30 <= 9
        end
        return v31, "Only digits are valid."
    end,
    ["Parse"] = function(p32) --[[Function name: Parse, line 92]]
        return p32
    end
}
local u34 = u1.MakeDictionary({
    "true",
    "t",
    "yes",
    "y",
    "on",
    "enable",
    "enabled",
    "1",
    "+"
})
local u35 = u1.MakeDictionary({
    "false",
    "f",
    "no",
    "n",
    "off",
    "disable",
    "disabled",
    "0",
    "-"
})
local u39 = {
    ["Transform"] = function(p36) --[[Function name: Transform, line 102]]
        return p36:lower()
    end,
    ["Validate"] = function(p37) --[[Function name: Validate, line 106]]
        --[[
        Upvalues:
            [1] = u34
            [2] = u35
        --]]
        return u34[p37] ~= nil and true or u35[p37] ~= nil, "Please use true/yes/on or false/no/off."
    end,
    ["Parse"] = function(p38) --[[Function name: Parse, line 110]]
        --[[
        Upvalues:
            [1] = u34
            [2] = u35
        --]]
        if u34[p38] then
            return true
        elseif u35[p38] then
            return false
        else
            return nil
        end
    end
}
return function(p40) --[[Anonymous function at line 122]]
    --[[
    Upvalues:
        [1] = u4
        [2] = u8
        [3] = u13
        [4] = u18
        [5] = u23
        [6] = u28
        [7] = u33
        [8] = u39
        [9] = u1
    --]]
    p40:RegisterType("string", u4)
    p40:RegisterType("number", u8)
    p40:RegisterType("integer", u13)
    p40:RegisterType("positiveInteger", u18)
    p40:RegisterType("nonNegativeInteger", u23)
    p40:RegisterType("byte", u28)
    p40:RegisterType("digit", u33)
    p40:RegisterType("boolean", u39)
    p40:RegisterType("strings", u1.MakeListableType(u4))
    p40:RegisterType("numbers", u1.MakeListableType(u8))
    p40:RegisterType("integers", u1.MakeListableType(u13))
    p40:RegisterType("positiveIntegers", u1.MakeListableType(u18))
    p40:RegisterType("nonNegativeIntegers", u1.MakeListableType(u23))
    p40:RegisterType("bytes", u1.MakeListableType(u28))
    p40:RegisterType("digits", u1.MakeListableType(u33))
    p40:RegisterType("booleans", u1.MakeListableType(u39))
end