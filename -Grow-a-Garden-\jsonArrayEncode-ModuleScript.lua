-- Full Path: -Grow-a-Garden-\\jsonArrayEncode-ModuleScript.lua
local u1 = game:GetService("HttpService")
local v3 = {
    ["Name"] = "json-array-encode",
    ["Aliases"] = {},
    ["Description"] = "Encodes a comma-separated list into a JSON array",
    ["Group"] = "DefaultUtil",
    ["Args"] = {
        {
            ["Type"] = "string",
            ["Name"] = "CSV",
            ["Description"] = "The comma-separated list"
        }
    },
    ["Run"] = function(_, p2) --[[Function name: Run, line 16]]
        --[[
        Upvalues:
            [1] = u1
        --]]
        return u1:JSONEncode(p2:split(","))
    end
}
return v3