-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Data\SettingsRegistry-ModuleScript.lua
local v1 = {}
local v2 = {
    ["Title"] = "Audio",
    ["Description"] = "Toggle your audio",
    ["BackgroundTexture"] = "http://www.roblox.com/asset/?id=139632749336322",
    ["SettingsData"] = {
        ["Type"] = "Boolean",
        ["DefaultValue"] = true
    },
    ["LayoutOrder"] = 1
}
v1.Audio = v2
local v3 = {
    ["Title"] = "Textures",
    ["Description"] = "Might help with performance!",
    ["BackgroundTexture"] = "http://www.roblox.com/asset/?id=106644443200974",
    ["SettingsData"] = {
        ["Type"] = "Boolean",
        ["DefaultValue"] = true
    },
    ["LayoutOrder"] = 2,
    ["Disabled"] = true
}
v1.Textures = v3
local v4 = {
    ["Title"] = "Render Details",
    ["Description"] = "Control the amount of plant detail",
    ["BackgroundTexture"] = "http://www.roblox.com/asset/?id=118134047539160",
    ["SettingsData"] = {
        ["Type"] = "Multiple",
        ["DefaultValue"] = "All",
        ["Options"] = { "None", "Less", "All" }
    },
    ["LayoutOrder"] = 3,
    ["Disabled"] = true
}
v1.ShowDetails = v4
local v5 = {
    ["Title"] = "Visual Effects",
    ["Description"] = "Might help with performance!",
    ["BackgroundTexture"] = "http://www.roblox.com/asset/?id=107407961055661",
    ["SettingsData"] = {
        ["Type"] = "Boolean",
        ["DefaultValue"] = true
    },
    ["LayoutOrder"] = 4,
    ["Disabled"] = true
}
v1.VisualEffects = v5
local v6 = {
    ["Title"] = "Gear Shop Button",
    ["Description"] = "Teleport to the gear shop faster!",
    ["BackgroundTexture"] = "http://www.roblox.com/asset/?id=139632749336322",
    ["SettingsData"] = {
        ["Type"] = "Boolean",
        ["DefaultValue"] = false
    },
    ["LayoutOrder"] = 5,
    ["Disabled"] = true
}
v1.GearShopButton = v6
local v7 = {
    ["Title"] = "Pet Shop Button",
    ["Description"] = "Teleport to the pet shop faster!",
    ["BackgroundTexture"] = "http://www.roblox.com/asset/?id=109511942140479",
    ["SettingsData"] = {
        ["Type"] = "Boolean",
        ["DefaultValue"] = false
    },
    ["LayoutOrder"] = 6,
    ["Disabled"] = true
}
v1.PetShopButton = v7
local v8 = {
    ["Title"] = "Recieve Gifts",
    ["Description"] = "Players can send you gift requests!",
    ["BackgroundTexture"] = "http://www.roblox.com/asset/?id=96138528769349",
    ["SettingsData"] = {
        ["Type"] = "Boolean",
        ["DefaultValue"] = true
    },
    ["LayoutOrder"] = 7
}
v1.RecieveGifts = v8
local v9 = {
    ["Title"] = "Pet Update Rate",
    ["Description"] = "Change the speed which pets update!",
    ["BackgroundTexture"] = "http://www.roblox.com/asset/?id=85951736855597",
    ["SettingsData"] = {
        ["Type"] = "Slider",
        ["DefaultValue"] = 0.035,
        ["Scale"] = NumberRange.new(0.035, 1)
    },
    ["LayoutOrder"] = 1,
    ["Disabled"] = true
}
v1.PetUpdateRate = v9
local v10 = {
    ["Title"] = "Plant Collisions",
    ["Description"] = "Toggle collisions with plants!",
    ["BackgroundTexture"] = "http://www.roblox.com/asset/?id=76649946815634",
    ["SettingsData"] = {
        ["Type"] = "Boolean",
        ["DefaultValue"] = true
    },
    ["LayoutOrder"] = 1,
    ["Disabled"] = true
}
v1.PlantableCollisions = v10
for v11, v12 in v1 do
    v12.Name = v11
end
return v1