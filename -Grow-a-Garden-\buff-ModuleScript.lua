-- Full Path: -Grow-a-Garden-\\buff-ModuleScript.lua
local v1 = require(script.Parent.Parent.process.bufferWriter)
require(script.Parent.Parent.types)
local u2 = v1.u16
local u3 = v1.copy
local u4 = v1.dyn_alloc
local u11 = {
    ["read"] = function(p5, p6) --[[Function name: read, line 9]]
        local v7 = buffer.readu16(p5, p6)
        local v8 = buffer.create(v7)
        buffer.copy(v8, 0, p5, p6 + 2, v7)
        return v8, v7 + 2
    end,
    ["write"] = function(p9) --[[Function name: write, line 18]]
        --[[
        Upvalues:
            [1] = u2
            [2] = u4
            [3] = u3
        --]]
        local v10 = buffer.len(p9)
        u2(v10)
        u4(v10)
        u3(p9)
    end
}
return function() --[[Anonymous function at line 29]]
    --[[
    Upvalues:
        [1] = u11
    --]]
    return u11
end