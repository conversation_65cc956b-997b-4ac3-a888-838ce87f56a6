-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\CreateTestPart-ModuleScript.lua
return function(p1) --[[Function name: CreateTestPart, line 1]]
    local v2 = Instance.new("Part")
    v2.Anchored = true
    v2.Size = Vector3.new(0.5, 0.5, 0.5)
    v2.Shape = Enum.PartType.Ball
    v2.Transparency = 0.5
    v2.Color = Color3.fromRGB(255, 0, 0)
    v2.Material = Enum.Material.Neon
    v2.Position = p1
    v2.CanQuery = false
    v2.Name = "TestPart"
    local u3 = Instance.new("SphereHandleAdornment")
    u3.Adornee = v2
    u3.AdornCullingMode = Enum.AdornCullingMode.Never
    u3.Radius = 0.5
    u3.AlwaysOnTop = true
    u3.Parent = v2
    task.delay(0.2, function() --[[Anonymous function at line 19]]
        --[[
        Upvalues:
            [1] = u3
        --]]
        u3.ZIndex = 99
    end)
    v2.Parent = workspace
    return v2
end