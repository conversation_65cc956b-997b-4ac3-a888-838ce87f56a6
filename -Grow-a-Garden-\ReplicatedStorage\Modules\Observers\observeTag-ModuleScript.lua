-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\Observers\observeTag-ModuleScript.lua
local u1 = game:GetService("CollectionService")
function observeTag(u2, u3, u4)
    --[[
    Upvalues:
        [1] = u1
    --]]
    local u5 = {}
    local u6 = {}
    local u7 = nil
    local function u32(u8) --[[Anonymous function at line 139]]
        --[[
        Upvalues:
            [1] = u7
            [2] = u5
            [3] = u6
            [4] = u4
            [5] = u3
            [6] = u2
        --]]
        if not u7.Connected then
            return
        end
        if u5[u8] ~= nil then
            return
        end
        u5[u8] = "__dead__"
        u6[u8] = u8.AncestryChanged:Connect(function() --[[Anonymous function at line 149]]
            --[[
            Upvalues:
                [1] = u8
                [2] = u4
                [3] = u5
                [4] = u3
                [5] = u2
            --]]
            local u9 = u8
            local v10
            if u4 == nil then
                v10 = true
                ::l3::
                if v10 then
                    if u5[u9] == "__dead__" then
                        u5[u9] = "__inflight__"
                        task.defer(function() --[[Anonymous function at line 82]]
                            --[[
                            Upvalues:
                                [1] = u5
                                [2] = u9
                                [3] = u3
                                [4] = u2
                            --]]
                            if u5[u9] == "__inflight__" then
                                local v14, v15 = xpcall(function(p11) --[[Anonymous function at line 88]]
                                    --[[
                                    Upvalues:
                                        [1] = u3
                                    --]]
                                    local v12 = u3(p11)
                                    if v12 ~= nil then
                                        local v13 = typeof(v12) == "function"
                                        assert(v13, "callback must return a function or nil")
                                    end
                                    return v12
                                end, debug.traceback, u9)
                                if v14 then
                                    if u5[u9] == "__inflight__" then
                                        u5[u9] = v15
                                    elseif v15 ~= nil then
                                        task.spawn(v15)
                                        return
                                    end
                                else
                                    local v16 = string.split(v15, "\n")[1]
                                    local v17 = string.find(v16, ": ")
                                    local v18 = not v17 and "" or v16:sub(v17 + 1)
                                    warn((("error while calling observeTag(\"%*\") callback:%*\n%*"):format(u2, v18, v15)))
                                end
                            else
                                return
                            end
                        end)
                        return
                    end
                else
                    local v19 = u5[u9]
                    u5[u9] = "__dead__"
                    if typeof(v19) == "function" then
                        task.spawn(v19)
                    end
                end
                return
            else
                for _, v20 in u4 do
                    if u9:IsDescendantOf(v20) then
                        v10 = true
                        goto l3
                    end
                end
                v10 = false
                goto l3
            end
        end)
        local v21
        if u4 == nil then
            v21 = true
            ::l7::
            if v21 then
                if u5[u8] == "__dead__" then
                    u5[u8] = "__inflight__"
                    task.defer(function() --[[Anonymous function at line 82]]
                        --[[
                        Upvalues:
                            [1] = u5
                            [2] = u8
                            [3] = u3
                            [4] = u2
                        --]]
                        if u5[u8] == "__inflight__" then
                            local v25, v26 = xpcall(function(p22) --[[Anonymous function at line 88]]
                                --[[
                                Upvalues:
                                    [1] = u3
                                --]]
                                local v23 = u3(p22)
                                if v23 ~= nil then
                                    local v24 = typeof(v23) == "function"
                                    assert(v24, "callback must return a function or nil")
                                end
                                return v23
                            end, debug.traceback, u8)
                            if v25 then
                                if u5[u8] == "__inflight__" then
                                    u5[u8] = v26
                                elseif v26 ~= nil then
                                    task.spawn(v26)
                                    return
                                end
                            else
                                local v27 = string.split(v26, "\n")[1]
                                local v28 = string.find(v27, ": ")
                                local v29 = not v28 and "" or v27:sub(v28 + 1)
                                warn((("error while calling observeTag(\"%*\") callback:%*\n%*"):format(u2, v29, v26)))
                            end
                        else
                            return
                        end
                    end)
                    return
                end
            else
                local v30 = u5[u8]
                u5[u8] = "__dead__"
                if typeof(v30) == "function" then
                    task.spawn(v30)
                end
            end
            return
        else
            for _, v31 in u4 do
                if u8:IsDescendantOf(v31) then
                    v21 = true
                    goto l7
                end
            end
            v21 = false
            goto l7
        end
    end
    u7 = u1:GetInstanceAddedSignal(u2):Connect(u32)
    local u36 = u1:GetInstanceRemovedSignal(u2):Connect(function(p33) --[[Function name: OnInstanceRemoved, line 155]]
        --[[
        Upvalues:
            [1] = u5
            [2] = u6
        --]]
        local v34 = u5[p33]
        u5[p33] = "__dead__"
        if typeof(v34) == "function" then
            task.spawn(v34)
        end
        local v35 = u6[p33]
        if v35 then
            v35:Disconnect()
            u6[p33] = nil
        end
        u5[p33] = nil
    end)
    task.defer(function() --[[Anonymous function at line 172]]
        --[[
        Upvalues:
            [1] = u7
            [2] = u1
            [3] = u2
            [4] = u32
        --]]
        if u7.Connected then
            for _, v37 in u1:GetTagged(u2) do
                task.spawn(u32, v37)
            end
        end
    end)
    return function() --[[Anonymous function at line 183]]
        --[[
        Upvalues:
            [1] = u7
            [2] = u36
            [3] = u5
            [4] = u6
        --]]
        u7:Disconnect()
        u36:Disconnect()
        local v38 = next(u5)
        while v38 do
            local v39 = u5[v38]
            u5[v38] = "__dead__"
            if typeof(v39) == "function" then
                task.spawn(v39)
            end
            local v40 = u6[v38]
            if v40 then
                v40:Disconnect()
                u6[v38] = nil
            end
            u5[v38] = nil
            v38 = next(u5)
        end
    end
end
return observeTag