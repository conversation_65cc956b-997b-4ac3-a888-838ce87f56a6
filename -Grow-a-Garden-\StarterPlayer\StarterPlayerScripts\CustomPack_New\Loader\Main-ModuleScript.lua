-- Full Path: -Grow-a-Garden-\StarterPlayer\StarterPlayerScripts\CustomPack_New\Loader\Main-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
local v2 = require(v1.Data.CategoryList)
local u3 = {}
local u4 = "All"
local u5 = nil
local u6 = nil
local u7 = nil
local u8 = nil
for _, v9 in v2 do
    u3[v9.Name] = v9
end
local function u13(p10) --[[Anonymous function at line 26]]
    --[[
    Upvalues:
        [1] = u4
        [2] = u3
    --]]
    if not p10 then
        return false
    end
    if u4 == "All" then
        return true
    end
    local v11 = u3[u4]
    if not v11 then
        return false
    end
    for _, v12 in v11.Tags do
        if p10:GetAttribute("ItemType") == v12 then
            return true
        end
    end
    return false
end
local function u17(p14) --[[Anonymous function at line 47]]
    --[[
    Upvalues:
        [1] = u3
    --]]
    for v15, v16 in u3 do
        for _, _ in v16 do
            if p14:GetAttribute("ItemType") == v15 then
                return v15
            end
        end
    end
    return "All"
end
local u18 = {
    ["OpenClose"] = nil,
    ["IsOpen"] = false,
    ["StateChanged"] = Instance.new("BindableEvent"),
    ["ModuleName"] = "Backpack",
    ["KeepVRTopbarOpen"] = true,
    ["VRIsExclusive"] = true,
    ["VRClosesNonExclusive"] = true
}
local u19 = script:GetAttribute("TextSize")
local v20 = script:GetAttribute("BackgroundTransparency")
local u21 = script:GetAttribute("BackgroundColor")
local u22 = script:GetAttribute("DraggableColor")
local u23 = script:GetAttribute("EquippedColor")
local u24 = script:GetAttribute("SlotLockedTransparency")
local u25 = script:GetAttribute("BorderColor")
local v26 = { Enum.KeyCode.Backquote }
local v27 = script:GetAttribute("FullSlots")
local v28 = script:GetAttribute("EmptySlots")
local v29 = script:GetAttribute("SearchBoxColor")
local v30 = script:GetAttribute("SearchBoxTransparency")
local v31 = script:WaitForChild("Api")
v31.Parent = game.ReplicatedStorage
local u32 = game:GetService("ReplicatedStorage"):WaitForChild("GameEvents"):WaitForChild("Favorite_Item")
local u33 = nil
local u34 = true
local function v38() --[[Anonymous function at line 127]]
    local v35 = Instance.new("ScreenGui", game.Players.LocalPlayer.PlayerGui)
    local v36 = Instance.new("Frame", v35)
    v36.BackgroundTransparency = 1
    v36.Size = UDim2.new(1, 0, 1, 0)
    local v37 = v36.AbsoluteSize
    v35:Destroy()
    return v37
end
local u39 = Enum.KeyCode.Zero.Value
local u40 = Enum.KeyCode.Backspace.Value
local u41 = {
    [Enum.UserInputType.Gamepad1] = true,
    [Enum.UserInputType.Gamepad2] = true,
    [Enum.UserInputType.Gamepad3] = true,
    [Enum.UserInputType.Gamepad4] = true,
    [Enum.UserInputType.Gamepad5] = true,
    [Enum.UserInputType.Gamepad6] = true,
    [Enum.UserInputType.Gamepad7] = true,
    [Enum.UserInputType.Gamepad8] = true
}
local u42 = game:GetService("UserInputService")
local v43 = game:GetService("Players")
local v44 = game:GetService("ReplicatedStorage")
local u45 = game:GetService("StarterGui")
local u46 = game:GetService("GuiService")
local v47 = v43.LocalPlayer.PlayerGui
local v48 = Instance.new("ScreenGui", v47)
v48.DisplayOrder = 120
v48.IgnoreGuiInset = true
v48.ResetOnSpawn = false
v48.Name = "BackpackGui"
local u49 = game:GetService("ContextActionService")
local v50 = game:GetService("RunService")
local u51 = game:GetService("VRService")
local u52 = require(script.Utility)
require(script.GameTranslator)
local v53 = require(v44.Modules.Icon)
local u54 = u46:IsTenFootInterface()
local u55
if u54 then
    u55 = 100
    u19 = 24
else
    u55 = 60
end
local u56 = false
local v57 = u42.TouchEnabled
if v57 then
    v57 = v38().X < 1024
end
local u58 = v43.LocalPlayer
local u59 = nil
local u60 = nil
local u61 = nil
local u62 = nil
local u63 = nil
local u64 = nil
local u65 = u58.Character or u58.CharacterAdded:Wait()
local u66 = u65:FindFirstChildOfClass("Humanoid")
local u67 = u58:WaitForChild("Backpack")
local u68 = v53.new()
u68:setImage("rbxasset://textures/ui/TopBar/inventoryOff.png", "deselected")
u68:setImage("rbxasset://textures/ui/TopBar/inventoryOn.png", "selected")
u68:bindToggleKey(v26[1], v26[2])
u68:setName("InventoryIcon")
u68:setImageScale(1.12)
u68:setOrder(-5)
u68:setCaption("Toggle the backpack.")
u68.deselectWhenOtherIconSelected = false
local u69 = {}
local u70 = nil
local u71 = {}
local u72 = {}
local u73 = {}
local u74 = 0
local u75 = false
local u76 = false
local u77 = false
local u78 = false
local u79 = {}
local u80 = {}
local u81 = false
local u82 = 0
local u83 = u51.VREnabled
local u84 = u83 and v28 and v28 or (v57 and 5 or v27)
local u85 = u83 and 3 or (v57 and 2 or 4)
local u86 = nil
local function u90(p87, p88) --[[Anonymous function at line 243]]
    --[[
    Upvalues:
        [1] = u19
    --]]
    local v89 = Instance.new(p87)
    v89.Name = p88
    v89.BackgroundColor3 = Color3.new(0, 0, 0)
    v89.BackgroundTransparency = 1
    v89.BorderColor3 = Color3.new(0, 0, 0)
    v89.BorderSizePixel = 0
    v89.Size = UDim2.new(1, 0, 1, 0)
    if p87:match("Text") then
        v89.TextColor3 = Color3.new(1, 1, 1)
        v89.Text = ""
        v89.FontFace = script:GetAttribute("LabelFont")
        v89.TextSize = u19
        v89.TextWrapped = true
        if p87 == "TextButton" then
            v89.FontFace = script:GetAttribute("SlotFont")
        end
    end
    return v89
end
local function u96() --[[Anonymous function at line 288]]
    --[[
    Upvalues:
        [1] = u61
        [2] = u84
        [3] = u74
        [4] = u69
    --]]
    local v91 = u61.Visible
    local v92 = v91 and u84 or u74
    local _ = v92 >= 1
    local v93 = 0
    for v94 = 1, u84 do
        local v95 = u69[v94]
        if v95.Tool or v91 then
            v93 = v93 + 1
            v95:Readjust(v93, v92)
            v95.Frame.Visible = true
        else
            v95.Frame.Visible = false
        end
    end
end
local function u101() --[[Anonymous function at line 306]]
    --[[
    Upvalues:
        [1] = u63
        [2] = u55
        [3] = u64
    --]]
    local v97 = u63.AbsoluteSize.X / (u55 + 5)
    local v98 = math.floor(v97)
    local v99 = (#u64:GetChildren() - 1) / v98
    local v100 = math.ceil(v99) * (u55 + 5) + 5
    u63.CanvasSize = UDim2.new(0, 0, 0, v100)
end
local function u104() --[[Anonymous function at line 313]]
    --[[
    Upvalues:
        [1] = u84
        [2] = u69
        [3] = u101
    --]]
    for v102 = u84 + 1, #u69 do
        local v103 = u69[v102]
        v103.Frame.LayoutOrder = v103.Index
        v103.Frame.Visible = v103.Tool ~= nil
    end
    u101()
end
local function v105() --[[Anonymous function at line 322]]
    --[[
    Upvalues:
        [1] = u60
        [2] = u84
        [3] = u55
        [4] = u61
        [5] = u85
        [6] = u83
        [7] = u63
        [8] = u96
        [9] = u104
    --]]
    u60.Size = UDim2.new(0, 5 + u84 * (u55 + 5), 0, u55 + 5 + 5)
    u60.Position = UDim2.new(0.5, -u60.Size.X.Offset / 2, 1, -u60.Size.Y.Offset)
    u61.Size = UDim2.new(0, u60.Size.X.Offset, 0, u60.Size.Y.Offset * u85 + 40 + (u83 and 80 or 0))
    u61.Position = UDim2.new(0.5, -u61.Size.X.Offset / 2, 1, u60.Position.Y.Offset - u61.Size.Y.Offset)
    u63.Size = UDim2.new(1, u63.ScrollBarThickness + 1, 1, -40 - (u83 and 80 or 0))
    u63.Position = UDim2.new(0, 0, 0, 40 + (u83 and 40 or 0))
    u96()
    u104()
end
os.clock()
local function u213(p106, p107) --[[Anonymous function at line 376]]
    --[[
    Upvalues:
        [1] = u69
        [2] = u51
        [3] = u24
        [4] = u22
        [5] = u21
        [6] = u60
        [7] = u55
        [8] = u84
        [9] = u61
        [10] = u42
        [11] = u74
        [12] = u76
        [13] = u56
        [14] = u49
        [15] = u71
        [16] = u70
        [17] = u65
        [18] = u86
        [19] = u90
        [20] = u23
        [21] = u73
        [22] = u68
        [23] = u7
        [24] = u101
        [25] = u33
        [26] = u66
        [27] = u67
        [28] = u25
        [29] = u32
        [30] = u213
        [31] = u64
        [32] = u78
        [33] = u72
        [34] = u39
        [35] = u58
        [36] = u17
        [37] = u4
        [38] = u6
        [39] = u5
        [40] = u8
        [41] = u63
    --]]
    local v108 = p107 or #u69 + 1
    local u109 = {
        ["Tool"] = nil,
        ["Index"] = v108,
        ["Frame"] = nil
    }
    local u110 = nil
    local u111 = nil
    local u112 = nil
    local u113 = nil
    local u114 = nil
    local u115 = nil
    local u116 = nil
    local u117 = nil
    local u118 = nil
    local function u119() --[[Anonymous function at line 405]]
        --[[
        Upvalues:
            [1] = u51
            [2] = u110
            [3] = u24
            [4] = u22
            [5] = u21
        --]]
        local _ = u51.VREnabled
        u110.SelectionImageObject = nil
        u110.BackgroundTransparency = u110.Draggable and 0 or u24
        u110.BackgroundColor3 = u110.Draggable and u22 or u21
    end
    function u109.Readjust(_, p120, p121) --[[Anonymous function at line 432]]
        --[[
        Upvalues:
            [1] = u60
            [2] = u55
            [3] = u110
        --]]
        local v122 = u60.Size.X.Offset / 2
        local v123 = u55 + 5
        local v124 = p120 - (p121 / 2 + 0.5)
        u110.Position = UDim2.new(0, v122 - u55 / 2 + v123 * v124, 0, 5)
    end
    function u109.Fill(p125, u126) --[[Anonymous function at line 440]]
        --[[
        Upvalues:
            [1] = u115
            [2] = u109
            [3] = u112
            [4] = u113
            [5] = u117
            [6] = u114
            [7] = u84
            [8] = u61
            [9] = u42
            [10] = u110
            [11] = u74
            [12] = u76
            [13] = u56
            [14] = u49
            [15] = u71
            [16] = u70
            [17] = u69
        --]]
        if u115 then
            u115:Disconnect()
            u115 = nil
        end
        if not u126 then
            return p125:Clear()
        end
        p125.Tool = u126
        u115 = u126:GetAttributeChangedSignal("Favorite"):Connect(function() --[[Anonymous function at line 452]]
            --[[
            Upvalues:
                [1] = u109
                [2] = u126
            --]]
            u109.Frame.FavIcon.Visible = u126:GetAttribute("Favorite")
        end)
        xpcall(function() --[[Anonymous function at line 456]]
            --[[
            Upvalues:
                [1] = u109
                [2] = u126
            --]]
            u109.Frame.FavIcon.Visible = u126:GetAttribute("Favorite")
        end, warn)
        local function u129() --[[Anonymous function at line 460]]
            --[[
            Upvalues:
                [1] = u126
                [2] = u112
                [3] = u113
                [4] = u117
            --]]
            local v127 = u126.TextureId
            u112.Image = v127
            if v127 ~= "" then
                u113.Visible = false
            end
            if string.find(u126.Name, "Strawberry") then
                u113.TextSize = 12
            end
            u113.Text = u126.Name
            if u117 and u126:IsA("Tool") then
                u117.Text = u126.ToolTip
                local v128 = u117.TextBounds.X + 24
                u117.Size = UDim2.new(0, v128, 0, 24)
                u117.Position = UDim2.new(0.5, -v128 / 2, 0, -28)
            end
        end
        u129()
        if u114 then
            u114:disconnect()
            u114 = nil
        end
        u114 = u126.Changed:connect(function(p130) --[[Anonymous function at line 510]]
            --[[
            Upvalues:
                [1] = u129
            --]]
            if p130 == "TextureId" or (p130 == "Name" or p130 == "ToolTip") then
                u129()
            end
        end)
        local v131 = p125.Index <= u84
        if (not v131 or u61.Visible) and not u42.VREnabled then
            u110.Draggable = true
        end
        p125:UpdateEquipView()
        if v131 then
            u74 = u74 + 1
            if u76 and (u74 >= 1 and not u56) then
                u56 = true
                u49:BindAction("RBXHotbarEquip", changeToolFunc, false, Enum.KeyCode.ButtonL1, Enum.KeyCode.ButtonR1)
            end
        end
        u71[u126] = p125
        for v132 = 1, u84 do
            v133 = u69[v132]
            if not v133.Tool then
                ::l23::
                u70 = v133
                return
            end
        end
        local v133 = nil
        goto l23
    end
    function u109.Clear(p134) --[[Anonymous function at line 541]]
        --[[
        Upvalues:
            [1] = u114
            [2] = u115
            [3] = u112
            [4] = u113
            [5] = u117
            [6] = u110
            [7] = u84
            [8] = u74
            [9] = u56
            [10] = u49
            [11] = u71
            [12] = u70
            [13] = u69
        --]]
        if not p134.Tool then
            return
        end
        if u114 then
            u114:disconnect()
            u114 = nil
        end
        if u115 then
            u115:disconnect()
            u115 = nil
        end
        u112.Image = ""
        u113.Text = ""
        if u117 then
            u117.Text = ""
            u117.Visible = false
        end
        u110.Draggable = false
        u110.FavIcon.Visible = false
        p134:UpdateEquipView(true)
        if p134.Index <= u84 then
            u74 = u74 - 1
            if u74 < 1 then
                u56 = false
                u49:UnbindAction("RBXHotbarEquip")
            end
        end
        u71[p134.Tool] = nil
        p134.Tool = nil
        for v135 = 1, u84 do
            v136 = u69[v135]
            if not v136.Tool then
                ::l16::
                u70 = v136
                return
            end
        end
        local v136 = nil
        goto l16
    end
    function u109.UpdateEquipView(p137, p138) --[[Anonymous function at line 578]]
        --[[
        Upvalues:
            [1] = u65
            [2] = u86
            [3] = u109
            [4] = u116
            [5] = u90
            [6] = u110
            [7] = u23
            [8] = u119
        --]]
        if not p138 then
            local v139 = p137.Tool
            if v139 then
                v139 = v139.Parent == u65
            end
            if v139 then
                u86 = u109
                if not u116 then
                    u116 = u90("Frame", "Equipped")
                    u116.ZIndex = u110.ZIndex
                    local v140 = Instance.new("UICorner")
                    v140.CornerRadius = script:GetAttribute("CornerRadius")
                    v140.Parent = u116
                    local v141 = Instance.new("UIStroke")
                    v141.Color = u23
                    v141.Thickness = 3
                    v141.Parent = u116
                end
                u116.Parent = u110
                ::l10::
                u119()
                return
            end
        end
        if u116 then
            u116.Parent = nil
        end
        goto l10
    end
    function u109.IsEquipped(p142) --[[Anonymous function at line 604]]
        --[[
        Upvalues:
            [1] = u65
        --]]
        local v143 = p142.Tool
        if v143 then
            v143 = v143.Parent == u65
        end
        return v143
    end
    function u109.Delete(u144) --[[Anonymous function at line 608]]
        --[[
        Upvalues:
            [1] = u73
            [2] = u110
            [3] = u68
            [4] = u7
            [5] = u69
            [6] = u101
        --]]
        if u73[u110] then
            u73[u110] = nil
            u68:unlock()
            if u7 then
                u7:Destroy()
            end
        end
        u110:Destroy()
        task.delay(0.5, function() --[[Anonymous function at line 620]]
            --[[
            Upvalues:
                [1] = u69
                [2] = u144
            --]]
            table.remove(u69, u144.Index)
            local v145 = #u69
            for v146 = u144.Index, v145 do
                u69[v146]:SlideBack()
            end
        end)
        u101()
    end
    function u109.Swap(p147, p148) --[[Anonymous function at line 633]]
        local v149 = p147.Tool
        local v150 = p148.Tool
        p147:Clear()
        if v150 then
            p148:Clear()
            p147:Fill(v150)
        end
        if v149 then
            p148:Fill(v149)
        else
            p148:Clear()
        end
    end
    function u109.SlideBack(p151) --[[Anonymous function at line 647]]
        --[[
        Upvalues:
            [1] = u110
        --]]
        p151.Index = p151.Index - 1
        u110.Name = p151.Index
        u110.LayoutOrder = p151.Index
    end
    function u109.TurnNumber(_, p152) --[[Anonymous function at line 653]]
        --[[
        Upvalues:
            [1] = u118
        --]]
        if u118 then
            u118.Visible = p152
        end
    end
    function u109.SetClickability(p153, p154) --[[Anonymous function at line 659]]
        --[[
        Upvalues:
            [1] = u42
            [2] = u110
            [3] = u119
        --]]
        if p153.Tool then
            if u42.VREnabled then
                u110.Draggable = false
            else
                u110.Draggable = not p154
            end
            u119()
        end
    end
    function u109.CheckTerms(p155, p156) --[[Anonymous function at line 670]]
        --[[
        Upvalues:
            [1] = u113
            [2] = u117
        --]]
        local v157 = 0
        local v158 = p155.Tool
        local v159
        if v158 then
            v159 = v157
            for v160 in pairs(p156) do
                local _, v161 = u113.Text:lower():gsub(v160, "")
                v157 = v159 + v161
                if v158:IsA("Tool") then
                    local _, v162 = (u117 and u117.Text or ""):lower():gsub(v160, "")
                    v157 = v157 + v162
                    v159 = v157
                else
                    v159 = v157
                end
            end
        else
            v159 = v157
        end
        return v159
    end
    function u109.Select(p163) --[[Anonymous function at line 698]]
        --[[
        Upvalues:
            [1] = u109
            [2] = u33
            [3] = u65
            [4] = u66
            [5] = u67
        --]]
        local v164 = u109.Tool
        if v164 then
            u33 = p163
            local v165
            if v164 then
                v165 = v164.Parent == u65
            else
                v165 = v164
            end
            if v165 then
                u33 = nil
                if u66 then
                    u66:UnequipTools()
                    return
                end
            elseif v164.Parent == u67 then
                if u66 then
                    u66:UnequipTools()
                end
                v164.Parent = u65
            end
        end
    end
    u110 = u90("TextButton", v108)
    local u166 = Instance.new("UIStroke")
    u166.Parent = u110
    local v167 = Instance.new("UICorner")
    v167.CornerRadius = script:GetAttribute("CornerRadius")
    v167.Parent = u110
    u166.Thickness = 0
    u110.BackgroundColor3 = u21
    u166.Color = u25
    u110.Text = ""
    u110.AutoButtonColor = false
    u110.BorderSizePixel = 0
    u110.Size = UDim2.new(0, u55, 0, u55)
    u110.Active = true
    u110.Draggable = false
    u110.BackgroundTransparency = u24
    u110.MouseButton1Click:Connect(function() --[[Anonymous function at line 736]]
        --[[
        Upvalues:
            [1] = u109
        --]]
        changeSlot(u109)
    end)
    local u168 = os.clock()
    if u42.TouchEnabled then
        local v169 = u110
        v169.MouseButton1Click:Connect(function() --[[Anonymous function at line 768]]
            --[[
            Upvalues:
                [1] = u168
                [2] = u109
                [3] = u32
            --]]
            if os.clock() > u168 + 0.25 then
                u168 = os.clock()
                return
            else
                local v170 = u109.Tool
                if v170 then
                    u32:FireServer(v170)
                end
            end
        end)
        v169.TouchLongPress:Connect(function(_, p171, _) --[[Anonymous function at line 776]]
            --[[
            Upvalues:
                [1] = u109
                [2] = u32
            --]]
            if p171 == Enum.UserInputState.End then
                local v172 = u109.Tool
                if not v172 then
                    return
                end
                u32:FireServer(v172)
            end
        end)
    else
        u110.MouseButton2Click:Connect(function() --[[Anonymous function at line 782]]
            --[[
            Upvalues:
                [1] = u109
                [2] = u32
            --]]
            local v173 = u109.Tool
            if v173 then
                u32:FireServer(v173)
            end
        end)
    end
    u109.Frame = u110
    local v174 = u90("Frame", "SelectionObjectClipper")
    v174.Visible = false
    v174.Parent = u110
    local v175 = u90("ImageLabel", "Selector")
    v175.Size = UDim2.new(1, 0, 1, 0)
    v175.Image = "rbxasset://textures/ui/Keyboard/key_selection_9slice.png"
    v175.ScaleType = Enum.ScaleType.Slice
    v175.SliceCenter = Rect.new(12, 12, 52, 52)
    v175.Parent = v174
    u112 = u90("ImageLabel", "Icon")
    u112.Size = UDim2.new(0.8, 0, 0.8, 0)
    u112.Position = UDim2.new(0.1, 0, 0.1, 0)
    u112.Parent = u110
    local v176 = u90("ImageLabel", "FavIcon")
    v176.Size = UDim2.new(0.2, 0, 0.2, 0)
    v176.Position = UDim2.new(0.8, 0, 0.8, 0)
    v176.Parent = u110
    v176.Visible = false
    v176.Image = "rbxassetid://80131230547874"
    v176.ImageColor3 = Color3.fromRGB(255, 0, 0)
    v176.ZIndex = 10
    u113 = u90("TextLabel", "ToolName")
    u113.Size = UDim2.new(1, -2, 1, -2)
    u113.Position = UDim2.new(0, 1, 0, 1)
    u113.Parent = u110
    u109.Frame.LayoutOrder = u109.Index
    if v108 <= u84 then
        u117 = u90("TextLabel", "ToolTip")
        u117.ZIndex = 2
        u117.FontFace = script:GetAttribute("ToolTipFont")
        u117.TextWrapped = false
        u117.TextYAlignment = Enum.TextYAlignment.Center
        u117.BackgroundColor3 = Color3.new(0.4, 0.4, 0.4)
        u117.BackgroundTransparency = 0
        u117.Visible = false
        u117.Parent = u110
        local v177 = Instance.new("UICorner")
        v177.CornerRadius = script:GetAttribute("CornerRadius")
        v177.Parent = u117
        u110.MouseEnter:Connect(function() --[[Anonymous function at line 840]]
            --[[
            Upvalues:
                [1] = u117
            --]]
            if u117.Text ~= "" then
                u117.Visible = true
            end
        end)
        u110.MouseLeave:Connect(function() --[[Anonymous function at line 845]]
            --[[
            Upvalues:
                [1] = u117
            --]]
            u117.Visible = false
        end)
        function u109.MoveToInventory(p178) --[[Anonymous function at line 849]]
            --[[
            Upvalues:
                [1] = u109
                [2] = u84
                [3] = u213
                [4] = u64
                [5] = u65
                [6] = u66
                [7] = u78
                [8] = u61
            --]]
            if u109.Index <= u84 then
                local v179 = u109.Tool
                p178:Clear()
                local v180 = u213(u64)
                v180:Fill(v179)
                if v179 then
                    v179 = v179.Parent == u65
                end
                if v179 and u66 then
                    u66:UnequipTools()
                end
                if u78 then
                    v180.Frame.Visible = false
                    v180.Parent = u61
                end
            end
        end
        if v108 < 10 or v108 == u84 then
            local v181 = v108 < 10 and (v108 or 0) or 0
            u118 = u90("TextLabel", "Number")
            u118.Text = v181
            u118.Size = UDim2.new(0, 15, 0, 15)
            u118.Visible = false
            u118.Parent = u110
            u72[u39 + v181] = u109.Select
        end
    end
    local u182 = u110.Position
    local u183 = 0
    local u184 = nil
    local u185 = nil
    u110.DragBegin:connect(function(p186) --[[Anonymous function at line 885]]
        --[[
        Upvalues:
            [1] = u73
            [2] = u110
            [3] = u182
            [4] = u166
            [5] = u68
            [6] = u185
            [7] = u109
            [8] = u58
            [9] = u111
            [10] = u112
            [11] = u113
            [12] = u118
            [13] = u116
            [14] = u184
            [15] = u64
            [16] = u61
            [17] = u90
            [18] = u7
        --]]
        u73[u110] = true
        u182 = p186
        u166.Thickness = 2
        u68:lock()
        if u185 then
            u185:Disconnect()
            u185 = nil
        end
        if u109.Tool then
            u185 = u109.Tool.AncestryChanged:Connect(function(p187, _) --[[Anonymous function at line 898]]
                --[[
                Upvalues:
                    [1] = u109
                    [2] = u185
                    [3] = u58
                    [4] = u111
                --]]
                if u109.Tool and p187 == u109.Tool then
                    local v188 = u109.Tool.Parent
                    if v188 ~= u58.Backpack and v188 ~= u58.Character then
                        if u111 then
                            u111:Destroy()
                            u111 = nil
                        end
                        if u185 then
                            u185:Disconnect()
                            u185 = nil
                        end
                    end
                elseif u185 then
                    u185:Disconnect()
                    u185 = nil
                end
            end)
        elseif u185 then
            u185:Disconnect()
            u185 = nil
        end
        u110.ZIndex = 2
        u112.ZIndex = 2
        u113.ZIndex = 2
        u110.Parent.ZIndex = 2
        if u118 then
            u118.ZIndex = 2
        end
        if u116 then
            u116.ZIndex = 2
            for _, v189 in u116:GetChildren() do
                if not (v189:IsA("UICorner") or v189:IsA("UIStroke")) then
                    v189.ZIndex = 2
                end
            end
        end
        u184 = u110.Parent
        if u184 == u64 then
            local _ = u110.AbsolutePosition
            local v190 = UDim2.new(0, u110.AbsolutePosition.X - u61.AbsolutePosition.X, 0, u110.AbsolutePosition.Y - u61.AbsolutePosition.Y)
            u110.Parent = u61
            u110.Position = v190
            u111 = u90("Frame", "FakeSlot")
            u111.LayoutOrder = u110.LayoutOrder
            u111.Size = u110.Size
            u111.BackgroundTransparency = 1
            u111.Parent = u64
            u7 = u111
        end
    end)
    u110.DragStopped:connect(function(p191, p192) --[[Anonymous function at line 958]]
        --[[
        Upvalues:
            [1] = u73
            [2] = u110
            [3] = u111
            [4] = u185
            [5] = u182
            [6] = u184
            [7] = u166
            [8] = u68
            [9] = u112
            [10] = u113
            [11] = u118
            [12] = u116
            [13] = u109
            [14] = u84
            [15] = u61
            [16] = u17
            [17] = u4
            [18] = u6
            [19] = u5
            [20] = u8
            [21] = u183
            [22] = u70
            [23] = u60
            [24] = u69
            [25] = u65
            [26] = u66
            [27] = u78
        --]]
        u73[u110] = nil
        if u111 then
            u111:Destroy()
            u111 = nil
        end
        if u185 then
            u185:Disconnect()
            u185 = nil
        end
        local v193 = tick()
        u110.Position = u182
        if u184 and u110.Parent ~= u184 then
            u110.Parent = u184
        end
        u166.Thickness = 0
        u68:unlock()
        u110.ZIndex = 1
        u112.ZIndex = 1
        u113.ZIndex = 1
        if u184 then
            u184.ZIndex = 1
        end
        if u118 then
            u118.ZIndex = 1
        end
        if u116 then
            u116.ZIndex = 1
            for _, v194 in pairs(u116:GetChildren()) do
                if not (v194:IsA("UICorner") or v194:IsA("UIStroke")) then
                    v194.ZIndex = 1
                end
            end
        end
        if u109.Tool then
            local v195 = u61
            local v196 = v195.AbsolutePosition
            local v197 = v195.AbsoluteSize
            local v198
            if v196.X < p191 and (p191 <= v196.X + v197.X and v196.Y < p192) then
                v198 = p192 <= v196.Y + v197.Y
            else
                v198 = false
            end
            if v198 then
                if u109.Index <= u84 then
                    print((u17(u109.Tool)))
                    if u4 ~= "All" then
                        u6((u17(u109.Tool)))
                    end
                    print(u4)
                    print(u109.Tool)
                    u109:MoveToInventory()
                    u5.Text = ""
                    task.delay(0.01, function() --[[Anonymous function at line 1020]]
                        --[[
                        Upvalues:
                            [1] = u8
                        --]]
                        if u8 then
                            u8(true)
                        end
                    end)
                end
                if u84 < u109.Index and v193 - u183 < 0.5 then
                    if u70 then
                        local v199 = u109.Tool
                        u109:Clear()
                        u70:Fill(v199)
                        u109:Delete()
                        v193 = 0
                    else
                        v193 = 0
                    end
                end
            else
                local v200 = u60
                local v201 = v200.AbsolutePosition
                local v202 = v200.AbsoluteSize
                local v203
                if v201.X < p191 and (p191 <= v201.X + v202.X and v201.Y < p192) then
                    v203 = p192 <= v201.Y + v202.Y
                else
                    v203 = false
                end
                if v203 then
                    local v204 = { (1 / 0), nil }
                    for v205 = 1, u84 do
                        local v206 = u69[v205]
                        local v207 = v206.Frame
                        local v208 = Vector2.new(p191, p192)
                        local v209 = (v207.AbsolutePosition + v207.AbsoluteSize / 2 - v208).magnitude
                        if v209 < v204[1] then
                            v204 = { v209, v206 }
                        end
                    end
                    local v210 = v204[2]
                    if v210 ~= u109 then
                        u109:Swap(v210)
                        if u84 < u109.Index then
                            local v211 = u109.Tool
                            if v211 then
                                if v211 then
                                    v211 = v211.Parent == u65
                                end
                                if v211 and u66 then
                                    u66:UnequipTools()
                                end
                                if u78 then
                                    u109.Frame.Visible = false
                                    u109.Frame.Parent = u61
                                end
                            else
                                u109:Delete()
                            end
                        end
                    end
                elseif u109.Index <= u84 then
                    u109:MoveToInventory()
                    task.delay(0.01, function() --[[Anonymous function at line 1065]]
                        --[[
                        Upvalues:
                            [1] = u8
                        --]]
                        if u8 then
                            u8(true)
                        end
                    end)
                elseif u109.Tool and (u109.Tool:IsA("Tool") and u109.Tool.CanBeDropped) then
                    u109.Tool.Parent = workspace
                end
            end
            u183 = v193
            task.delay(0.01, function() --[[Anonymous function at line 1077]]
                --[[
                Upvalues:
                    [1] = u8
                --]]
                if u8 then
                    u8(true)
                end
            end)
        else
            local _ = u84 < u109.Index
        end
    end)
    u110.Parent = p106
    u69[v108] = u109
    if u84 < v108 then
        u101()
        if u61.Visible and not u78 then
            local v212 = u63.CanvasSize.Y.Offset - u63.AbsoluteSize.Y
            u63.CanvasPosition = Vector2.new(0, (math.max(0, v212)))
        end
    end
    return u109
end
local function u224(p214) --[[Anonymous function at line 1101]]
    --[[
    Upvalues:
        [1] = u65
        [2] = u66
        [3] = u82
        [4] = u63
        [5] = u75
        [6] = u71
        [7] = u58
        [8] = u70
        [9] = u213
        [10] = u64
        [11] = u69
        [12] = u67
        [13] = u96
        [14] = u8
        [15] = u84
        [16] = u61
    --]]
    if p214:IsA("Tool") then
        local _ = p214.Parent
        if p214.Parent == u65 then
            u82 = tick()
        end
        local v215 = u63
        local v216 = u63.CanvasPosition
        if not u75 and (p214.Parent == u65 and not u71[p214]) then
            local v217 = u58:FindFirstChild("StarterGear")
            if v217 and v217:FindFirstChild(p214.Name) then
                u75 = true
                for v218 = (u70 or u213(u64)).Index, 1, -1 do
                    local v219 = u69[v218]
                    local v220 = v218 - 1
                    if v220 > 0 then
                        u69[v220]:Swap(v219)
                    else
                        v219:Fill(p214)
                    end
                end
                for _, v221 in pairs(u65:GetChildren()) do
                    if v221:IsA("Tool") and v221 ~= p214 then
                        v221.Parent = u67
                    end
                end
                u96()
                v215.CanvasPosition = v216
                if u8 then
                    u8(true)
                end
                return
            end
        end
        local v222 = u71[p214]
        if v222 then
            v222:UpdateEquipView()
        else
            local v223 = u70 or u213(u64)
            v223:Fill(p214)
            if v223.Index <= u84 and not u61.Visible then
                u96()
                v215.CanvasPosition = v216
            end
        end
        task.delay(0.05, function() --[[Anonymous function at line 1169]]
            --[[
            Upvalues:
                [1] = u8
            --]]
            if u8 then
                u8(true)
            end
        end)
    elseif p214:IsA("Humanoid") and p214.Parent == u65 then
        u66 = p214
    end
end
local function u228(p225) --[[Anonymous function at line 1177]]
    --[[
    Upvalues:
        [1] = u82
        [2] = u65
        [3] = u67
        [4] = u71
        [5] = u84
        [6] = u61
        [7] = u96
    --]]
    if p225:IsA("Tool") then
        u82 = tick()
        local v226 = p225.Parent
        if v226 ~= u65 and v226 ~= u67 then
            local v227 = u71[p225]
            if v227 then
                v227:Clear()
                if u84 < v227.Index then
                    v227:Delete()
                    return
                end
                if not u61.Visible then
                    u96()
                end
            end
        end
    else
        return
    end
end
local function v247(p229) --[[Anonymous function at line 1208]]
    --[[
    Upvalues:
        [1] = u69
        [2] = u84
        [3] = u80
        [4] = u65
        [5] = u228
        [6] = u224
        [7] = u67
        [8] = u58
        [9] = u96
    --]]
    for v230 = #u69, 1, -1 do
        local v231 = u69[v230]
        if v231.Tool then
            v231:Clear()
        end
        if u84 < v230 then
            v231:Delete()
        end
    end
    for _, v232 in pairs(u80) do
        v232:Disconnect()
    end
    u80 = {}
    u65 = p229
    local v233 = u80
    local v234 = p229.ChildRemoved
    local v235 = u228
    table.insert(v233, v234:Connect(v235))
    local v236 = u80
    local v237 = p229.ChildAdded
    local v238 = u224
    table.insert(v236, v237:Connect(v238))
    for _, v239 in pairs(p229:GetChildren()) do
        task.spawn(u224, v239)
    end
    u67 = u58:WaitForChild("Backpack")
    local v240 = u80
    local v241 = u67.ChildRemoved
    local v242 = u228
    table.insert(v240, v241:Connect(v242))
    local v243 = u80
    local v244 = u67.ChildAdded
    local v245 = u224
    table.insert(v243, v244:Connect(v245))
    for _, v246 in pairs(u67:GetChildren()) do
        task.spawn(u224, v246)
    end
    u96()
end
local u248 = game:GetService("Players").LocalPlayer:WaitForChild("PlayerGui")
u42:GetMouseLocation()
local function u252(p249) --[[Anonymous function at line 1253]]
    --[[
    Upvalues:
        [1] = u42
        [2] = u248
    --]]
    local v250 = u42:GetMouseLocation()
    for _, v251 in u248:GetGuiObjectsAtPosition(v250.X, v250.Y) do
        if v251.Name:find(p249) then
            return v251
        end
    end
end
local function v257(p253, p254) --[[Anonymous function at line 1265]]
    --[[
    Upvalues:
        [1] = u77
        [2] = u76
        [3] = u40
        [4] = u72
        [5] = u61
        [6] = u252
        [7] = u68
    --]]
    if p254 == false then
        local v255 = (p253.KeyCode == Enum.KeyCode.ButtonL2 or p253.UserInputType == Enum.UserInputType.Keyboard and (not u77 and (u76 or p253.KeyCode.Value == u40))) and u72[p253.KeyCode.Value]
        if v255 then
            v255(p254)
        end
        local v256 = p253.UserInputType
        if (v256 == Enum.UserInputType.MouseButton1 or v256 == Enum.UserInputType.Touch) and (u61.Visible and not u252("CategoryFrame")) then
            u68:deselect()
        end
    end
end
local function v261(p258) --[[Anonymous function at line 1284]]
    --[[
    Upvalues:
        [1] = u42
        [2] = u84
        [3] = u69
    --]]
    if p258 == "KeyboardEnabled" or p258 == "VREnabled" then
        local v259 = u42.KeyboardEnabled
        if v259 then
            v259 = not u42.VREnabled
        end
        for v260 = 1, u84 do
            u69[v260]:TurnNumber(v259)
        end
    end
end
local u262 = nil
local u263 = nil
local function u264() --[[Anonymous function at line 1296]] end
local _ = Vector2.new(0, 0)
function unbindAllGamepadEquipActions()
    --[[
    Upvalues:
        [1] = u49
    --]]
    u49:UnbindAction("RBXBackpackHasGamepadFocus")
    u49:UnbindAction("RBXCloseInventory")
end
function changeToolFunc(_, p265, u266)
    --[[
    Upvalues:
        [1] = u262
        [2] = u263
        [3] = u66
        [4] = u65
        [5] = u71
        [6] = u84
        [7] = u69
        [8] = u86
    --]]
    if p265 == Enum.UserInputState.Begin then
        if u262 and (u262.KeyCode == Enum.KeyCode.ButtonR1 and u266.KeyCode == Enum.KeyCode.ButtonL1 or u262.KeyCode == Enum.KeyCode.ButtonL1 and u266.KeyCode == Enum.KeyCode.ButtonR1) and tick() - u263 <= 0.06 then
            if u66 then
                u66:UnequipTools()
            end
            u262 = u266
            u263 = tick()
        else
            u262 = u266
            u263 = tick()
            delay(0.06, function() --[[Anonymous function at line 1395]]
                --[[
                Upvalues:
                    [1] = u262
                    [2] = u266
                    [3] = u65
                    [4] = u66
                    [5] = u71
                    [6] = u84
                    [7] = u69
                    [8] = u86
                --]]
                if u262 ~= u266 then
                    return
                end
                local v267 = u266.KeyCode == Enum.KeyCode.ButtonL1 and -1 or 1
                local v268 = nil
                if u65 and u66 then
                    for _, v269 in ipairs(u65:GetChildren()) do
                        if v269:IsA("Tool") then
                            v268 = v269
                            break
                        end
                    end
                end
                local v270 = -1
                if v268 then
                    local v271 = u71[v268]
                    if v271 and v271.Index <= u84 then
                        v270 = v271.Index
                    end
                end
                if v270 == -1 then
                    local v272 = (not u86 or (not u86.Tool or u86.Index > u84)) and -1 or u86.Index
                    if v272 == -1 then
                        for v273 = v267 == -1 and (u84 or 1) or 1, v267 == -1 and 1 or u84, v267 do
                            if u69[v273] and u69[v273].Tool then
                                u69[v273]:Select()
                                return
                            end
                        end
                        if u66 then
                            u66:UnequipTools()
                        end
                        return
                    end
                    local v274 = v267 + v272
                    if u84 < v274 then
                        v274 = 1
                    elseif v274 < 1 then
                        v274 = u84
                    end
                    local v275 = v274
                    local v276 = false
                    while true do
                        if u69[v274] and u69[v274].Tool then
                            v276 = true
                            break
                        end
                        v274 = v274 + v267
                        if u84 < v274 then
                            v274 = 1
                        elseif v274 < 1 then
                            v274 = u84
                        end
                        if v274 == v275 or v276 then
                            break
                        end
                    end
                    if v276 then
                        u69[v274]:Select()
                    elseif u66 then
                        u66:UnequipTools()
                    end
                else
                    local v277 = v267 + v270
                    if u84 < v277 then
                        v277 = 1
                    elseif v277 < 1 then
                        v277 = u84
                    end
                    local v278 = v277
                    local v279 = false
                    while true do
                        if u69[v277] and u69[v277].Tool then
                            v279 = true
                            break
                        end
                        v277 = v277 + v267
                        if u84 < v277 then
                            v277 = 1
                        elseif v277 < 1 then
                            v277 = u84
                        end
                        if v277 == v278 or v279 then
                            break
                        end
                    end
                    if v279 then
                        if u69[v277].Tool == v268 then
                            local v280 = 0
                            for v281 = 1, u84 do
                                if u69[v281] and u69[v281].Tool then
                                    v280 = v280 + 1
                                end
                            end
                            if v280 <= 1 then
                                if u66 then
                                    u66:UnequipTools()
                                end
                                u86 = nil
                            else
                                u69[v277]:Select()
                            end
                        else
                            u69[v277]:Select()
                            return
                        end
                    else
                        if u66 then
                            u66:UnequipTools()
                        end
                        u86 = nil
                        return
                    end
                end
            end)
        end
    else
        return
    end
end
function getGamepadSwapSlot()
    --[[
    Upvalues:
        [1] = u69
    --]]
    for v282 = 1, #u69 do
        if u69[v282].Frame:WaitForChild("UIStroke").Thickness > 0 then
            return u69[v282]
        end
    end
end
function changeSlot(u283)
    --[[
    Upvalues:
        [1] = u51
        [2] = u61
        [3] = u46
        [4] = u62
        [5] = u84
    --]]
    local v284 = not u51.VREnabled or u61.Visible
    if u283.Frame == u46.SelectedObject and v284 then
        local v285 = getGamepadSwapSlot()
        if not v285 then
            local u286 = u283.Frame.Size
            local u287 = u283.Frame.Position
            u283.Frame:TweenSizeAndPosition(u286 + UDim2.new(0, 10, 0, 10), u287 - UDim2.new(0, 5, 0, 5), Enum.EasingDirection.Out, Enum.EasingStyle.Quad, 0.1, true, function() --[[Anonymous function at line 1554]]
                --[[
                Upvalues:
                    [1] = u283
                    [2] = u286
                    [3] = u287
                --]]
                u283.Frame:TweenSizeAndPosition(u286, u287, Enum.EasingDirection.In, Enum.EasingStyle.Quad, 0.1, true)
            end)
            u283.Frame:WaitForChild("UIStroke").Thickness = 3
            u62.SelectionImageObject.Visible = true
            return
        end
        v285.Frame:WaitForChild("UIStroke").Thickness = 0
        if v285 ~= u283 then
            u283:Swap(v285)
            u62.SelectionImageObject.Visible = false
            if u84 < u283.Index and not u283.Tool then
                if u46.SelectedObject == u283.Frame then
                    u46.SelectedObject = v285.Frame
                end
                u283:Delete()
            end
            if u84 < v285.Index and not v285.Tool then
                if u46.SelectedObject == v285.Frame then
                    u46.SelectedObject = u283.Frame
                end
                v285:Delete()
                return
            end
        end
    else
        u283:Select()
        u62.SelectionImageObject.Visible = false
    end
end
function vrMoveSlotToInventory()
    --[[
    Upvalues:
        [1] = u51
        [2] = u62
    --]]
    if u51.VREnabled then
        local v288 = getGamepadSwapSlot()
        if v288 and v288.Tool then
            v288:WaitForChild("UIStroke").Thickness = 0
            v288:MoveToInventory()
            u62.SelectionImageObject.Visible = false
        end
    end
end
function enableGamepadInventoryControl()
    --[[
    Upvalues:
        [1] = u61
        [2] = u68
        [3] = u49
        [4] = u264
        [5] = u42
        [6] = u46
        [7] = u60
    --]]
    local function v291(_, p289, _) --[[Anonymous function at line 1579]]
        --[[
        Upvalues:
            [1] = u61
            [2] = u68
        --]]
        if p289 == Enum.UserInputState.Begin then
            if getGamepadSwapSlot() then
                local v290 = getGamepadSwapSlot()
                if v290 and typeof(v290) == "Instance" then
                    v290:WaitForChild("UIStroke").Thickness = 0
                    return
                end
            elseif u61.Visible then
                u68:deselect()
            end
        end
    end
    u49:BindAction("RBXBackpackHasGamepadFocus", u264, false, Enum.UserInputType.Gamepad1)
    u49:BindAction("RBXCloseInventory", v291, false, Enum.KeyCode.ButtonB, Enum.KeyCode.ButtonStart)
    if not u42.VREnabled then
        u46.SelectedObject = u60:FindFirstChild("1")
    end
end
function disableGamepadInventoryControl()
    --[[
    Upvalues:
        [1] = u84
        [2] = u69
        [3] = u46
        [4] = u59
    --]]
    unbindAllGamepadEquipActions()
    for v292 = 1, u84 do
        local v293 = u69[v292]
        if v293 and v293.Frame then
            v293.Frame:WaitForChild("UIStroke").Thickness = 0
        end
    end
    if u46.SelectedObject and u46.SelectedObject:IsDescendantOf(u59) then
        u46.SelectedObject = nil
    end
end
function gamepadDisconnected()
    --[[
    Upvalues:
        [1] = u81
    --]]
    u81 = false
    disableGamepadInventoryControl()
end
function gamepadConnected()
    --[[
    Upvalues:
        [1] = u81
        [2] = u46
        [3] = u59
        [4] = u74
        [5] = u76
        [6] = u56
        [7] = u49
        [8] = u61
    --]]
    u81 = true
    u46:AddSelectionParent("RBXBackpackSelection", u59)
    if u74 >= 1 and (u76 and not u56) then
        u56 = true
        u49:BindAction("RBXHotbarEquip", changeToolFunc, false, Enum.KeyCode.ButtonL1, Enum.KeyCode.ButtonR1)
    end
    if u61.Visible then
        enableGamepadInventoryControl()
    end
end
local function u297(p294) --[[Anonymous function at line 1651]]
    --[[
    Upvalues:
        [1] = u45
        [2] = u68
        [3] = u46
        [4] = u76
        [5] = u59
        [6] = u79
        [7] = u74
        [8] = u56
        [9] = u49
    --]]
    if p294 then
        p294 = u45:GetCore("TopbarEnabled")
    end
    local v295 = u68
    local v296
    if p294 then
        v296 = not u46.MenuIsOpen
    else
        v296 = p294
    end
    v295:setEnabled(v296)
    u76 = p294
    u59.Visible = p294
    for _, _ in pairs(u79) do

    end
    if p294 then
        if u74 >= 1 and (u76 and not u56) then
            u56 = true
            u49:BindAction("RBXHotbarEquip", changeToolFunc, false, Enum.KeyCode.ButtonL1, Enum.KeyCode.ButtonR1)
            return
        end
    else
        disableGamepadInventoryControl()
        u56 = false
        u49:UnbindAction("RBXHotbarEquip")
    end
end
local function v303(p298, p299) --[[Anonymous function at line 1677]]
    --[[
    Upvalues:
        [1] = u90
    --]]
    local v300 = u90("ImageButton", p298)
    v300.Size = UDim2.new(0, 40, 0, 40)
    v300.Image = "rbxasset://textures/ui/Keyboard/close_button_background.png"
    local v301 = u90("ImageLabel", "Icon")
    v301.Size = UDim2.new(0.5, 0, 0.5, 0)
    v301.Position = UDim2.new(0.25, 0, 0.25, 0)
    v301.Image = p299
    v301.Parent = v300
    local v302 = u90("ImageLabel", "Selection")
    v302.Size = UDim2.new(0.9, 0, 0.9, 0)
    v302.Position = UDim2.new(0.05, 0, 0.05, 0)
    v302.Image = "rbxasset://textures/ui/Keyboard/close_button_selection.png"
    v300.SelectionImageObject = v302
    return v300, v301, v302
end
local v304 = u90("Frame", "Backpack")
v304.Visible = false
v304.Parent = v48
local v305 = u90("Frame", "Hotbar")
v305.Parent = v304
local v306 = v304
local u307 = v305
for v308 = 1, u84 do
    local v309 = u213(u307, v308)
    v309.Frame.Visible = false
    if not u70 then
        local v310 = v309
        u70 = v310
    end
end
u68.selected:Connect(function() --[[Anonymous function at line 1717]]
    --[[
    Upvalues:
        [1] = u46
        [2] = u18
    --]]
    if not u46.MenuIsOpen then
        u18.OpenClose()
    end
end)
u68.deselected:Connect(function() --[[Anonymous function at line 1722]]
    --[[
    Upvalues:
        [1] = u61
        [2] = u18
    --]]
    if u61.Visible then
        u18.OpenClose()
    end
end)
LeftBumperButton = u90("ImageLabel", "LeftBumper")
LeftBumperButton.Size = UDim2.new(0, 40, 0, 40)
LeftBumperButton.Position = UDim2.new(0, -LeftBumperButton.Size.X.Offset, 0.5, -LeftBumperButton.Size.Y.Offset / 2)
RightBumperButton = u90("ImageLabel", "RightBumper")
RightBumperButton.Size = UDim2.new(0, 40, 0, 40)
RightBumperButton.Position = UDim2.new(1, 0, 0.5, -RightBumperButton.Size.Y.Offset / 2)
local v311 = u90("Frame", "Inventory")
local v312 = Instance.new("UICorner")
v312.CornerRadius = script:GetAttribute("CornerRadius")
v312.Parent = v311
v311.BackgroundTransparency = v20
v311.BackgroundColor3 = u21
v311.Active = true
v311.Visible = false
v311.Parent = v306
local u313 = game:GetService("TweenService")
require(v44.Item_Module)
local u314 = script.CategoryTemplate:Clone()
u314.Parent = v311
local function v316(p315) --[[Anonymous function at line 1757]]
    --[[
    Upvalues:
        [1] = u4
        [2] = u314
        [3] = u8
    --]]
    if u4 ~= p315 then
        u4 = p315
        u314.Text = ("%* Items"):format(p315)
        u8()
    end
end
local v317 = script.CategoryFrame:Clone()
v317.Size = v57 and UDim2.fromScale(0.14, 1) or v317.Size
v317.Parent = v311
local v318 = v317.CategoryTemplate
local u319 = v311
local u320 = v316
local function v328(p321, p322) --[[Anonymous function at line 1780]]
    local v323, v324, v325 = p321:ToHSV()
    local v326 = v325 + p322
    local v327 = math.clamp(v326, 0, 1)
    return Color3.fromHSV(v323, v324, v327)
end
for _, v329 in v2 do
    local u330 = v329.Name
    local v331 = v318:Clone()
    local v332 = v331.ImageButton
    local v333 = v331.BackgroundColor3
    local u334 = u313:Create(v331, TweenInfo.new(0.2), {
        ["BackgroundColor3"] = v328(v333, 0.2)
    })
    local u335 = u313:Create(v331, TweenInfo.new(0.2), {
        ["BackgroundColor3"] = v333
    })
    v332.MouseEnter:Connect(function() --[[Anonymous function at line 1801]]
        --[[
        Upvalues:
            [1] = u334
        --]]
        u334:Play()
    end)
    v332.MouseLeave:Connect(function() --[[Anonymous function at line 1804]]
        --[[
        Upvalues:
            [1] = u335
        --]]
        u335:Play()
    end)
    v332.MouseButton1Down:Connect(function() --[[Anonymous function at line 1808]]
        --[[
        Upvalues:
            [1] = u335
        --]]
        u335:Play()
    end)
    v332.MouseButton1Up:Connect(function() --[[Anonymous function at line 1812]]
        --[[
        Upvalues:
            [1] = u334
            [2] = u320
            [3] = u330
        --]]
        u334:Play()
        u320(u330)
    end)
    v332.Image = v329.Image
    v331.Visible = true
    v331.Parent = v317
end
local v336 = u90("TextButton", "VRInventorySelector")
v336.Position = UDim2.new(0, 0, 0, 0)
v336.Size = UDim2.new(1, 0, 1, 0)
v336.BackgroundTransparency = 1
v336.Text = ""
v336.Parent = u319
local v337 = u90("ImageLabel", "Selector")
v337.Size = UDim2.new(1, 0, 1, 0)
v337.Image = "rbxasset://textures/ui/Keyboard/key_selection_9slice.png"
v337.ScaleType = Enum.ScaleType.Slice
v337.SliceCenter = Rect.new(12, 12, 52, 52)
v337.Visible = false
v336.SelectionImageObject = v337
v336.MouseButton1Click:Connect(function() --[[Anonymous function at line 1837]]
    vrMoveSlotToInventory()
end)
local u338 = u90("ScrollingFrame", "ScrollingFrame")
u338.Selectable = false
u338.CanvasSize = UDim2.new(0, 0, 0, 0)
u338.Parent = u319
local u339 = u90("Frame", "UIGridFrame")
u339.Selectable = false
u339.Size = UDim2.new(1, -10, 1, 0)
u339.Position = UDim2.new(0, 5, 0, 0)
u339.Parent = u338
local v340 = Instance.new("UIGridLayout")
v340.SortOrder = Enum.SortOrder.LayoutOrder
v340.CellSize = UDim2.new(0, u55, 0, u55)
v340.CellPadding = UDim2.new(0, 5, 0, 5)
v340.Parent = u339
local u341 = v303("ScrollUpButton", "rbxasset://textures/ui/Backpack/ScrollUpArrow.png")
u341.Size = UDim2.new(0, 34, 0, 34)
u341.Position = UDim2.new(0.5, -u341.Size.X.Offset / 2, 0, 43)
u341.Icon.Position = u341.Icon.Position - UDim2.new(0, 0, 0, 2)
u341.MouseButton1Click:Connect(function() --[[Anonymous function at line 1863]]
    --[[
    Upvalues:
        [1] = u338
        [2] = u55
    --]]
    local v342 = u338
    local v343 = Vector2.new
    local v344 = u338.CanvasPosition.X
    local v345 = u338.CanvasSize.Y.Offset - u338.AbsoluteWindowSize.Y
    local v346 = u338.CanvasPosition.Y - (u55 + 5)
    local v347 = math.max(0, v346)
    v342.CanvasPosition = v343(v344, (math.min(v345, v347)))
end)
local u348 = v303("ScrollDownButton", "rbxasset://textures/ui/Backpack/ScrollUpArrow.png")
u348.Rotation = 180
u348.Icon.Position = u348.Icon.Position - UDim2.new(0, 0, 0, 2)
u348.Size = UDim2.new(0, 34, 0, 34)
u348.Position = UDim2.new(0.5, -u348.Size.X.Offset / 2, 1, -u348.Size.Y.Offset - 3)
u348.MouseButton1Click:Connect(function() --[[Anonymous function at line 1874]]
    --[[
    Upvalues:
        [1] = u338
        [2] = u55
    --]]
    local v349 = u338
    local v350 = Vector2.new
    local v351 = u338.CanvasPosition.X
    local v352 = u338.CanvasSize.Y.Offset - u338.AbsoluteWindowSize.Y
    local v353 = u338.CanvasPosition.Y + (u55 + 5)
    local v354 = math.max(0, v353)
    v349.CanvasPosition = v350(v351, (math.min(v352, v354)))
end)
u338.Changed:Connect(function(p355) --[[Anonymous function at line 1880]]
    --[[
    Upvalues:
        [1] = u338
        [2] = u341
        [3] = u348
    --]]
    if p355 == "AbsoluteWindowSize" or (p355 == "CanvasPosition" or p355 == "CanvasSize") then
        local v356 = u338.CanvasPosition.Y ~= 0
        local v357 = u338.CanvasPosition.Y < u338.CanvasSize.Y.Offset - u338.AbsoluteWindowSize.Y
        u341.Visible = v356
        u348.Visible = v357
    end
end)
v105()
local u358 = u52:Create("Frame")({
    ["Name"] = "GamepadHintsFrame",
    ["Size"] = UDim2.new(0, u307.Size.X.Offset, 0, u54 and 95 or 60),
    ["BackgroundTransparency"] = 1,
    ["Visible"] = false,
    ["Parent"] = v306
})
local function v366(p359, p360, p361) --[[Anonymous function at line 1903]]
    --[[
    Upvalues:
        [1] = u52
        [2] = u358
        [3] = u54
    --]]
    local v362 = u52:Create("Frame")({
        ["Name"] = "HintFrame",
        ["Size"] = UDim2.new(1, 0, 1, -5),
        ["Position"] = UDim2.new(0, 0, 0, 0),
        ["BackgroundTransparency"] = 1,
        ["Parent"] = u358
    })
    local v363 = u52:Create("ImageLabel")
    local v364 = {
        ["Name"] = "HintImage",
        ["Size"] = u54 and UDim2.new(0, 90, 0, 90) or UDim2.new(0, 60, 0, 60),
        ["BackgroundTransparency"] = 1
    }
    if u54 then
        p359 = p360 or p359
    end
    v364.Image = p359
    v364.Parent = v362
    v363(v364)
    local v365 = u52:Create("TextLabel")({
        ["Name"] = "HintText",
        ["Position"] = UDim2.new(0, u54 and 100 or 70, 0, 0),
        ["Size"] = UDim2.new(1, -(u54 and 100 or 70), 1, 0),
        ["Font"] = Enum.Font.SourceSansBold,
        ["FontSize"] = u54 and Enum.FontSize.Size36 or Enum.FontSize.Size24,
        ["BackgroundTransparency"] = 1,
        ["Text"] = p361,
        ["TextColor3"] = Color3.new(1, 1, 1),
        ["TextXAlignment"] = Enum.TextXAlignment.Left,
        ["TextWrapped"] = true,
        ["Parent"] = v362
    })
    Instance.new("UITextSizeConstraint", v365).MaxTextSize = v365.TextSize
end
local function u372() --[[Anonymous function at line 1940]]
    --[[
    Upvalues:
        [1] = u358
        [2] = u307
        [3] = u54
        [4] = u319
    --]]
    u358.Size = UDim2.new(u307.Size.X.Scale, u307.Size.X.Offset, 0, u54 and 95 or 60)
    u358.Position = UDim2.new(u307.Position.X.Scale, u307.Position.X.Offset, u319.Position.Y.Scale, u319.Position.Y.Offset - u358.Size.Y.Offset)
    local v367 = u358:GetChildren()
    local v368 = 0
    for v369 = 1, #v367 do
        v367[v369].Size = UDim2.new(1, 0, 1, -5)
        v367[v369].Position = UDim2.new(0, 0, 0, 0)
        v368 = v368 + (v367[v369].HintText.Position.X.Offset + v367[v369].HintText.TextBounds.X)
    end
    local v370 = (u358.AbsoluteSize.X - v368) / (#v367 - 1)
    for v371 = 1, #v367 do
        v367[v371].Position = v371 == 1 and UDim2.new(0, 0, 0, 0) or UDim2.new(0, v367[v371 - 1].Position.X.Offset + v367[v371 - 1].Size.X.Offset + v370, 0, 0)
        v367[v371].Size = UDim2.new(0, v367[v371].HintText.Position.X.Offset + v367[v371].HintText.TextBounds.X, 1, -5)
    end
end
v366("rbxasset://textures/ui/Settings/Help/XButtonDark.png", "rbxasset://textures/ui/Settings/Help/<EMAIL>", "Remove From Hotbar ")
v366("rbxasset://textures/ui/Settings/Help/AButtonDark.png", "rbxasset://textures/ui/Settings/Help/<EMAIL>", "Select/Swap")
v366("rbxasset://textures/ui/Settings/Help/BButtonDark.png", "rbxasset://textures/ui/Settings/Help/<EMAIL>", "Close Backpack")
local u373 = u90("Frame", "Search")
local v374 = Instance.new("UICorner")
v374.CornerRadius = script:GetAttribute("CornerRadius")
v374.Parent = u373
u373.BackgroundColor3 = v29
u373.BackgroundTransparency = v30
u373.Size = UDim2.new(0, 190, 0, 30)
u373.Position = UDim2.new(1, -u373.Size.X.Offset - 5, 0, 5)
u373.Parent = u319
local u375 = u90("TextBox", "TextBox")
u375.PlaceholderText = "search"
u375.ClearTextOnFocus = false
u375.FontSize = Enum.FontSize.Size24
u375.TextXAlignment = Enum.TextXAlignment.Left
local v376 = Instance.new("UIPadding")
v376.Parent = u375
v376.PaddingLeft = UDim.new(0, 8)
u375.Size = u373.Size - UDim2.fromOffset(0, 0)
u375.Position = UDim2.new(0, 0, 0, 0)
u375.Parent = u373
local u377 = script.StrokeTemplate:Clone()
u377.Enabled = false
u377.Parent = u373
task.spawn(function() --[[Anonymous function at line 1994]]
    --[[
    Upvalues:
        [1] = u377
        [2] = u313
    --]]
    while true do
        repeat
            task.wait()
        until u377.Enabled
        local v378 = u313:Create(u377.UIGradient, TweenInfo.new(0.05), {
            ["Rotation"] = u377.UIGradient.Rotation + 1
        })
        v378:Play()
        v378.Completed:Wait()
    end
end)
local _ = u375
local u379 = u90("TextButton", "X")
local v380 = Instance.new("UICorner")
v380.CornerRadius = script:GetAttribute("CornerRadius")
v380.Parent = u379
u379.Text = "X"
u379.ZIndex = 10
u379.TextColor3 = Color3.new(1, 1, 1)
u379.FontSize = Enum.FontSize.Size24
u379.TextYAlignment = Enum.TextYAlignment.Bottom
u379.BackgroundTransparency = 1
u379.Size = UDim2.new(0, u373.Size.Y.Offset - 10, 0, u373.Size.Y.Offset - 10)
u379.Position = UDim2.new(1, -u379.Size.X.Offset - 10, 0.5, -u379.Size.Y.Offset / 2)
u379.Visible = false
u379.BorderSizePixel = 0
u379.Parent = u373
local function u394(_) --[[Anonymous function at line 2025]]
    --[[
    Upvalues:
        [1] = u375
        [2] = u84
        [3] = u69
        [4] = u319
        [5] = u78
        [6] = u13
        [7] = u339
        [8] = u101
        [9] = u379
    --]]
    local v381 = {}
    for v382 in u375.Text:gmatch("%S+") do
        v381[v382:lower()] = true
    end
    local v383 = {}
    for v384 = u84 + 1, #u69 do
        local u385 = u69[v384]
        local v386 = { u385, (u385:CheckTerms(v381)) }
        table.insert(v383, v386)
        u385.Frame.Visible = false
        pcall(function() --[[Anonymous function at line 2049]]
            --[[
            Upvalues:
                [1] = u385
                [2] = u319
            --]]
            u385.Frame.Parent = u319
        end)
    end
    table.sort(v383, function(p387, p388) --[[Anonymous function at line 2054]]
        return p387[2] > p388[2]
    end)
    u78 = true
    local v389 = 0
    for _, v390 in v383 do
        local v391 = v390[1]
        local v392 = v390[2]
        local v393 = v391.Tool
        if (v392 > 0 or u375.Text == "") and u13(v393) then
            v391.Frame.Visible = true
            v391.Frame.Parent = u339
            v391.Frame.LayoutOrder = u84 + v389
            v389 = v389 + 1
        else
            v391.Frame.Visible = false
        end
    end
    u101()
    u379.ZIndex = 3
end
local _ = u394
local function u397() --[[Anonymous function at line 2080]]
    --[[
    Upvalues:
        [1] = u78
        [2] = u84
        [3] = u69
        [4] = u4
        [5] = u339
        [6] = u13
        [7] = u379
        [8] = u101
    --]]
    print("Clear Results")
    u78 = false
    for v395 = u84 + 1, #u69 do
        local v396 = u69[v395]
        if u4 == "All" then
            v396.Frame.LayoutOrder = v396.Index
            v396.Frame.Parent = u339
            v396.Frame.Visible = true
        elseif u4 ~= "All" then
            if u13(v396.Tool) then
                v396.Frame.Visible = true
            else
                v396.Frame.Visible = false
            end
        end
    end
    u379.ZIndex = 0
    u101()
end
u379.MouseButton1Click:Connect(function() --[[Function name: reset, line 2106]]
    --[[
    Upvalues:
        [1] = u397
        [2] = u375
    --]]
    u397()
    u375.Text = ""
end)
u375.Changed:Connect(function(p398) --[[Function name: onChanged, line 2111]]
    --[[
    Upvalues:
        [1] = u320
        [2] = u375
        [3] = u394
        [4] = u379
        [5] = u377
    --]]
    if p398 == "Text" then
        u320("All")
        local v399 = u375.Text
        u394()
        local v400 = u379
        local v401
        if v399 == "" then
            v401 = false
        else
            v401 = v399 ~= ""
        end
        v400.Visible = v401
        local v402 = u377
        local v403
        if v399 == "" then
            v403 = false
        else
            v403 = v399 ~= ""
        end
        v402.Enabled = v403
    end
end)
u375.FocusLost:Connect(function(p404) --[[Function name: focusLost, line 2126]]
    --[[
    Upvalues:
        [1] = u394
    --]]
    if p404 then
        u394()
    end
end)
u18.StateChanged.Event:Connect(function(p405) --[[Anonymous function at line 2137]]
    --[[
    Upvalues:
        [1] = u319
        [2] = u68
    --]]
    if not (p405 or u319.Visible) then
        u68:deselect()
    end
end)
u72[Enum.KeyCode.Escape.Value] = function(p406) --[[Anonymous function at line 2147]]
    --[[
    Upvalues:
        [1] = u319
        [2] = u68
    --]]
    if not p406 then
        if u319.Visible then
            u68:deselect()
        end
    end
end
u72[Enum.KeyCode.ButtonL2.Value] = function(_) --[[Anonymous function at line 2155]]
    --[[
    Upvalues:
        [1] = u33
        [2] = u32
    --]]
    if u33 then
        local v407 = u33.Tool
        if v407 then
            u32:FireServer(v407)
        end
    else
        return
    end
end
u42.LastInputTypeChanged:Connect(function(p408) --[[Function name: detectGamepad, line 2161]]
    --[[
    Upvalues:
        [1] = u42
        [2] = u373
    --]]
    if p408 == Enum.UserInputType.Gamepad1 and not u42.VREnabled then
        u373.Visible = false
    else
        u373.Visible = true
    end
end)
u46.MenuOpened:Connect(function() --[[Anonymous function at line 2171]]
    --[[
    Upvalues:
        [1] = u319
        [2] = u68
    --]]
    if u319.Visible then
        u68:deselect()
    end
end)
local function u411(_, p409, _) --[[Anonymous function at line 2179]]
    --[[
    Upvalues:
        [1] = u46
        [2] = u84
        [3] = u69
    --]]
    if p409 == Enum.UserInputState.Begin then
        if u46.SelectedObject then
            for v410 = 1, u84 do
                if u69[v410].Frame == u46.SelectedObject and u69[v410].Tool then
                    u69[v410]:MoveToInventory()
                    return
                end
            end
        end
    else
        return
    end
end
u45:SetCoreGuiEnabled(Enum.CoreGuiType.Backpack, false)
function u18.OpenClose() --[[Anonymous function at line 2191]]
    --[[
    Upvalues:
        [1] = u73
        [2] = u319
        [3] = u96
        [4] = u307
        [5] = u84
        [6] = u69
        [7] = u81
        [8] = u41
        [9] = u42
        [10] = u372
        [11] = u358
        [12] = u49
        [13] = u411
        [14] = u18
    --]]
    if not next(u73) then
        u319.Visible = not u319.Visible
        local v412 = u319.Visible
        u96()
        u307.Active = not u307.Active
        for v413 = 1, u84 do
            u69[v413]:SetClickability(not v412)
        end
    end
    if u319.Visible then
        if u81 then
            if u41[u42:GetLastInputType()] then
                u372()
                u358.Visible = not u42.VREnabled
            end
            enableGamepadInventoryControl()
        end
    else
        if u81 then
            u358.Visible = false
        end
        disableGamepadInventoryControl()
    end
    if u319.Visible then
        u49:BindAction("RBXRemoveSlot", u411, false, Enum.KeyCode.ButtonX)
    else
        u49:UnbindAction("RBXRemoveSlot")
    end
    u18.IsOpen = u319.Visible
    u18.StateChanged:Fire(u319.Visible)
end
while not u58 do
    wait()
    u58 = v43.LocalPlayer
end
u58.CharacterAdded:Connect(v247)
if u58.Character then
    v247(u58.Character)
end
u42.InputBegan:Connect(v257)
u42.TextBoxFocused:Connect(function() --[[Anonymous function at line 2255]]
    --[[
    Upvalues:
        [1] = u77
    --]]
    u77 = true
end)
u42.TextBoxFocusReleased:Connect(function() --[[Anonymous function at line 2256]]
    --[[
    Upvalues:
        [1] = u77
    --]]
    u77 = false
end)
u72[u40] = function() --[[Anonymous function at line 2259]]
    --[[
    Upvalues:
        [1] = u66
    --]]
    if u66 then
        u66:UnequipTools()
    end
end
u42.Changed:Connect(v261)
v261("KeyboardEnabled")
if u42:GetGamepadConnected(Enum.UserInputType.Gamepad1) then
    gamepadConnected()
end
u42.GamepadConnected:Connect(function(p414) --[[Anonymous function at line 2271]]
    if p414 == Enum.UserInputType.Gamepad1 then
        gamepadConnected()
    end
end)
u42.GamepadDisconnected:Connect(function(p415) --[[Anonymous function at line 2276]]
    if p415 == Enum.UserInputType.Gamepad1 then
        gamepadDisconnected()
    end
end)
function u18.SetBackpackEnabled(_, p416) --[[Anonymous function at line 2283]]
    --[[
    Upvalues:
        [1] = u34
    --]]
    u34 = p416
end
function u18.IsOpened(_) --[[Anonymous function at line 2287]]
    --[[
    Upvalues:
        [1] = u18
    --]]
    return u18.IsOpen
end
function u18.GetBackpackEnabled(_) --[[Anonymous function at line 2291]]
    --[[
    Upvalues:
        [1] = u34
    --]]
    return u34
end
function u18.GetStateChangedEvent(_) --[[Anonymous function at line 2295]]
    --[[
    Upvalues:
        [1] = u67
    --]]
    return u67.StateChanged
end
v50.Heartbeat:Connect(function() --[[Anonymous function at line 2299]]
    --[[
    Upvalues:
        [1] = u297
        [2] = u34
    --]]
    u297(u34)
end)
v31.Event:Connect(function(p417, p418) --[[Anonymous function at line 2302]]
    --[[
    Upvalues:
        [1] = u18
    --]]
    if p417 == "SetBackpackEnabled" then
        u18:SetBackpackEnabled(p418)
    elseif p417 == "SetInventoryOpen" then
        if type(p418) == "boolean" and p418 == true then
            u18.IsOpen = true
            return
        end
        if type(p418) == "boolean" then
            u18.IsOpen = false
            return
        end
    elseif p417 == "ToggleBackpack" then
        u18.OpenClose()
    end
end)
return u18