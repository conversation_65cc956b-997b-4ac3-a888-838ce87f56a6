-- Full Path: -Grow-a-Garden-\\ScoundrelClonedSeed-LocalScript.lua
local v1 = game:GetService("ReplicatedStorage")
local u2 = require(v1.Modules.PetServices.ActivePetsService)
local u3 = v1:WaitForChild("Seed_Models")
local v4 = v1.GameEvents:WaitForChild("ScoundrelVisualSeed_RE")
local v5 = v1.GameEvents:WaitForChild("ScoundrelDestroySeed_RE")
v4.OnClientEvent:Connect(function(u6, u7, u8, u9) --[[Anonymous function at line 24]]
    --[[
    Upvalues:
        [1] = u2
        [2] = u3
    --]]
    task.defer(function() --[[Anonymous function at line 25]]
        --[[
        Upvalues:
            [1] = u2
            [2] = u6
            [3] = u7
            [4] = u3
            [5] = u8
            [6] = u9
        --]]
        local v10 = u2:GetClientPetState(u6.Name)
        if v10 then
            v10 = v10[u7]
        end
        if v10 then
            v10 = v10.Asset
        end
        if v10 then
            v10 = v10:FindFirstChildWhichIsA("Model", true)
        end
        if v10 then
            local v11 = u3:FindFirstChild(u8)
            if v11 and v11:IsA("BasePart") then
                local v12 = v11:Clone()
                v12:SetAttribute("UUID", u9)
                v12.Anchored = false
                v12.Parent = v10
                local v13 = v10:FindFirstChild("FruitHold", true)
                if v13 then
                    v12.CFrame = v13.CFrame
                    local v14 = Instance.new("WeldConstraint")
                    v14.Part0 = v13
                    v14.Part1 = v12
                    v14.Parent = v12
                end
            else
                warn("Invalid seed model for:", u8)
            end
        else
            return
        end
    end)
end)
v5.OnClientEvent:Connect(function(p15) --[[Anonymous function at line 54]]
    for _, v16 in ipairs(workspace:GetDescendants()) do
        if v16:IsA("BasePart") and v16:GetAttribute("UUID") == p15 then
            v16:Destroy()
        end
    end
end)