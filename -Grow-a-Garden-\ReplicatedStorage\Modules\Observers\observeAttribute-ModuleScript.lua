-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\Observers\observeAttribute-ModuleScript.lua
local function u1(_) --[[Anonymous function at line 20]]
    return true
end
return function(u2, u3, u4, u5) --[[Function name: observeAttribute, line 61]]
    --[[
    Upvalues:
        [1] = u1
    --]]
    local u6 = nil
    local u7 = nil
    local u8 = 0
    if u5 == nil then
        u5 = u1
    end
    local function u12() --[[Anonymous function at line 74]]
        --[[
        Upvalues:
            [1] = u6
            [2] = u8
            [3] = u2
            [4] = u3
            [5] = u5
            [6] = u4
            [7] = u7
        --]]
        if u6 ~= nil then
            task.spawn(u6)
            u6 = nil
        end
        u8 = u8 + 1
        local u9 = u8
        local u10 = u2:GetAttribute(u3)
        if u10 ~= nil and u5(u10) then
            task.spawn(function() --[[Anonymous function at line 86]]
                --[[
                Upvalues:
                    [1] = u4
                    [2] = u10
                    [3] = u9
                    [4] = u8
                    [5] = u7
                    [6] = u6
                --]]
                local v11 = u4(u10)
                if u9 == u8 and u7.Connected then
                    u6 = v11
                else
                    task.spawn(v11)
                end
            end)
        end
    end
    local u13 = u2:GetAttributeChangedSignal(u3):Connect(u12)
    task.defer(function() --[[Anonymous function at line 101]]
        --[[
        Upvalues:
            [1] = u13
            [2] = u12
        --]]
        if u13.Connected then
            u12()
        end
    end)
    return function() --[[Anonymous function at line 110]]
        --[[
        Upvalues:
            [1] = u13
            [2] = u6
        --]]
        u13:Disconnect()
        if u6 ~= nil then
            task.spawn(u6)
            u6 = nil
        end
    end
end