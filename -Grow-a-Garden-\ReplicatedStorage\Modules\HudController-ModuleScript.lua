-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\HudController-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
game:GetService("SoundService")
local v2 = game:GetService("Players")
local u3 = require(v1.Modules.GuiController)
local u4 = v2.LocalPlayer.PlayerGui
local u5 = u4.Hud_UI
local v6 = {
    ["Start"] = function(_) --[[Function name: Start, line 14]]
        --[[
        Upvalues:
            [1] = u5
            [2] = u3
            [3] = u4
        --]]
        u5.SideBtns.Shop.Activated:Connect(function() --[[Anonymous function at line 15]]
            --[[
            Upvalues:
                [1] = u3
                [2] = u4
            --]]
            u3:Toggle(u4.Shop_UI)
        end)
    end
}
task.spawn(v6.Start, v6)
return v6