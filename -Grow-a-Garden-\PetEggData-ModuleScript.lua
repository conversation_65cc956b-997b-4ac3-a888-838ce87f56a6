-- Full Path: -Grow-a-Garden-\\PetEggData-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
require(v1.Modules.Chalk)
local v2 = {
    ["Common Egg"] = {
        ["EggName"] = "Common Egg",
        ["EggRarity"] = "Common",
        ["StockChance"] = 1,
        ["Price"] = 50000,
        ["PurchaseID"] = 3276346455,
        ["LayoutOrder"] = 1,
        ["PetEggDescription"] = "It\'s a dog"
    },
    ["Uncommon Egg"] = {
        ["EggName"] = "Uncommon Egg",
        ["EggRarity"] = "Uncommon",
        ["StockChance"] = 6,
        ["Price"] = 150000,
        ["PurchaseID"] = 3276346509,
        ["LayoutOrder"] = 1,
        ["PetEggDescription"] = "It\'s a dog"
    },
    ["Rare Egg"] = {
        ["EggName"] = "Rare Egg",
        ["EggRarity"] = "Rare",
        ["StockChance"] = 20,
        ["Price"] = 600000,
        ["PurchaseID"] = 3276346557,
        ["LayoutOrder"] = 1,
        ["PetEggDescription"] = "It\'s a dog"
    },
    ["Legendary Egg"] = {
        ["EggName"] = "Legendary Egg",
        ["EggRarity"] = "Legendary",
        ["StockChance"] = 50,
        ["Price"] = 3000000,
        ["PurchaseID"] = 3276346676,
        ["LayoutOrder"] = 1,
        ["PetEggDescription"] = "It\'s a dog"
    },
    ["Mythical Egg"] = {
        ["EggName"] = "Mythical Egg",
        ["EggRarity"] = "Mythical",
        ["StockChance"] = 70,
        ["Price"] = 8000000,
        ["PurchaseID"] = 3286560171,
        ["LayoutOrder"] = 1,
        ["PetEggDescription"] = "It\'s a dog"
    },
    ["Bug Egg"] = {
        ["EggName"] = "Bug Egg",
        ["EggRarity"] = "Divine",
        ["StockChance"] = 100,
        ["Price"] = 50000000,
        ["PurchaseID"] = 3277000452,
        ["LayoutOrder"] = 1,
        ["PetEggDescription"] = "It\'s a dog"
    }
}
return v2