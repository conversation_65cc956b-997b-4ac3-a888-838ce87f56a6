-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\Chalk-ModuleScript.lua
local u1 = {
    ["FONT_COLOR_RGB"] = {
        ["Start"] = "<font color=\"rgb(%s,%s,%s)\">",
        ["End"] = "</font>"
    },
    ["FONT_COLOR_HEX"] = {
        ["Start"] = "<font color=\"#%s\">",
        ["End"] = "</font>"
    },
    ["FONT_SIZE"] = {
        ["Start"] = "<font size=\"%d\">",
        ["End"] = "</font>"
    },
    ["FONT_FACE"] = {
        ["Start"] = "<font face=\"%s\">",
        ["End"] = "</font>"
    },
    ["FONT_FAMILY"] = {
        ["Start"] = "<font family=\"%s\">",
        ["End"] = "</font>"
    },
    ["FONT_WEIGHT"] = {
        ["Start"] = "<font weight=\"%s\">",
        ["End"] = "</font>"
    },
    ["FONT_TRANSPARENCY"] = {
        ["Start"] = "<font transparency=\"%s\">",
        ["End"] = "</font>"
    },
    ["STROKE"] = {
        ["Start"] = "<stroke color=\"#%s\" joins=\"%s\" thickness=\"%s\" transparency=\"%s\">",
        ["End"] = "</stroke>"
    },
    ["BOLD"] = {
        ["Start"] = "<b>",
        ["End"] = "</b>"
    },
    ["ITALIC"] = {
        ["Start"] = "<i>",
        ["End"] = "</i>"
    },
    ["UNDERLINE"] = {
        ["Start"] = "<u>",
        ["End"] = "</u>"
    },
    ["STRIKETHROUGH"] = {
        ["Start"] = "<s>",
        ["End"] = "</s>"
    },
    ["UPPERCASE"] = {
        ["Start"] = "<uppercase>",
        ["End"] = "</uppercase>"
    },
    ["SMALLCAPS"] = {
        ["Start"] = "<smallcaps>",
        ["End"] = "</smallcaps>"
    }
}
local u2 = {}
local u3 = {}
u3.__index = u3
local u4 = {}
local u5 = "FONT_SIZE"
function u4.size(p6) --[[Anonymous function at line 93]]
    --[[
    Upvalues:
        [1] = u1
        [2] = u5
    --]]
    local u7 = u1[u5].Start:format(p6)
    return function(p8) --[[Anonymous function at line 96]]
        --[[
        Upvalues:
            [1] = u7
            [2] = u1
            [3] = u5
        --]]
        return ("%*%*%*"):format(u7, p8, u1[u5].End)
    end
end
local u9 = "FONT_FACE"
function u4.face(p10) --[[Anonymous function at line 93]]
    --[[
    Upvalues:
        [1] = u1
        [2] = u9
    --]]
    local u11 = u1[u9].Start:format(p10)
    return function(p12) --[[Anonymous function at line 96]]
        --[[
        Upvalues:
            [1] = u11
            [2] = u1
            [3] = u9
        --]]
        return ("%*%*%*"):format(u11, p12, u1[u9].End)
    end
end
local u13 = "FONT_FAMILY"
function u4.family(p14) --[[Anonymous function at line 93]]
    --[[
    Upvalues:
        [1] = u1
        [2] = u13
    --]]
    local u15 = u1[u13].Start:format(p14)
    return function(p16) --[[Anonymous function at line 96]]
        --[[
        Upvalues:
            [1] = u15
            [2] = u1
            [3] = u13
        --]]
        return ("%*%*%*"):format(u15, p16, u1[u13].End)
    end
end
local u17 = "FONT_WEIGHT"
function u4.weight(p18) --[[Anonymous function at line 93]]
    --[[
    Upvalues:
        [1] = u1
        [2] = u17
    --]]
    local u19 = u1[u17].Start:format(p18)
    return function(p20) --[[Anonymous function at line 96]]
        --[[
        Upvalues:
            [1] = u19
            [2] = u1
            [3] = u17
        --]]
        return ("%*%*%*"):format(u19, p20, u1[u17].End)
    end
end
local u21 = "FONT_TRANSPARENCY"
function u4.transparency(p22) --[[Anonymous function at line 93]]
    --[[
    Upvalues:
        [1] = u1
        [2] = u21
    --]]
    local u23 = u1[u21].Start:format(p22)
    return function(p24) --[[Anonymous function at line 96]]
        --[[
        Upvalues:
            [1] = u23
            [2] = u1
            [3] = u21
        --]]
        return ("%*%*%*"):format(u23, p24, u1[u21].End)
    end
end
function u4.stroke(p25) --[[Anonymous function at line 135]]
    --[[
    Upvalues:
        [1] = u1
    --]]
    local v26 = ("#%*"):format((p25.Color:ToHex()))
    local v27 = p25.Joins or "Round"
    local v28 = p25.Thickness or 1
    local v29 = p25.Transparency or 0
    local u30 = u1.STROKE.Start:format(v26, v27, v28, v29)
    return function(p31) --[[Anonymous function at line 145]]
        --[[
        Upvalues:
            [1] = u30
            [2] = u1
        --]]
        return ("%*%*%*"):format(u30, p31, u1.STROKE.End)
    end
end
function u4.color(...) --[[Anonymous function at line 149]]
    --[[
    Upvalues:
        [1] = u1
    --]]
    local v32 = { ... }
    local v33 = v32[1]
    local v34
    if typeof(v33) == "string" then
        local v35 = v33:gsub("#", "")
        v34 = string.match(v35, "^%x%x%x%x%x%x$")
    else
        v34 = nil
    end
    local v36 = v32[1]
    local v37 = typeof(v36) == "Color3"
    local v38 = v32[1]
    local v39 = v37 and v38 and v38 or Color3.fromRGB(v38, v32[2], v32[3])
    local u40 = v34 and u1.FONT_COLOR_HEX.Start:format(v38:gsub("#", ""))
    if not u40 then
        local v41 = u1.FONT_COLOR_RGB.Start
        local v42 = v39.R * 255
        local v43 = math.floor(v42)
        local v44 = v39.G * 255
        local v45 = math.floor(v44)
        local v46 = v39.B * 255
        u40 = v41:format(v43, v45, (math.floor(v46)))
    end
    return function(p47) --[[Anonymous function at line 163]]
        --[[
        Upvalues:
            [1] = u40
            [2] = u1
        --]]
        return ("%*%*%*"):format(u40, p47, u1.FONT_COLOR_RGB.End)
    end
end
local u48 = "BOLD"
function u2.bold(p49) --[[Anonymous function at line 103]]
    --[[
    Upvalues:
        [1] = u1
        [2] = u48
    --]]
    local v50 = u1[u48]
    return ("%*%*%*"):format(v50.Start, p49, v50.End)
end
local u51 = "ITALIC"
function u2.italic(p52) --[[Anonymous function at line 103]]
    --[[
    Upvalues:
        [1] = u1
        [2] = u51
    --]]
    local v53 = u1[u51]
    return ("%*%*%*"):format(v53.Start, p52, v53.End)
end
local u54 = "UNDERLINE"
function u2.underline(p55) --[[Anonymous function at line 103]]
    --[[
    Upvalues:
        [1] = u1
        [2] = u54
    --]]
    local v56 = u1[u54]
    return ("%*%*%*"):format(v56.Start, p55, v56.End)
end
local u57 = "STRIKETHROUGH"
function u2.strikethrough(p58) --[[Anonymous function at line 103]]
    --[[
    Upvalues:
        [1] = u1
        [2] = u57
    --]]
    local v59 = u1[u57]
    return ("%*%*%*"):format(v59.Start, p58, v59.End)
end
local u60 = "UPPERCASE"
function u2.uppercase(p61) --[[Anonymous function at line 103]]
    --[[
    Upvalues:
        [1] = u1
        [2] = u60
    --]]
    local v62 = u1[u60]
    return ("%*%*%*"):format(v62.Start, p61, v62.End)
end
local u63 = "SMALLCAPS"
function u2.smallcaps(p64) --[[Anonymous function at line 103]]
    --[[
    Upvalues:
        [1] = u1
        [2] = u63
    --]]
    local v65 = u1[u63]
    return ("%*%*%*"):format(v65.Start, p64, v65.End)
end
local u66 = Color3.fromRGB(255, 255, 255)
function u2.white(p67) --[[Anonymous function at line 110]]
    --[[
    Upvalues:
        [1] = u66
        [2] = u1
    --]]
    local v68 = u66.r * 255
    local v69 = math.floor(v68)
    local v70 = u66.g * 255
    local v71 = math.floor(v70)
    local v72 = u66.b * 255
    local v73 = math.floor(v72)
    return ("%*%*%*"):format(u1.FONT_COLOR_RGB.Start:format(v69, v71, v73), p67, u1.FONT_COLOR_RGB.End)
end
local u74 = Color3.fromRGB(0, 0, 0)
function u2.black(p75) --[[Anonymous function at line 110]]
    --[[
    Upvalues:
        [1] = u74
        [2] = u1
    --]]
    local v76 = u74.r * 255
    local v77 = math.floor(v76)
    local v78 = u74.g * 255
    local v79 = math.floor(v78)
    local v80 = u74.b * 255
    local v81 = math.floor(v80)
    return ("%*%*%*"):format(u1.FONT_COLOR_RGB.Start:format(v77, v79, v81), p75, u1.FONT_COLOR_RGB.End)
end
local u82 = Color3.fromRGB(255, 0, 0)
function u2.red(p83) --[[Anonymous function at line 110]]
    --[[
    Upvalues:
        [1] = u82
        [2] = u1
    --]]
    local v84 = u82.r * 255
    local v85 = math.floor(v84)
    local v86 = u82.g * 255
    local v87 = math.floor(v86)
    local v88 = u82.b * 255
    local v89 = math.floor(v88)
    return ("%*%*%*"):format(u1.FONT_COLOR_RGB.Start:format(v85, v87, v89), p83, u1.FONT_COLOR_RGB.End)
end
local u90 = Color3.fromRGB(153, 51, 0)
function u2.brown(p91) --[[Anonymous function at line 110]]
    --[[
    Upvalues:
        [1] = u90
        [2] = u1
    --]]
    local v92 = u90.r * 255
    local v93 = math.floor(v92)
    local v94 = u90.g * 255
    local v95 = math.floor(v94)
    local v96 = u90.b * 255
    local v97 = math.floor(v96)
    return ("%*%*%*"):format(u1.FONT_COLOR_RGB.Start:format(v93, v95, v97), p91, u1.FONT_COLOR_RGB.End)
end
local u98 = Color3.fromRGB(255, 153, 0)
function u2.orange(p99) --[[Anonymous function at line 110]]
    --[[
    Upvalues:
        [1] = u98
        [2] = u1
    --]]
    local v100 = u98.r * 255
    local v101 = math.floor(v100)
    local v102 = u98.g * 255
    local v103 = math.floor(v102)
    local v104 = u98.b * 255
    local v105 = math.floor(v104)
    return ("%*%*%*"):format(u1.FONT_COLOR_RGB.Start:format(v101, v103, v105), p99, u1.FONT_COLOR_RGB.End)
end
local u106 = Color3.fromRGB(255, 255, 0)
function u2.yellow(p107) --[[Anonymous function at line 110]]
    --[[
    Upvalues:
        [1] = u106
        [2] = u1
    --]]
    local v108 = u106.r * 255
    local v109 = math.floor(v108)
    local v110 = u106.g * 255
    local v111 = math.floor(v110)
    local v112 = u106.b * 255
    local v113 = math.floor(v112)
    return ("%*%*%*"):format(u1.FONT_COLOR_RGB.Start:format(v109, v111, v113), p107, u1.FONT_COLOR_RGB.End)
end
local u114 = Color3.fromRGB(153, 255, 0)
function u2.lime(p115) --[[Anonymous function at line 110]]
    --[[
    Upvalues:
        [1] = u114
        [2] = u1
    --]]
    local v116 = u114.r * 255
    local v117 = math.floor(v116)
    local v118 = u114.g * 255
    local v119 = math.floor(v118)
    local v120 = u114.b * 255
    local v121 = math.floor(v120)
    return ("%*%*%*"):format(u1.FONT_COLOR_RGB.Start:format(v117, v119, v121), p115, u1.FONT_COLOR_RGB.End)
end
local u122 = Color3.fromRGB(0, 255, 0)
function u2.green(p123) --[[Anonymous function at line 110]]
    --[[
    Upvalues:
        [1] = u122
        [2] = u1
    --]]
    local v124 = u122.r * 255
    local v125 = math.floor(v124)
    local v126 = u122.g * 255
    local v127 = math.floor(v126)
    local v128 = u122.b * 255
    local v129 = math.floor(v128)
    return ("%*%*%*"):format(u1.FONT_COLOR_RGB.Start:format(v125, v127, v129), p123, u1.FONT_COLOR_RGB.End)
end
local u130 = Color3.fromRGB(0, 0, 255)
function u2.blue(p131) --[[Anonymous function at line 110]]
    --[[
    Upvalues:
        [1] = u130
        [2] = u1
    --]]
    local v132 = u130.r * 255
    local v133 = math.floor(v132)
    local v134 = u130.g * 255
    local v135 = math.floor(v134)
    local v136 = u130.b * 255
    local v137 = math.floor(v136)
    return ("%*%*%*"):format(u1.FONT_COLOR_RGB.Start:format(v133, v135, v137), p131, u1.FONT_COLOR_RGB.End)
end
local u138 = Color3.fromRGB(102, 0, 153)
function u2.purple(p139) --[[Anonymous function at line 110]]
    --[[
    Upvalues:
        [1] = u138
        [2] = u1
    --]]
    local v140 = u138.r * 255
    local v141 = math.floor(v140)
    local v142 = u138.g * 255
    local v143 = math.floor(v142)
    local v144 = u138.b * 255
    local v145 = math.floor(v144)
    return ("%*%*%*"):format(u1.FONT_COLOR_RGB.Start:format(v141, v143, v145), p139, u1.FONT_COLOR_RGB.End)
end
local u146 = Color3.fromRGB(255, 0, 255)
function u2.pink(p147) --[[Anonymous function at line 110]]
    --[[
    Upvalues:
        [1] = u146
        [2] = u1
    --]]
    local v148 = u146.r * 255
    local v149 = math.floor(v148)
    local v150 = u146.g * 255
    local v151 = math.floor(v150)
    local v152 = u146.b * 255
    local v153 = math.floor(v152)
    return ("%*%*%*"):format(u1.FONT_COLOR_RGB.Start:format(v149, v151, v153), p147, u1.FONT_COLOR_RGB.End)
end
function u3.new(p154) --[[Anonymous function at line 188]]
    --[[
    Upvalues:
        [1] = u4
        [2] = u2
    --]]
    local v155 = {}
    local v156 = {}
    local u157 = {}
    v155.Formatters = u157
    local u158 = nil
    local u159 = nil
    function v156.__call(_, ...) --[[Anonymous function at line 196]]
        --[[
        Upvalues:
            [1] = u158
            [2] = u157
        --]]
        local v160 = { ... }
        if u158 then
            local v161 = u158
            u158 = nil
            return v161(unpack(v160))
        else
            local v162 = {}
            for _, v165 in v160 do
                for _, v164 in u157 do
                    local v165 = v164(v165)
                end
                table.insert(v162, v165)
            end
            return unpack(v162)
        end
    end
    function v156.__index(p166, p167) --[[Anonymous function at line 220]]
        --[[
        Upvalues:
            [1] = u4
            [2] = u158
            [3] = u157
            [4] = u159
            [5] = u2
        --]]
        local u168 = u4[p167]
        if u168 then
            u158 = function(...) --[[Anonymous function at line 224]]
                --[[
                Upvalues:
                    [1] = u168
                    [2] = u157
                    [3] = u159
                --]]
                local v169 = u168(...)
                local v170 = u157
                table.insert(v170, v169)
                return u159
            end
            return u159
        end
        local v171 = u2[p167]
        local v172 = u157
        table.insert(v172, v171)
        return p166
    end
    v156:__index(p154)
    u159 = setmetatable(v155, v156)
    return u159
end
return setmetatable({}, {
    ["__index"] = function(_, p173) --[[Function name: __index, line 249]]
        --[[
        Upvalues:
            [1] = u3
        --]]
        return u3.new(p173)
    end,
    ["__newindex"] = function() --[[Function name: __newindex, line 252]]
        return error("Chalk is READONLY")
    end
})