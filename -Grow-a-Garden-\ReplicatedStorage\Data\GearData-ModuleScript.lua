-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Data\GearData-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
local v2 = require(v1.Modules.Chalk)
local v3 = {
    ["Watering Can"] = {
        ["GearName"] = "Watering Can",
        ["GearRarity"] = "Common",
        ["StockChance"] = 1,
        ["StockAmount"] = { 1, 3 },
        ["Price"] = 50000,
        ["PurchaseID"] = 3260229242,
        ["GiftPurchaseID"] = 3269349334,
        ["Stack"] = 10,
        ["DisplayInShop"] = true,
        ["LayoutOrder"] = 1,
        ["Asset"] = "rbxassetid://140223014467344",
        ["GearDescription"] = "Speeds up plant growth. 10 uses"
    },
    ["Trowel"] = {
        ["GearName"] = "Trowel",
        ["GearRarity"] = "Uncommon",
        ["StockChance"] = 1,
        ["StockAmount"] = { 1, 3 },
        ["Price"] = 100000,
        ["PurchaseID"] = 3265946561,
        ["Stack"] = 5,
        ["DisplayInShop"] = true,
        ["LayoutOrder"] = 2,
        ["Asset"] = "rbxassetid://115261280019001",
        ["GearDescription"] = "Moves plants. 5 uses"
    },
    ["Recall Wrench"] = {
        ["GearName"] = "Recall Wrench",
        ["GearRarity"] = "Uncommon",
        ["StockChance"] = 1,
        ["StockAmount"] = { 1, 3 },
        ["Price"] = 150000,
        ["PurchaseID"] = 3282918403,
        ["GiftPurchaseID"] = 3282918955,
        ["Stack"] = 5,
        ["DisplayInShop"] = true,
        ["LayoutOrder"] = 3,
        ["Asset"] = "rbxassetid://98327818593168",
        ["GearDescription"] = "Teleports to Gear Shop. 5 uses"
    },
    ["Basic Sprinkler"] = {
        ["GearName"] = "Basic Sprinkler",
        ["GearRarity"] = "Rare",
        ["StockChance"] = 3,
        ["StockAmount"] = { 1, 3 },
        ["Price"] = 25000,
        ["PurchaseID"] = 3265889601,
        ["GiftPurchaseID"] = 3269349445,
        ["Stack"] = 1,
        ["DisplayInShop"] = true,
        ["LayoutOrder"] = 4,
        ["Asset"] = "rbxassetid://120314005864914",
        ["GearDescription"] = "Increases growth speed and fruit size. Lasts 5 minutes"
    },
    ["Advanced Sprinkler"] = {
        ["GearName"] = "Advanced Sprinkler",
        ["GearRarity"] = "Legendary",
        ["StockChance"] = 7,
        ["StockAmount"] = { 1, 2 },
        ["Price"] = 50000,
        ["PurchaseID"] = 3265889751,
        ["GiftPurchaseID"] = 3269349768,
        ["Stack"] = 1,
        ["DisplayInShop"] = true,
        ["LayoutOrder"] = 5,
        ["Asset"] = "rbxassetid://90193997839883",
        ["GearDescription"] = "Increases growth speed and mutation chances. Lasts 5 minutes"
    },
    ["Godly Sprinkler"] = {
        ["GearName"] = "Godly Sprinkler",
        ["GearRarity"] = "Mythical",
        ["StockChance"] = 11,
        ["StockAmount"] = { 1, 1 },
        ["Price"] = 120000,
        ["PurchaseID"] = 3265889948,
        ["GiftPurchaseID"] = 3269349908,
        ["Stack"] = 1,
        ["DisplayInShop"] = true,
        ["LayoutOrder"] = 6,
        ["Asset"] = "rbxassetid://75442797836082",
        ["GearDescription"] = "Increases growth speed, mutation chances and fruit size. Lasts 5 minutes"
    },
    ["Lightning Rod"] = {
        ["GearName"] = "Lightning Rod",
        ["GearRarity"] = "Mythical",
        ["StockChance"] = 50,
        ["StockAmount"] = { 1, 1 },
        ["Price"] = 1000000,
        ["PurchaseID"] = 3265946758,
        ["GiftPurchaseID"] = 3274108730,
        ["Stack"] = 1,
        ["DisplayInShop"] = true,
        ["LayoutOrder"] = 7,
        ["Asset"] = "rbxassetid://125433095334047",
        ["GearDescription"] = "Redirects lightning 3 times before being destroyed"
    },
    ["Master Sprinkler"] = {
        ["GearName"] = "Master Sprinkler",
        ["GearRarity"] = "Divine",
        ["StockChance"] = 100,
        ["StockAmount"] = { 1, 1 },
        ["Price"] = 10000000,
        ["PurchaseID"] = 3267580365,
        ["GiftPurchaseID"] = 3269350167,
        ["Stack"] = 1,
        ["DisplayInShop"] = true,
        ["LayoutOrder"] = 8,
        ["Asset"] = "rbxassetid://98504519094449",
        ["GearDescription"] = v2.yellow("Greatly increases growth speed, mutation chances and fruit size. Lasts 10 minutes")
    },
    ["Favorite Tool"] = {
        ["GearName"] = "Favorite Tool",
        ["GearRarity"] = "Divine",
        ["StockChance"] = 1,
        ["StockAmount"] = { 1, 3 },
        ["Price"] = 20000000,
        ["PurchaseID"] = 3281679093,
        ["Stack"] = 20,
        ["GiftPurchaseID"] = 3281679185,
        ["DisplayInShop"] = true,
        ["LayoutOrder"] = 9,
        ["Asset"] = "rbxassetid://129676033321965",
        ["GearDescription"] = v2.orange("Favorites your fruit to prevent collecting.") .. v2.orange(" 20 uses")
    },
    ["Harvest Tool"] = {
        ["GearName"] = "Harvest Tool",
        ["GearRarity"] = "Divine",
        ["StockChance"] = 1,
        ["StockAmount"] = { 1, 3 },
        ["Price"] = 30000000,
        ["PurchaseID"] = 3286038236,
        ["Stack"] = 5,
        ["GiftPurchaseID"] = 3286038078,
        ["LayoutOrder"] = 10,
        ["DisplayInShop"] = true,
        ["Asset"] = "rbxassetid://99521037861537",
        ["GearDescription"] = v2.orange("Harvest all fruit from a chosen plant. 5 uses")
    }
}
return v3