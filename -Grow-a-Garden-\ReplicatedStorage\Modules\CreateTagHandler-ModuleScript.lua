-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\CreateTagHandler-ModuleScript.lua
local u1 = game:GetService("CollectionService")
return function(p2) --[[Function name: <PERSON>reate<PERSON><PERSON><PERSON><PERSON><PERSON>, line 3]]
    --[[
    Upvalues:
        [1] = u1
    --]]
    local v3 = p2.Tag
    local v4 = p2.OnInstanceAdded or function() --[[Anonymous function at line 5]] end
    local v5 = p2.OnInstanceRemoved or function() --[[Anonymous function at line 6]] end
    for _, v6 in u1:GetTagged(v3) do
        v4(v6)
    end
    u1:GetInstanceAddedSignal(v3):Connect(v4)
    u1:GetInstanceRemovedSignal(v3):Connect(v5)
end