-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\TweenModel-ModuleScript.lua
local u1 = game:GetService("TweenService")
local v2 = game:GetService("RunService")
local u3 = v2:IsServer() and v2.Heartbeat or v2.RenderStepped
return function(u4, p5, u6) --[[Function name: TweenModel, line 8]]
    --[[
    Upvalues:
        [1] = u1
        [2] = u3
    --]]
    local u7 = Instance.new("NumberValue")
    u7.Value = u4:GetScale()
    local u8 = Instance.new("CFrameValue")
    u8.Value = u4:GetPivot()
    local u9 = u1:Create(u7, p5, {
        ["Value"] = u6.Scale
    })
    local u10 = u1:Create(u8, p5, {
        ["Value"] = u6.CFrame
    })
    local u11 = nil
    local u12 = nil
    u12 = {
        ["Instance"] = u4,
        ["Play"] = function() --[[Anonymous function at line 35]]
            --[[
            Upvalues:
                [1] = u9
                [2] = u10
                [3] = u11
                [4] = u3
                [5] = u12
                [6] = u6
                [7] = u4
                [8] = u7
                [9] = u8
            --]]
            u9:Play()
            u10:Play()
            u11 = u3:Connect(function() --[[Anonymous function at line 39]]
                --[[
                Upvalues:
                    [1] = u12
                    [2] = u9
                    [3] = u11
                    [4] = u6
                    [5] = u4
                    [6] = u7
                    [7] = u8
                --]]
                u12.PlaybackState = u9.PlaybackState
                if u9.PlaybackState ~= Enum.PlaybackState.Playing then
                    return u11:Disconnect()
                end
                if u6.Scale then
                    local v13 = u4
                    local v14 = u7.Value
                    v13:ScaleTo((math.clamp(v14, 1e-9, (1 / 0))))
                end
                if u6.CFrame then
                    u4:PivotTo(u8.Value)
                end
            end)
        end,
        ["Pause"] = function() --[[Anonymous function at line 52]]
            --[[
            Upvalues:
                [1] = u9
                [2] = u10
            --]]
            u9:Pause()
            u10:Pause()
        end,
        ["Destroy"] = function() --[[Anonymous function at line 56]]
            --[[
            Upvalues:
                [1] = u9
                [2] = u10
            --]]
            u9:Destroy()
            u10:Destroy()
        end,
        ["Cancel"] = function() --[[Anonymous function at line 60]]
            --[[
            Upvalues:
                [1] = u9
                [2] = u10
            --]]
            u9:Cancel()
            u10:Cancel()
        end,
        ["PlaybackState"] = Enum.PlaybackState.Begin,
        ["TweenInfo"] = p5,
        ["Completed"] = u9.Completed
    }
    return u12
end