-- Full Path: -Grow-a-Garden-\StarterPlayer\StarterPlayerScripts\MainLocal-LocalScript.lua
local v1 = game:GetService("ReplicatedStorage")
local v2 = game:GetService("Players")
for _, v3 in v1:WaitForChild("ReplicateUI"):GetChildren() do
    v3.Parent = v2.LocalPlayer.PlayerGui
end
for _, u4 in pairs(v1.Modules:GetDescendants()) do
    if u4:IsA("ModuleScript") then
        task.spawn(function() --[[Anonymous function at line 11]]
            --[[
            Upvalues:
                [1] = u4
            --]]
            debug.setmemorycategory(u4.Name)
            local v5, v6 = pcall(function() --[[Anonymous function at line 14]]
                --[[
                Upvalues:
                    [1] = u4
                --]]
                require(u4)
            end)
            if not v5 then
                warn(u4.Name, v6)
            end
        end)
    end
end