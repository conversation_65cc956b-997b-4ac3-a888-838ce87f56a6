-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\TutorialController-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
local u2 = game:GetService("Players").LocalPlayer
local v3 = {}
local u4 = require(v1.Modules.ABTestController)
local u5 = require(v1.Modules.Remotes)
function v3.Start(_) --[[Anonymous function at line 12]]
    --[[
    Upvalues:
        [1] = u5
        [2] = u4
        [3] = u2
    --]]
    local u6 = nil
    u5.Tutorial.Start.listen(function(p7, _) --[[Anonymous function at line 14]]
        --[[
        Upvalues:
            [1] = u6
            [2] = u4
            [3] = u2
        --]]
        local v8 = u6
        local v9
        if type(v8) == "function" then
            u6()
            v9 = 10
        else
            v9 = 10
        end
        while not u4:IsLoaded() and v9 > 0 do
            v9 = v9 - task.wait(1)
        end
        local v10 = p7 or u2:GetAttribute("AB_TutorialVariant") or "Variant1"
        local v11 = script.Variants:FindFirstChild(v10) or script.Variants:FindFirstChild("Variant1")
        if v11 then
            u6 = require(v11)()
        else
            warn("No tutorial variant found to start")
        end
    end)
end
task.spawn(v3.Start, v3)
return v3