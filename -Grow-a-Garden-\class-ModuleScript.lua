-- Full Path: -Grow-a-Garden-\\class-ModuleScript.lua
local u1 = {
    "Set",
    "Get",
    "Private",
    "ReadOnly",
    "WriteOnly",
    "Constructor",
    "GlobalGetFunction",
    "GlobalSetFunction"
}
local function u7(p2) --[[Anonymous function at line 5]]
    --[[
    Upvalues:
        [1] = u7
    --]]
    local v3 = {}
    for v4, v6 in p2 do
        if type(v6) == "table" then
            local v6 = u7(v6) or v6
        end
        v3[v4] = v6
    end
    return v3
end
return function(u8) --[[Function name: class, line 13]]
    --[[
    Upvalues:
        [1] = u7
        [2] = u1
    --]]
    local u9 = {}
    u9.__index = u9
    u9.__customclass = true
    local u10 = u7(u8.Set or {})
    local u11 = u7(u8.Get or {})
    local u12 = u7(u8.ReadOnly or {})
    local u13 = u7(u8.WriteOnly or {})
    local u14 = u7(u8.Private or {})
    local u15 = u8.GlobalGetFunction
    local u16 = u8.GlobalSetFunction
    local u17 = u8.Constructor
    table.freeze(u10)
    table.freeze(u11)
    table.freeze(u12)
    table.freeze(u13)
    table.freeze(u14)
    table.freeze(u8)
    function u9.new(...) --[[Anonymous function at line 35]]
        --[[
        Upvalues:
            [1] = u8
            [2] = u7
            [3] = u1
            [4] = u14
            [5] = u13
            [6] = u11
            [7] = u15
            [8] = u9
            [9] = u12
            [10] = u10
            [11] = u16
            [12] = u17
        --]]
        local u18 = {}
        local u19 = {}
        u8 = u7(u8)
        for v20, v21 in u8 do
            if not table.find(u1, v20) then
                u18[v20] = v21
            end
        end
        local v37 = {
            ["__index"] = function(p22, p23) --[[Function name: __index, line 50]]
                --[[
                Upvalues:
                    [1] = u14
                    [2] = u19
                    [3] = u13
                    [4] = u11
                    [5] = u15
                    [6] = u9
                    [7] = u18
                --]]
                local v24 = debug.info(2, "f")
                local v25 = u14[p23]
                if not u19[v24] and v25 then
                    return error((("Cannot access private function %* from %*"):format(v24, (debug.info(2, "f")))))
                end
                if u13[p23] then
                    return error((("Cannot read from write only property %*"):format(p23)))
                end
                local v26 = u11[p23]
                if u15 then
                    u15(p22, p23)
                end
                local v27 = u9
                local v28 = rawget(v27, p23) or v26 and v26(p22)
                if not v28 then
                    local v29 = u18
                    v28 = rawget(v29, p23)
                end
                return v28
            end,
            ["__newindex"] = function(p30, p31, p32) --[[Function name: __newindex, line 73]]
                --[[
                Upvalues:
                    [1] = u14
                    [2] = u19
                    [3] = u12
                    [4] = u10
                    [5] = u18
                    [6] = u16
                --]]
                local v33 = debug.info(2, "f")
                local v34 = u14[p31]
                if not u19[v33] and v34 then
                    return error((("Cannot access private function %* from %*"):format(v33, (debug.info(2, "f")))))
                end
                if u12[p31] then
                    return error((("Cannot write to read only property %*"):format(p31)))
                end
                local v35 = u10[p31]
                if v35 then
                    v35(p30, p31, p32)
                else
                    local v36 = u18
                    rawset(v36, p31, p32)
                end
                if u16 then
                    u16(p30, p31, p32)
                end
            end
        }
        local v38 = setmetatable({
            ["RealData"] = u18
        }, v37)
        for _, v39 in u9 do
            if type(v39) == "function" then
                u19[v39] = true
            end
        end
        u19[debug.info(1, "f")] = true
        if u17 then
            u17(v38, ...)
        end
        return v38
    end
    return u9
end