-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\DataService-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
v1:WaitFor<PERSON>hild("Modules")
local v2 = require(v1.Modules.ReplicationClass)
require(script:<PERSON><PERSON><PERSON><PERSON>hil<PERSON>("class"))
local v3 = v1:<PERSON><PERSON><PERSON><PERSON>hil<PERSON>("Data")
require(v3:<PERSON><PERSON><PERSON><PERSON>hild("DefaultData"))
local v4 = game:GetService("Players").LocalPlayer
local v5 = {
    ["Receiver"] = v2.new((("%*_DataServiceProfile"):format(v4.Name)))
}
v5.Receiver:YieldUntilData()
function v5.GetPathSignal(p6, p7) --[[Anonymous function at line 33]]
    local v8 = p6.Receiver
    if v8 then
        v8:YieldUntilData(25)
        return v8:GetPathSignal(p7)
    end
end
function v5.GetData(p9) --[[Anonymous function at line 39]]
    local v10 = p9.Receiver
    if not v10 then
        return {}
    end
    v10:YieldUntilData()
    local v11 = v10:YieldUntilData(10)
    return not v11 and {} or v11.Table
end
return v5