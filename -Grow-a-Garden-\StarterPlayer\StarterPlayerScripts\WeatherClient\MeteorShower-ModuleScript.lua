-- Full Path: -Grow-a-Garden-\StarterPlayer\StarterPlayerScripts\WeatherClient\MeteorShower-ModuleScript.lua
local v1 = {}
local _ = game.ReplicatedStorage.RainParticle
Random.new()
local _ = workspace.CurrentCamera
local v2 = RaycastParams.new()
v2.FilterDescendantsInstances = { workspace.Terrain, workspace }
v2.FilterType = Enum.RaycastFilterType.Include
local u3 = false
local v4 = script.Sky
require(game.ReplicatedStorage.Modules.SkyboxManager).AddSkybox(v4)
local v5 = game.Lighting.ColorCorrection:Clone()
v5.Name = script.Name
v5.Parent = game.Lighting
local function v6() --[[Anonymous function at line 47]]
    --[[
    Upvalues:
        [1] = u3
    --]]
    u3 = true
    task.spawn(function() --[[Anonymous function at line 54]] end)
end
workspace:GetAttributeChangedSignal("MeteorShower"):Connect(function() --[[Anonymous function at line 62]]
    --[[
    Upvalues:
        [1] = u3
    --]]
    if workspace:GetAttribute("MeteorShower") then
        u3 = true
        task.spawn(function() --[[Anonymous function at line 54]] end)
    else
        u3 = false
    end
end)
game.ReplicatedStorage.RainSplash:Clone().Parent = workspace.WeatherVisuals
local v7 = game:GetService("ReplicatedStorage"):WaitForChild("GameEvents")
if workspace:GetAttribute("MeteorShower") then
    task.defer(v6)
else
    u3 = false
end
local u9 = require(game.ReplicatedStorage.Code.CameraShaker).new(Enum.RenderPriority.Camera.Value, function(p8) --[[Anonymous function at line 86]]
    workspace.CurrentCamera.CFrame = workspace.CurrentCamera.CFrame * p8
end)
local function u16(p10, p11) --[[Anonymous function at line 90]]
    local v12 = Instance.new("Part")
    v12.Anchored = true
    v12.CanCollide = false
    v12.Transparency = 1
    v12.Size = Vector3.new(1, 1, 1)
    v12.CFrame = CFrame.new(p10 + Vector3.new(0, 4, 0))
    v12.Parent = workspace.WeatherVisuals
    local v13 = Instance.new("Attachment", v12)
    local v14 = Instance.new("Attachment")
    v14.Name = "MeteorTracker"
    v14.Parent = p11
    local v15 = Instance.new("Beam")
    v15.Attachment0 = v13
    v15.Attachment1 = v14
    v15.Width0 = 0.5
    v15.Width1 = 0.1
    v15.LightEmission = 1
    v15.TextureSpeed = 5
    v15.Color = ColorSequence.new(Color3.fromRGB(100, 200, 255))
    v15.Transparency = NumberSequence.new(0.2)
    v15.FaceCamera = true
    v15.Parent = v12
    return v12
end
local function u40(p17, p18) --[[Anonymous function at line 121]]
    --[[
    Upvalues:
        [1] = u16
        [2] = u9
    --]]
    local v19 = math.random(0, 360)
    local v20 = math.rad(v19)
    local v21 = math.cos(v20)
    local v22 = math.sin(v20)
    local v23 = Vector3.new(v21, 0, v22)
    local v24 = math.random(300, 500)
    local v25 = math.random(250, 350)
    local v26 = p17 + (v23 * v24 + Vector3.new(0, v25, 0))
    local v27 = p18 or p17
    local v28 = game.ReplicatedStorage.ShootingStar:Clone()
    v28.CFrame = CFrame.new(v26, p17)
    v28.Parent = workspace.WeatherVisuals
    local v29
    if p18 then
        v29 = u16(p18, v28)
    else
        v29 = nil
    end
    local v30 = (v27 - v26).Magnitude / 300
    local v31 = 0
    local v32 = false
    local v33 = false
    while v31 < v30 do
        local v34 = task.wait()
        v31 = v31 + v34
        if v30 - v31 < 3.5 and not v32 then
            v32 = true
            if Random.new():NextInteger(1, 2) == 1 then
                v28.StarFallSFX_02_GrowAGarden:Play()
            else
                v28.StarFallSFX_01_GrowAGarden:Play()
            end
        end
        local v35 = v28.Position
        if p18 and (not v33 and (v27 - v35).Magnitude <= 80) then
            p17 = v27
            v33 = true
        end
        local v36 = v35 + (p17 - v35).Unit * 300 * v34
        v28.CFrame = CFrame.new(v36, p17)
    end
    for _, v37 in v28:GetDescendants() do
        if v37:IsA("BillboardGui") then
            v37.Enabled = false
        end
    end
    u9:Shake(u9.Presets.Explosion)
    local v38 = game.ReplicatedStorage.StarHit:Clone()
    v38.CFrame = CFrame.new(v27)
    v38.Parent = workspace.WeatherVisuals
    for _, v39 in v38:GetDescendants() do
        if v39:IsA("ParticleEmitter") then
            v39:Emit(v39:GetAttribute("EmitCount"))
        elseif v39:IsA("Sound") then
            v39:Play()
        elseif v39:IsA("PointLight") then
            game.TweenService:Create(v39, TweenInfo.new(3), {
                ["Range"] = 0
            }):Play()
        end
    end
    game.Debris:AddItem(v28, 3)
    game.Debris:AddItem(v38, 3)
    if v29 then
        game.Debris:AddItem(v29, 1)
    end
end
v7.MeteorShower.OnClientEvent:Connect(function(p41, p42) --[[Anonymous function at line 216]]
    --[[
    Upvalues:
        [1] = u40
    --]]
    u40(p41, p42)
end)
return v1