-- Full Path: -Grow-a-Garden-\ReplicatedStorage\CmdrClient\Types\Player-ModuleScript.lua
local u1 = require(script.Parent.Parent.Shared.Util)
local u2 = game:GetService("Players")
local u8 = {
    ["Transform"] = function(p3) --[[Function name: Transform, line 5]]
        --[[
        Upvalues:
            [1] = u1
            [2] = u2
        --]]
        return u1.MakeFuzzyFinder(u2:GetPlayers())(p3)
    end,
    ["Validate"] = function(p4) --[[Function name: Validate, line 11]]
        return #p4 > 0, "No player with that name could be found."
    end,
    ["Autocomplete"] = function(p5) --[[Function name: Autocomplete, line 15]]
        --[[
        Upvalues:
            [1] = u1
        --]]
        return u1.GetNames(p5)
    end,
    ["Parse"] = function(p6) --[[Function name: Parse, line 19]]
        return p6[1]
    end,
    ["Default"] = function(p7) --[[Function name: Default, line 23]]
        return p7.Name
    end,
    ["ArgumentOperator<PERSON>liases"] = {
        ["me"] = ".",
        ["all"] = "*",
        ["others"] = "**",
        ["random"] = "?"
    }
}
return function(p9) --[[Anonymous function at line 35]]
    --[[
    Upvalues:
        [1] = u8
        [2] = u1
    --]]
    p9:RegisterType("player", u8)
    p9:RegisterType("players", u1.MakeListableType(u8, {
        ["Prefixes"] = "% teamPlayers"
    }))
end