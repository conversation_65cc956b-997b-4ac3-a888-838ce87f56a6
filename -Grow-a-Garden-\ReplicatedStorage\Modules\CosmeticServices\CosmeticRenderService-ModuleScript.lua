-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\CosmeticServices\CosmeticRenderService-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
local v2 = game:GetService("Players").LocalPlayer
local u3 = v2.Character or v2.CharacterAdded:Wait()
v2.CharacterAdded:Connect(function(p4) --[[Anonymous function at line 7]]
    --[[
    Upvalues:
        [1] = u3
    --]]
    u3 = p4
end)
require(v1.Modules.GetFarmAsync)
local u5 = require(v1.Data.CosmeticRegistry).CosmeticList
local u6 = require(v1.Modules.TweenModel)
local u7 = require(v1.Modules.GetFarmAncestor)
local u8 = require(v1.Modules.GetPlayerFromFarm)
require(v1.Modules.GetFarm)
local u9 = {}
local u10 = {}
local function u13(p11) --[[Anonymous function at line 58]]
    for _, v12 in p11:GetDescendants() do
        if v12:IsA("BasePart") then
            v12.CanCollide = false
        end
    end
end
require(v1.Modules.CreateTagHandler)({
    ["Tag"] = "CosmeticPart",
    ["OnInstanceAdded"] = function(u14) --[[Function name: OnInstanceAdded, line 67]]
        --[[
        Upvalues:
            [1] = u9
            [2] = u5
            [3] = u7
            [4] = u8
        --]]
        local v15 = u14:GetAttribute("CosmeticType")
        if not v15 then
            return warn(u14, "Does Not Have CosmeticType")
        end
        u9[u14] = {}
        local v16 = u9[u14]
        local v17 = u5[v15]
        if not v17 then
            return warn(u14, (("Cannot find Data for %*"):format(v15)))
        end
        local u18 = v17.Model:Clone()
        v16.Model = u18
        local v19 = u18:GetExtentsSize().Y / 2
        u18:PivotTo(u14.CFrame * CFrame.new(0, v19, 0))
        v16.Owner = u8((u7(u14)))
        v16.CFrameConnection = u14:GetPropertyChangedSignal("CFrame"):Connect(function() --[[Function name: UpdatePosition, line 79]]
            --[[
            Upvalues:
                [1] = u18
                [2] = u14
            --]]
            local v20 = u18:GetExtentsSize().Y / 2
            u18:PivotTo(u14.CFrame * CFrame.new(0, v20, 0))
        end)
        u18.Parent = u14
    end,
    ["OnInstanceRemoved"] = function(p21) --[[Function name: OnInstanceRemoved, line 121]]
        --[[
        Upvalues:
            [1] = u9
            [2] = u13
            [3] = u6
            [4] = u10
        --]]
        local u22 = u9[p21]
        if u22 then
            u9[p21] = nil
            local u23 = u22.Model
            if u23 then
                task.spawn(function() --[[Anonymous function at line 127]]
                    --[[
                    Upvalues:
                        [1] = u22
                        [2] = u23
                        [3] = u13
                        [4] = u6
                        [5] = u10
                    --]]
                    local v24 = u22.Owner
                    if v24 then
                        v24 = v24.Character
                    end
                    u23.PrimaryPart:RemoveTag("CosmeticHitBox")
                    u23.Parent = workspace.Camera
                    u13(u23)
                    local v25 = v24 and u6(u23, TweenInfo.new(0.4), {
                        ["Scale"] = 0,
                        ["CFrame"] = v24:GetPivot()
                    }) or u6(u23, TweenInfo.new(0.1), {
                        ["Scale"] = 0
                    })
                    v25.Completed:Once(function() --[[Anonymous function at line 135]]
                        --[[
                        Upvalues:
                            [1] = u10
                            [2] = u23
                        --]]
                        u10[u23] = nil
                        u23:Destroy()
                    end)
                    v25:Play()
                end)
            end
            local v26 = u22.CFrameConnection
            if v26 then
                v26:Disconnect()
            end
        end
    end
})
return {}