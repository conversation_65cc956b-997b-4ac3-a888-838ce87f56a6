-- Full Path: -Grow-a-Garden-\\ActivePetInputService-ModuleScript.lua
local u1 = game:GetService("UserInputService")
game:GetService("GuiService")
game:GetService("CollectionService")
game:GetService("RunService")
local u2 = game:GetService("TweenService")
local v3 = game:GetService("ReplicatedStorage")
local u4 = game:GetService("Players").LocalPlayer
local u5 = require(v3.Modules.PetServices.PetInformationUserInterfaceService)
local u6 = require(v3.Modules.PetServices.PetActionUserInterfaceService)
local u7 = workspace.CurrentCamera
workspace:GetPropertyChangedSignal("CurrentCamera"):Connect(function() --[[Anonymous function at line 15]]
    --[[
    Upvalues:
        [1] = u7
    --]]
    u7 = workspace.CurrentCamera
end)
local u8 = require(v3.Modules.GetMouseToWorld)
local u9 = Instance.new("Highlight")
u9.FillTransparency = 1
local u10 = nil
local u11 = nil
local function u12() --[[Anonymous function at line 28]]
    --[[
    Upvalues:
        [1] = u11
        [2] = u2
        [3] = u9
    --]]
    if u11 then
        u2:Create(u9, TweenInfo.new(0.2), {
            ["OutlineTransparency"] = 0.8
        }):Play()
    end
end
local function u13() --[[Anonymous function at line 36]]
    --[[
    Upvalues:
        [1] = u11
        [2] = u2
        [3] = u9
        [4] = u4
        [5] = u6
    --]]
    if u11 then
        u2:Create(u9, TweenInfo.new(0.2), {
            ["OutlineTransparency"] = 0
        }):Play()
        if u4:GetAttribute("DontOpenAction") then
            return
        elseif u6.Target == u11 then
            u6:Toggle(false)
        else
            if u6.Target ~= nil then
                u6:Toggle(false)
            end
            task.delay(0.1, function() --[[Anonymous function at line 53]]
                --[[
                Upvalues:
                    [1] = u6
                    [2] = u11
                --]]
                u6:SetTarget(u11)
                u6:Toggle(true)
            end)
        end
    else
        return
    end
end
local u14 = {
    [Enum.UserInputType.Touch] = { function(_) --[[Anonymous function at line 61]]
            --[[
            Upvalues:
                [1] = u12
            --]]
            u12()
        end },
    [Enum.UserInputType.MouseButton1] = { function() --[[Anonymous function at line 66]]
            --[[
            Upvalues:
                [1] = u12
            --]]
            u12()
        end },
    [Enum.KeyCode.ButtonR2] = { function() --[[Anonymous function at line 71]]
            --[[
            Upvalues:
                [1] = u12
            --]]
            u12()
        end }
}
local u15 = {
    [Enum.UserInputType.Touch] = { function(_) --[[Anonymous function at line 79]]
            --[[
            Upvalues:
                [1] = u13
            --]]
            u13()
        end },
    [Enum.UserInputType.MouseButton1] = { function() --[[Anonymous function at line 84]]
            --[[
            Upvalues:
                [1] = u13
            --]]
            u13()
        end },
    [Enum.KeyCode.ButtonR2] = { function() --[[Anonymous function at line 89]]
            --[[
            Upvalues:
                [1] = u13
            --]]
            u13()
        end }
}
u1.InputBegan:Connect(function(u16, _) --[[Anonymous function at line 95]]
    --[[
    Upvalues:
        [1] = u14
    --]]
    local v17 = u14[u16.KeyCode] or u14[u16.UserInputType]
    if v17 then
        for _, u18 in v17 do
            task.spawn(function() --[[Anonymous function at line 99]]
                --[[
                Upvalues:
                    [1] = u18
                    [2] = u16
                --]]
                u18(u16)
            end)
        end
    end
end)
u1.InputEnded:Connect(function(u19, _) --[[Anonymous function at line 106]]
    --[[
    Upvalues:
        [1] = u15
    --]]
    local v20 = u15[u19.KeyCode] or u15[u19.UserInputType]
    if v20 then
        for _, u21 in v20 do
            task.spawn(function() --[[Anonymous function at line 110]]
                --[[
                Upvalues:
                    [1] = u21
                    [2] = u19
                --]]
                u21(u19)
            end)
        end
    end
end)
u1.TouchTap:Connect(function() --[[Anonymous function at line 116]]
    --[[
    Upvalues:
        [1] = u12
    --]]
    u12()
end)
u1.TouchEnded:Connect(function() --[[Anonymous function at line 120]]
    --[[
    Upvalues:
        [1] = u13
    --]]
    u13()
end)
u9.Parent = script
task.spawn(function() --[[Anonymous function at line 127]]
    --[[
    Upvalues:
        [1] = u1
        [2] = u10
        [3] = u8
        [4] = u6
        [5] = u5
        [6] = u11
        [7] = u9
    --]]
    while true do
        u10 = u1:GetMouseLocation()
        local v22 = RaycastParams.new()
        v22.FilterDescendantsInstances = {}
        local v23 = u8(v22, 1000)
        if v23 then
            v23 = v23.Instance
        end
        if v23 then
            v23 = v23:FindFirstAncestor("PetMover")
        end
        if u6.Active then
            if u6.Target == u6.Target then
                u5:Toggle(false)
            end
        else
            u5:SetTarget(v23)
            if v23 then
                u5:Toggle(true)
            else
                u5:Toggle(false)
            end
        end
        if v23 and v23:FindFirstChildWhichIsA("Model", true) then
            u11 = v23
            local v24 = u9
            if v23 then
                v23 = v23:FindFirstChildWhichIsA("Model", true)
            end
            v24.Adornee = v23
        else
            u11 = nil
            u9.Adornee = nil
        end
        task.wait(0.2)
    end
end)
return {}