-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\ByteNet\dataTypes\string-ModuleScript.lua
local v1 = require(script.Parent.Parent.process.bufferWriter)
require(script.Parent.Parent.types)
local u2 = v1.u16
local u3 = v1.writestring
local u4 = v1.dyn_alloc
local u10 = {
    ["read"] = function(p5, p6) --[[Function name: read, line 11]]
        local v7 = buffer.readu16(p5, p6)
        return buffer.readstring(p5, p6 + 2, v7), v7 + 2
    end,
    ["write"] = function(p8) --[[Function name: write, line 16]]
        --[[
        Upvalues:
            [1] = u2
            [2] = u4
            [3] = u3
        --]]
        local v9 = string.len(p8)
        u2(v9)
        u4(v9)
        u3(p8)
    end
}
return function() --[[Anonymous function at line 25]]
    --[[
    Upvalues:
        [1] = u10
    --]]
    return u10
end