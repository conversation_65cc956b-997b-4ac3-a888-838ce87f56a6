-- Full Path: -Grow-a-Garden-\ReplicatedStorage\GameAnalytics\GameAnalyticsClient-ModuleScript.lua
local v1 = {}
local u2 = game:GetService("GuiService")
local u3 = game:GetService("UserInputService")
local u4 = game:GetService("ReplicatedStorage")
local u5 = game:GetService("ScriptContext")
function v1.initClient() --[[Anonymous function at line 15]]
    --[[
    Upvalues:
        [1] = u5
        [2] = u4
        [3] = u2
        [4] = u3
    --]]
    local v6 = require(script.Parent.GameAnalytics.Postie)
    u5.Error:Connect(function(p7, p8, u9) --[[Anonymous function at line 18]]
        --[[
        Upvalues:
            [1] = u4
        --]]
        if u9 then
            local u10 = nil
            local v11, _ = pcall(function() --[[Anonymous function at line 24]]
                --[[
                Upvalues:
                    [1] = u10
                    [2] = u9
                --]]
                u10 = u9:GetFullName()
            end)
            if v11 then
                u4.GameAnalyticsError:FireServer(p7, p8, u10)
            end
        else
            return
        end
    end)
    local function v12() --[[Anonymous function at line 35]]
        --[[
        Upvalues:
            [1] = u2
            [2] = u3
        --]]
        return u2:IsTenFootInterface() and "Console" or (u3.TouchEnabled and not u3.MouseEnabled and "Mobile" or "Desktop")
    end
    v6.setCallback("getPlatform", v12)
end
return v1