-- Full Path: -Grow-a-Garden-\\Team-ModuleScript.lua
local u1 = game:GetService("Teams")
local u2 = require(script.Parent.Parent.Shared.Util)
local u7 = {
    ["Transform"] = function(p3) --[[Function name: Transform, line 5]]
        --[[
        Upvalues:
            [1] = u2
            [2] = u1
        --]]
        return u2.MakeFuzzyFinder(u1:GetTeams())(p3)
    end,
    ["Validate"] = function(p4) --[[Function name: Validate, line 11]]
        return #p4 > 0, "No team with that name could be found."
    end,
    ["Autocomplete"] = function(p5) --[[Function name: Autocomplete, line 15]]
        --[[
        Upvalues:
            [1] = u2
        --]]
        return u2.GetNames(p5)
    end,
    ["Parse"] = function(p6) --[[Function name: Parse, line 19]]
        return p6[1]
    end
}
local u9 = {
    ["Listable"] = true,
    ["Transform"] = u7.Transform,
    ["Validate"] = u7.Validate,
    ["Autocomplete"] = u7.Autocomplete,
    ["Parse"] = function(p8) --[[Function name: Parse, line 30]]
        return p8[1]:GetPlayers()
    end
}
local u11 = {
    ["Transform"] = u7.Transform,
    ["Validate"] = u7.Validate,
    ["Autocomplete"] = u7.Autocomplete,
    ["Parse"] = function(p10) --[[Function name: Parse, line 40]]
        return p10[1].TeamColor
    end
}
return function(p12) --[[Anonymous function at line 45]]
    --[[
    Upvalues:
        [1] = u7
        [2] = u2
        [3] = u9
        [4] = u11
    --]]
    p12:RegisterType("team", u7)
    p12:RegisterType("teams", u2.MakeListableType(u7))
    p12:RegisterType("teamPlayers", u9)
    p12:RegisterType("teamColor", u11)
    p12:RegisterType("teamColors", u2.MakeListableType(u11))
end