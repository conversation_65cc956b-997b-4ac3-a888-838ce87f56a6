-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Data\InfinitePackData-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
local u2 = require(v1.Modules.WeightRandom)
local u3 = {
    ["Free"] = {
        {
            ["Name"] = "50\194\162",
            ["Coins"] = 50,
            ["Chance"] = 0.3333333333333333,
            ["Icon"] = "rbxassetid://111559087552483",
            ["Color"] = Color3.new(1, 1, 0)
        },
        {
            ["Name"] = "100\194\162",
            ["Coins"] = 50,
            ["Chance"] = 0.3333333333333333,
            ["Icon"] = "rbxassetid://84541528477238",
            ["Color"] = Color3.new(1, 1, 0)
        },
        {
            ["Name"] = "Watering Can X1",
            ["Coins"] = 25,
            ["Chance"] = 0.3333333333333333,
            ["Icon"] = "rbxassetid://108707176647018",
            ["Color"] = Color3.new(0.666667, 0.666667, 0.666667)
        }
    },
    ["Paid"] = {
        {
            ["Name"] = "Super Seed",
            ["Chance"] = 5,
            ["Icon"] = "rbxassetid://119802391042790",
            ["Color"] = Color3.new(1, 0, 0)
        },
        {
            ["Name"] = "Apple Seed",
            ["Chance"] = 20,
            ["Icon"] = "rbxassetid://128318449902634",
            ["Color"] = Color3.new(0, 1, 0)
        },
        {
            ["Name"] = "500\194\162",
            ["Chance"] = 20,
            ["Icon"] = "rbxassetid://94889540639216",
            ["Color"] = Color3.new(1, 1, 0)
        },
        {
            ["Name"] = "1,000\194\162",
            ["Chance"] = 20,
            ["Icon"] = "rbxassetid://123750064988458",
            ["Color"] = Color3.new(1, 1, 0)
        },
        {
            ["Name"] = "2,000\194\162",
            ["Chance"] = 20,
            ["Icon"] = "rbxassetid://71146286015050",
            ["Color"] = Color3.new(1, 1, 0)
        },
        {
            ["Name"] = "Watering Can X1",
            ["Chance"] = 25,
            ["Icon"] = "rbxassetid://108707176647018",
            ["Color"] = Color3.new(0.666667, 0.666667, 0.666667)
        },
        {
            ["Name"] = "Watering Can X3",
            ["Chance"] = 10,
            ["Icon"] = "rbxassetid://70390145378562",
            ["Color"] = Color3.new(0.666667, 0.666667, 0.666667)
        },
        {
            ["Name"] = "Watering Can X5",
            ["Chance"] = 5,
            ["Icon"] = "rbxassetid://73981405252852",
            ["Color"] = Color3.new(0.666667, 0.666667, 0.666667)
        },
        {
            ["Name"] = "Watering Can X10",
            ["Chance"] = 3,
            ["Icon"] = "rbxassetid://106733159472445",
            ["Color"] = Color3.new(0.666667, 0.666667, 0.666667)
        }
    }
}
local u4 = {
    { 3250226689, 15 },
    { 3250227730, 25 },
    { 3250228324, 40 },
    { 3250229031, 70 },
    { 3250229162, (1 / 0) }
}
local _ = game.PlaceId == 140398800602847
return {
    ["Rewards"] = u3,
    ["GetRewards"] = function(p5, p6) --[[Function name: GetRewards, line 119]]
        --[[
        Upvalues:
            [1] = u2
            [2] = u4
            [3] = u3
        --]]
        local v7 = table.create(p6)
        local v8 = Random.new(p5)
        for v9 = 1, p6 do
            local v10 = u2.array
            local v11
            if v9 == 1 then
                v11 = 0
            else
                if v9 == 2 then
                    ::l7::
                    for _, v12 in u4 do
                        if v9 < v12[2] then
                            v11 = v12[1]
                            goto l5
                        end
                    end
                    v11 = 0
                    goto l5
                end
                local v13 = v9 - 2
                if math.max(v13, 0) % 5 == 0 then
                    goto l7
                end
                v11 = 0
            end
            ::l5::
            local v14
            if v11 == 0 then
                v14 = u3.Free
            else
                v14 = u3.Paid
            end
            v7[v9] = v10(v14, v8)
        end
        return v7
    end,
    ["GetProductFor"] = function(p15) --[[Function name: GetProductFor, line 99]]
        --[[
        Upvalues:
            [1] = u4
        --]]
        if p15 == 1 then
            return 0
        end
        if p15 ~= 2 then
            local v16 = p15 - 2
            if math.max(v16, 0) % 5 ~= 0 then
                return 0
            end
        end
        for _, v17 in u4 do
            if p15 < v17[2] then
                return v17[1]
            end
        end
        return 0
    end
}