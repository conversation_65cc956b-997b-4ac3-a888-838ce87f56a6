-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\CosmeticServices\UserInterface\CosmeticActionUserInterfaceService\Actions\Delete-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
local u2 = require(v1.Modules.CosmeticServices.CosmeticService)
local u3 = require(v1.Modules.PlaySound)
return function(u4) --[[Function name: Loader, line 6]]
    --[[
    Upvalues:
        [1] = u2
        [2] = u3
    --]]
    return {
        ["InputBegan"] = function() --[[Anonymous function at line 8]] end,
        ["InputEnded"] = function() --[[Anonymous function at line 11]]
            --[[
            Upvalues:
                [1] = u4
                [2] = u2
                [3] = u3
            --]]
            local v5 = u4.Target
            if v5 then
                local v6 = v5.Parent
                if v6 then
                    u2:Unequip(v6:GetAttribute("CosmeticUUID"))
                    u4:Toggle(false)
                    u3("rbxassetid://139578759536123").Volume = 0.5
                end
            else
                return
            end
        end,
        ["InstantAction"] = true
    }
end