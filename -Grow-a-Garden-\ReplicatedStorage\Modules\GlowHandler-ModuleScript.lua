-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\GlowHandler-ModuleScript.lua
local v1 = game:GetService("CollectionService")
local v2 = game:GetService("ReplicatedStorage")
local u3 = game:GetService("TweenService")
local v4 = require(v2.Modules.DataService)
local u5 = require(v2.Modules.Settings.SettingsService)
local u6 = script.GlowTexture
local u7 = {
    ["Ancestors"] = {},
    ["FancyTextures"] = {}
}
local u8 = {
    "Front",
    "Back",
    "Right",
    "Left",
    "Top",
    "Bottom"
}
local u9 = { "R", "G", "B" }
local u10 = u5:GetSetting("Textures")
local function u18(p11) --[[Anonymous function at line 40]]
    --[[
    Upvalues:
        [1] = u9
    --]]
    local v12 = {
        ["R"] = 0,
        ["G"] = 0,
        ["B"] = 0
    }
    local v13 = 0
    local v14 = 0
    for v15 = 1, #u9 do
        local v16 = u9[v15]
        local v17 = p11[v16]
        v12[v16] = v17
        if v13 < v17 then
            v14 = v16
            v13 = v17
        end
    end
    v12[v14] = 25555
    return Color3.new(v12.R, v12.G, v12.B)
end
local function u26(p19, p20) --[[Anonymous function at line 67]]
    --[[
    Upvalues:
        [1] = u18
        [2] = u7
        [3] = u8
        [4] = u6
        [5] = u3
    --]]
    if p20 then
        local v21 = u18(p19.Color)
        if not u7.FancyTextures[p19] then
            u7.FancyTextures[p19] = {}
        end
        for v22 = 1, #u8 do
            local v23 = u6:Clone()
            v23.Parent = p19
            v23.Face = u8[v22]
            v23.Color3 = v21
            v23.Transparency = 1
            u3:Create(v23, TweenInfo.new(0.5), {
                ["Transparency"] = 0.4
            }):Play()
            local v24 = u7.FancyTextures[p19]
            table.insert(v24, v23)
        end
    elseif u7.FancyTextures[p19] then
        for _, v25 in u7.FancyTextures[p19] do
            u3:Create(v25, TweenInfo.new(0.75), {
                ["Transparency"] = 1
            }):Play()
            game.Debris:AddItem(v25, 0.75)
        end
    end
end
local function u37(u27) --[[Anonymous function at line 96]]
    --[[
    Upvalues:
        [1] = u7
        [2] = u3
        [3] = u10
        [4] = u26
    --]]
    local u28 = u27:FindFirstAncestorWhichIsA("Model")
    if u28 then
        u27:SetAttribute("BaseMaterial", u27.Material.Name)
        if u7.Ancestors[u28] then
            local v29 = u7.Ancestors[u28]
            table.insert(v29, u27)
        else
            u7.Ancestors[u28] = { u27 }
        end
        u27.Touched:Connect(function(p30) --[[Anonymous function at line 109]]
            --[[
            Upvalues:
                [1] = u28
                [2] = u27
                [3] = u3
                [4] = u7
                [5] = u10
                [6] = u26
            --]]
            local v31 = p30.Parent
            if v31 then
                if game.Players:GetPlayerFromCharacter(v31) then
                    local v32 = u28:FindFirstChild("Age", true)
                    if v32 then
                        local v33 = v32.Value
                        local v34 = u27.Name
                        if v33 < tonumber(v34) then
                            return
                        elseif not u28:GetAttribute("GlowToggled") then
                            u28:SetAttribute("GlowToggled", true)
                            local v35 = u28:FindFirstChild("GlowLight", true)
                            if v35 then
                                v35.Brightness = 0
                                u3:Create(v35, TweenInfo.new(0.3), {
                                    ["Brightness"] = 1
                                }):Play()
                            end
                            for _, u36 in u7.Ancestors[u28] do
                                if u10 then
                                    u26(u36, true)
                                else
                                    u36.Material = "Neon"
                                end
                                task.delay(8, function() --[[Anonymous function at line 133]]
                                    --[[
                                    Upvalues:
                                        [1] = u36
                                        [2] = u26
                                    --]]
                                    u36.Material = u36:GetAttribute("BaseMaterial")
                                    u26(u36, false)
                                end)
                            end
                            task.wait(8)
                            if v35 then
                                u3:Create(v35, TweenInfo.new(0.3), {
                                    ["Brightness"] = 0
                                }):Play()
                            end
                            task.wait(1)
                            u28:SetAttribute("GlowToggled", false)
                        end
                    else
                        return
                    end
                else
                    return
                end
            else
                return
            end
        end)
    end
end
v1:GetInstanceAddedSignal("GlowPart"):Connect(function(p38) --[[Anonymous function at line 152]]
    --[[
    Upvalues:
        [1] = u37
    --]]
    u37(p38)
end)
v4:GetPathSignal("Settings/Textures"):Connect(function() --[[Anonymous function at line 156]]
    --[[
    Upvalues:
        [1] = u10
        [2] = u5
        [3] = u7
        [4] = u26
    --]]
    u10 = u5:GetSetting("Textures")
    if not u10 then
        for v39, _ in u7.FancyTextures do
            u26(v39, false)
        end
    end
end)
return u7