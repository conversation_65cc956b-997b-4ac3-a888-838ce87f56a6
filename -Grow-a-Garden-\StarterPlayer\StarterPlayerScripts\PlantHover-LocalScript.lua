-- Full Path: -Grow-a-Garden-\StarterPlayer\StarterPlayerScripts\PlantHover-LocalScript.lua
local u1 = game:GetService("RunService")
local u2 = game:GetService("UserInputService")
local v3 = game:GetService("GuiService")
game:GetService("CollectionService")
game:GetService("TweenService")
local v4 = game:GetService("ReplicatedStorage")
local v5 = game:GetService("Players")
local v6 = v4:WaitForChild("Modules")
require(v6:WaitForChild("TimeHelper"))
local u7 = v5.LocalPlayer
local u8 = u7:WaitForChild("PlayerGui"):WaitForChild("PlantHover_UI")
local u9 = u8:WaitForChild("Frame")
local u10 = u9:WaitForChild("PlantName")
local u11 = u9:WaitForChild("PlantInfo")
local v12 = require(v4.Modules.CreateTagHandler)
local u13 = require(v4.Data.GrowableData)
local u14 = require(v4.Modules.NumberUtil)
local v15 = require(v4.Modules.Trove)
local v16 = require(v4.Modules.Remotes)
local u17 = workspace.CurrentCamera
workspace:GetPropertyChangedSignal("CurrentCamera"):Connect(function() --[[Anonymous function at line 26]]
    --[[
    Upvalues:
        [1] = u17
    --]]
    workspace.CurrentCamera = u17
end)
require(v4.Modules.GetMouseToWorld)
local u18 = 0
local u19 = nil
local u20 = {}
local u21 = {}
local u22 = {}
local u23 = workspace:GetAttribute("CurrentWeatherEvent") or "Default"
local u24 = u13:GetDataForWeather(u23)
workspace:GetAttributeChangedSignal("CurrentWeatherEvent"):Connect(function() --[[Anonymous function at line 46]]
    --[[
    Upvalues:
        [1] = u23
        [2] = u24
        [3] = u13
    --]]
    u23 = workspace:GetAttribute("CurrentWeatherEvent") or "Default"
    u24 = u13:GetDataForWeather(u23)
end)
local u25 = RaycastParams.new()
u25.FilterDescendantsInstances = u20
u25.FilterType = Enum.RaycastFilterType.Include
v12({
    ["Tag"] = "Growable",
    ["OnInstanceAdded"] = function(p26) --[[Function name: OnInstanceAdded, line 57]]
        --[[
        Upvalues:
            [1] = u20
            [2] = u25
        --]]
        if p26:IsDescendantOf(workspace) then
            local v27 = u20
            table.insert(v27, p26)
            u25:AddToFilter(p26)
        end
    end,
    ["OnInstanceRemoved"] = function(p28) --[[Function name: OnInstanceRemoved, line 65]]
        --[[
        Upvalues:
            [1] = u20
            [2] = u25
            [3] = u21
            [4] = u22
        --]]
        local v29 = table.find(u20, p28)
        if v29 then
            table.remove(u20, v29)
            u25.FilterDescendantsInstances = u20
            u21[p28] = nil
            u22[p28] = nil
        end
    end
})
local u30 = false
local u31 = v15.new()
local function u39(p32) --[[Anonymous function at line 85]]
    --[[
    Upvalues:
        [1] = u13
        [2] = u22
        [3] = u21
    --]]
    if p32 then
        local v33 = u13:GetDataForPlant(p32)
        if v33 then
            local v34 = v33.GrowTickTime
            local v35 = (p32:GetAttribute("MaxAge") or 1) - p32.Grow.Age.Value
            local v36 = Random.new(p32.Item_Seed.Value):NextNumber() * (v34.Max - v34.Min) + v34.Min
            local v37 = u22
            local v38 = v35 / v33.GrowRate
            v37[p32] = math.ceil(v38) * v36
            u21[p32] = nil
        end
    else
        return
    end
end
v16.Plant.Update.listen(function(p40) --[[Anonymous function at line 103]]
    --[[
    Upvalues:
        [1] = u30
        [2] = u39
    --]]
    if u30 then
        u39(p40)
    end
end)
local u41 = false
local function v55() --[[Anonymous function at line 192]]
    --[[
    Upvalues:
        [1] = u41
        [2] = u7
        [3] = u30
        [4] = u20
        [5] = u22
        [6] = u24
        [7] = u21
        [8] = u39
        [9] = u31
        [10] = u1
    --]]
    u41 = u7:GetAttribute("AB_GrowTimer")
    if u41 then
        if not u30 then
            u30 = true
            local u42 = 0
            local function v53(p43) --[[Anonymous function at line 119]]
                --[[
                Upvalues:
                    [1] = u42
                    [2] = u20
                    [3] = u22
                    [4] = u24
                    [5] = u21
                    [6] = u39
                --]]
                u42 = u42 + p43
                if u42 >= 0.2 then
                    local v44 = u42
                    u42 = 0
                    for _, v45 in u20 do
                        local v46 = u22[v45]
                        if v46 then
                            if v46 > 0 then
                                local v47 = (v45:GetAttribute("GrowRateMulti") or 1) * u24.GrowRateMulti
                                local v48 = u22
                                v48[v45] = v48[v45] - v44 * v47
                            end
                        else
                            local v49 = v45:FindFirstChild("Grow")
                            if v49 then
                                local v50 = v49:FindFirstChild("Age")
                                if v50 and v45:GetAttribute("MaxAge") then
                                    local v51 = v50.Value
                                    local v52 = u21[v45]
                                    if v52 ~= v51 or v51 == 0 then
                                        if v52 and not u22[v45] then
                                            u39(v45)
                                        else
                                            u21[v45] = v51
                                        end
                                    end
                                end
                            end
                        end
                    end
                end
            end
            u31:Add(u1.PostSimulation:Connect(v53))
        end
    elseif u30 then
        u30 = false
        for _, v54 in u20 do
            u21[v54] = nil
            u22[v54] = nil
        end
        u31:Clean()
    end
end
task.spawn(v55)
u7:GetAttributeChangedSignal("AB_GrowTimer"):Connect(v55)
local u56 = 0
local u57 = false
local u58 = v3:GetGuiInset()
local function v75(p59) --[[Anonymous function at line 209]]
    --[[
    Upvalues:
        [1] = u2
        [2] = u8
        [3] = u56
        [4] = u17
        [5] = u58
        [6] = u25
        [7] = u57
        [8] = u9
        [9] = u19
        [10] = u18
        [11] = u10
        [12] = u11
        [13] = u41
        [14] = u22
        [15] = u14
    --]]
    local v60 = u2:GetMouseLocation()
    u8.Frame.Position = UDim2.new(0.01, v60.X, 0, v60.Y)
    u56 = u56 + p59
    if u56 >= 0.1 then
        u56 = 0
        local v61 = u17:ScreenPointToRay(v60.X + u58.X, v60.Y - u58.Y)
        local v62 = workspace:Raycast(v61.Origin, v61.Direction * 150, u25)
        local v63 = v62 ~= nil
        if v62 then
            v62 = v62.Instance
        end
        if v62 and v62:FindFirstAncestor("Fruits") then
            v63 = false
            v62 = nil
        elseif u57 then
            u57 = false
            u9.Show_Val.Value = false
            u9.Visible = false
        end
        if v63 then
            local v64 = v62:FindFirstAncestorWhichIsA("Model")
            local v65 = v64:GetAttribute("MaxAge")
            if not v65 then
                return
            end
            local v66 = v64.Grow:FindFirstChild("Age")
            if not v66 then
                return
            end
            local v67 = v66.Value / v65 * 100
            local v68 = math.clamp(v67, 0, 100)
            if v64 ~= u19 then
                u19 = v64
                u18 = (not v66 or (v65 <= 0 or not v68)) and 0 or v68
            end
            local v69 = u18
            local v70 = v68 - u18
            local v71 = p59 * 10
            u18 = v69 + v70 * math.clamp(v71, 0, 1)
            if u18 ~= u18 or (u18 == (1 / 0) or u18 == (-1 / 0)) then
                u18 = 0
            end
            if not u57 then
                u57 = true
                u9.Show_Val.Value = true
                u9.Visible = true
            end
            u10.Text = v64.Name
            if v68 >= 100 then
                u11.Text = ""
                return
            end
            if u41 then
                local v72 = u22[v64] or 0
                local v73 = u11
                local v74
                if v72 <= 0 then
                    v74 = ""
                elseif v72 < 60 then
                    v74 = ("%**"):format((string.format("%.1fs", v72)))
                else
                    v74 = ("%**"):format((u14.compactFormat(v72)))
                end
                v73.Text = v74
                return
            end
            u11.Text = string.format("%.0f%% Grown", u18)
        end
    end
end
u1.PostSimulation:Connect(v75)