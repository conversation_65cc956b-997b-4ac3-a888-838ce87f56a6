-- Full Path: -Grow-a-Garden-\StarterPlayer\StarterPlayerScripts\PlayerModule\ControlModule-ModuleScript.lua
local u1 = {}
u1.__index = u1
local u2 = game:GetService("Players")
local u3 = game:GetService("RunService")
local u4 = game:GetService("UserInputService")
local u5 = game:GetService("GuiService")
local u6 = game:GetService("Workspace")
local u7 = UserSettings():GetService("UserGameSettings")
local u8 = game:GetService("VRService")
script.Parent:WaitForChild("CommonUtils")
local u9 = require(script:WaitForChild("Keyboard"))
local u10 = require(script:WaitFor<PERSON>hild("Gamepad"))
local u11 = require(script:WaitForChild("DynamicThumbstick"))
local v12, v13 = pcall(function() --[[Anonymous function at line 34]]
    return UserSettings():IsUserFeatureEnabled("UserDynamicThumbstickSafeAreaUpdate")
end)
local u14 = v12 and v13
local u15 = require(script:Wait<PERSON><PERSON><PERSON>hild("TouchThumbstick"))
local u16 = require(script:WaitForChild("ClickToMoveController"))
local u17 = require(script:WaitForChild("TouchJump"))
local u18 = require(script:WaitForChild("VehicleController"))
local u19 = Enum.ContextActionPriority.Medium.Value
local u20 = {
    [Enum.TouchMovementMode.DPad] = u11,
    [Enum.DevTouchMovementMode.DPad] = u11,
    [Enum.TouchMovementMode.Thumbpad] = u11,
    [Enum.DevTouchMovementMode.Thumbpad] = u11,
    [Enum.TouchMovementMode.Thumbstick] = u15,
    [Enum.DevTouchMovementMode.Thumbstick] = u15,
    [Enum.TouchMovementMode.DynamicThumbstick] = u11,
    [Enum.DevTouchMovementMode.DynamicThumbstick] = u11,
    [Enum.TouchMovementMode.ClickToMove] = u16,
    [Enum.DevTouchMovementMode.ClickToMove] = u16,
    [Enum.TouchMovementMode.Default] = u11,
    [Enum.ComputerMovementMode.Default] = u9,
    [Enum.ComputerMovementMode.KeyboardMouse] = u9,
    [Enum.DevComputerMovementMode.KeyboardMouse] = u9,
    [Enum.DevComputerMovementMode.Scriptable] = nil,
    [Enum.ComputerMovementMode.ClickToMove] = u16,
    [Enum.DevComputerMovementMode.ClickToMove] = u16
}
local u21 = {
    [Enum.UserInputType.Keyboard] = u9,
    [Enum.UserInputType.MouseButton1] = u9,
    [Enum.UserInputType.MouseButton2] = u9,
    [Enum.UserInputType.MouseButton3] = u9,
    [Enum.UserInputType.MouseWheel] = u9,
    [Enum.UserInputType.MouseMovement] = u9,
    [Enum.UserInputType.Gamepad1] = u10,
    [Enum.UserInputType.Gamepad2] = u10,
    [Enum.UserInputType.Gamepad3] = u10,
    [Enum.UserInputType.Gamepad4] = u10
}
local u22 = nil
function u1.new() --[[Anonymous function at line 93]]
    --[[
    Upvalues:
        [1] = u1
        [2] = u2
        [3] = u18
        [4] = u19
        [5] = u3
        [6] = u4
        [7] = u7
        [8] = u5
    --]]
    local v23 = u1
    local u24 = setmetatable({}, v23)
    u24.controllers = {}
    u24.activeControlModule = nil
    u24.activeController = nil
    u24.touchJumpController = nil
    u24.moveFunction = u2.LocalPlayer.Move
    u24.humanoid = nil
    u24.lastInputType = Enum.UserInputType.None
    u24.controlsEnabled = true
    u24.humanoidSeatedConn = nil
    u24.vehicleController = nil
    u24.touchControlFrame = nil
    u24.currentTorsoAngle = 0
    u24.inputMoveVector = Vector3.new(0, 0, 0)
    u24.vehicleController = u18.new(u19)
    u2.LocalPlayer.CharacterAdded:Connect(function(p25) --[[Anonymous function at line 119]]
        --[[
        Upvalues:
            [1] = u24
        --]]
        u24:OnCharacterAdded(p25)
    end)
    u2.LocalPlayer.CharacterRemoving:Connect(function(p26) --[[Anonymous function at line 120]]
        --[[
        Upvalues:
            [1] = u24
        --]]
        u24:OnCharacterRemoving(p26)
    end)
    if u2.LocalPlayer.Character then
        u24:OnCharacterAdded(u2.LocalPlayer.Character)
    end
    u3:BindToRenderStep("ControlScriptRenderstep", Enum.RenderPriority.Input.Value, function(p27) --[[Anonymous function at line 125]]
        --[[
        Upvalues:
            [1] = u24
        --]]
        u24:OnRenderStepped(p27)
    end)
    u4.LastInputTypeChanged:Connect(function(p28) --[[Anonymous function at line 129]]
        --[[
        Upvalues:
            [1] = u24
        --]]
        u24:OnLastInputTypeChanged(p28)
    end)
    u7:GetPropertyChangedSignal("TouchMovementMode"):Connect(function() --[[Anonymous function at line 134]]
        --[[
        Upvalues:
            [1] = u24
        --]]
        u24:OnTouchMovementModeChange()
    end)
    u2.LocalPlayer:GetPropertyChangedSignal("DevTouchMovementMode"):Connect(function() --[[Anonymous function at line 137]]
        --[[
        Upvalues:
            [1] = u24
        --]]
        u24:OnTouchMovementModeChange()
    end)
    u7:GetPropertyChangedSignal("ComputerMovementMode"):Connect(function() --[[Anonymous function at line 141]]
        --[[
        Upvalues:
            [1] = u24
        --]]
        u24:OnComputerMovementModeChange()
    end)
    u2.LocalPlayer:GetPropertyChangedSignal("DevComputerMovementMode"):Connect(function() --[[Anonymous function at line 144]]
        --[[
        Upvalues:
            [1] = u24
        --]]
        u24:OnComputerMovementModeChange()
    end)
    u24.playerGui = nil
    u24.touchGui = nil
    u24.playerGuiAddedConn = nil
    u5:GetPropertyChangedSignal("TouchControlsEnabled"):Connect(function() --[[Anonymous function at line 153]]
        --[[
        Upvalues:
            [1] = u24
        --]]
        u24:UpdateTouchGuiVisibility()
        u24:UpdateActiveControlModuleEnabled()
    end)
    if not u4.TouchEnabled then
        u24:OnLastInputTypeChanged(u4:GetLastInputType())
        return u24
    end
    u24.playerGui = u2.LocalPlayer:FindFirstChildOfClass("PlayerGui")
    if not u24.playerGui then
        u24.playerGuiAddedConn = u2.LocalPlayer.ChildAdded:Connect(function(p29) --[[Anonymous function at line 164]]
            --[[
            Upvalues:
                [1] = u24
                [2] = u4
            --]]
            if p29:IsA("PlayerGui") then
                u24.playerGui = p29
                u24:CreateTouchGuiContainer()
                u24.playerGuiAddedConn:Disconnect()
                u24.playerGuiAddedConn = nil
                u24:OnLastInputTypeChanged(u4:GetLastInputType())
            end
        end)
        return u24
    end
    u24:CreateTouchGuiContainer()
    u24:OnLastInputTypeChanged(u4:GetLastInputType())
    return u24
end
function u1.GetMoveVector(p30) --[[Anonymous function at line 184]]
    return not p30.activeController and Vector3.new(0, 0, 0) or p30.activeController:GetMoveVector()
end
function u1.GetEstimatedVRTorsoFrame(p31) --[[Anonymous function at line 204]]
    --[[
    Upvalues:
        [1] = u8
    --]]
    local v32 = u8:GetUserCFrame(Enum.UserCFrame.Head)
    local _, v33, _ = v32:ToEulerAnglesYXZ()
    local v34 = -v33
    if u8:GetUserCFrameEnabled(Enum.UserCFrame.RightHand) and u8:GetUserCFrameEnabled(Enum.UserCFrame.LeftHand) then
        local v35 = u8:GetUserCFrame(Enum.UserCFrame.LeftHand)
        local v36 = u8:GetUserCFrame(Enum.UserCFrame.RightHand)
        local v37 = v32.Position - v35.Position
        local v38 = v32.Position - v36.Position
        local v39 = v37.X
        local v40 = v37.Z
        local v41 = -math.atan2(v39, v40)
        local v42 = v38.X
        local v43 = v38.Z
        local v44 = (-math.atan2(v42, v43) - v41 + 12.566370614359172) % 6.283185307179586
        if v44 > 3.141592653589793 then
            v44 = v44 - 6.283185307179586
        end
        local v45 = (v41 + v44 / 2 + 12.566370614359172) % 6.283185307179586
        if v45 > 3.141592653589793 then
            v45 = v45 - 6.283185307179586
        end
        local v46 = (v34 - p31.currentTorsoAngle + 12.566370614359172) % 6.283185307179586
        if v46 > 3.141592653589793 then
            v46 = v46 - 6.283185307179586
        end
        local v47 = (v45 - p31.currentTorsoAngle + 12.566370614359172) % 6.283185307179586
        if v47 > 3.141592653589793 then
            v47 = v47 - 6.283185307179586
        end
        local v48
        if v47 > -1.5707963267948966 then
            v48 = v47 < 1.5707963267948966
        else
            v48 = false
        end
        if not v48 then
            v47 = v46
        end
        local v49 = math.min(v47, v46)
        local v50 = math.max(v47, v46)
        local v51 = 0
        if v49 > 0 then
            v50 = v49
        elseif v50 >= 0 then
            v50 = v51
        end
        p31.currentTorsoAngle = v50 + p31.currentTorsoAngle
    else
        p31.currentTorsoAngle = v34
    end
    return CFrame.new(v32.Position) * CFrame.fromEulerAnglesYXZ(0, -p31.currentTorsoAngle, 0)
end
function u1.GetActiveController(p52) --[[Anonymous function at line 248]]
    return p52.activeController
end
function u1.UpdateActiveControlModuleEnabled(u53) --[[Anonymous function at line 253]]
    --[[
    Upvalues:
        [1] = u2
        [2] = u16
        [3] = u15
        [4] = u11
        [5] = u17
        [6] = u5
        [7] = u4
    --]]
    local function v54() --[[Anonymous function at line 266]]
        --[[
        Upvalues:
            [1] = u53
            [2] = u16
            [3] = u15
            [4] = u11
            [5] = u17
            [6] = u2
        --]]
        if u53.touchControlFrame and (u53.activeControlModule == u16 or (u53.activeControlModule == u15 or u53.activeControlModule == u11)) then
            if not u53.controllers[u17] then
                u53.controllers[u17] = u17.new()
            end
            u53.touchJumpController = u53.controllers[u17]
            u53.touchJumpController:Enable(true, u53.touchControlFrame)
        elseif u53.touchJumpController then
            u53.touchJumpController:Enable(false)
        end
        if u53.activeControlModule == u16 then
            u53.activeController:Enable(true, u2.LocalPlayer.DevComputerMovementMode == Enum.DevComputerMovementMode.UserChoice, u53.touchJumpController)
            return
        elseif u53.touchControlFrame then
            u53.activeController:Enable(true, u53.touchControlFrame)
        else
            u53.activeController:Enable(true)
        end
    end
    if u53.activeController then
        if u53.controlsEnabled then
            if u5.TouchControlsEnabled or (not u4.TouchEnabled or u53.activeControlModule ~= u16 and (u53.activeControlModule ~= u15 and u53.activeControlModule ~= u11)) then
                v54()
            else
                u53.activeController:Enable(false)
                if u53.touchJumpController then
                    u53.touchJumpController:Enable(false)
                end
                if u53.moveFunction then
                    u53.moveFunction(u2.LocalPlayer, Vector3.new(0, 0, 0), true)
                end
            end
        else
            u53.activeController:Enable(false)
            if u53.touchJumpController then
                u53.touchJumpController:Enable(false)
            end
            if u53.moveFunction then
                u53.moveFunction(u2.LocalPlayer, Vector3.new(0, 0, 0), true)
            end
            return
        end
    else
        return
    end
end
function u1.Enable(p55, p56) --[[Anonymous function at line 325]]
    local v57 = p56 == nil and true or p56
    if p55.controlsEnabled == v57 then
        return
    else
        p55.controlsEnabled = v57
        if p55.activeController then
            p55:UpdateActiveControlModuleEnabled()
        end
    end
end
function u1.Disable(p58) --[[Anonymous function at line 340]]
    p58:Enable(false)
end
function u1.SelectComputerMovementModule(_) --[[Anonymous function at line 346]]
    --[[
    Upvalues:
        [1] = u4
        [2] = u2
        [3] = u21
        [4] = u22
        [5] = u7
        [6] = u9
        [7] = u16
        [8] = u20
    --]]
    if u4.KeyboardEnabled or u4.GamepadEnabled then
        local v59 = u2.LocalPlayer.DevComputerMovementMode
        local v60
        if v59 == Enum.DevComputerMovementMode.UserChoice then
            v60 = u21[u22]
            if u7.ComputerMovementMode == Enum.ComputerMovementMode.ClickToMove and v60 == u9 then
                v60 = u16
            end
        else
            v60 = u20[v59]
            if not v60 and v59 ~= Enum.DevComputerMovementMode.Scriptable then
                warn("No character control module is associated with DevComputerMovementMode ", v59)
            end
        end
        if v60 then
            return v60, true
        elseif v59 == Enum.DevComputerMovementMode.Scriptable then
            return nil, true
        else
            return nil, false
        end
    else
        return nil, false
    end
end
function u1.SelectTouchModule(_) --[[Anonymous function at line 384]]
    --[[
    Upvalues:
        [1] = u4
        [2] = u2
        [3] = u20
        [4] = u7
    --]]
    if not u4.TouchEnabled then
        return nil, false
    end
    local v61 = u2.LocalPlayer.DevTouchMovementMode
    local v62
    if v61 == Enum.DevTouchMovementMode.UserChoice then
        v62 = u20[u7.TouchMovementMode]
    else
        if v61 == Enum.DevTouchMovementMode.Scriptable then
            return nil, true
        end
        v62 = u20[v61]
    end
    return v62, true
end
local function u65() --[[Anonymous function at line 400]]
    --[[
    Upvalues:
        [1] = u4
    --]]
    local v63 = u4:GetGamepadState(Enum.UserInputType.Gamepad1)
    for _, v64 in pairs(v63) do
        if v64.KeyCode == Enum.KeyCode.Thumbstick2 then
            return v64.Position
        end
    end
    return Vector3.new(0, 0, 0)
end
function u1.calculateRawMoveVector(p66, p67, p68) --[[Anonymous function at line 410]]
    --[[
    Upvalues:
        [1] = u6
        [2] = u8
        [3] = u65
    --]]
    local v69 = u6.CurrentCamera
    if not v69 then
        return p68
    end
    local v70 = v69.CFrame
    if u8.VREnabled and p67.RootPart then
        u8:GetUserCFrame(Enum.UserCFrame.Head)
        local v71 = p66:GetEstimatedVRTorsoFrame()
        if (v69.Focus.Position - v70.Position).Magnitude < 3 then
            v70 = v70 * v71
        else
            v70 = v69.CFrame * (v71.Rotation + v71.Position * v69.HeadScale)
        end
    end
    if p67:GetState() ~= Enum.HumanoidStateType.Swimming then
        local _, _, _, v76, v73, v74, _, _, v75, _, _, v76 = v70:GetComponents()
        if v75 >= 1 or v75 <= -1 then
            v74 = -v73 * math.sign(v75)
        end
        local v77 = v76 * v76 + v74 * v74
        local v78 = math.sqrt(v77)
        local v79 = (v76 * p68.X + v74 * p68.Z) / v78
        local v80 = (v76 * p68.Z - v74 * p68.X) / v78
        return Vector3.new(v79, 0, v80)
    end
    if not u8.VREnabled then
        return v70:VectorToWorldSpace(p68)
    end
    local v81 = p68.X
    local v82 = p68.Z
    local v83 = Vector3.new(v81, 0, v82)
    if v83.Magnitude < 0.01 then
        return Vector3.new(0, 0, 0)
    end
    local v84 = -u65().Y * 1.3962634015954636
    local v85 = -v83.X
    local v86 = -v83.Z
    local v87 = math.atan2(v85, v86)
    local _, v88, _ = v70:ToEulerAnglesYXZ()
    local v89 = v87 + v88
    return CFrame.fromEulerAnglesYXZ(v84, v89, 0).LookVector
end
function u1.OnRenderStepped(p90, p91) --[[Anonymous function at line 469]]
    --[[
    Upvalues:
        [1] = u10
        [2] = u8
        [3] = u2
    --]]
    if p90.activeController and (p90.activeController.enabled and p90.humanoid) then
        local v92 = p90.activeController:GetMoveVector()
        local v93 = p90.activeController:IsMoveVectorCameraRelative()
        local v94 = p90:GetClickToMoveController()
        if p90.activeController == v94 then
            v94:OnRenderStepped(p91)
        elseif v92.magnitude > 0 then
            v94:CleanupPath()
        else
            v94:OnRenderStepped(p91)
            v92 = v94:GetMoveVector()
            v93 = v94:IsMoveVectorCameraRelative()
        end
        if p90.vehicleController then
            local v95
            v92, v95 = p90.vehicleController:Update(v92, v93, p90.activeControlModule == u10)
        end
        if v93 then
            v92 = p90:calculateRawMoveVector(p90.humanoid, v92)
        end
        p90.inputMoveVector = v92
        if u8.VREnabled then
            v92 = p90:updateVRMoveVector(v92)
        end
        p90.moveFunction(u2.LocalPlayer, v92, false)
        local v96 = p90.humanoid
        local v97 = not p90.activeController:GetIsJumping() and p90.touchJumpController
        if v97 then
            v97 = p90.touchJumpController:GetIsJumping()
        end
        v96.Jump = v97
    end
end
function u1.updateVRMoveVector(p98, p99) --[[Anonymous function at line 518]]
    --[[
    Upvalues:
        [1] = u8
    --]]
    local v100 = workspace.CurrentCamera
    local v101 = (v100.Focus.Position - v100.CFrame.Position).Magnitude < 5
    if p99.Magnitude ~= 0 or (not v101 or (not u8.AvatarGestures or (not p98.humanoid or p98.humanoid.Sit))) then
        return p99
    end
    local v102 = u8:GetUserCFrame(Enum.UserCFrame.Head)
    local v103 = v102.Rotation + v102.Position * v100.HeadScale
    local v104 = -0.7 * p98.humanoid.RootPart.Size.Y / 2
    local v105 = (v100.CFrame * v103 * CFrame.new(0, v104, 0)).Position - p98.humanoid.RootPart.CFrame.Position
    local v106 = v105.x
    local v107 = v105.z
    return Vector3.new(v106, 0, v107)
end
function u1.OnHumanoidSeated(p108, p109, p110) --[[Anonymous function at line 543]]
    --[[
    Upvalues:
        [1] = u19
    --]]
    if p109 then
        if p110 and p110:IsA("VehicleSeat") then
            if not p108.vehicleController then
                p108.vehicleController = p108.vehicleController.new(u19)
            end
            p108.vehicleController:Enable(true, p110)
            return
        end
    elseif p108.vehicleController then
        p108.vehicleController:Enable(false, p110)
    end
end
function u1.OnCharacterAdded(u111, p112) --[[Anonymous function at line 558]]
    u111.humanoid = p112:FindFirstChildOfClass("Humanoid")
    while not u111.humanoid do
        p112.ChildAdded:wait()
        u111.humanoid = p112:FindFirstChildOfClass("Humanoid")
    end
    u111:UpdateTouchGuiVisibility()
    if u111.humanoidSeatedConn then
        u111.humanoidSeatedConn:Disconnect()
        u111.humanoidSeatedConn = nil
    end
    u111.humanoidSeatedConn = u111.humanoid.Seated:Connect(function(p113, p114) --[[Anonymous function at line 571]]
        --[[
        Upvalues:
            [1] = u111
        --]]
        u111:OnHumanoidSeated(p113, p114)
    end)
end
function u1.OnCharacterRemoving(p115, _) --[[Anonymous function at line 576]]
    p115.humanoid = nil
    p115:UpdateTouchGuiVisibility()
end
function u1.UpdateTouchGuiVisibility(p116) --[[Anonymous function at line 582]]
    --[[
    Upvalues:
        [1] = u5
    --]]
    if p116.touchGui then
        local v117 = p116.humanoid
        if v117 then
            v117 = u5.TouchControlsEnabled
        end
        p116.touchGui.Enabled = v117 and true or false
    end
end
function u1.SwitchToController(p118, p119) --[[Anonymous function at line 596]]
    --[[
    Upvalues:
        [1] = u19
    --]]
    if p119 then
        if not p118.controllers[p119] then
            p118.controllers[p119] = p119.new(u19)
        end
        if p118.activeController ~= p118.controllers[p119] then
            if p118.activeController then
                p118.activeController:Enable(false)
            end
            p118.activeController = p118.controllers[p119]
            p118.activeControlModule = p119
            p118:UpdateActiveControlModuleEnabled()
        end
    else
        if p118.activeController then
            p118.activeController:Enable(false)
        end
        p118.activeController = nil
        p118.activeControlModule = nil
    end
end
function u1.OnLastInputTypeChanged(p120, p121) --[[Anonymous function at line 624]]
    --[[
    Upvalues:
        [1] = u22
        [2] = u21
    --]]
    if u22 == p121 then
        warn("LastInputType Change listener called with current type.")
    end
    u22 = p121
    if u22 == Enum.UserInputType.Touch then
        local v122, v123 = p120:SelectTouchModule()
        if v123 then
            while not p120.touchControlFrame do
                wait()
            end
            p120:SwitchToController(v122)
        end
    elseif u21[u22] ~= nil then
        local v124 = p120:SelectComputerMovementModule()
        if v124 then
            p120:SwitchToController(v124)
        end
    end
    p120:UpdateTouchGuiVisibility()
end
function u1.OnComputerMovementModeChange(p125) --[[Anonymous function at line 651]]
    local v126, v127 = p125:SelectComputerMovementModule()
    if v127 then
        p125:SwitchToController(v126)
    end
end
function u1.OnTouchMovementModeChange(p128) --[[Anonymous function at line 658]]
    local v129, v130 = p128:SelectTouchModule()
    if v130 then
        while not p128.touchControlFrame do
            wait()
        end
        p128:SwitchToController(v129)
    end
end
function u1.CreateTouchGuiContainer(p131) --[[Anonymous function at line 668]]
    --[[
    Upvalues:
        [1] = u14
    --]]
    if p131.touchGui then
        p131.touchGui:Destroy()
    end
    p131.touchGui = Instance.new("ScreenGui")
    p131.touchGui.Name = "TouchGui"
    p131.touchGui.ResetOnSpawn = false
    p131.touchGui.ZIndexBehavior = Enum.ZIndexBehavior.Sibling
    p131:UpdateTouchGuiVisibility()
    if u14 then
        p131.touchGui.ClipToDeviceSafeArea = false
    end
    p131.touchControlFrame = Instance.new("Frame")
    p131.touchControlFrame.Name = "TouchControlFrame"
    p131.touchControlFrame.Size = UDim2.new(1, 0, 1, 0)
    p131.touchControlFrame.BackgroundTransparency = 1
    p131.touchControlFrame.Parent = p131.touchGui
    p131.touchGui.Parent = p131.playerGui
end
function u1.GetClickToMoveController(p132) --[[Anonymous function at line 691]]
    --[[
    Upvalues:
        [1] = u16
        [2] = u19
    --]]
    if not p132.controllers[u16] then
        p132.controllers[u16] = u16.new(u19)
    end
    return p132.controllers[u16]
end
return u1.new()