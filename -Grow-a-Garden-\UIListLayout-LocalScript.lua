-- Full Path: -Grow-a-Garden-\\UIListLayout-LocalScript.lua
local u1 = game:GetService("GamepadService")
local v2 = game:GetService("UserInputService")
if (v2.GamepadEnabled and not (v2.KeyboardEnabled or v2.MouseEnabled) and true or false) == true then
    function upd()
        --[[
        Upvalues:
            [1] = u1
        --]]
        if #script.Parent:GetChildren() == 2 then
            u1:DisableGamepadCursor()
        else
            u1:EnableGamepadCursor(script.Parent:FindFirstChildWhichIsA("Frame"))
        end
    end
    script.Parent.ChildAdded:Connect(function() --[[Anonymous function at line 17]]
        upd()
    end)
    script.Parent.ChildRemoved:Connect(function() --[[Anonymous function at line 20]]
        upd()
    end)
end