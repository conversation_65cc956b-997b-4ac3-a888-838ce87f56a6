-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Data\QuestData\QuestContainers\Daily-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
require(v1.Data.QuestData.Types)
local v2 = require(v1.Data.QuestData.Quests)
local v3 = require(v1.Data.QuestData.QuestRewards)
local u4 = {
    v2.Plant:Use({
        ["Target"] = 100,
        ["Arguments"] = { "Carrot" }
    }),
    v2.Plant:Use({
        ["Target"] = 5,
        ["Arguments"] = { "Watermelon" }
    }),
    v2.Plant:Use({
        ["Target"] = 5,
        ["Arguments"] = { "Pumpkin" }
    }),
    v2.Harvest:Use({
        ["Target"] = 50,
        ["Arguments"] = { "Strawberry" }
    }),
    v2.Harvest:Use({
        ["Target"] = 100,
        ["Arguments"] = { "Blueberry" }
    }),
    v2.Harvest:Use({
        ["Target"] = 25,
        ["Arguments"] = { "Apple" }
    }),
    v2.Harvest:Use({
        ["Target"] = 3,
        ["Arguments"] = { "Dragon Fruit" }
    }),
    v2.EarnSheckles:Use({
        ["Target"] = 25000
    })
}
local u5 = { v3["Seed Pack"]:Use({
        ["Amount"] = 1
    }), v3["Seed Pack"]:Use({
        ["Amount"] = 2
    }), v3["Seed Pack"]:Use({
        ["Amount"] = 3
    }) }
return {
    ["Type"] = "Daily",
    ["Display"] = function(_) --[[Function name: Display, line 61]]
        return "Daily Quests"
    end,
    ["Generate"] = function(_) --[[Function name: Generate, line 65]]
        --[[
        Upvalues:
            [1] = u4
            [2] = u5
        --]]
        local v6 = Random.new()
        local v7 = table.create(3)
        local v8 = table.clone(u4)
        v6:Shuffle(v8)
        for v9 = 1, 3 do
            v7[v9] = table.remove(v8, 1) or u4[v6:NextInteger(1, #u4)]
        end
        return {
            ["Quests"] = v7,
            ["Rewards"] = { u5[v6:NextInteger(1, #u5)] }
        }
    end
}