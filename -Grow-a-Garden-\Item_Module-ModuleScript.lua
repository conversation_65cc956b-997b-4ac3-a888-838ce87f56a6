-- Full Path: -Grow-a-Garden-\\Item_Module-ModuleScript.lua
local u1 = require(game.ReplicatedStorage.Modules.StringUtils)
local v2 = {}
local u3 = {
    {
        "Carrot",
        0.275,
        20,
        100
    },
    {
        "Strawberry",
        0.3,
        15,
        100
    },
    {
        "Blueberry",
        0.2,
        20,
        100
    },
    {
        "Orange Tulip",
        0.05,
        850,
        55
    },
    {
        "Tomato",
        0.5,
        30,
        100
    },
    {
        "Corn",
        2,
        40,
        100
    },
    {
        "Daffodil",
        0.2,
        1000,
        45
    },
    {
        "Watermelon",
        7,
        3000,
        70
    },
    {
        "Pumpkin",
        8,
        3400,
        80
    },
    {
        "Apple",
        3,
        275,
        50
    },
    {
        "Bamboo",
        4,
        4000,
        35
    },
    {
        "Coconut",
        14,
        400,
        70
    },
    {
        "Cactus",
        7,
        3400,
        100
    },
    {
        "Dragon Fruit",
        12,
        4750,
        100
    },
    {
        "Mango",
        15,
        6500,
        200
    },
    {
        "Grape",
        3,
        7850,
        200
    },
    {
        "Mushroom",
        25,
        151000,
        220
    },
    {
        "Pepper",
        5,
        8000,
        200
    },
    {
        "Cacao",
        8,
        11000,
        250
    },
    {
        "Beanstalk",
        10,
        20000,
        300
    },
    {
        "Chocolate Carrot",
        0.275,
        11000,
        100
    },
    {
        "Red Lollipop",
        4,
        50000,
        65
    },
    {
        "Blue Lollipop",
        1,
        50000,
        65
    },
    {
        "Candy Sunflower",
        1.5,
        80000,
        85
    },
    {
        "Easter Egg",
        3,
        2500,
        20
    },
    {
        "<PERSON> Blossom",
        3,
        100000,
        40
    },
    {
        "Peach",
        2,
        300,
        70
    },
    {
        "Raspberry",
        0.75,
        100,
        70
    },
    {
        "Pineapple",
        3,
        2000,
        70
    },
    {
        "Papaya",
        3,
        1000,
        60
    },
    {
        "Banana",
        1.5,
        1750,
        100
    },
    {
        "Passionfruit",
        3,
        3550,
        40
    },
    {
        "Soul Fruit",
        25,
        7750,
        200
    },
    {
        "Cursed Fruit",
        30,
        25750,
        200
    },
    {
        "Mega Mushroom",
        70,
        500,
        2000000
    },
    {
        "Cherry Blossom",
        3,
        500,
        400
    },
    {
        "Purple Cabbage",
        5,
        500,
        70
    },
    {
        "Lemon",
        1,
        350,
        50
    },
    {
        "Pear",
        3,
        500,
        20
    },
    {
        "Crocus",
        0.05,
        850,
        55
    },
    {
        "Pink Tulip",
        0.05,
        850,
        55
    },
    {
        "Succulent",
        3,
        500,
        150
    },
    {
        "Avocado",
        2,
        500,
        150
    },
    {
        "Cranberry",
        1,
        2000,
        50
    },
    {
        "Durian",
        8,
        5000,
        200
    },
    {
        "Eggplant",
        5,
        7500,
        220
    },
    {
        "Lotus",
        20,
        17000,
        650
    },
    {
        "Venus Fly Trap",
        10,
        25000,
        650
    },
    {
        "Nightshade",
        0.5,
        3500,
        100
    },
    {
        "Glowshroom",
        0.75,
        300,
        100
    },
    {
        "Mint",
        1,
        5250,
        150
    },
    {
        "Moonflower",
        2,
        9500,
        200
    },
    {
        "Starfruit",
        3,
        15000,
        250
    },
    {
        "Moonglow",
        7,
        20000,
        400
    },
    {
        "Moon Blossom",
        3,
        50000,
        400
    },
    {
        "Crimson Vine",
        1,
        1250,
        100
    },
    {
        "Moon Melon",
        8,
        18000,
        300
    },
    {
        "Blood Banana",
        1.5,
        6000,
        200
    },
    {
        "Celestiberry",
        2,
        8000,
        200
    },
    {
        "Moon Mango",
        15,
        25000,
        300
    }
}
local u4 = {
    { "Normal", 1000, 1 },
    { "Gold", 10, 20 },
    { "Rainbow", 1, 50 }
}
local u5 = {
    { "Gold", 4, 20 },
    { "Rainbow", 1, 50 }
}
local u6 = {
    { "Common", Color3.new(0.666667, 0.666667, 0.666667) },
    { "Uncommon", Color3.new(0.32549, 0.666667, 0.384314) },
    { "Rare", Color3.new(0.027451, 0.466667, 1) },
    { "Legendary", Color3.new(1, 1, 0) },
    { "Mythical", Color3.new(0.666667, 0.333333, 1) },
    { "Divine", Color3.fromRGB(255, 85, 0) },
    { "Prismatic", Color3.fromHSV(0, 1, 1), true }
}
local u7 = {
    {
        "Carrot Seed",
        "Carrot",
        1.1,
        "Common",
        3248692171,
        10,
        5,
        3269339926
    },
    {
        "Strawberry Seed",
        "Strawberry",
        2,
        "Common",
        3248695947,
        50,
        3,
        3269339924
    },
    {
        "Tomato Seed",
        "Tomato",
        4,
        "Rare",
        3248696942,
        800,
        1,
        3269339929
    },
    {
        "Blueberry Seed",
        "Blueberry",
        3,
        "Uncommon",
        3248690960,
        400,
        3,
        3269339931
    },
    {
        "Orange Tulip Seed",
        "Orange Tulip",
        3,
        "Common",
        3265927408,
        100,
        0,
        3269339925
    },
    {
        "Corn Seed",
        "Corn",
        5,
        "Rare",
        3248692845,
        1300,
        0,
        3269339919
    },
    {
        "Pumpkin Seed",
        "Pumpkin",
        10,
        "Legendary",
        3248695199,
        3000,
        0,
        3269339934
    },
    {
        "Daffodil Seed",
        "Daffodil",
        10,
        "Common",
        3265927978,
        100,
        0,
        3269339921
    },
    {
        "Watermelon Seed",
        "Watermelon",
        7,
        "Legendary",
        3248697546,
        2500,
        0,
        3269339904
    },
    {
        "Apple Seed",
        "Apple",
        14,
        "Legendary",
        3248716238,
        3250,
        0,
        3269339923
    },
    {
        "Bamboo Seed",
        "Bamboo",
        3,
        "Uncommon",
        3261009117,
        4000,
        3,
        3269339922
    },
    {
        "Purple Cabbage Seed",
        "Purple Cabbage",
        40,
        "Mythical",
        3273005969,
        5250,
        0,
        3273008000
    },
    {
        "Coconut Seed",
        "Coconut",
        20,
        "Mythical",
        3248744789,
        4750,
        0,
        3269339918
    },
    {
        "Cactus Seed",
        "Cactus",
        100,
        "Legendary",
        3260940714,
        17000,
        0,
        3269339920
    },
    {
        "Eggplant Seed",
        "Eggplant",
        16,
        "Mythical",
        3273006109,
        30000,
        0,
        3273007733
    },
    {
        "Dragon Fruit Seed",
        "Dragon Fruit",
        40,
        "Mythical",
        3253012192,
        27000,
        0,
        3269339909
    },
    {
        "Mango Seed",
        "Mango",
        70,
        "Mythical",
        3259333414,
        50000,
        0,
        3269339912
    },
    {
        "Grape Seed",
        "Grape",
        20,
        "Mythical",
        3261068725,
        4750,
        0,
        3269339905
    },
    {
        "Pepper Seed",
        "Pepper",
        2,
        "Rare",
        3277675404,
        25000,
        3,
        3277675837
    },
    {
        "Cacao Seed",
        "Cacao",
        2,
        "Rare",
        3282870834,
        50,
        3,
        3282871246
    },
    {
        "Beanstalk Seed",
        "Beanstalk",
        2,
        "Rare",
        3284390402,
        50,
        3,
        3284390954
    },
    {
        "Red Lollipop Seed",
        "Red Lollipop",
        1.5,
        "Rare",
        3268186603,
        100,
        0,
        nil
    },
    {
        "Blue Lollipop Seed",
        "Blue Lollipop",
        1.5,
        "Rare",
        3268186832,
        100,
        0,
        nil
    },
    {
        "Candy Sunflower Seed",
        "Candy Sunflower",
        1.5,
        "Legendary",
        3268187175,
        500,
        0,
        nil
    },
    {
        "Candy Blossom Seed",
        "Candy Blossom",
        10,
        "Divine",
        3268187638,
        8000,
        0,
        nil
    },
    {
        "Peach Seed",
        "Peach",
        10,
        "Rare",
        0,
        10000,
        0,
        nil
    },
    {
        "Raspberry Seed",
        "Raspberry",
        10,
        "Rare",
        0,
        10000,
        0,
        nil
    },
    {
        "Pineapple Seed",
        "Pineapple",
        10,
        "Rare",
        0,
        10000,
        0,
        nil
    },
    {
        "Papaya Seed",
        "Papaya",
        10,
        "Rare",
        3265927598,
        10000,
        0,
        nil
    },
    {
        "Banana Seed",
        "Banana",
        10,
        "Rare",
        3269001250,
        8000,
        0,
        nil
    },
    {
        "Passionfruit Seed",
        "Passionfruit",
        10,
        "Rare",
        3265927598,
        10000,
        0,
        nil
    },
    {
        "Soul Fruit Seed",
        "Soul Fruit",
        10000000,
        "Robux",
        1,
        0,
        0,
        nil
    },
    {
        "Cursed Fruit Seed",
        "Cursed Fruit",
        10000000,
        "Robux",
        1,
        0,
        0,
        nil
    },
    {
        "Cranberry Seed",
        "Cranberry",
        10,
        "Rare",
        0,
        10000,
        0,
        nil
    },
    {
        "Durian Seed",
        "Durian",
        2,
        "Common",
        0,
        50,
        3,
        nil
    },
    {
        "Lotus Seed",
        "Lotus",
        2,
        "Common",
        0,
        50,
        3,
        nil
    },
    {
        "Venus Fly Trap Seed",
        "Venus Fly Trap",
        10,
        "Rare",
        0,
        10000,
        0,
        nil
    },
    {
        "Nightshade Seed",
        "Nightshade",
        2,
        "Rare",
        0,
        50,
        3,
        nil
    },
    {
        "Glowshroom Seed",
        "Glowshroom",
        2,
        "Rare",
        0,
        50,
        3,
        nil
    },
    {
        "Mint Seed",
        "Mint",
        2,
        "Rare",
        0,
        50,
        3,
        nil
    },
    {
        "Moonflower Seed",
        "Moonflower",
        2,
        "Rare",
        0,
        50,
        3,
        nil
    },
    {
        "Starfruit Seed",
        "Starfruit",
        2,
        "Rare",
        0,
        50,
        3,
        nil
    },
    {
        "Moonglow Seed",
        "Moonglow",
        2,
        "Rare",
        0,
        50,
        3,
        nil
    },
    {
        "Moon Blossom Seed",
        "Moon Blossom",
        2,
        "Rare",
        0,
        50,
        3,
        nil
    },
    {
        "Mega Mushroom Seed",
        "Mega Mushroom",
        10,
        "Divine",
        0,
        10000,
        0,
        nil
    },
    {
        "Mushroom Seed",
        "Mushroom",
        10,
        "Rare",
        3273973729,
        10000,
        0,
        3273973524
    },
    {
        "Succulent Seed",
        "Succulent",
        2,
        "Common",
        0,
        50,
        3,
        nil
    },
    {
        "Lemon Seed",
        "Lemon",
        4,
        "Rare",
        0,
        800,
        1,
        nil
    },
    {
        "Cherry Blossom Seed",
        "Cherry Blossom",
        2,
        "Common",
        0,
        50,
        3,
        nil
    },
    {
        "Avocado Seed",
        "Avocado",
        2,
        "Rare",
        0,
        50,
        3,
        nil
    },
    {
        "Pink Tulip Seed",
        "Pink Tulip",
        15,
        "Common",
        3265927598,
        100,
        0,
        nil
    },
    {
        "Crocus Seed",
        "Crocus",
        10,
        "Common",
        3265927895,
        100,
        0,
        nil
    },
    {
        "Crimson Vine Seed",
        "Crimson Vine",
        10,
        "Common",
        3265927895,
        100,
        0,
        nil
    },
    {
        "Moon Melon Seed",
        "Moon Melon",
        10,
        "Common",
        3265927895,
        100,
        0,
        nil
    },
    {
        "Blood Banana Seed",
        "Blood Banana",
        10,
        "Common",
        3265927895,
        100,
        0,
        nil
    },
    {
        "Moon Mango Seed",
        "Moon Mango",
        10,
        "Common",
        3265927895,
        100,
        0,
        nil
    },
    {
        "Celestiberry Seed",
        "Celestiberry",
        10,
        "Common",
        3265927895,
        100,
        0,
        nil
    }
}
function v2.Return_All_Data() --[[Anonymous function at line 1006]]
    --[[
    Upvalues:
        [1] = u3
    --]]
    return u3
end
function v2.Return_All_Seeds() --[[Anonymous function at line 1009]]
    --[[
    Upvalues:
        [1] = u7
    --]]
    return u7
end
function v2.Return_Seed_Data(p8) --[[Anonymous function at line 1012]]
    --[[
    Upvalues:
        [1] = u7
    --]]
    for _, v9 in pairs(u7) do
        if v9[1] == p8 then
            return v9
        end
    end
end
function v2.Return_Rarity_Data(p10) --[[Anonymous function at line 1019]]
    --[[
    Upvalues:
        [1] = u6
    --]]
    for _, v11 in pairs(u6) do
        if v11[1] == p10 then
            return v11
        end
    end
end
function v2.Return_Multiplier(p12) --[[Anonymous function at line 1026]]
    --[[
    Upvalues:
        [1] = u4
    --]]
    for _, v13 in pairs(u4) do
        if v13[1] == p12 then
            return v13[3]
        end
    end
    return 0
end
function v2.Return_Random_Rarity(p14) --[[Anonymous function at line 1038]]
    --[[
    Upvalues:
        [1] = u4
    --]]
    local v15 = math.max(p14, 1)
    local v16 = 0
    for _, v17 in u4 do
        v16 = v16 + v17[2] / (v17[1] == "Normal" and v15 and v15 or 1)
    end
    local v18 = math.random(0, v16)
    local v19 = 0
    for _, v20 in u4 do
        v19 = v19 + v20[2] / (v20[1] == "Normal" and v15 and v15 or 1)
        if v18 <= v19 then
            return v20[1]
        end
    end
end
function v2.Return_Random_Super_Rarity() --[[Anonymous function at line 1061]]
    --[[
    Upvalues:
        [1] = u5
    --]]
    local v21 = 0
    for _, v22 in ipairs(u5) do
        v21 = v21 + v22[2]
    end
    local v23 = math.random(1, v21)
    local v24 = 0
    for _, v25 in ipairs(u5) do
        v24 = v24 + v25[2]
        if v23 <= v24 then
            return v25[1]
        end
    end
end
function v2.Return_Data(p26) --[[Anonymous function at line 1079]]
    --[[
    Upvalues:
        [1] = u1
        [2] = u3
    --]]
    local v27 = u1:StipFlavourText(p26)
    for _, v28 in u3 do
        if v28[1] == v27 then
            return v28
        end
    end
    warn((("Item_Module.Return_Data | Could not find data for item:\"%*\""):format(p26)))
end
return v2