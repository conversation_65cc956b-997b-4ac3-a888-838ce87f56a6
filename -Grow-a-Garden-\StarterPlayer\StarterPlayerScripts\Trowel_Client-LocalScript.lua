-- Full Path: -Grow-a-Garden-\StarterPlayer\StarterPlayerScripts\Trowel_Client-LocalScript.lua
local v1 = game:GetService("Players")
local u2 = game:GetService("UserInputService")
local u3 = game:GetService("CollectionService")
local v4 = game:GetService("ReplicatedStorage")
local u5 = game:GetService("TweenService")
local u6 = game:GetService("RunService")
game:GetService("ProximityPromptService")
local u7 = v4:WaitForChild("GameEvents"):WaitForChild("TrowelRemote")
local u8 = require(v4.Modules.GetFarm)
local u9 = require(v4.Modules.Notification)
local u10 = require(v4.Modules.EffectController.Libraries.Default)
local u11 = require(v4.Modules.ProximityPromptController)
local u12 = v1.LocalPlayer
local u13 = workspace.CurrentCamera
local u14 = u12.PlayerGui:WaitFor<PERSON>hild("Trowel_Client")
local v15 = u14.Frame.CancelFrame:WaitForChild("Cancel")
local v16 = u14.Frame.RotateFrame:WaitForChild("Rotate")
local u17 = nil
local u18 = nil
local u19 = nil
local u20 = nil
local u21 = script.Highlight
local u22 = {}
local u23 = nil
local u24 = false
local function u27(p25) --[[Anonymous function at line 53]]
    --[[
    Upvalues:
        [1] = u8
        [2] = u12
    --]]
    local v26 = u8(u12)
    if v26 then
        if p25:IsDescendantOf(v26) then
            if p25:FindFirstChild("Grow") then
                if p25:FindFirstChild("Fruits") then
                    return p25:FindFirstChild("Fruit_Spawn") and true or false
                else
                    return false
                end
            else
                return false
            end
        else
            return false
        end
    else
        return false
    end
end
local function u32(p28) --[[Anonymous function at line 62]]
    --[[
    Upvalues:
        [1] = u13
        [2] = u3
    --]]
    local v29 = u13:ViewportPointToRay(p28.X, p28.Y)
    local v30 = RaycastParams.new()
    v30.FilterType = Enum.RaycastFilterType.Exclude
    v30.FilterDescendantsInstances = { u3:GetTagged("ShovelIgnore") }
    local v31 = workspace:Raycast(v29.Origin, v29.Direction * 500, v30)
    if v31 and v31.Instance then
        return v31
    end
end
local function u37(p33) --[[Anonymous function at line 76]]
    --[[
    Upvalues:
        [1] = u13
        [2] = u8
        [3] = u12
    --]]
    local v34 = u13:ViewportPointToRay(p33.X, p33.Y)
    local v35 = RaycastParams.new()
    v35.FilterType = Enum.RaycastFilterType.Include
    v35.FilterDescendantsInstances = { u8(u12).Important.Plant_Locations:GetChildren() }
    local v36 = workspace:Raycast(v34.Origin, v34.Direction * 500, v35)
    if v36 and v36.Instance then
        return v36
    end
end
local function v38() --[[Anonymous function at line 90]]
    --[[
    Upvalues:
        [1] = u17
        [2] = u20
    --]]
    if u17 then
        u20 = u20 * CFrame.Angles(0, 0.7853981633974483, 0)
    end
end
local function u41() --[[Anonymous function at line 95]]
    --[[
    Upvalues:
        [1] = u17
        [2] = u24
        [3] = u18
        [4] = u12
        [5] = u7
        [6] = u9
        [7] = u22
        [8] = u21
    --]]
    if u17 and (not u24 and u18) then
        local v39 = u17
        u17 = nil
        if u12.Character:FindFirstChildOfClass("Humanoid") then
            u12.Character:FindFirstChildOfClass("Humanoid"):UnequipTools()
        end
        u7:InvokeServer("Cancel", u18, v39)
        u9:CreateNotification((("Canceled moving %*!"):format(v39.Name)))
        for _, v40 in u22 or {} do
            v40.CanCollide = true
            v40.CanQuery = true
        end
        u21.Adornee = nil
    end
end
u6.RenderStepped:Connect(function() --[[Anonymous function at line 119]]
    --[[
    Upvalues:
        [1] = u14
        [2] = u17
        [3] = u18
        [4] = u12
        [5] = u23
        [6] = u41
        [7] = u2
        [8] = u37
        [9] = u20
        [10] = u19
        [11] = u32
        [12] = u27
        [13] = u21
        [14] = u5
    --]]
    u14.Enabled = u17 ~= nil
    local v42 = u12.Character:FindFirstChildOfClass("Tool")
    local v43
    if v42 then
        v43 = v42.Name:match("Trowel")
    else
        v43 = v42
    end
    u18 = v43 and v42
    if u18 then
        if not u23 then
            u23 = u18.Destroying:Once(function() --[[Anonymous function at line 130]]
                --[[
                Upvalues:
                    [1] = u23
                    [2] = u41
                --]]
                u23:Disconnect()
                u23 = nil
                u41()
            end)
        end
        local v44 = u2:GetMouseLocation()
        if u17 then
            local v45 = u37(v44)
            if v45 and v45.Position then
                u17:PivotTo(CFrame.new(v45.Position) * CFrame.new(0, 3, 0) * u20)
            else
                u17:PivotTo(u19)
            end
        else
            local v46 = u32(v44)
            if v46 then
                local v47 = v46.Instance.Parent
                if u27(v47) then
                    if u21.Adornee ~= v47 then
                        u21.FillTransparency = 1
                        u5:Create(u21, TweenInfo.new(0.25), {
                            ["FillTransparency"] = 0.65
                        }):Play()
                    end
                    u21.Adornee = v47
                else
                    u21.Adornee = nil
                end
            else
                return
            end
        end
    else
        if u17 then
            u41()
        end
        u21.Adornee = nil
        return
    end
end)
v15.Activated:Connect(u41)
v16.Activated:Connect(v38)
local function v64(p48, p49) --[[Anonymous function at line 178]]
    --[[
    Upvalues:
        [1] = u12
        [2] = u24
        [3] = u9
        [4] = u2
        [5] = u17
        [6] = u37
        [7] = u8
        [8] = u21
        [9] = u20
        [10] = u19
        [11] = u6
        [12] = u5
        [13] = u7
        [14] = u18
        [15] = u10
        [16] = u13
        [17] = u22
        [18] = u32
        [19] = u27
    --]]
    if u12.Character then
        local v50 = u12.Character:FindFirstChildOfClass("Tool")
        local v51
        if v50 then
            v51 = v50.Name:match("Trowel")
        else
            v51 = v50
        end
        if v51 and v50 and not p49 then
            if u24 then
                u9:CreateNotification("Please wait before trying again!")
                return
            else
                local v52 = p48 or u2:GetMouseLocation()
                if u17 then
                    local u53 = u37(v52)
                    if u53 and u53.Instance.Name == "Can_Plant" then
                        local v54 = RaycastParams.new()
                        v54.FilterType = Enum.RaycastFilterType.Include
                        v54.FilterDescendantsInstances = { u8(u12).Important.Plants_Physical:GetChildren() }
                        if not workspace:Raycast(u53.Position + Vector3.new(0, 20, 0), Vector3.new(0, -25, 0), v54) then
                            u21.Adornee = nil
                            u24 = true
                            task.spawn(function() --[[Anonymous function at line 215]]
                                --[[
                                Upvalues:
                                    [1] = u53
                                    [2] = u20
                                    [3] = u19
                                    [4] = u17
                                    [5] = u6
                                    [6] = u5
                                    [7] = u7
                                    [8] = u18
                                    [9] = u9
                                    [10] = u10
                                    [11] = u13
                                    [12] = u24
                                --]]
                                local v55 = CFrame.new(u53.Position) * CFrame.new(0, 3, 0) * u20
                                local v56 = CFrame.new(u53.Position.X, u19.Y, u53.Position.Z) * u20
                                local v57 = u17
                                v57:PivotTo(v55)
                                local v58 = 0
                                while v58 < 0.15 do
                                    v58 = v58 + u6.Heartbeat:Wait()
                                    v57:PivotTo(v55:Lerp(v56, (u5:GetValue(v58 / 0.1, Enum.EasingStyle.Quad, Enum.EasingDirection.In))))
                                end
                                u7:InvokeServer("Place", u18, v57, v56)
                                u9:CreateNotification((("Successfully moved %*!"):format(v57.Name)))
                                u10:SetScale(u10:CreateEffect({
                                    ["Object"] = script.Place,
                                    ["Emit"] = true,
                                    ["Position"] = u53.Position,
                                    ["DebrisTime"] = 1
                                }), 3, {})
                                u10:PlaySound(script.PlantTrowel, u13)
                                task.wait(1)
                                u24 = false
                            end)
                            for _, v59 in u22 or {} do
                                v59.CanCollide = true
                                v59.CanQuery = true
                            end
                            u17 = nil
                        end
                    else
                        u9:CreateNotification("Can\'t place here!")
                        return
                    end
                else
                    local v60 = u32(v52)
                    if v60 then
                        local v61 = v60.Instance.Parent
                        if u27(v61) then
                            if u18 and u18.Name then
                                u24 = true
                                u7:InvokeServer("Pickup", u18, v61)
                                u24 = false
                                u9:CreateNotification((("Picked up %*!"):format(v61.Name)))
                                u19 = v61:GetPivot()
                                u20 = u19.Rotation
                                for _, v62 in v61:GetDescendants() do
                                    if v62:IsA("BasePart") and v62.CanCollide then
                                        v62.CanCollide = false
                                        v62.CanQuery = false
                                        local v63 = u22
                                        table.insert(v63, v62)
                                    elseif v62:IsA("BasePart") then
                                        v62.CanQuery = false
                                    end
                                end
                                u17 = v61
                            else
                                u9:CreateNotification("Trowel not detected, please try again or report if this is an error!")
                            end
                        else
                            u9:CreateNotification("Can\'t move this!")
                            return
                        end
                    else
                        return
                    end
                end
            end
        end
    end
end
if u2.TouchEnabled then
    u2.TouchTapInWorld:Connect(v64)
else
    u12:GetMouse().Button1Down:Connect(v64)
end
local u65 = v16:FindFirstChild("TextLabel")
local u66 = v15:FindFirstChild("TextLabel")
local u67 = u14.Frame.RotateFrame.RotateBG
local u68 = u14.Frame.CancelFrame.CancelBG
local u69 = u67:FindFirstChild("RotateIcon")
local u70 = u68:FindFirstChild("CancelIcon")
local function v75() --[[Anonymous function at line 327]]
    --[[
    Upvalues:
        [1] = u65
        [2] = u66
        [3] = u69
        [4] = u70
        [5] = u2
        [6] = u67
        [7] = u68
    --]]
    if u65 and u66 then
        if u69 and u70 then
            local v71 = u2.TouchEnabled
            local v72 = u2.GamepadEnabled
            if v71 then
                u65.Text = "Rotate"
                u66.Text = "Cancel"
                u69.Visible = false
                u70.Visible = false
                return
            elseif v72 then
                local v73 = u2:GetImageForKeyCode(Enum.KeyCode.DPadRight)
                local v74 = u2:GetImageForKeyCode(Enum.KeyCode.ButtonB)
                u65.Text = "Rotate"
                u66.Text = "Cancel"
                if v73 then
                    u69.Image = v73
                    u67.Visible = true
                else
                    u67.Visible = false
                end
                if v74 then
                    u70.Image = v74
                    u68.Visible = true
                else
                    u68.Visible = false
                end
            else
                u65.Text = "[R] Rotate"
                u66.Text = "[C] Cancel"
                u67.Visible = false
                u68.Visible = false
                return
            end
        else
            return
        end
    else
        return
    end
end
v75()
u2.LastInputTypeChanged:Connect(v75)
local function u84() --[[Anonymous function at line 378]]
    --[[
    Upvalues:
        [1] = u12
        [2] = u8
        [3] = u11
        [4] = u2
        [5] = u17
        [6] = u20
        [7] = u41
        [8] = u84
    --]]
    local v76 = false
    while not v76 do
        local v77 = u12.Character:FindFirstChildOfClass("Tool")
        v77 = v77
        local v78
        if v77 then
            v78 = v77.Name:match("Trowel")
        else
            v78 = v77
        end
        local v79 = v78 and v77
        u8(u12)
        local u80 = nil
        if u12.Character and v79 then
            v79.Equipped:Connect(function() --[[Anonymous function at line 391]]
                --[[
                Upvalues:
                    [1] = u11
                    [2] = u80
                    [3] = u2
                    [4] = u17
                    [5] = u20
                    [6] = u41
                --]]
                u11:AddDisabler("Trowel")
                u80 = u2.InputBegan:Connect(function(p81, p82) --[[Anonymous function at line 395]]
                    --[[
                    Upvalues:
                        [1] = u17
                        [2] = u20
                        [3] = u41
                    --]]
                    if p82 then
                        return
                    elseif p81.KeyCode == Enum.KeyCode.R or p81.KeyCode == Enum.KeyCode.DPadRight then
                        if u17 then
                            u20 = u20 * CFrame.Angles(0, 0.7853981633974483, 0)
                        end
                    else
                        if p81.KeyCode == Enum.KeyCode.C or p81.KeyCode == Enum.KeyCode.ButtonB then
                            u41()
                        end
                        return
                    end
                end)
            end)
            v79.Unequipped:Connect(function() --[[Anonymous function at line 407]]
                --[[
                Upvalues:
                    [1] = u11
                    [2] = u41
                    [3] = u80
                --]]
                u11:RemoveDisabler("Trowel")
                u41()
                if u80 then
                    local v83 = u80
                    if typeof(v83) == "RBXScriptConnection" then
                        u80:Disconnect()
                    end
                end
            end)
            v79.Destroying:Once(u84)
            v76 = true
        end
        task.wait(0.1)
    end
end
task.spawn(u84)