-- Full Path: -Grow-a-Garden-\\GiftData-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
local v2 = require(v1.Item_Module)
local v3 = require(v1.Data.GearData)
local v4 = require(v1.Data.EasterData)
local v5 = {
    ["Open 1 - Exotic Seed Pack"] = {
        ["GiftId"] = 3269033223,
        ["NormalId"] = 3267528421,
        ["Display"] = "1 Exotic Seed Pack"
    },
    ["Open 3 - Exotic Seed Pack"] = {
        ["GiftId"] = 3269033336,
        ["NormalId"] = 3267548052,
        ["Display"] = "3 Exotic Seed Packs"
    },
    ["Open 10 - Exotic Seed Pack"] = {
        ["GiftId"] = 3269033529,
        ["NormalId"] = 3267548161,
        ["Display"] = "10 Exotic Seed Packs"
    },
    ["Open 1 - Bug Egg"] = {
        ["GiftId"] = 3277815193,
        ["NormalId"] = 3278426597,
        ["Display"] = "1 Bug Egg"
    },
    ["Open 3 - Bug Egg"] = {
        ["GiftId"] = 3277815350,
        ["NormalId"] = 3277814538,
        ["Display"] = "3 Bug Eggs"
    },
    ["Open 10 - Bug Egg"] = {
        ["GiftId"] = 3277815654,
        ["NormalId"] = 3277814625,
        ["Display"] = "10 Bug Eggs"
    },
    ["10 Premium Seed Sacks"] = {
        ["GiftId"] = 3273679009,
        ["NormalId"] = 3273679012,
        ["Display"] = "10 Premium Seed Sacks"
    },
    ["10 Premium Night Seeds"] = {
        ["GiftId"] = 3282733014,
        ["NormalId"] = 3282157462,
        ["Display"] = "10 Premium Night Seeds"
    },
    ["10 Premium Night Eggs"] = {
        ["GiftId"] = 3282661439,
        ["NormalId"] = 3282157312,
        ["Display"] = "10 Premium Night Eggs"
    },
    ["+1 Exclusive Cosmetic Crate"] = {
        ["GiftId"] = 3290672926,
        ["NormalId"] = 3290672626,
        ["Display"] = "1 Exclusive Cosmetic Crate"
    },
    ["+3 Exclusive Cosmetic Crates"] = {
        ["GiftId"] = 3290673007,
        ["NormalId"] = 3290672712,
        ["Display"] = "3 Exclusive Cosmetic Crates"
    },
    ["+10 Exclusive Cosmetic Crates"] = {
        ["GiftId"] = 3290673073,
        ["NormalId"] = 3290672848,
        ["Display"] = "10 Exclusive Cosmetic Crates"
    },
    ["Sheckles 1"] = {
        ["GiftId"] = 3269363716,
        ["NormalId"] = 3250137172,
        ["Display"] = "100\194\162"
    },
    ["Sheckles 2"] = {
        ["GiftId"] = 3269364749,
        ["NormalId"] = 3250137261,
        ["Display"] = "250\194\162"
    },
    ["Sheckles 3"] = {
        ["GiftId"] = 3269364968,
        ["NormalId"] = 3250137355,
        ["Display"] = "1,000\194\162"
    },
    ["Sheckles 4"] = {
        ["GiftId"] = 3269365441,
        ["NormalId"] = 3250139018,
        ["Display"] = "5,000\194\162"
    }
}
for _, v6 in v2.Return_All_Seeds() do
    if v6[8] and v6[5] then
        v5[("Seed-%*"):format(v6[1])] = {
            ["GiftId"] = v6[8],
            ["NormalId"] = v6[5],
            ["Display"] = v6[1]
        }
    end
end
for v7, v8 in v3 do
    if v8.GiftPurchaseID and v8.PurchaseID then
        v5[("Gear-%*"):format(v7)] = {
            ["GiftId"] = v8.GiftPurchaseID,
            ["NormalId"] = v8.PurchaseID,
            ["Display"] = v8.GearName
        }
    end
end
for v9, v10 in v4 do
    if v10.GiftPurchaseID and v10.PurchaseID then
        v5[("EasterSeed-%*"):format(v9)] = {
            ["GiftId"] = v10.GiftPurchaseID,
            ["NormalId"] = v10.PurchaseID,
            ["Display"] = v10.SeedName
        }
    end
end
return v5