-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\StringUtils-ModuleScript.lua
local u3 = {
    ["StipFlavourText"] = function(_, p1) --[[Function name: StipFlavourText, line 4]]
        if p1 and p1 ~= "" then
            return p1:gsub("%b[]", ""):gsub("^%s*(.-)%s*$", "%1")
        end
        warn("StringUtils:StipFlavourText | No item name passed!")
        return ""
    end,
    ["FormatGearName"] = function(_, p2) --[[Function name: FormatGearName, line 16]]
        --[[
        Upvalues:
            [1] = u3
        --]]
        if p2 and p2 ~= "" then
            return u3:StipFlavourText(p2):gsub("%s+", ""):gsub("(%u)", " %1"):gsub("^%s+", "")
        end
        warn("StringUtils:FormatGearName | No gear name passed!")
        return ""
    end
}
return u3