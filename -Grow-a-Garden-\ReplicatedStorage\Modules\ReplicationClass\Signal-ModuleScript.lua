-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\ReplicationClass\Signal-ModuleScript.lua
local v1 = Instance.new("BindableEvent")
local u2 = false
v1.Event:Connect(function() --[[Anonymous function at line 7]]
    --[[
    Upvalues:
        [1] = u2
    --]]
    u2 = true
end)
v1:Fire()
v1:Destroy()
local v3 = u2 == false and true or false
local v4 = require(script.Deferred)
local v5 = require(script.Immediate)
local v6 = v3 and v4 and v4 or v5
v6.Deferred = v4
v6.Immediate = v5
return v6