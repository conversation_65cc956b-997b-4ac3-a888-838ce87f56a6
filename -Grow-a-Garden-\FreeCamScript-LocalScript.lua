-- Full Path: -Grow-a-Garden-\\FreeCamScript-LocalScript.lua
local v1 = game:GetService("ReplicatedStorage")
local v2 = game:GetService("Players").LocalPlayer
local v3 = require(v1.Data.FreeCamUsers)
local v4 = game:GetService("UserInputService")
if table.find(v3, v2.Name) then
    local u5 = require(script.Freecam)
    local u6 = false
    v4.InputBegan:Connect(function(p7) --[[Anonymous function at line 16]]
        --[[
        Upvalues:
            [1] = u6
            [2] = u5
        --]]
        if p7.KeyCode == Enum.KeyCode.P or p7.KeyCode == Enum.KeyCode.ButtonX then
            u6 = not u6
            if u6 then
                u5:StartFreecam(true, false)
                return
            end
            u5:StopFreecam(true)
        end
    end)
end