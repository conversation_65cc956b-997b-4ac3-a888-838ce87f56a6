-- Full Path: -Grow-a-Garden-\\Main-ModuleScript.lua
local u1 = {
    ["OpenClose"] = nil,
    ["IsOpen"] = false,
    ["StateChanged"] = Instance.new("BindableEvent"),
    ["ModuleName"] = "Backpack",
    ["KeepVRTopbarOpen"] = true,
    ["VRIsExclusive"] = true,
    ["VRClosesNonExclusive"] = true
}
local u2 = script:GetAttribute("TextSize")
local v3 = script:GetAttribute("BackgroundTransparency")
local u4 = script:GetAttribute("BackgroundColor")
local u5 = script:GetAttribute("DraggableColor")
local u6 = script:GetAttribute("EquippedColor")
local u7 = script:GetAttribute("SlotLockedTransparency")
local u8 = script:GetAttribute("BorderColor")
local v9 = { Enum.KeyCode.Backquote }
local v10 = script:GetAttribute("FullSlots")
local v11 = script:GetAttribute("EmptySlots")
local v12 = script:Get<PERSON><PERSON>ribut<PERSON>("SearchBoxColor")
local v13 = script:Get<PERSON><PERSON>ribute("SearchBoxTransparency")
local v14 = script:WaitForChild("Api")
v14.Parent = game.ReplicatedStorage
local u15 = game:GetService("ReplicatedStorage"):WaitForChild("GameEvents"):WaitForChild("Favorite_Item")
local u16 = nil
local u17 = true
local function v21() --[[Anonymous function at line 72]]
    local v18 = Instance.new("ScreenGui", game.Players.LocalPlayer.PlayerGui)
    local v19 = Instance.new("Frame", v18)
    v19.BackgroundTransparency = 1
    v19.Size = UDim2.new(1, 0, 1, 0)
    local v20 = v19.AbsoluteSize
    v18:Destroy()
    return v20
end
local u22 = Enum.KeyCode.Zero.Value
local u23 = Enum.KeyCode.Backspace.Value
local u24 = {
    [Enum.UserInputType.Gamepad1] = true,
    [Enum.UserInputType.Gamepad2] = true,
    [Enum.UserInputType.Gamepad3] = true,
    [Enum.UserInputType.Gamepad4] = true,
    [Enum.UserInputType.Gamepad5] = true,
    [Enum.UserInputType.Gamepad6] = true,
    [Enum.UserInputType.Gamepad7] = true,
    [Enum.UserInputType.Gamepad8] = true
}
local u25 = game:GetService("UserInputService")
local v26 = game:GetService("Players")
local v27 = game:GetService("ReplicatedStorage")
local u28 = game:GetService("StarterGui")
local u29 = game:GetService("GuiService")
local v30 = v26.LocalPlayer.PlayerGui
local v31 = Instance.new("ScreenGui", v30)
v31.DisplayOrder = 120
v31.IgnoreGuiInset = true
v31.ResetOnSpawn = false
v31.Name = "BackpackGui"
local u32 = game:GetService("ContextActionService")
local v33 = game:GetService("RunService")
local u34 = game:GetService("VRService")
local u35 = require(script.Utility)
require(script.GameTranslator)
local v36 = require(v27.Modules.Icon)
local u37 = u29:IsTenFootInterface()
local u38
if u37 then
    u2 = 24
    u38 = 100
else
    u38 = 60
end
local u39 = false
local v40 = u25.TouchEnabled
if v40 then
    v40 = v21().X < 1024
end
local u41 = v26.LocalPlayer
local u42 = nil
local u43 = nil
local u44 = nil
local u45 = nil
local u46 = nil
local u47 = nil
local u48 = u41.Character or u41.CharacterAdded:Wait()
local u49 = u48:FindFirstChildOfClass("Humanoid")
local u50 = u41:WaitForChild("Backpack")
local u51 = v36.new()
u51:setImage("rbxasset://textures/ui/TopBar/inventoryOff.png", "deselected")
u51:setImage("rbxasset://textures/ui/TopBar/inventoryOn.png", "selected")
u51:bindToggleKey(v9[1], v9[2])
u51:setName("InventoryIcon")
u51:setImageScale(1.12)
u51:setOrder(-5)
u51:setCaption("Toggle the backpack.")
u51.deselectWhenOtherIconSelected = false
local u52 = {}
local u53 = nil
local u54 = {}
local u55 = {}
local u56 = {}
local u57 = 0
local u58 = false
local u59 = false
local u60 = false
local u61 = false
local u62 = {}
local u63 = {}
local u64 = false
local u65 = 0
local u66 = u34.VREnabled
local u67 = u66 and v11 and v11 or (v40 and 5 or v10)
local u68 = u66 and 3 or (v40 and 2 or 4)
local u69 = nil
local function u73(p70, p71) --[[Anonymous function at line 188]]
    --[[
    Upvalues:
        [1] = u2
    --]]
    local v72 = Instance.new(p70)
    v72.Name = p71
    v72.BackgroundColor3 = Color3.new(0, 0, 0)
    v72.BackgroundTransparency = 1
    v72.BorderColor3 = Color3.new(0, 0, 0)
    v72.BorderSizePixel = 0
    v72.Size = UDim2.new(1, 0, 1, 0)
    if p70:match("Text") then
        v72.TextColor3 = Color3.new(1, 1, 1)
        v72.Text = ""
        v72.FontFace = script:GetAttribute("LabelFont")
        v72.TextSize = u2
        v72.TextWrapped = true
        if p70 == "TextButton" then
            v72.FontFace = script:GetAttribute("SlotFont")
        end
    end
    return v72
end
local function u79() --[[Anonymous function at line 233]]
    --[[
    Upvalues:
        [1] = u44
        [2] = u67
        [3] = u57
        [4] = u52
    --]]
    local v74 = u44.Visible
    local v75 = v74 and u67 or u57
    local _ = v75 >= 1
    local v76 = 0
    for v77 = 1, u67 do
        local v78 = u52[v77]
        if v78.Tool or v74 then
            v76 = v76 + 1
            v78:Readjust(v76, v75)
            v78.Frame.Visible = true
        else
            v78.Frame.Visible = false
        end
    end
end
local function u84() --[[Anonymous function at line 251]]
    --[[
    Upvalues:
        [1] = u46
        [2] = u38
        [3] = u47
    --]]
    local v80 = u46.AbsoluteSize.X / (u38 + 5)
    local v81 = math.floor(v80)
    local v82 = (#u47:GetChildren() - 1) / v81
    local v83 = math.ceil(v82) * (u38 + 5) + 5
    u46.CanvasSize = UDim2.new(0, 0, 0, v83)
end
local function u87() --[[Anonymous function at line 258]]
    --[[
    Upvalues:
        [1] = u67
        [2] = u52
        [3] = u84
    --]]
    for v85 = u67 + 1, #u52 do
        local v86 = u52[v85]
        v86.Frame.LayoutOrder = v86.Index
        v86.Frame.Visible = v86.Tool ~= nil
    end
    u84()
end
local function v88() --[[Anonymous function at line 267]]
    --[[
    Upvalues:
        [1] = u43
        [2] = u67
        [3] = u38
        [4] = u44
        [5] = u68
        [6] = u66
        [7] = u46
        [8] = u79
        [9] = u87
    --]]
    u43.Size = UDim2.new(0, 5 + u67 * (u38 + 5), 0, u38 + 5 + 5)
    u43.Position = UDim2.new(0.5, -u43.Size.X.Offset / 2, 1, -u43.Size.Y.Offset)
    u44.Size = UDim2.new(0, u43.Size.X.Offset, 0, u43.Size.Y.Offset * u68 + 40 + (u66 and 80 or 0))
    u44.Position = UDim2.new(0.5, -u44.Size.X.Offset / 2, 1, u43.Position.Y.Offset - u44.Size.Y.Offset)
    u46.Size = UDim2.new(1, u46.ScrollBarThickness + 1, 1, -40 - (u66 and 80 or 0))
    u46.Position = UDim2.new(0, 0, 0, 40 + (u66 and 40 or 0))
    u79()
    u87()
end
os.clock()
local function u193(p89, p90) --[[Anonymous function at line 321]]
    --[[
    Upvalues:
        [1] = u52
        [2] = u34
        [3] = u7
        [4] = u5
        [5] = u4
        [6] = u43
        [7] = u38
        [8] = u67
        [9] = u44
        [10] = u25
        [11] = u57
        [12] = u59
        [13] = u39
        [14] = u32
        [15] = u54
        [16] = u53
        [17] = u48
        [18] = u69
        [19] = u73
        [20] = u6
        [21] = u84
        [22] = u16
        [23] = u49
        [24] = u50
        [25] = u8
        [26] = u15
        [27] = u193
        [28] = u47
        [29] = u61
        [30] = u55
        [31] = u22
        [32] = u56
        [33] = u51
        [34] = u41
        [35] = u46
    --]]
    local v91 = p90 or #u52 + 1
    local u92 = {
        ["Tool"] = nil,
        ["Index"] = v91,
        ["Frame"] = nil
    }
    local u93 = nil
    local u94 = nil
    local u95 = nil
    local u96 = nil
    local u97 = nil
    local u98 = nil
    local u99 = nil
    local u100 = nil
    local u101 = nil
    local function u102() --[[Anonymous function at line 350]]
        --[[
        Upvalues:
            [1] = u34
            [2] = u93
            [3] = u7
            [4] = u5
            [5] = u4
        --]]
        local _ = u34.VREnabled
        u93.SelectionImageObject = nil
        u93.BackgroundTransparency = u93.Draggable and 0 or u7
        u93.BackgroundColor3 = u93.Draggable and u5 or u4
    end
    function u92.Readjust(_, p103, p104) --[[Anonymous function at line 377]]
        --[[
        Upvalues:
            [1] = u43
            [2] = u38
            [3] = u93
        --]]
        local v105 = u43.Size.X.Offset / 2
        local v106 = u38 + 5
        local v107 = p103 - (p104 / 2 + 0.5)
        u93.Position = UDim2.new(0, v105 - u38 / 2 + v106 * v107, 0, 5)
    end
    function u92.Fill(p108, u109) --[[Anonymous function at line 385]]
        --[[
        Upvalues:
            [1] = u98
            [2] = u92
            [3] = u95
            [4] = u96
            [5] = u100
            [6] = u97
            [7] = u67
            [8] = u44
            [9] = u25
            [10] = u93
            [11] = u57
            [12] = u59
            [13] = u39
            [14] = u32
            [15] = u54
            [16] = u53
            [17] = u52
        --]]
        if u98 then
            u98:Disconnect()
            u98 = nil
        end
        if not u109 then
            return p108:Clear()
        end
        p108.Tool = u109
        u98 = u109:GetAttributeChangedSignal("Favorite"):Connect(function() --[[Anonymous function at line 397]]
            --[[
            Upvalues:
                [1] = u92
                [2] = u109
            --]]
            u92.Frame.FavIcon.Visible = u109:GetAttribute("Favorite")
        end)
        xpcall(function() --[[Anonymous function at line 401]]
            --[[
            Upvalues:
                [1] = u92
                [2] = u109
            --]]
            u92.Frame.FavIcon.Visible = u109:GetAttribute("Favorite")
        end, warn)
        local function u112() --[[Anonymous function at line 405]]
            --[[
            Upvalues:
                [1] = u109
                [2] = u95
                [3] = u96
                [4] = u100
            --]]
            local v110 = u109.TextureId
            u95.Image = v110
            if v110 ~= "" then
                u96.Visible = false
            end
            if string.find(u109.Name, "Strawberry") then
                u96.TextSize = 12
            end
            u96.Text = u109.Name
            if u100 and u109:IsA("Tool") then
                u100.Text = u109.ToolTip
                local v111 = u100.TextBounds.X + 24
                u100.Size = UDim2.new(0, v111, 0, 24)
                u100.Position = UDim2.new(0.5, -v111 / 2, 0, -28)
            end
        end
        u112()
        if u97 then
            u97:disconnect()
            u97 = nil
        end
        u97 = u109.Changed:connect(function(p113) --[[Anonymous function at line 455]]
            --[[
            Upvalues:
                [1] = u112
            --]]
            if p113 == "TextureId" or (p113 == "Name" or p113 == "ToolTip") then
                u112()
            end
        end)
        local v114 = p108.Index <= u67
        if (not v114 or u44.Visible) and not u25.VREnabled then
            u93.Draggable = true
        end
        p108:UpdateEquipView()
        if v114 then
            u57 = u57 + 1
            if u59 and (u57 >= 1 and not u39) then
                u39 = true
                u32:BindAction("RBXHotbarEquip", changeToolFunc, false, Enum.KeyCode.ButtonL1, Enum.KeyCode.ButtonR1)
            end
        end
        u54[u109] = p108
        for v115 = 1, u67 do
            v116 = u52[v115]
            if not v116.Tool then
                ::l23::
                u53 = v116
                return
            end
        end
        local v116 = nil
        goto l23
    end
    function u92.Clear(p117) --[[Anonymous function at line 486]]
        --[[
        Upvalues:
            [1] = u97
            [2] = u98
            [3] = u95
            [4] = u96
            [5] = u100
            [6] = u93
            [7] = u67
            [8] = u57
            [9] = u39
            [10] = u32
            [11] = u54
            [12] = u53
            [13] = u52
        --]]
        if not p117.Tool then
            return
        end
        if u97 then
            u97:disconnect()
            u97 = nil
        end
        if u98 then
            u98:disconnect()
            u98 = nil
        end
        u95.Image = ""
        u96.Text = ""
        if u100 then
            u100.Text = ""
            u100.Visible = false
        end
        u93.Draggable = false
        u93.FavIcon.Visible = false
        p117:UpdateEquipView(true)
        if p117.Index <= u67 then
            u57 = u57 - 1
            if u57 < 1 then
                u39 = false
                u32:UnbindAction("RBXHotbarEquip")
            end
        end
        u54[p117.Tool] = nil
        p117.Tool = nil
        for v118 = 1, u67 do
            v119 = u52[v118]
            if not v119.Tool then
                ::l16::
                u53 = v119
                return
            end
        end
        local v119 = nil
        goto l16
    end
    function u92.UpdateEquipView(p120, p121) --[[Anonymous function at line 524]]
        --[[
        Upvalues:
            [1] = u48
            [2] = u69
            [3] = u92
            [4] = u99
            [5] = u73
            [6] = u93
            [7] = u6
            [8] = u102
        --]]
        if not p121 then
            local v122 = p120.Tool
            if v122 then
                v122 = v122.Parent == u48
            end
            if v122 then
                u69 = u92
                if not u99 then
                    u99 = u73("Frame", "Equipped")
                    u99.ZIndex = u93.ZIndex
                    local v123 = Instance.new("UICorner")
                    v123.CornerRadius = script:GetAttribute("CornerRadius")
                    v123.Parent = u99
                    local v124 = Instance.new("UIStroke")
                    v124.Color = u6
                    v124.Thickness = 3
                    v124.Parent = u99
                end
                u99.Parent = u93
                ::l10::
                u102()
                return
            end
        end
        if u99 then
            u99.Parent = nil
        end
        goto l10
    end
    function u92.IsEquipped(p125) --[[Anonymous function at line 550]]
        --[[
        Upvalues:
            [1] = u48
        --]]
        local v126 = p125.Tool
        if v126 then
            v126 = v126.Parent == u48
        end
        return v126
    end
    function u92.Delete(p127) --[[Anonymous function at line 554]]
        --[[
        Upvalues:
            [1] = u93
            [2] = u52
            [3] = u84
        --]]
        u93:Destroy()
        table.remove(u52, p127.Index)
        local v128 = #u52
        for v129 = p127.Index, v128 do
            u52[v129]:SlideBack()
        end
        u84()
    end
    function u92.Swap(p130, p131) --[[Anonymous function at line 567]]
        local v132 = p130.Tool
        local v133 = p131.Tool
        p130:Clear()
        if v133 then
            p131:Clear()
            p130:Fill(v133)
        end
        if v132 then
            p131:Fill(v132)
        else
            p131:Clear()
        end
    end
    function u92.SlideBack(p134) --[[Anonymous function at line 581]]
        --[[
        Upvalues:
            [1] = u93
        --]]
        p134.Index = p134.Index - 1
        u93.Name = p134.Index
        u93.LayoutOrder = p134.Index
    end
    function u92.TurnNumber(_, p135) --[[Anonymous function at line 587]]
        --[[
        Upvalues:
            [1] = u101
        --]]
        if u101 then
            u101.Visible = p135
        end
    end
    function u92.SetClickability(p136, p137) --[[Anonymous function at line 593]]
        --[[
        Upvalues:
            [1] = u25
            [2] = u93
            [3] = u102
        --]]
        if p136.Tool then
            if u25.VREnabled then
                u93.Draggable = false
            else
                u93.Draggable = not p137
            end
            u102()
        end
    end
    function u92.CheckTerms(p138, p139) --[[Anonymous function at line 604]]
        --[[
        Upvalues:
            [1] = u96
            [2] = u100
        --]]
        local v140 = 0
        local v141 = p138.Tool
        local v142
        if v141 then
            v142 = v140
            for v143 in pairs(p139) do
                local _, v144 = u96.Text:lower():gsub(v143, "")
                v140 = v142 + v144
                if v141:IsA("Tool") then
                    local _, v145 = (u100 and u100.Text or ""):lower():gsub(v143, "")
                    v140 = v140 + v145
                    v142 = v140
                else
                    v142 = v140
                end
            end
        else
            v142 = v140
        end
        return v142
    end
    function u92.Select(p146) --[[Anonymous function at line 632]]
        --[[
        Upvalues:
            [1] = u92
            [2] = u16
            [3] = u48
            [4] = u49
            [5] = u50
        --]]
        local v147 = u92.Tool
        if v147 then
            u16 = p146
            local v148
            if v147 then
                v148 = v147.Parent == u48
            else
                v148 = v147
            end
            if v148 then
                u16 = nil
                if u49 then
                    u49:UnequipTools()
                    return
                end
            elseif v147.Parent == u50 then
                if u49 then
                    u49:UnequipTools()
                end
                v147.Parent = u48
            end
        end
    end
    u93 = u73("TextButton", v91)
    local u149 = Instance.new("UIStroke")
    u149.Parent = u93
    local v150 = Instance.new("UICorner")
    v150.CornerRadius = script:GetAttribute("CornerRadius")
    v150.Parent = u93
    u149.Thickness = 0
    u93.BackgroundColor3 = u4
    u149.Color = u8
    u93.Text = ""
    u93.AutoButtonColor = false
    u93.BorderSizePixel = 0
    u93.Size = UDim2.new(0, u38, 0, u38)
    u93.Active = true
    u93.Draggable = false
    u93.BackgroundTransparency = u7
    u93.MouseButton1Click:Connect(function() --[[Anonymous function at line 670]]
        --[[
        Upvalues:
            [1] = u92
        --]]
        changeSlot(u92)
    end)
    local u151 = os.clock()
    if u25.TouchEnabled then
        u93.MouseButton1Click:Connect(function() --[[Anonymous function at line 677]]
            --[[
            Upvalues:
                [1] = u151
                [2] = u92
                [3] = u15
            --]]
            if os.clock() > u151 + 0.25 then
                u151 = os.clock()
                return
            else
                local v152 = u92.Tool
                if v152 then
                    u15:FireServer(v152)
                end
            end
        end)
        u93.TouchLongPress:Connect(function(_, p153, _) --[[Anonymous function at line 685]]
            --[[
            Upvalues:
                [1] = u92
                [2] = u15
            --]]
            if p153 == Enum.UserInputState.End then
                local v154 = u92.Tool
                if not v154 then
                    return
                end
                u15:FireServer(v154)
            end
        end)
    else
        u93.MouseButton2Click:Connect(function() --[[Anonymous function at line 691]]
            --[[
            Upvalues:
                [1] = u92
                [2] = u15
            --]]
            local v155 = u92.Tool
            if v155 then
                u15:FireServer(v155)
            end
        end)
    end
    u92.Frame = u93
    local v156 = u73("Frame", "SelectionObjectClipper")
    v156.Visible = false
    v156.Parent = u93
    local v157 = u73("ImageLabel", "Selector")
    v157.Size = UDim2.new(1, 0, 1, 0)
    v157.Image = "rbxasset://textures/ui/Keyboard/key_selection_9slice.png"
    v157.ScaleType = Enum.ScaleType.Slice
    v157.SliceCenter = Rect.new(12, 12, 52, 52)
    v157.Parent = v156
    u95 = u73("ImageLabel", "Icon")
    u95.Size = UDim2.new(0.8, 0, 0.8, 0)
    u95.Position = UDim2.new(0.1, 0, 0.1, 0)
    u95.Parent = u93
    local v158 = u73("ImageLabel", "FavIcon")
    v158.Size = UDim2.new(0.2, 0, 0.2, 0)
    v158.Position = UDim2.new(0.8, 0, 0.8, 0)
    v158.Parent = u93
    v158.Visible = false
    v158.Image = "rbxassetid://80131230547874"
    v158.ImageColor3 = Color3.fromRGB(255, 0, 0)
    v158.ZIndex = 10
    u96 = u73("TextLabel", "ToolName")
    u96.Size = UDim2.new(1, -2, 1, -2)
    u96.Position = UDim2.new(0, 1, 0, 1)
    u96.Parent = u93
    u92.Frame.LayoutOrder = u92.Index
    if v91 <= u67 then
        u100 = u73("TextLabel", "ToolTip")
        u100.ZIndex = 2
        u100.FontFace = script:GetAttribute("ToolTipFont")
        u100.TextWrapped = false
        u100.TextYAlignment = Enum.TextYAlignment.Center
        u100.BackgroundColor3 = Color3.new(0.4, 0.4, 0.4)
        u100.BackgroundTransparency = 0
        u100.Visible = false
        u100.Parent = u93
        local v159 = Instance.new("UICorner")
        v159.CornerRadius = script:GetAttribute("CornerRadius")
        v159.Parent = u100
        u93.MouseEnter:Connect(function() --[[Anonymous function at line 749]]
            --[[
            Upvalues:
                [1] = u100
            --]]
            if u100.Text ~= "" then
                u100.Visible = true
            end
        end)
        u93.MouseLeave:Connect(function() --[[Anonymous function at line 754]]
            --[[
            Upvalues:
                [1] = u100
            --]]
            u100.Visible = false
        end)
        function u92.MoveToInventory(p160) --[[Anonymous function at line 758]]
            --[[
            Upvalues:
                [1] = u92
                [2] = u67
                [3] = u193
                [4] = u47
                [5] = u48
                [6] = u49
                [7] = u61
                [8] = u44
            --]]
            if u92.Index <= u67 then
                local v161 = u92.Tool
                p160:Clear()
                local v162 = u193(u47)
                v162:Fill(v161)
                if v161 then
                    v161 = v161.Parent == u48
                end
                if v161 and u49 then
                    u49:UnequipTools()
                end
                if u61 then
                    v162.Frame.Visible = false
                    v162.Parent = u44
                end
            end
        end
        if v91 < 10 or v91 == u67 then
            local v163 = v91 < 10 and (v91 or 0) or 0
            u101 = u73("TextLabel", "Number")
            u101.Text = v163
            u101.Size = UDim2.new(0, 15, 0, 15)
            u101.Visible = false
            u101.Parent = u93
            u55[u22 + v163] = u92.Select
        end
    end
    local u164 = u93.Position
    local u165 = 0
    local u166 = nil
    local u167 = nil
    u93.DragBegin:connect(function(p168) --[[Anonymous function at line 793]]
        --[[
        Upvalues:
            [1] = u56
            [2] = u93
            [3] = u164
            [4] = u149
            [5] = u51
            [6] = u167
            [7] = u92
            [8] = u41
            [9] = u94
            [10] = u95
            [11] = u96
            [12] = u101
            [13] = u99
            [14] = u166
            [15] = u47
            [16] = u44
            [17] = u73
        --]]
        u56[u93] = true
        u164 = p168
        u149.Thickness = 2
        u51:lock()
        u167 = u92.Tool.AncestryChanged:Connect(function() --[[Anonymous function at line 800]]
            --[[
            Upvalues:
                [1] = u92
                [2] = u41
                [3] = u94
            --]]
            if not (u92.Tool and (u92.Tool:IsDescendantOf(u41.Backpack) or u92.Tool:IsDescendantOf(u41.Character))) then
                if u94 then
                    u94:Destroy()
                end
            end
        end)
        u93.ZIndex = 2
        u95.ZIndex = 2
        u96.ZIndex = 2
        u93.Parent.ZIndex = 2
        if u101 then
            u101.ZIndex = 2
        end
        if u99 then
            u99.ZIndex = 2
            for _, v169 in pairs(u99:GetChildren()) do
                if not (v169:IsA("UICorner") or v169:IsA("UIStroke")) then
                    v169.ZIndex = 2
                end
            end
        end
        u166 = u93.Parent
        if u166 == u47 then
            local _ = u93.AbsolutePosition
            local v170 = UDim2.new(0, u93.AbsolutePosition.X - u44.AbsolutePosition.X, 0, u93.AbsolutePosition.Y - u44.AbsolutePosition.Y)
            u93.Parent = u44
            u93.Position = v170
            u94 = u73("Frame", "FakeSlot")
            u94.LayoutOrder = u93.LayoutOrder
            u94.Size = u93.Size
            u94.BackgroundTransparency = 1
            u94.Parent = u47
        end
    end)
    u93.DragStopped:connect(function(p171, p172) --[[Anonymous function at line 848]]
        --[[
        Upvalues:
            [1] = u94
            [2] = u167
            [3] = u93
            [4] = u164
            [5] = u166
            [6] = u149
            [7] = u51
            [8] = u95
            [9] = u96
            [10] = u101
            [11] = u99
            [12] = u56
            [13] = u92
            [14] = u44
            [15] = u67
            [16] = u165
            [17] = u53
            [18] = u43
            [19] = u52
            [20] = u48
            [21] = u49
            [22] = u61
        --]]
        if u94 then
            u94:Destroy()
        end
        if u167 then
            u167:Disconnect()
        end
        local v173 = tick()
        u93.Position = u164
        u93.Parent = u166
        u149.Thickness = 0
        u51:unlock()
        u93.ZIndex = 1
        u95.ZIndex = 1
        u96.ZIndex = 1
        u166.ZIndex = 1
        if u101 then
            u101.ZIndex = 1
        end
        if u99 then
            u99.ZIndex = 1
            for _, v174 in pairs(u99:GetChildren()) do
                if not (v174:IsA("UICorner") or v174:IsA("UIStroke")) then
                    v174.ZIndex = 1
                end
            end
        end
        u56[u93] = nil
        if u92.Tool then
            local v175 = u44
            local v176 = v175.AbsolutePosition
            local v177 = v175.AbsoluteSize
            local v178
            if v176.X < p171 and (p171 <= v176.X + v177.X and v176.Y < p172) then
                v178 = p172 <= v176.Y + v177.Y
            else
                v178 = false
            end
            if v178 then
                if u92.Index <= u67 then
                    u92:MoveToInventory()
                end
                if u67 < u92.Index and v173 - u165 < 0.5 then
                    if u53 then
                        local v179 = u92.Tool
                        u92:Clear()
                        u53:Fill(v179)
                        u92:Delete()
                        v173 = 0
                    else
                        v173 = 0
                    end
                end
            else
                local v180 = u43
                local v181 = v180.AbsolutePosition
                local v182 = v180.AbsoluteSize
                local v183
                if v181.X < p171 and (p171 <= v181.X + v182.X and v181.Y < p172) then
                    v183 = p172 <= v181.Y + v182.Y
                else
                    v183 = false
                end
                if v183 then
                    local v184 = { (1 / 0), nil }
                    for v185 = 1, u67 do
                        local v186 = u52[v185]
                        local v187 = v186.Frame
                        local v188 = Vector2.new(p171, p172)
                        local v189 = (v187.AbsolutePosition + v187.AbsoluteSize / 2 - v188).magnitude
                        if v189 < v184[1] then
                            v184 = { v189, v186 }
                        end
                    end
                    local v190 = v184[2]
                    if v190 ~= u92 then
                        u92:Swap(v190)
                        if u67 < u92.Index then
                            local v191 = u92.Tool
                            if v191 then
                                if v191 then
                                    v191 = v191.Parent == u48
                                end
                                if v191 and u49 then
                                    u49:UnequipTools()
                                end
                                if u61 then
                                    u92.Frame.Visible = false
                                    u92.Frame.Parent = u44
                                end
                            else
                                u92:Delete()
                            end
                        end
                    end
                elseif u92.Index <= u67 then
                    u92:MoveToInventory()
                end
            end
            u165 = v173
        end
    end)
    u93.Parent = p89
    u52[v91] = u92
    if u67 < v91 then
        u84()
        if u44.Visible and not u61 then
            local v192 = u46.CanvasSize.Y.Offset - u46.AbsoluteSize.Y
            u46.CanvasPosition = Vector2.new(0, (math.max(0, v192)))
        end
    end
    return u92
end
local function u202(p194) --[[Anonymous function at line 962]]
    --[[
    Upvalues:
        [1] = u48
        [2] = u49
        [3] = u65
        [4] = u58
        [5] = u54
        [6] = u41
        [7] = u53
        [8] = u193
        [9] = u47
        [10] = u52
        [11] = u50
        [12] = u79
        [13] = u67
        [14] = u44
    --]]
    if p194:IsA("Tool") then
        if p194.Parent == u48 then
            u65 = tick()
        end
        if not u58 and (p194.Parent == u48 and not u54[p194]) then
            local v195 = u41:FindFirstChild("StarterGear")
            if v195 and v195:FindFirstChild(p194.Name) then
                u58 = true
                for v196 = (u53 or u193(u47)).Index, 1, -1 do
                    local v197 = u52[v196]
                    local v198 = v196 - 1
                    if v198 > 0 then
                        u52[v198]:Swap(v197)
                    else
                        v197:Fill(p194)
                    end
                end
                for _, v199 in pairs(u48:GetChildren()) do
                    if v199:IsA("Tool") and v199 ~= p194 then
                        v199.Parent = u50
                    end
                end
                u79()
                return
            end
        end
        local v200 = u54[p194]
        if v200 then
            v200:UpdateEquipView()
        else
            local v201 = u53 or u193(u47)
            v201:Fill(p194)
            if v201.Index <= u67 and not u44.Visible then
                u79()
            end
        end
    else
        if p194:IsA("Humanoid") and p194.Parent == u48 then
            u49 = p194
        end
        return
    end
end
local function u206(p203) --[[Anonymous function at line 1018]]
    --[[
    Upvalues:
        [1] = u65
        [2] = u48
        [3] = u50
        [4] = u54
        [5] = u67
        [6] = u44
        [7] = u79
    --]]
    if p203:IsA("Tool") then
        u65 = tick()
        local v204 = p203.Parent
        if v204 ~= u48 and v204 ~= u50 then
            local v205 = u54[p203]
            if v205 then
                v205:Clear()
                if u67 < v205.Index then
                    v205:Delete()
                    return
                end
                if not u44.Visible then
                    u79()
                end
            end
        end
    else
        return
    end
end
local function v225(p207) --[[Anonymous function at line 1044]]
    --[[
    Upvalues:
        [1] = u52
        [2] = u67
        [3] = u63
        [4] = u48
        [5] = u206
        [6] = u202
        [7] = u50
        [8] = u41
        [9] = u79
    --]]
    for v208 = #u52, 1, -1 do
        local v209 = u52[v208]
        if v209.Tool then
            v209:Clear()
        end
        if u67 < v208 then
            v209:Delete()
        end
    end
    for _, v210 in pairs(u63) do
        v210:Disconnect()
    end
    u63 = {}
    u48 = p207
    local v211 = u63
    local v212 = p207.ChildRemoved
    local v213 = u206
    table.insert(v211, v212:Connect(v213))
    local v214 = u63
    local v215 = p207.ChildAdded
    local v216 = u202
    table.insert(v214, v215:Connect(v216))
    for _, v217 in pairs(p207:GetChildren()) do
        task.spawn(u202, v217)
    end
    u50 = u41:WaitForChild("Backpack")
    local v218 = u63
    local v219 = u50.ChildRemoved
    local v220 = u206
    table.insert(v218, v219:Connect(v220))
    local v221 = u63
    local v222 = u50.ChildAdded
    local v223 = u202
    table.insert(v221, v222:Connect(v223))
    for _, v224 in pairs(u50:GetChildren()) do
        task.spawn(u202, v224)
    end
    u79()
end
local function v230(p226, p227) --[[Anonymous function at line 1082]]
    --[[
    Upvalues:
        [1] = u60
        [2] = u59
        [3] = u23
        [4] = u55
        [5] = u44
        [6] = u51
    --]]
    if p227 == false then
        local v228 = (p226.KeyCode == Enum.KeyCode.ButtonL2 or p226.UserInputType == Enum.UserInputType.Keyboard and (not u60 and (u59 or p226.KeyCode.Value == u23))) and u55[p226.KeyCode.Value]
        if v228 then
            v228(p227)
        end
        local v229 = p226.UserInputType
        if (v229 == Enum.UserInputType.MouseButton1 or v229 == Enum.UserInputType.Touch) and u44.Visible then
            u51:deselect()
        end
    end
end
local function v234(p231) --[[Anonymous function at line 1101]]
    --[[
    Upvalues:
        [1] = u25
        [2] = u67
        [3] = u52
    --]]
    if p231 == "KeyboardEnabled" or p231 == "VREnabled" then
        local v232 = u25.KeyboardEnabled
        if v232 then
            v232 = not u25.VREnabled
        end
        for v233 = 1, u67 do
            u52[v233]:TurnNumber(v232)
        end
    end
end
local u235 = nil
local u236 = nil
local function u237() --[[Anonymous function at line 1113]] end
local _ = Vector2.new(0, 0)
function unbindAllGamepadEquipActions()
    --[[
    Upvalues:
        [1] = u32
    --]]
    u32:UnbindAction("RBXBackpackHasGamepadFocus")
    u32:UnbindAction("RBXCloseInventory")
end
function changeToolFunc(_, p238, u239)
    --[[
    Upvalues:
        [1] = u235
        [2] = u236
        [3] = u49
        [4] = u67
        [5] = u52
        [6] = u69
    --]]
    if p238 == Enum.UserInputState.Begin then
        if u235 and (u235.KeyCode == Enum.KeyCode.ButtonR1 and u239.KeyCode == Enum.KeyCode.ButtonL1 or u235.KeyCode == Enum.KeyCode.ButtonL1 and u239.KeyCode == Enum.KeyCode.ButtonR1) and tick() - u236 <= 0.06 then
            if u49 then
                u49:UnequipTools()
            end
            u235 = u239
            u236 = tick()
        else
            u235 = u239
            u236 = tick()
            delay(0.06, function() --[[Anonymous function at line 1212]]
                --[[
                Upvalues:
                    [1] = u235
                    [2] = u239
                    [3] = u67
                    [4] = u52
                    [5] = u49
                    [6] = u69
                --]]
                if u235 == u239 then
                    local v240 = u239.KeyCode == Enum.KeyCode.ButtonL1 and -1 or 1
                    for v241 = 1, u67 do
                        if u52[v241]:IsEquipped() then
                            local v242 = v240 + v241
                            local v243 = false
                            if u67 < v242 then
                                v242 = 1
                                v243 = true
                            elseif v242 < 1 then
                                v242 = u67
                                v243 = true
                            end
                            local v244 = v242
                            while not u52[v242].Tool do
                                v242 = v242 + v240
                                if v242 == v244 then
                                    return
                                end
                                if u67 < v242 then
                                    v242 = 1
                                    v243 = true
                                elseif v242 < 1 then
                                    v242 = u67
                                    v243 = true
                                end
                            end
                            if v243 then
                                if u49 then
                                    u49:UnequipTools()
                                end
                                u69 = nil
                            else
                                u52[v242]:Select()
                            end
                        end
                    end
                    if u69 and u69.Tool then
                        u69:Select()
                    else
                        for v245 = v240 == -1 and (u67 or 1) or 1, v240 == -1 and 1 or u67, v240 do
                            if u52[v245].Tool then
                                u52[v245]:Select()
                                return
                            end
                        end
                    end
                else
                    return
                end
            end)
        end
    else
        return
    end
end
function getGamepadSwapSlot()
    --[[
    Upvalues:
        [1] = u52
    --]]
    for v246 = 1, #u52 do
        if u52[v246].Frame:WaitForChild("UIStroke").Thickness > 0 then
            return u52[v246]
        end
    end
end
function changeSlot(u247)
    --[[
    Upvalues:
        [1] = u34
        [2] = u44
        [3] = u29
        [4] = u45
        [5] = u67
    --]]
    local v248 = not u34.VREnabled or u44.Visible
    if u247.Frame == u29.SelectedObject and v248 then
        local v249 = getGamepadSwapSlot()
        if not v249 then
            local u250 = u247.Frame.Size
            local u251 = u247.Frame.Position
            u247.Frame:TweenSizeAndPosition(u250 + UDim2.new(0, 10, 0, 10), u251 - UDim2.new(0, 5, 0, 5), Enum.EasingDirection.Out, Enum.EasingStyle.Quad, 0.1, true, function() --[[Anonymous function at line 1313]]
                --[[
                Upvalues:
                    [1] = u247
                    [2] = u250
                    [3] = u251
                --]]
                u247.Frame:TweenSizeAndPosition(u250, u251, Enum.EasingDirection.In, Enum.EasingStyle.Quad, 0.1, true)
            end)
            u247.Frame:WaitForChild("UIStroke").Thickness = 3
            u45.SelectionImageObject.Visible = true
            return
        end
        v249.Frame:WaitForChild("UIStroke").Thickness = 0
        if v249 ~= u247 then
            u247:Swap(v249)
            u45.SelectionImageObject.Visible = false
            if u67 < u247.Index and not u247.Tool then
                if u29.SelectedObject == u247.Frame then
                    u29.SelectedObject = v249.Frame
                end
                u247:Delete()
            end
            if u67 < v249.Index and not v249.Tool then
                if u29.SelectedObject == v249.Frame then
                    u29.SelectedObject = u247.Frame
                end
                v249:Delete()
                return
            end
        end
    else
        u247:Select()
        u45.SelectionImageObject.Visible = false
    end
end
function vrMoveSlotToInventory()
    --[[
    Upvalues:
        [1] = u34
        [2] = u45
    --]]
    if u34.VREnabled then
        local v252 = getGamepadSwapSlot()
        if v252 and v252.Tool then
            v252:WaitForChild("UIStroke").Thickness = 0
            v252:MoveToInventory()
            u45.SelectionImageObject.Visible = false
        end
    end
end
function enableGamepadInventoryControl()
    --[[
    Upvalues:
        [1] = u44
        [2] = u51
        [3] = u32
        [4] = u237
        [5] = u25
        [6] = u29
        [7] = u43
    --]]
    local function v255(_, p253, _) --[[Anonymous function at line 1338]]
        --[[
        Upvalues:
            [1] = u44
            [2] = u51
        --]]
        if p253 == Enum.UserInputState.Begin then
            if getGamepadSwapSlot() then
                local v254 = getGamepadSwapSlot()
                if v254 and typeof(v254) == "Instance" then
                    v254:WaitForChild("UIStroke").Thickness = 0
                    return
                end
            elseif u44.Visible then
                u51:deselect()
            end
        end
    end
    u32:BindAction("RBXBackpackHasGamepadFocus", u237, false, Enum.UserInputType.Gamepad1)
    u32:BindAction("RBXCloseInventory", v255, false, Enum.KeyCode.ButtonB, Enum.KeyCode.ButtonStart)
    if not u25.VREnabled then
        u29.SelectedObject = u43:FindFirstChild("1")
    end
end
function disableGamepadInventoryControl()
    --[[
    Upvalues:
        [1] = u67
        [2] = u52
        [3] = u29
        [4] = u42
    --]]
    unbindAllGamepadEquipActions()
    for v256 = 1, u67 do
        local v257 = u52[v256]
        if v257 and v257.Frame then
            v257.Frame:WaitForChild("UIStroke").Thickness = 0
        end
    end
    if u29.SelectedObject and u29.SelectedObject:IsDescendantOf(u42) then
        u29.SelectedObject = nil
    end
end
function gamepadDisconnected()
    --[[
    Upvalues:
        [1] = u64
    --]]
    u64 = false
    disableGamepadInventoryControl()
end
function gamepadConnected()
    --[[
    Upvalues:
        [1] = u64
        [2] = u29
        [3] = u42
        [4] = u57
        [5] = u59
        [6] = u39
        [7] = u32
        [8] = u44
    --]]
    u64 = true
    u29:AddSelectionParent("RBXBackpackSelection", u42)
    if u57 >= 1 and (u59 and not u39) then
        u39 = true
        u32:BindAction("RBXHotbarEquip", changeToolFunc, false, Enum.KeyCode.ButtonL1, Enum.KeyCode.ButtonR1)
    end
    if u44.Visible then
        enableGamepadInventoryControl()
    end
end
local function u261(p258) --[[Anonymous function at line 1410]]
    --[[
    Upvalues:
        [1] = u28
        [2] = u51
        [3] = u29
        [4] = u59
        [5] = u42
        [6] = u62
        [7] = u57
        [8] = u39
        [9] = u32
    --]]
    if p258 then
        p258 = u28:GetCore("TopbarEnabled")
    end
    local v259 = u51
    local v260
    if p258 then
        v260 = not u29.MenuIsOpen
    else
        v260 = p258
    end
    v259:setEnabled(v260)
    u59 = p258
    u42.Visible = p258
    for _, _ in pairs(u62) do

    end
    if p258 then
        if u57 >= 1 and (u59 and not u39) then
            u39 = true
            u32:BindAction("RBXHotbarEquip", changeToolFunc, false, Enum.KeyCode.ButtonL1, Enum.KeyCode.ButtonR1)
            return
        end
    else
        disableGamepadInventoryControl()
        u39 = false
        u32:UnbindAction("RBXHotbarEquip")
    end
end
local function v267(p262, p263) --[[Anonymous function at line 1436]]
    --[[
    Upvalues:
        [1] = u73
    --]]
    local v264 = u73("ImageButton", p262)
    v264.Size = UDim2.new(0, 40, 0, 40)
    v264.Image = "rbxasset://textures/ui/Keyboard/close_button_background.png"
    local v265 = u73("ImageLabel", "Icon")
    v265.Size = UDim2.new(0.5, 0, 0.5, 0)
    v265.Position = UDim2.new(0.25, 0, 0.25, 0)
    v265.Image = p263
    v265.Parent = v264
    local v266 = u73("ImageLabel", "Selection")
    v266.Size = UDim2.new(0.9, 0, 0.9, 0)
    v266.Position = UDim2.new(0.05, 0, 0.05, 0)
    v266.Image = "rbxasset://textures/ui/Keyboard/close_button_selection.png"
    v264.SelectionImageObject = v266
    return v264, v265, v266
end
local v268 = u73("Frame", "Backpack")
v268.Visible = false
v268.Parent = v31
local v269 = u73("Frame", "Hotbar")
v269.Parent = v268
local v270 = v268
local u271 = v269
for v272 = 1, u67 do
    local v273 = u193(u271, v272)
    v273.Frame.Visible = false
    if not u53 then
        local v274 = v273
        u53 = v274
    end
end
u51.selected:Connect(function() --[[Anonymous function at line 1476]]
    --[[
    Upvalues:
        [1] = u29
        [2] = u1
    --]]
    if not u29.MenuIsOpen then
        u1.OpenClose()
    end
end)
u51.deselected:Connect(function() --[[Anonymous function at line 1481]]
    --[[
    Upvalues:
        [1] = u44
        [2] = u1
    --]]
    if u44.Visible then
        u1.OpenClose()
    end
end)
LeftBumperButton = u73("ImageLabel", "LeftBumper")
LeftBumperButton.Size = UDim2.new(0, 40, 0, 40)
LeftBumperButton.Position = UDim2.new(0, -LeftBumperButton.Size.X.Offset, 0.5, -LeftBumperButton.Size.Y.Offset / 2)
RightBumperButton = u73("ImageLabel", "RightBumper")
RightBumperButton.Size = UDim2.new(0, 40, 0, 40)
RightBumperButton.Position = UDim2.new(1, 0, 0.5, -RightBumperButton.Size.Y.Offset / 2)
local u275 = u73("Frame", "Inventory")
local v276 = Instance.new("UICorner")
v276.CornerRadius = script:GetAttribute("CornerRadius")
v276.Parent = u275
u275.BackgroundTransparency = v3
u275.BackgroundColor3 = u4
u275.Active = true
u275.Visible = false
u275.Parent = v270
local v277 = u73("TextButton", "VRInventorySelector")
v277.Position = UDim2.new(0, 0, 0, 0)
v277.Size = UDim2.new(1, 0, 1, 0)
v277.BackgroundTransparency = 1
v277.Text = ""
v277.Parent = u275
local v278 = u73("ImageLabel", "Selector")
v278.Size = UDim2.new(1, 0, 1, 0)
v278.Image = "rbxasset://textures/ui/Keyboard/key_selection_9slice.png"
v278.ScaleType = Enum.ScaleType.Slice
v278.SliceCenter = Rect.new(12, 12, 52, 52)
v278.Visible = false
v277.SelectionImageObject = v278
v277.MouseButton1Click:Connect(function() --[[Anonymous function at line 1521]]
    vrMoveSlotToInventory()
end)
local u279 = u73("ScrollingFrame", "ScrollingFrame")
u279.Selectable = false
u279.CanvasSize = UDim2.new(0, 0, 0, 0)
u279.Parent = u275
local u280 = u73("Frame", "UIGridFrame")
u280.Selectable = false
u280.Size = UDim2.new(1, -10, 1, 0)
u280.Position = UDim2.new(0, 5, 0, 0)
u280.Parent = u279
local v281 = Instance.new("UIGridLayout")
v281.SortOrder = Enum.SortOrder.LayoutOrder
v281.CellSize = UDim2.new(0, u38, 0, u38)
v281.CellPadding = UDim2.new(0, 5, 0, 5)
v281.Parent = u280
local u282 = v267("ScrollUpButton", "rbxasset://textures/ui/Backpack/ScrollUpArrow.png")
u282.Size = UDim2.new(0, 34, 0, 34)
u282.Position = UDim2.new(0.5, -u282.Size.X.Offset / 2, 0, 43)
u282.Icon.Position = u282.Icon.Position - UDim2.new(0, 0, 0, 2)
u282.MouseButton1Click:Connect(function() --[[Anonymous function at line 1547]]
    --[[
    Upvalues:
        [1] = u279
        [2] = u38
    --]]
    local v283 = u279
    local v284 = Vector2.new
    local v285 = u279.CanvasPosition.X
    local v286 = u279.CanvasSize.Y.Offset - u279.AbsoluteWindowSize.Y
    local v287 = u279.CanvasPosition.Y - (u38 + 5)
    local v288 = math.max(0, v287)
    v283.CanvasPosition = v284(v285, (math.min(v286, v288)))
end)
local u289 = v267("ScrollDownButton", "rbxasset://textures/ui/Backpack/ScrollUpArrow.png")
u289.Rotation = 180
u289.Icon.Position = u289.Icon.Position - UDim2.new(0, 0, 0, 2)
u289.Size = UDim2.new(0, 34, 0, 34)
u289.Position = UDim2.new(0.5, -u289.Size.X.Offset / 2, 1, -u289.Size.Y.Offset - 3)
u289.MouseButton1Click:Connect(function() --[[Anonymous function at line 1558]]
    --[[
    Upvalues:
        [1] = u279
        [2] = u38
    --]]
    local v290 = u279
    local v291 = Vector2.new
    local v292 = u279.CanvasPosition.X
    local v293 = u279.CanvasSize.Y.Offset - u279.AbsoluteWindowSize.Y
    local v294 = u279.CanvasPosition.Y + (u38 + 5)
    local v295 = math.max(0, v294)
    v290.CanvasPosition = v291(v292, (math.min(v293, v295)))
end)
u279.Changed:Connect(function(p296) --[[Anonymous function at line 1564]]
    --[[
    Upvalues:
        [1] = u279
        [2] = u282
        [3] = u289
    --]]
    if p296 == "AbsoluteWindowSize" or (p296 == "CanvasPosition" or p296 == "CanvasSize") then
        local v297 = u279.CanvasPosition.Y ~= 0
        local v298 = u279.CanvasPosition.Y < u279.CanvasSize.Y.Offset - u279.AbsoluteWindowSize.Y
        u282.Visible = v297
        u289.Visible = v298
    end
end)
v88()
local u299 = u35:Create("Frame")({
    ["Name"] = "GamepadHintsFrame",
    ["Size"] = UDim2.new(0, u271.Size.X.Offset, 0, u37 and 95 or 60),
    ["BackgroundTransparency"] = 1,
    ["Visible"] = false,
    ["Parent"] = v270
})
local function v307(p300, p301, p302) --[[Anonymous function at line 1587]]
    --[[
    Upvalues:
        [1] = u35
        [2] = u299
        [3] = u37
    --]]
    local v303 = u35:Create("Frame")({
        ["Name"] = "HintFrame",
        ["Size"] = UDim2.new(1, 0, 1, -5),
        ["Position"] = UDim2.new(0, 0, 0, 0),
        ["BackgroundTransparency"] = 1,
        ["Parent"] = u299
    })
    local v304 = u35:Create("ImageLabel")
    local v305 = {
        ["Name"] = "HintImage",
        ["Size"] = u37 and UDim2.new(0, 90, 0, 90) or UDim2.new(0, 60, 0, 60),
        ["BackgroundTransparency"] = 1
    }
    if u37 then
        p300 = p301 or p300
    end
    v305.Image = p300
    v305.Parent = v303
    v304(v305)
    local v306 = u35:Create("TextLabel")({
        ["Name"] = "HintText",
        ["Position"] = UDim2.new(0, u37 and 100 or 70, 0, 0),
        ["Size"] = UDim2.new(1, -(u37 and 100 or 70), 1, 0),
        ["Font"] = Enum.Font.SourceSansBold,
        ["FontSize"] = u37 and Enum.FontSize.Size36 or Enum.FontSize.Size24,
        ["BackgroundTransparency"] = 1,
        ["Text"] = p302,
        ["TextColor3"] = Color3.new(1, 1, 1),
        ["TextXAlignment"] = Enum.TextXAlignment.Left,
        ["TextWrapped"] = true,
        ["Parent"] = v303
    })
    Instance.new("UITextSizeConstraint", v306).MaxTextSize = v306.TextSize
end
local function u313() --[[Anonymous function at line 1624]]
    --[[
    Upvalues:
        [1] = u299
        [2] = u271
        [3] = u37
        [4] = u275
    --]]
    u299.Size = UDim2.new(u271.Size.X.Scale, u271.Size.X.Offset, 0, u37 and 95 or 60)
    u299.Position = UDim2.new(u271.Position.X.Scale, u271.Position.X.Offset, u275.Position.Y.Scale, u275.Position.Y.Offset - u299.Size.Y.Offset)
    local v308 = u299:GetChildren()
    local v309 = 0
    for v310 = 1, #v308 do
        v308[v310].Size = UDim2.new(1, 0, 1, -5)
        v308[v310].Position = UDim2.new(0, 0, 0, 0)
        v309 = v309 + (v308[v310].HintText.Position.X.Offset + v308[v310].HintText.TextBounds.X)
    end
    local v311 = (u299.AbsoluteSize.X - v309) / (#v308 - 1)
    for v312 = 1, #v308 do
        v308[v312].Position = v312 == 1 and UDim2.new(0, 0, 0, 0) or UDim2.new(0, v308[v312 - 1].Position.X.Offset + v308[v312 - 1].Size.X.Offset + v311, 0, 0)
        v308[v312].Size = UDim2.new(0, v308[v312].HintText.Position.X.Offset + v308[v312].HintText.TextBounds.X, 1, -5)
    end
end
v307("rbxasset://textures/ui/Settings/Help/XButtonDark.png", "rbxasset://textures/ui/Settings/Help/<EMAIL>", "Remove From Hotbar")
v307("rbxasset://textures/ui/Settings/Help/AButtonDark.png", "rbxasset://textures/ui/Settings/Help/<EMAIL>", "Select/Swap")
v307("rbxasset://textures/ui/Settings/Help/BButtonDark.png", "rbxasset://textures/ui/Settings/Help/<EMAIL>", "Close Backpack")
local u314 = u73("Frame", "Search")
local v315 = Instance.new("UICorner")
v315.CornerRadius = script:GetAttribute("CornerRadius")
v315.Parent = u314
u314.BackgroundColor3 = v12
u314.BackgroundTransparency = v13
u314.Size = UDim2.new(0, 190, 0, 30)
u314.Position = UDim2.new(1, -u314.Size.X.Offset - 5, 0, 5)
u314.Parent = u275
local u316 = u73("TextBox", "TextBox")
u316.PlaceholderText = "Search"
u316.ClearTextOnFocus = false
u316.FontSize = Enum.FontSize.Size24
u316.TextXAlignment = Enum.TextXAlignment.Left
local v317 = Instance.new("UIPadding")
v317.Parent = u316
v317.PaddingLeft = UDim.new(0, 8)
u316.Size = u314.Size - UDim2.fromOffset(0, 0)
u316.Position = UDim2.new(0, 0, 0, 0)
u316.Parent = u314
local u318 = u73("TextButton", "X")
local v319 = Instance.new("UICorner")
v319.CornerRadius = script:GetAttribute("CornerRadius")
v319.Parent = u318
u318.Text = "X"
u318.ZIndex = 10
u318.TextColor3 = Color3.new(1, 1, 1)
u318.FontSize = Enum.FontSize.Size24
u318.TextYAlignment = Enum.TextYAlignment.Bottom
u318.BackgroundTransparency = 1
u318.Size = UDim2.new(0, u314.Size.Y.Offset - 10, 0, u314.Size.Y.Offset - 10)
u318.Position = UDim2.new(1, -u318.Size.X.Offset - 10, 0.5, -u318.Size.Y.Offset / 2)
u318.Visible = false
u318.BorderSizePixel = 0
u318.Parent = u314
local function u331() --[[Anonymous function at line 1689]]
    --[[
    Upvalues:
        [1] = u316
        [2] = u67
        [3] = u52
        [4] = u275
        [5] = u61
        [6] = u280
        [7] = u279
        [8] = u84
        [9] = u318
    --]]
    local v320 = {}
    for v321 in u316.Text:gmatch("%S+") do
        v320[v321:lower()] = true
    end
    local v322 = {}
    for v323 = u67 + 1, #u52 do
        local v324 = u52[v323]
        local v325 = { v324, (v324:CheckTerms(v320)) }
        table.insert(v322, v325)
        v324.Frame.Visible = false
        v324.Frame.Parent = u275
    end
    table.sort(v322, function(p326, p327) --[[Anonymous function at line 1704]]
        return p326[2] > p327[2]
    end)
    u61 = true
    local v328 = 0
    for _, v329 in ipairs(v322) do
        local v330 = v329[1]
        if v329[2] > 0 then
            v330.Frame.Visible = true
            v330.Frame.Parent = u280
            v330.Frame.LayoutOrder = u67 + v328
            v328 = v328 + 1
        end
    end
    u279.CanvasPosition = Vector2.new(0, 0)
    u84()
    u318.ZIndex = 3
end
local function u334() --[[Anonymous function at line 1726]]
    --[[
    Upvalues:
        [1] = u318
        [2] = u61
        [3] = u67
        [4] = u52
        [5] = u280
        [6] = u84
    --]]
    if u318.ZIndex > 0 then
        u61 = false
        for v332 = u67 + 1, #u52 do
            local v333 = u52[v332]
            v333.Frame.LayoutOrder = v333.Index
            v333.Frame.Parent = u280
            v333.Frame.Visible = true
        end
        u318.ZIndex = 0
    end
    u84()
end
u318.MouseButton1Click:Connect(function() --[[Function name: reset, line 1740]]
    --[[
    Upvalues:
        [1] = u334
        [2] = u316
    --]]
    u334()
    u316.Text = ""
end)
u316.Changed:Connect(function(p335) --[[Function name: onChanged, line 1745]]
    --[[
    Upvalues:
        [1] = u316
        [2] = u334
        [3] = u331
        [4] = u318
    --]]
    if p335 == "Text" then
        local v336 = u316.Text
        if v336 == "" then
            u334()
        elseif v336 ~= "Search" then
            u331()
        end
        local v337 = u318
        local v338
        if v336 == "" then
            v338 = false
        else
            v338 = v336 ~= "Search"
        end
        v337.Visible = v338
    end
end)
u316.FocusLost:Connect(function(p339) --[[Function name: focusLost, line 1757]]
    --[[
    Upvalues:
        [1] = u331
    --]]
    if p339 then
        u331()
    end
end)
u1.StateChanged.Event:Connect(function(p340) --[[Anonymous function at line 1768]]
    --[[
    Upvalues:
        [1] = u334
        [2] = u316
        [3] = u275
        [4] = u51
    --]]
    if not p340 then
        u334()
        u316.Text = ""
        if not u275.Visible then
            u51:deselect()
        end
    end
end)
u55[Enum.KeyCode.Escape.Value] = function(p341) --[[Anonymous function at line 1778]]
    --[[
    Upvalues:
        [1] = u334
        [2] = u316
        [3] = u275
        [4] = u51
    --]]
    if p341 then
        u334()
        u316.Text = ""
    elseif u275.Visible then
        u51:deselect()
    end
end
u55[Enum.KeyCode.ButtonL2.Value] = function(_) --[[Anonymous function at line 1786]]
    --[[
    Upvalues:
        [1] = u16
        [2] = u15
    --]]
    if u16 then
        local v342 = u16.Tool
        if v342 then
            u15:FireServer(v342)
        end
    else
        return
    end
end
u25.LastInputTypeChanged:Connect(function(p343) --[[Function name: detectGamepad, line 1792]]
    --[[
    Upvalues:
        [1] = u25
        [2] = u314
    --]]
    if p343 == Enum.UserInputType.Gamepad1 and not u25.VREnabled then
        u314.Visible = false
    else
        u314.Visible = true
    end
end)
u29.MenuOpened:Connect(function() --[[Anonymous function at line 1802]]
    --[[
    Upvalues:
        [1] = u275
        [2] = u51
    --]]
    if u275.Visible then
        u51:deselect()
    end
end)
local function u346(_, p344, _) --[[Anonymous function at line 1810]]
    --[[
    Upvalues:
        [1] = u29
        [2] = u67
        [3] = u52
    --]]
    if p344 == Enum.UserInputState.Begin then
        if u29.SelectedObject then
            for v345 = 1, u67 do
                if u52[v345].Frame == u29.SelectedObject and u52[v345].Tool then
                    u52[v345]:MoveToInventory()
                    return
                end
            end
        end
    else
        return
    end
end
local function v349() --[[Anonymous function at line 1822]]
    --[[
    Upvalues:
        [1] = u56
        [2] = u275
        [3] = u79
        [4] = u271
        [5] = u67
        [6] = u52
        [7] = u64
        [8] = u24
        [9] = u25
        [10] = u313
        [11] = u299
        [12] = u32
        [13] = u346
        [14] = u1
    --]]
    if not next(u56) then
        u275.Visible = not u275.Visible
        local v347 = u275.Visible
        u79()
        u271.Active = not u271.Active
        for v348 = 1, u67 do
            u52[v348]:SetClickability(not v347)
        end
    end
    if u275.Visible then
        if u64 then
            if u24[u25:GetLastInputType()] then
                u313()
                u299.Visible = not u25.VREnabled
            end
            enableGamepadInventoryControl()
        end
    else
        if u64 then
            u299.Visible = false
        end
        disableGamepadInventoryControl()
    end
    if u275.Visible then
        u32:BindAction("RBXRemoveSlot", u346, false, Enum.KeyCode.ButtonX)
    else
        u32:UnbindAction("RBXRemoveSlot")
    end
    u1.IsOpen = u275.Visible
    u1.StateChanged:Fire(u275.Visible)
end
u28:SetCoreGuiEnabled(Enum.CoreGuiType.Backpack, false)
u1.OpenClose = v349
while not u41 do
    wait()
    u41 = v26.LocalPlayer
end
u41.CharacterAdded:Connect(v225)
if u41.Character then
    v225(u41.Character)
end
u25.InputBegan:Connect(v230)
u25.TextBoxFocused:Connect(function() --[[Anonymous function at line 1886]]
    --[[
    Upvalues:
        [1] = u60
    --]]
    u60 = true
end)
u25.TextBoxFocusReleased:Connect(function() --[[Anonymous function at line 1887]]
    --[[
    Upvalues:
        [1] = u60
    --]]
    u60 = false
end)
u55[u23] = function() --[[Anonymous function at line 1890]]
    --[[
    Upvalues:
        [1] = u49
    --]]
    if u49 then
        u49:UnequipTools()
    end
end
u25.Changed:Connect(v234)
local v350 = u25.KeyboardEnabled
if v350 then
    v350 = not u25.VREnabled
end
for v351 = 1, u67 do
    u52[v351]:TurnNumber(v350)
end
if u25:GetGamepadConnected(Enum.UserInputType.Gamepad1) then
    gamepadConnected()
end
u25.GamepadConnected:Connect(function(p352) --[[Anonymous function at line 1902]]
    if p352 == Enum.UserInputType.Gamepad1 then
        gamepadConnected()
    end
end)
u25.GamepadDisconnected:Connect(function(p353) --[[Anonymous function at line 1907]]
    if p353 == Enum.UserInputType.Gamepad1 then
        gamepadDisconnected()
    end
end)
function u1.SetBackpackEnabled(_, p354) --[[Anonymous function at line 1914]]
    --[[
    Upvalues:
        [1] = u17
    --]]
    u17 = p354
end
function u1.IsOpened(_) --[[Anonymous function at line 1918]]
    --[[
    Upvalues:
        [1] = u1
    --]]
    return u1.IsOpen
end
function u1.GetBackpackEnabled(_) --[[Anonymous function at line 1922]]
    --[[
    Upvalues:
        [1] = u17
    --]]
    return u17
end
function u1.GetStateChangedEvent(_) --[[Anonymous function at line 1926]]
    --[[
    Upvalues:
        [1] = u50
    --]]
    return u50.StateChanged
end
v33.Heartbeat:Connect(function() --[[Anonymous function at line 1930]]
    --[[
    Upvalues:
        [1] = u261
        [2] = u17
    --]]
    u261(u17)
end)
v14.Event:Connect(function(p355, p356) --[[Anonymous function at line 1933]]
    --[[
    Upvalues:
        [1] = u1
    --]]
    if p355 == "SetBackpackEnabled" then
        u1:SetBackpackEnabled(p356)
    elseif p355 == "SetInventoryOpen" then
        if type(p356) == "boolean" and p356 == true then
            u1.IsOpen = true
            return
        end
        if type(p356) == "boolean" then
            u1.IsOpen = false
            return
        end
    elseif p355 == "ToggleBackpack" then
        u1.OpenClose()
    end
end)
return u1