-- Full Path: -Grow-a-Garden-\StarterPlayer\StarterPlayerScripts\InventorySorter-LocalScript.lua
local v1 = game:GetService("Players").LocalPlayer:WaitForChild("PlayerGui")
local u2 = game:GetService("ReplicatedStorage").GameEvents:WaitF<PERSON><PERSON>hild("SortBackpackEvent")
local u3 = v1:Wait<PERSON><PERSON><PERSON>hil<PERSON>("BackpackGui"):Wait<PERSON><PERSON><PERSON>hil<PERSON>("Backpack"):WaitFor<PERSON>hild("Inventory")
local u4 = Instance.new("TextButton")
u4.Name = "SortButton"
u4.Text = "Sort"
u4.Size = UDim2.new(0, 80, 0, 30)
u4.Position = UDim2.new(1, 10, 0.5, -15)
u4.AnchorPoint = Vector2.new(0, 0.5)
u4.BackgroundColor3 = Color3.fromRGB(50, 50, 50)
u4.TextColor3 = Color3.fromRGB(255, 255, 255)
u4.Parent = u3
u3:GetPropertyChangedSignal("Visible"):Connect(function() --[[Anonymous function at line 20]]
    --[[
    Upvalues:
        [1] = u4
        [2] = u3
    --]]
    u4.Visible = u3.Visible
end)
u4.Visible = u3.Visible
u4.MouseButton1Click:Connect(function() --[[Anonymous function at line 25]]
    --[[
    Upvalues:
        [1] = u2
    --]]
    u2:FireServer()
end)