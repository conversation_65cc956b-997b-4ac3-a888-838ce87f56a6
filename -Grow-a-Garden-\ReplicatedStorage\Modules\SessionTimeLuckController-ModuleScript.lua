-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\SessionTimeLuckController-ModuleScript.lua
local v1 = game:GetService("Players")
local u2 = game:GetService("ReplicatedStorage")
local u3 = require(u2.Data.SessionTimeLuckData)
local u4 = require(u2.Modules.GetFarm)
local u5 = v1.LocalPlayer
local v18 = {
    ["GetCurrentLuck"] = function(_) --[[Function name: GetCurrentLuck, line 12]]
        --[[
        Upvalues:
            [1] = u5
            [2] = u3
        --]]
        if u5:GetAttribute("SessionTimeLuckDisabled") then
            return 0
        end
        local v6 = u5:GetAttribute("SessionTimeLuckIndex") or 1
        local v7 = u3.Timer[v6 or 1]
        return not v7 and 0 or v7.Luck
    end,
    ["Start"] = function(_) --[[Function name: Start, line 26]]
        --[[
        Upvalues:
            [1] = u4
            [2] = u5
            [3] = u2
            [4] = u3
        --]]
        local v8 = nil
        while true do
            v8 = v8 or u4(u5)
            if v8 then
                break
            end
            task.wait(1)
        end
        local u9 = u2.Assets.Models.Luckboard:Clone()
        local v10 = v8:WaitForChild("LuckboardSpawn", 999)
        if v10 then
            u9:PivotTo(v10:GetPivot())
            for v11, v12 in u3.Timer do
                local v13 = u9.Main.SurfaceGui.Frame:FindFirstChild((tostring(v11)))
                if v13 then
                    v13.Seconds.Text = ("%* Minutes"):format(v12.Time // 60)
                    v13.TextLabel.Text = ("+%*%% Luck"):format(v12.Luck * 100)
                end
            end
            local u14 = u9.Main.SurfaceGui.Frame.ActiveBoost.TextLabel
            local function v17() --[[Anonymous function at line 59]]
                --[[
                Upvalues:
                    [1] = u5
                    [2] = u9
                    [3] = u2
                    [4] = u3
                    [5] = u14
                --]]
                if u5:GetAttribute("SessionTimeLuckDisabled") then
                    u9.Parent = u2
                else
                    u9.Parent = workspace.MapDecorations
                    local v15 = u5:GetAttribute("SessionTimeLuckIndex") or 1
                    local v16 = u3.Timer[v15 or 1]
                    u14.Text = not v16 and "1x Luck" or ("+%*%% Luck"):format(v16.Luck * 100)
                end
            end
            u5:GetAttributeChangedSignal("SessionTimeLuckIndex"):Connect(v17)
            u5:GetAttributeChangedSignal("SessionTimeLuckDisabled"):Connect(v17)
            task.spawn(v17)
        else
            u9:Destroy()
        end
    end
}
task.spawn(v18.Start, v18)
return v18