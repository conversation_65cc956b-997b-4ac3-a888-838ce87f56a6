-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Data\GrowableData-ModuleScript.lua
local u1 = game:GetService("ReplicatedStorage"):WaitForChild("Fruit_Spawn")
local v2 = game:GetService("RunService")
local v3 = {}
local u4 = {
    ["ChocolateRain"] = {
        ["GrowRateMulti"] = 1.5
    },
    ["DJJhai"] = {
        ["GrowRateMulti"] = 1.5
    },
    ["Disco"] = {
        ["GrowRateMulti"] = 1.5
    },
    ["Frost"] = {
        ["GrowRateMulti"] = 1.5
    },
    ["JandelLazer"] = {
        ["GrowRateMulti"] = 1.5
    },
    ["JandelStorm"] = {
        ["GrowRateMulti"] = 1.5
    },
    ["MeteorShower"] = {
        ["GrowRateMulti"] = 1.5
    },
    ["Rain"] = {
        ["GrowRateMulti"] = 1.5
    },
    ["SheckleRain"] = {
        ["GrowRateMulti"] = 1.5
    },
    ["Thunderstorm"] = {
        ["GrowRateMulti"] = 1.5
    },
    ["Tornado"] = {
        ["GrowRateMulti"] = 1.5
    },
    ["Default"] = {
        ["GrowRateMulti"] = 1
    }
}
local u5 = {}
local v6 = {
    ["PlantName"] = "Carrot",
    ["PlantData"] = {
        ["GrowTickTime"] = NumberRange.new(0.8, 1.2),
        ["GrowRate"] = 0.4,
        ["PlantDown"] = 0.25
    }
}
u5.Carrot = v6
local v7 = {
    ["PlantName"] = "Tomato",
    ["PlantData"] = {
        ["GrowTickTime"] = NumberRange.new(1.6, 2.4),
        ["GrowFruitTime"] = NumberRange.new(40, 60),
        ["GrowRate"] = 0.02,
        ["PlantDown"] = 0.25
    },
    ["FruitData"] = {
        ["GrowTickTime"] = NumberRange.new(0.8, 1.2),
        ["GrowRate"] = 0.05,
        ["PlantDown"] = 0.25
    }
}
u5.Tomato = v7
local v8 = {
    ["PlantName"] = "Apple",
    ["PlantData"] = {
        ["GrowTickTime"] = NumberRange.new(1.6, 2.4),
        ["GrowFruitTime"] = NumberRange.new(40, 60),
        ["GrowRate"] = 0.02,
        ["PlantDown"] = -0.5
    },
    ["FruitData"] = {
        ["GrowTickTime"] = NumberRange.new(0.8, 1.2),
        ["GrowRate"] = 0.05,
        ["PlantDown"] = 0.25
    }
}
u5.Apple = v8
local v9 = {
    ["PlantName"] = "Blue Lollipop",
    ["PlantData"] = {
        ["GrowTickTime"] = NumberRange.new(1.2, 1.4),
        ["GrowRate"] = 0.6,
        ["PlantDown"] = 0.25
    }
}
u5["Blue Lollipop"] = v9
local v10 = {
    ["PlantName"] = "Cherry Blossom",
    ["PlantData"] = {
        ["GrowTickTime"] = NumberRange.new(6, 8),
        ["GrowFruitTime"] = NumberRange.new(80, 160),
        ["GrowRate"] = 0.05,
        ["PlantDown"] = -0.5
    },
    ["FruitData"] = {
        ["GrowTickTime"] = NumberRange.new(1, 2),
        ["GrowRate"] = 0.05,
        ["PlantDown"] = 0.25
    }
}
u5["Cherry Blossom"] = v10
local v11 = {
    ["PlantName"] = "Pumpkin",
    ["PlantData"] = {
        ["GrowTickTime"] = NumberRange.new(1.6, 2.4),
        ["GrowRate"] = 0.005,
        ["PlantDown"] = -0.25
    }
}
u5.Pumpkin = v11
local v12 = {
    ["PlantName"] = "Watermelon",
    ["PlantData"] = {
        ["GrowTickTime"] = NumberRange.new(1.6, 2.4),
        ["GrowRate"] = 0.01,
        ["PlantDown"] = -0.25
    }
}
u5.Watermelon = v12
local v13 = {
    ["PlantName"] = "Coconut",
    ["PlantData"] = {
        ["GrowTickTime"] = NumberRange.new(1.6, 2.4),
        ["GrowFruitTime"] = NumberRange.new(40, 60),
        ["GrowRate"] = 0.001,
        ["PlantDown"] = -0.5
    },
    ["FruitData"] = {
        ["GrowTickTime"] = NumberRange.new(0.8, 1.2),
        ["GrowRate"] = 0.05,
        ["PlantDown"] = 0.25
    }
}
u5.Coconut = v13
local v14 = {
    ["PlantName"] = "Candy Blossom",
    ["PlantData"] = {
        ["GrowTickTime"] = NumberRange.new(4, 6),
        ["GrowFruitTime"] = NumberRange.new(120, 180),
        ["GrowRate"] = 0.05,
        ["PlantDown"] = -0.5
    },
    ["FruitData"] = {
        ["GrowTickTime"] = NumberRange.new(4, 5),
        ["GrowRate"] = 0.05,
        ["PlantDown"] = 0.25
    }
}
u5["Candy Blossom"] = v14
local v15 = {
    ["PlantName"] = "Beanstalk",
    ["PlantData"] = {
        ["GrowTickTime"] = NumberRange.new(8, 12),
        ["GrowFruitTime"] = NumberRange.new(290, 310),
        ["GrowRate"] = 0.05,
        ["PlantDown"] = 1
    },
    ["FruitData"] = {
        ["GrowTickTime"] = NumberRange.new(0.8, 1.2),
        ["GrowRate"] = 0.05,
        ["PlantDown"] = -0.25
    }
}
u5.Beanstalk = v15
local v16 = {
    ["PlantName"] = "Candy Sunflower",
    ["PlantData"] = {
        ["GrowTickTime"] = NumberRange.new(3, 4),
        ["GrowRate"] = 0.1,
        ["PlantDown"] = 0.25
    }
}
u5["Candy Sunflower"] = v16
local v17 = {
    ["PlantName"] = "Grape",
    ["PlantData"] = {
        ["GrowTickTime"] = NumberRange.new(8, 12),
        ["GrowFruitTime"] = NumberRange.new(290, 310),
        ["GrowRate"] = 0.05,
        ["PlantDown"] = 1
    },
    ["FruitData"] = {
        ["GrowTickTime"] = NumberRange.new(0.8, 1.2),
        ["GrowRate"] = 0.05,
        ["PlantDown"] = -0.25
    }
}
u5.Grape = v17
local v18 = {
    ["PlantName"] = "Venus Fly Trap",
    ["PlantData"] = {
        ["GrowTickTime"] = NumberRange.new(6, 8),
        ["GrowFruitTime"] = NumberRange.new(120, 160),
        ["GrowRate"] = 0.05,
        ["PlantDown"] = -0.5
    },
    ["FruitData"] = {
        ["GrowTickTime"] = NumberRange.new(3, 5),
        ["GrowRate"] = 0.05,
        ["PlantDown"] = 0.25
    }
}
u5["Venus Fly Trap"] = v18
local v19 = {
    ["PlantName"] = "Raspberry",
    ["PlantData"] = {
        ["GrowTickTime"] = NumberRange.new(1.6, 2.4),
        ["GrowFruitTime"] = NumberRange.new(40, 60),
        ["GrowRate"] = 0.02,
        ["PlantDown"] = -0.5
    },
    ["FruitData"] = {
        ["GrowTickTime"] = NumberRange.new(0.8, 1.2),
        ["GrowRate"] = 0.05,
        ["PlantDown"] = 0.25
    }
}
u5.Raspberry = v19
local v20 = {
    ["PlantName"] = "Peach",
    ["PlantData"] = {
        ["GrowTickTime"] = NumberRange.new(4, 6),
        ["GrowFruitTime"] = NumberRange.new(110, 130),
        ["GrowRate"] = 0.05,
        ["PlantDown"] = 2
    },
    ["FruitData"] = {
        ["GrowTickTime"] = NumberRange.new(0.8, 1.2),
        ["GrowRate"] = 0.025,
        ["PlantDown"] = -0.25
    }
}
u5.Peach = v20
local v21 = {
    ["PlantName"] = "Passionfruit",
    ["PlantData"] = {
        ["GrowTickTime"] = NumberRange.new(3, 5),
        ["GrowFruitTime"] = NumberRange.new(2, 4),
        ["GrowRate"] = 0.05,
        ["PlantDown"] = -0.5
    },
    ["FruitData"] = {
        ["GrowTickTime"] = NumberRange.new(1, 2),
        ["GrowRate"] = 0.05,
        ["PlantDown"] = 0.25
    }
}
u5.Passionfruit = v21
local v22 = {
    ["PlantName"] = "Pear",
    ["PlantData"] = {
        ["GrowTickTime"] = NumberRange.new(1.6, 2.4),
        ["GrowFruitTime"] = NumberRange.new(40, 60),
        ["GrowRate"] = 0.02,
        ["PlantDown"] = 0.25
    },
    ["FruitData"] = {
        ["GrowTickTime"] = NumberRange.new(0.8, 1.2),
        ["GrowRate"] = 0.05,
        ["PlantDown"] = 0.25
    }
}
u5.Pear = v22
local v23 = {
    ["PlantName"] = "Blueberry",
    ["PlantData"] = {
        ["GrowTickTime"] = NumberRange.new(1.6, 2.4),
        ["GrowFruitTime"] = NumberRange.new(40, 60),
        ["GrowRate"] = 0.02,
        ["PlantDown"] = -0.5
    },
    ["FruitData"] = {
        ["GrowTickTime"] = NumberRange.new(0.8, 1.2),
        ["GrowRate"] = 0.05,
        ["PlantDown"] = 0.25
    }
}
u5.Blueberry = v23
local v24 = {
    ["PlantName"] = "Celestiberry",
    ["PlantData"] = {
        ["GrowTickTime"] = NumberRange.new(0.6, 0.85),
        ["GrowFruitTime"] = NumberRange.new(170, 190),
        ["GrowRate"] = 0.02,
        ["PlantDown"] = -0.5
    },
    ["FruitData"] = {
        ["GrowTickTime"] = NumberRange.new(3, 4),
        ["GrowRate"] = 0.04,
        ["PlantDown"] = 0.25
    }
}
u5.Celestiberry = v24
local v25 = {
    ["PlantName"] = "Eggplant",
    ["PlantData"] = {
        ["GrowTickTime"] = NumberRange.new(4, 7),
        ["GrowFruitTime"] = NumberRange.new(110, 130),
        ["GrowRate"] = 0.05,
        ["PlantDown"] = -0.5
    },
    ["FruitData"] = {
        ["GrowTickTime"] = NumberRange.new(1, 2),
        ["GrowRate"] = 0.05,
        ["PlantDown"] = 0.25
    }
}
u5.Eggplant = v25
local v26 = {
    ["PlantName"] = "Easter Egg",
    ["PlantData"] = {
        ["GrowTickTime"] = NumberRange.new(2, 3),
        ["GrowFruitTime"] = NumberRange.new(120, 130),
        ["GrowRate"] = 0.05,
        ["PlantDown"] = -0.5
    },
    ["FruitData"] = {
        ["GrowTickTime"] = NumberRange.new(1.5, 2),
        ["GrowRate"] = 0.05,
        ["PlantDown"] = 0.25
    }
}
u5["Easter Egg"] = v26
local v27 = {
    ["PlantName"] = "Purple Cabbage",
    ["PlantData"] = {
        ["GrowTickTime"] = NumberRange.new(4, 7),
        ["GrowFruitTime"] = NumberRange.new(10, 20),
        ["GrowRate"] = 0.05,
        ["PlantDown"] = -0.5
    },
    ["FruitData"] = {
        ["GrowTickTime"] = NumberRange.new(1, 2),
        ["GrowRate"] = 0.05,
        ["PlantDown"] = 0.25
    }
}
u5["Purple Cabbage"] = v27
local v28 = {
    ["PlantName"] = "Lemon",
    ["PlantData"] = {
        ["GrowTickTime"] = NumberRange.new(4, 6),
        ["GrowFruitTime"] = NumberRange.new(110, 130),
        ["GrowRate"] = 0.05,
        ["PlantDown"] = -0.5
    },
    ["FruitData"] = {
        ["GrowTickTime"] = NumberRange.new(1, 2),
        ["GrowRate"] = 0.05,
        ["PlantDown"] = 0.25
    }
}
u5.Lemon = v28
local v29 = {
    ["PlantName"] = "Chocolate Carrot",
    ["PlantData"] = {
        ["GrowTickTime"] = NumberRange.new(1.5, 2.3),
        ["GrowRate"] = 0.4,
        ["PlantDown"] = 0.25
    }
}
u5["Chocolate Carrot"] = v29
local v30 = {
    ["PlantName"] = "Soul Fruit",
    ["PlantData"] = {
        ["GrowTickTime"] = NumberRange.new(1.5, 2),
        ["GrowFruitTime"] = NumberRange.new(200, 260),
        ["GrowRate"] = 0.05,
        ["PlantDown"] = -0.5
    },
    ["FruitData"] = {
        ["GrowTickTime"] = NumberRange.new(0.8, 1.3),
        ["GrowRate"] = 0.05,
        ["PlantDown"] = 0.25
    }
}
u5["Soul Fruit"] = v30
local v31 = {
    ["PlantName"] = "Crocus",
    ["PlantData"] = {
        ["GrowTickTime"] = NumberRange.new(0.8, 1.2),
        ["GrowRate"] = 0.4,
        ["PlantDown"] = 0.25
    }
}
u5.Crocus = v31
local v32 = {
    ["PlantName"] = "Pink Tulip",
    ["PlantData"] = {
        ["GrowTickTime"] = NumberRange.new(0.8, 1.2),
        ["GrowRate"] = 0.4,
        ["PlantDown"] = 0.25
    }
}
u5["Pink Tulip"] = v32
local v33 = {
    ["PlantName"] = "Orange Tulip",
    ["PlantData"] = {
        ["GrowTickTime"] = NumberRange.new(0.8, 1.2),
        ["GrowRate"] = 0.4,
        ["PlantDown"] = 0.25
    }
}
u5["Orange Tulip"] = v33
local v34 = {
    ["PlantName"] = "Daffodil",
    ["PlantData"] = {
        ["GrowTickTime"] = NumberRange.new(0.8, 1.2),
        ["GrowRate"] = 0.4,
        ["PlantDown"] = 0.25
    }
}
u5.Daffodil = v34
local v35 = {
    ["PlantName"] = "Cherry OLD",
    ["PlantData"] = {
        ["GrowTickTime"] = NumberRange.new(4, 6),
        ["GrowFruitTime"] = NumberRange.new(290, 310),
        ["GrowRate"] = 0.05,
        ["PlantDown"] = -0.5
    },
    ["FruitData"] = {
        ["GrowTickTime"] = NumberRange.new(0.8, 1.2),
        ["GrowRate"] = 0.05,
        ["PlantDown"] = -0.25
    }
}
u5["Cherry OLD"] = v35
local v36 = {
    ["PlantName"] = "Banana",
    ["PlantData"] = {
        ["GrowTickTime"] = NumberRange.new(6, 9),
        ["GrowFruitTime"] = NumberRange.new(80, 140),
        ["GrowRate"] = 0.05,
        ["PlantDown"] = -0.5
    },
    ["FruitData"] = {
        ["GrowTickTime"] = NumberRange.new(1, 2),
        ["GrowRate"] = 0.05,
        ["PlantDown"] = 0.25
    }
}
u5.Banana = v36
local v37 = {
    ["PlantName"] = "Red Lollipop",
    ["PlantData"] = {
        ["GrowTickTime"] = NumberRange.new(1.2, 1.4),
        ["GrowRate"] = 0.6,
        ["PlantDown"] = 0.25
    }
}
u5["Red Lollipop"] = v37
local v38 = {
    ["PlantName"] = "Bamboo",
    ["PlantData"] = {
        ["GrowTickTime"] = NumberRange.new(1.5, 3),
        ["GrowRate"] = 0.1875,
        ["PlantDown"] = -0.5
    }
}
u5.Bamboo = v38
local v39 = {
    ["PlantName"] = "Mushroom",
    ["PlantData"] = {
        ["GrowTickTime"] = NumberRange.new(3, 5),
        ["GrowRate"] = 0.05,
        ["PlantDown"] = -0.5
    }
}
u5.Mushroom = v39
local v40 = {
    ["PlantName"] = "Mega Mushroom",
    ["PlantData"] = {
        ["GrowTickTime"] = NumberRange.new(8, 12),
        ["GrowFruitTime"] = NumberRange.new(290, 310),
        ["GrowRate"] = 0.05,
        ["PlantDown"] = -0.5
    }
}
u5["Mega Mushroom"] = v40
local v41 = {
    ["PlantName"] = "Corn",
    ["PlantData"] = {
        ["GrowTickTime"] = NumberRange.new(1.6, 2.4),
        ["GrowFruitTime"] = NumberRange.new(70, 90),
        ["GrowRate"] = 0.005,
        ["PlantDown"] = 0.25
    },
    ["FruitData"] = {
        ["GrowTickTime"] = NumberRange.new(0.8, 1.2),
        ["GrowRate"] = 0.05,
        ["PlantDown"] = 0.25
    }
}
u5.Corn = v41
local v42 = {
    ["PlantName"] = "Cactus",
    ["PlantData"] = {
        ["GrowTickTime"] = NumberRange.new(8, 12),
        ["GrowFruitTime"] = NumberRange.new(290, 310),
        ["GrowRate"] = 0.05,
        ["PlantDown"] = 2
    },
    ["FruitData"] = {
        ["GrowTickTime"] = NumberRange.new(0.8, 1.2),
        ["GrowRate"] = 0.05,
        ["PlantDown"] = -0.25
    }
}
u5.Cactus = v42
local v43 = {
    ["PlantName"] = "Mango",
    ["PlantData"] = {
        ["GrowTickTime"] = NumberRange.new(4, 6),
        ["GrowFruitTime"] = NumberRange.new(290, 310),
        ["GrowRate"] = 0.05,
        ["PlantDown"] = 2
    },
    ["FruitData"] = {
        ["GrowTickTime"] = NumberRange.new(0.8, 1.2),
        ["GrowRate"] = 0.025,
        ["PlantDown"] = -0.25
    }
}
u5.Mango = v43
local v44 = {
    ["PlantName"] = "Moon Mango",
    ["PlantData"] = {
        ["GrowTickTime"] = NumberRange.new(5, 6.5),
        ["GrowFruitTime"] = NumberRange.new(1150, 1250),
        ["GrowRate"] = 0.01,
        ["PlantDown"] = 2
    },
    ["FruitData"] = {
        ["GrowTickTime"] = NumberRange.new(0.8, 1.2),
        ["GrowRate"] = 0.025,
        ["PlantDown"] = -0.25
    }
}
u5["Moon Mango"] = v44
local v45 = {
    ["PlantName"] = "Dragon Fruit",
    ["PlantData"] = {
        ["GrowTickTime"] = NumberRange.new(8, 12),
        ["GrowFruitTime"] = NumberRange.new(290, 310),
        ["GrowRate"] = 0.05,
        ["PlantDown"] = -0.5
    },
    ["FruitData"] = {
        ["GrowTickTime"] = NumberRange.new(1, 2),
        ["GrowRate"] = 0.05,
        ["PlantDown"] = -0.25
    }
}
u5["Dragon Fruit"] = v45
local v46 = {
    ["PlantName"] = "Pineapple",
    ["PlantData"] = {
        ["GrowTickTime"] = NumberRange.new(1.6, 2.4),
        ["GrowFruitTime"] = NumberRange.new(70, 90),
        ["GrowRate"] = 0.02,
        ["PlantDown"] = 0.25
    },
    ["FruitData"] = {
        ["GrowTickTime"] = NumberRange.new(0.8, 1.2),
        ["GrowRate"] = 0.05,
        ["PlantDown"] = 0.25
    }
}
u5.Pineapple = v46
local v47 = {
    ["PlantName"] = "Strawberry",
    ["PlantData"] = {
        ["GrowTickTime"] = NumberRange.new(1.6, 2.4),
        ["GrowFruitTime"] = NumberRange.new(13, 18),
        ["GrowRate"] = 0.1,
        ["PlantDown"] = 0.1
    },
    ["FruitData"] = {
        ["GrowTickTime"] = NumberRange.new(0.8, 1.2),
        ["GrowRate"] = 0.05,
        ["PlantDown"] = 0.25
    }
}
u5.Strawberry = v47
local v48 = {
    ["PlantName"] = "Cranberry",
    ["PlantData"] = {
        ["GrowTickTime"] = NumberRange.new(4, 7),
        ["GrowFruitTime"] = NumberRange.new(110, 130),
        ["GrowRate"] = 0.05,
        ["PlantDown"] = -0.5
    },
    ["FruitData"] = {
        ["GrowTickTime"] = NumberRange.new(1, 2),
        ["GrowRate"] = 0.05,
        ["PlantDown"] = 0.25
    }
}
u5.Cranberry = v48
local v49 = {
    ["PlantName"] = "Pepper",
    ["PlantData"] = {
        ["GrowTickTime"] = NumberRange.new(3, 4.5),
        ["GrowFruitTime"] = NumberRange.new(80, 160),
        ["GrowRate"] = 0.05,
        ["PlantDown"] = -0.5
    },
    ["FruitData"] = {
        ["GrowTickTime"] = NumberRange.new(0.8, 1.2),
        ["GrowRate"] = 0.05,
        ["PlantDown"] = 0.25
    }
}
u5.Pepper = v49
local v50 = {
    ["PlantName"] = "Succulent",
    ["PlantData"] = {
        ["GrowTickTime"] = NumberRange.new(5, 7),
        ["GrowFruitTime"] = NumberRange.new(110, 130),
        ["GrowRate"] = 0.05,
        ["PlantDown"] = -0.5
    },
    ["FruitData"] = {
        ["GrowTickTime"] = NumberRange.new(2, 3),
        ["GrowRate"] = 0.05,
        ["PlantDown"] = 0.25
    }
}
u5.Succulent = v50
local v51 = {
    ["PlantName"] = "Papaya",
    ["PlantData"] = {
        ["GrowTickTime"] = NumberRange.new(6, 8),
        ["GrowFruitTime"] = NumberRange.new(200, 260),
        ["GrowRate"] = 0.05,
        ["PlantDown"] = -0.5
    },
    ["FruitData"] = {
        ["GrowTickTime"] = NumberRange.new(1, 2),
        ["GrowRate"] = 0.05,
        ["PlantDown"] = 0.25
    }
}
u5.Papaya = v51
local v52 = {
    ["PlantName"] = "Lotus",
    ["PlantData"] = {
        ["GrowTickTime"] = NumberRange.new(2, 3.5),
        ["GrowFruitTime"] = NumberRange.new(200, 230),
        ["GrowRate"] = 0.05,
        ["PlantDown"] = -0.5
    },
    ["FruitData"] = {
        ["GrowTickTime"] = NumberRange.new(2, 3),
        ["GrowRate"] = 0.05,
        ["PlantDown"] = 0.25
    }
}
u5.Lotus = v52
local v53 = {
    ["PlantName"] = "Cacao",
    ["PlantData"] = {
        ["GrowTickTime"] = NumberRange.new(4, 6),
        ["GrowFruitTime"] = NumberRange.new(80, 160),
        ["GrowRate"] = 0.05,
        ["PlantDown"] = -0.5
    },
    ["FruitData"] = {
        ["GrowTickTime"] = NumberRange.new(1, 2),
        ["GrowRate"] = 0.05,
        ["PlantDown"] = 0.25
    }
}
u5.Cacao = v53
local v54 = {
    ["PlantName"] = "Avocado",
    ["PlantData"] = {
        ["GrowTickTime"] = NumberRange.new(3, 4.5),
        ["GrowFruitTime"] = NumberRange.new(80, 160),
        ["GrowRate"] = 0.05,
        ["PlantDown"] = -0.5
    },
    ["FruitData"] = {
        ["GrowTickTime"] = NumberRange.new(0.8, 1.2),
        ["GrowRate"] = 0.05,
        ["PlantDown"] = 0.25
    }
}
u5.Avocado = v54
local v55 = {
    ["PlantName"] = "Starfruit",
    ["PlantData"] = {
        ["GrowTickTime"] = NumberRange.new(4, 6),
        ["GrowFruitTime"] = NumberRange.new(80, 160),
        ["GrowRate"] = 0.05,
        ["PlantDown"] = -0.5
    },
    ["FruitData"] = {
        ["GrowTickTime"] = NumberRange.new(0.8, 1.2),
        ["GrowRate"] = 0.05,
        ["PlantDown"] = 0.25
    }
}
u5.Starfruit = v55
local v56 = {
    ["PlantName"] = "Mint",
    ["PlantData"] = {
        ["GrowTickTime"] = NumberRange.new(2, 4),
        ["GrowFruitTime"] = NumberRange.new(90, 120),
        ["GrowRate"] = 0.1875,
        ["PlantDown"] = -0.5
    },
    ["FruitData"] = {
        ["GrowTickTime"] = NumberRange.new(2, 4),
        ["GrowRate"] = 0.05,
        ["PlantDown"] = 0.25
    }
}
u5.Mint = v56
local v57 = {
    ["PlantName"] = "Moon Blossom",
    ["PlantData"] = {
        ["GrowTickTime"] = NumberRange.new(4, 6),
        ["GrowFruitTime"] = NumberRange.new(120, 180),
        ["GrowRate"] = 0.05,
        ["PlantDown"] = -0.5
    },
    ["FruitData"] = {
        ["GrowTickTime"] = NumberRange.new(1, 2),
        ["GrowRate"] = 0.05,
        ["PlantDown"] = 0.25
    }
}
u5["Moon Blossom"] = v57
local v58 = {
    ["PlantName"] = "Nightshade",
    ["PlantData"] = {
        ["GrowTickTime"] = NumberRange.new(1.5, 3),
        ["GrowRate"] = 0.1875,
        ["PlantDown"] = -0.5
    }
}
u5.Nightshade = v58
local v59 = {
    ["PlantName"] = "Durian",
    ["PlantData"] = {
        ["GrowTickTime"] = NumberRange.new(5, 7.5),
        ["GrowFruitTime"] = NumberRange.new(200, 230),
        ["GrowRate"] = 0.05,
        ["PlantDown"] = -0.5
    },
    ["FruitData"] = {
        ["GrowTickTime"] = NumberRange.new(2, 3),
        ["GrowRate"] = 0.05,
        ["PlantDown"] = 0.25
    }
}
u5.Durian = v59
local v60 = {
    ["PlantName"] = "Moonflower",
    ["PlantData"] = {
        ["GrowTickTime"] = NumberRange.new(2, 4),
        ["GrowFruitTime"] = NumberRange.new(60, 90),
        ["GrowRate"] = 0.1875,
        ["PlantDown"] = -0.5
    },
    ["FruitData"] = {
        ["GrowTickTime"] = NumberRange.new(0.8, 1.2),
        ["GrowRate"] = 0.05,
        ["PlantDown"] = 0.25
    }
}
u5.Moonflower = v60
local v61 = {
    ["PlantName"] = "Moonglow",
    ["PlantData"] = {
        ["GrowTickTime"] = NumberRange.new(3, 4.5),
        ["GrowFruitTime"] = NumberRange.new(80, 160),
        ["GrowRate"] = 0.05,
        ["PlantDown"] = -0.5
    },
    ["FruitData"] = {
        ["GrowTickTime"] = NumberRange.new(1, 2),
        ["GrowRate"] = 0.05,
        ["PlantDown"] = 0.25
    }
}
u5.Moonglow = v61
local v62 = {
    ["PlantName"] = "Glowshroom",
    ["PlantData"] = {
        ["GrowTickTime"] = NumberRange.new(2, 4),
        ["GrowFruitTime"] = NumberRange.new(60, 90),
        ["GrowRate"] = 0.1875,
        ["PlantDown"] = -0.5
    },
    ["FruitData"] = {
        ["GrowTickTime"] = NumberRange.new(1, 2),
        ["GrowRate"] = 0.05,
        ["PlantDown"] = 0.25
    }
}
u5.Glowshroom = v62
local v63 = {
    ["PlantName"] = "Cursed Fruit",
    ["PlantData"] = {
        ["GrowTickTime"] = NumberRange.new(6, 8),
        ["GrowFruitTime"] = NumberRange.new(120, 180),
        ["GrowRate"] = 0.05,
        ["PlantDown"] = -0.5
    },
    ["FruitData"] = {
        ["GrowTickTime"] = NumberRange.new(1, 2),
        ["GrowRate"] = 0.05,
        ["PlantDown"] = 0.25
    }
}
u5["Cursed Fruit"] = v63
local v64 = {
    ["PlantName"] = "Crimson Vine",
    ["PlantData"] = {
        ["GrowTickTime"] = NumberRange.new(1.6, 2.4),
        ["GrowFruitTime"] = NumberRange.new(40, 60),
        ["GrowRate"] = 0.02,
        ["PlantDown"] = -0.5
    },
    ["FruitData"] = {
        ["GrowTickTime"] = NumberRange.new(0.8, 1.2),
        ["GrowRate"] = 0.05,
        ["PlantDown"] = 0.25
    }
}
u5["Crimson Vine"] = v64
local v65 = {
    ["PlantName"] = "Moon Melon",
    ["PlantData"] = {
        ["GrowTickTime"] = NumberRange.new(8, 12),
        ["GrowFruitTime"] = NumberRange.new(140, 200),
        ["GrowRate"] = 0.05,
        ["PlantDown"] = -0.5
    },
    ["FruitData"] = {
        ["GrowTickTime"] = NumberRange.new(15, 20),
        ["GrowRate"] = 0.05,
        ["PlantDown"] = 0.25
    }
}
u5["Moon Melon"] = v65
local v66 = {
    ["PlantName"] = "Blood Banana",
    ["PlantData"] = {
        ["GrowTickTime"] = NumberRange.new(6, 9),
        ["GrowFruitTime"] = NumberRange.new(80, 140),
        ["GrowRate"] = 0.05,
        ["PlantDown"] = -0.5
    },
    ["FruitData"] = {
        ["GrowTickTime"] = NumberRange.new(1, 2),
        ["GrowRate"] = 0.05,
        ["PlantDown"] = 0.25
    }
}
u5["Blood Banana"] = v66
function v3.GetDataForWeather(_, p67) --[[Anonymous function at line 882]]
    --[[
    Upvalues:
        [1] = u4
    --]]
    if not p67 or type(p67) ~= "string" then
        warn("GrowableData:GetDataForWeather | weatherName is not applicable!")
        return u4.Default
    end
    local v68 = u4[p67]
    if v68 then
        return v68
    end
    warn("GrowableData:GetDataForWeather | weatherData is nil!")
    return u4.Default
end
function v3.GetDataForPlant(_, p69) --[[Anonymous function at line 897]]
    --[[
    Upvalues:
        [1] = u5
    --]]
    if not p69 then
        warn((("GrowableData:GetDataForPlan | %* is not a valid plant!"):format(not p69 and "nil" or p69.Name)))
        return nil
    end
    local v70 = p69.Parent and (p69.Parent.Name == "Fruits" or p69.Parent.Name == "Fruit_Spawn") and true or false
    local v71 = p69:FindFirstChild("Fruit_Spawn") and true or false
    local v72 = u5[p69.Name]
    if not v72 then
        warn((("GrowableData:GetDataForPlan | No GrowData for %*!"):format(p69.Name)))
        return nil
    end
    local v73 = nil
    if v70 and v72.FruitData then
        v73 = v72.FruitData
    elseif v72.PlantData then
        v73 = v72.PlantData
    end
    if not v73 then
        warn((("GrowableData:GetDataForPlan | %* has no PlantData or FruitData!"):format(p69.Name)))
        return nil
    end
    if not v73.GrowTickTime then
        warn((("GrowableData:GetDataForPlan | %* has no GrowTickTime!"):format(p69.Name)))
        return nil
    end
    if not v73.GrowRate then
        warn((("GrowableData:GetDataForPlan | %* has no GrowRate!"):format(p69.Name)))
        return nil
    end
    if v73.PlantDown == nil then
        warn((("GrowableData:GetDataForPlant | %* has no PlantDown!"):format(p69.Name)))
        return nil
    end
    if not v71 or v73.GrowFruitTime then
        return v73
    end
    warn((("GrowableData:GetDataForPlan | %* has no GrowFruitTime!"):format(p69.Name)))
    return nil
end
local function v80() --[[Anonymous function at line 1064]]
    --[[
    Upvalues:
        [1] = u5
        [2] = u1
    --]]
    local v74 = game:GetService("ServerStorage"):WaitForChild("Collectables")
    print("Validating GrowData against plants and fruits...")
    local v75 = 0
    for _, v76 in pairs(v74:GetChildren()) do
        local v77 = v76.Name
        if u5[v77] then
            if not (u5[v77].PlantData and u5[v77].PlantData.GrowTickTime) then
                warn("Missing GrowTickTime for plant: ", v76)
                v75 = v75 + 1
            end
            if not (u5[v77].PlantData and u5[v77].PlantData.GrowRate) then
                warn("Missing required GrowRate for plant: ", v76)
                v75 = v75 + 1
            end
            if not u5[v77].PlantData.PlantDown then
                warn("Missing required PlantDown for plant: ", v76)
                v75 = v75 + 1
            end
            if u1:FindFirstChild(v77) then
                if not u5[v77].PlantData.GrowFruitTime then
                    warn("Missing GrowFruitTime for plant with fruit: ", v76)
                    v75 = v75 + 1
                end
                if not (u5[v77].FruitData and u5[v77].FruitData.GrowTickTime) then
                    warn("Missing FruitData.GrowTickTime for fruit: ", v76)
                    v75 = v75 + 1
                end
                if not (u5[v77].FruitData and u5[v77].FruitData.GrowRate) then
                    warn("Missing required FruitData.GrowRate for fruit: ", v76)
                    v75 = v75 + 1
                end
            end
        else
            warn("Missing plant entry: ", v76)
            v75 = v75 + 1
        end
    end
    for _, v78 in pairs(u1:GetChildren()) do
        local v79 = v78.Name
        if u5[v79] then
            if u5[v79].FruitData then
                if u5[v79].FruitData and not u5[v79].FruitData.PlantDown then
                    warn("Missing required PlantDown for fruit: ", v78)
                    v75 = v75 + 1
                end
            else
                warn("Missing FruitData for fruit: ", v78)
                v75 = v75 + 1
            end
        else
            warn("Missing plant entry for fruit: ", v78)
            v75 = v75 + 1
        end
    end
    if v75 > 0 then
        print("Validation complete: " .. v75 .. " issues found.")
    else
        print("Validation passed. All plants and fruits are properly configured.")
    end
    return v75 == 0
end
if v2:IsStudio() and v2:IsServer() then
    v80()
end
return v3