-- Full Path: -Grow-a-Garden-\StarterPlayer\StarterPlayerScripts\PlayerModule\CameraModule\CameraInput-ModuleScript.lua
local u1 = game:GetService("ContextActionService")
local u2 = game:GetService("UserInputService")
local v3 = game:GetService("Players")
local v4 = game:GetService("RunService")
local u5 = UserSettings():GetService("UserGameSettings")
local u6 = game:GetService("VRService")
local v7 = script.Parent.Parent:WaitForChild("CommonUtils")
local u8 = require(v7:WaitForChild("FlagUtil")).getUserFlag("UserCameraInputDt")
local u9 = v3.LocalPlayer
local u10 = Enum.ContextActionPriority.Medium.Value
local u11 = Vector2.new(1, 0.77) * 0.06981317007977318
local u12 = Vector2.new(1, 0.77) * 0.008726646259971648
local u13 = Vector2.new(1, 0.77) * 0.12217304763960307
local u14 = Vector2.new(1, 0.66) * 0.017453292519943295
if u8 then
    u11 = u11 * 60
end
local v15, v16 = pcall(function() --[[Anonymous function at line 41]]
    return UserSettings():IsUserFeatureEnabled("UserResetTouchStateOnMenuOpen")
end)
local u17 = v15 and v16
local v18, v19 = pcall(function() --[[Anonymous function at line 49]]
    return UserSettings():IsUserFeatureEnabled("UserClearPanOnCameraDisable")
end)
local u20 = v18 and v19
local u21 = Instance.new("BindableEvent")
local u22 = Instance.new("BindableEvent")
local u23 = u21.Event
local u24 = u22.Event
u2.InputBegan:Connect(function(p25, p26) --[[Anonymous function at line 63]]
    --[[
    Upvalues:
        [1] = u21
    --]]
    if not p26 and p25.UserInputType == Enum.UserInputType.MouseButton2 then
        u21:Fire()
    end
end)
u2.InputEnded:Connect(function(p27, _) --[[Anonymous function at line 69]]
    --[[
    Upvalues:
        [1] = u22
    --]]
    if p27.UserInputType == Enum.UserInputType.MouseButton2 then
        u22:Fire()
    end
end)
local function u31(p28) --[[Anonymous function at line 80]]
    local v29 = (math.abs(p28) - 0.1) / 0.9 * 2
    local v30 = (math.exp(v29) - 1) / 6.38905609893065
    return math.sign(p28) * math.clamp(v30, 0, 1)
end
local function u36(p32) --[[Anonymous function at line 94]]
    local v33 = workspace.CurrentCamera
    if not v33 then
        return p32
    end
    local v34 = v33.CFrame:ToEulerAnglesYXZ()
    if p32.Y * v34 >= 0 then
        return p32
    end
    local v35 = (1 - (math.abs(v34) * 2 / 3.141592653589793) ^ 0.75) * 0.75 + 0.25
    return Vector2.new(1, v35) * p32
end
local function u43(p37) --[[Anonymous function at line 120]]
    --[[
    Upvalues:
        [1] = u9
    --]]
    local v38 = u9:FindFirstChildOfClass("PlayerGui")
    if v38 then
        v38 = v38:FindFirstChild("TouchGui")
    end
    local v39
    if v38 then
        v39 = v38:FindFirstChild("TouchControlFrame")
    else
        v39 = v38
    end
    if v39 then
        v39 = v39:FindFirstChild("DynamicThumbstickFrame")
    end
    if not v39 then
        return false
    end
    if not v38.Enabled then
        return false
    end
    local v40 = v39.AbsolutePosition
    local v41 = v40 + v39.AbsoluteSize
    local v42
    if p37.X >= v40.X and (p37.Y >= v40.Y and p37.X <= v41.X) then
        v42 = p37.Y <= v41.Y
    else
        v42 = false
    end
    return v42
end
local u44 = 0.016666666666666666
v4.Stepped:Connect(function(_, p45) --[[Anonymous function at line 145]]
    --[[
    Upvalues:
        [1] = u44
    --]]
    u44 = p45
end)
local v46 = {}
local u47 = {}
local u48 = 0
local u49 = {
    ["Thumbstick2"] = Vector2.new()
}
local u50 = {
    ["Left"] = 0,
    ["Right"] = 0,
    ["I"] = 0,
    ["O"] = 0
}
local u51 = {
    ["Movement"] = Vector2.new(),
    ["Wheel"] = 0,
    ["Pan"] = Vector2.new(),
    ["Pinch"] = 0
}
local u52 = {
    ["Move"] = Vector2.new(),
    ["Pinch"] = 0
}
local u53 = Instance.new("BindableEvent")
v46.gamepadZoomPress = u53.Event
local u54 = u6.VREnabled and Instance.new("BindableEvent") or nil
if u6.VREnabled then
    v46.gamepadReset = u54.Event
end
function v46.getRotationActivated() --[[Anonymous function at line 196]]
    --[[
    Upvalues:
        [1] = u48
        [2] = u49
    --]]
    return u48 > 0 and true or u49.Thumbstick2.Magnitude > 0
end
function v46.getRotation(p55, p56) --[[Anonymous function at line 200]]
    --[[
    Upvalues:
        [1] = u5
        [2] = u8
        [3] = u50
        [4] = u44
        [5] = u49
        [6] = u51
        [7] = u36
        [8] = u52
        [9] = u11
        [10] = u12
        [11] = u13
        [12] = u14
    --]]
    local v57 = Vector2.new(1, u5:GetCameraYInvertValue())
    local v58
    if u8 then
        v58 = Vector2.new(u50.Right - u50.Left, 0) * p55
    else
        v58 = Vector2.new(u50.Right - u50.Left, 0) * u44
    end
    local v59 = u49.Thumbstick2 * u5.GamepadCameraSensitivity
    if u8 then
        v59 = v59 * p55
    end
    local v60 = u51.Movement
    local v61 = u51.Pan
    local v62 = u36(u52.Move)
    if p56 then
        v58 = Vector2.new()
    end
    return (v58 * 2.0943951023931953 + v59 * u11 + v60 * u12 + v61 * u13 + v62 * u14) * v57
end
function v46.getZoomDelta() --[[Anonymous function at line 234]]
    --[[
    Upvalues:
        [1] = u50
        [2] = u51
        [3] = u52
    --]]
    local v63 = u50.O - u50.I
    local v64 = -u51.Wheel + u51.Pinch
    local v65 = -u52.Pinch
    return v63 * 0.1 + v64 * 1 + v65 * 0.04
end
local function u68(_, _, p66) --[[Anonymous function at line 242]]
    --[[
    Upvalues:
        [1] = u49
        [2] = u31
    --]]
    local v67 = p66.Position
    u49[p66.KeyCode.Name] = Vector2.new(u31(v67.X), -u31(v67.Y))
    return Enum.ContextActionResult.Pass
end
local function u71(_, p69, p70) --[[Anonymous function at line 258]]
    --[[
    Upvalues:
        [1] = u50
    --]]
    u50[p70.KeyCode.Name] = p69 == Enum.UserInputState.Begin and 1 or 0
end
local function u73(_, p72, _) --[[Anonymous function at line 262]]
    --[[
    Upvalues:
        [1] = u53
    --]]
    if p72 == Enum.UserInputState.Begin then
        u53:Fire()
    end
end
local function u75(_, p74, _) --[[Anonymous function at line 268]]
    --[[
    Upvalues:
        [1] = u54
    --]]
    if p74 == Enum.UserInputState.Begin then
        u54:Fire()
    end
end
local function u80() --[[Anonymous function at line 274]]
    --[[
    Upvalues:
        [1] = u49
        [2] = u50
        [3] = u51
        [4] = u52
        [5] = u20
        [6] = u48
    --]]
    local v76 = {
        u49,
        u50,
        u51,
        u52
    }
    for _, v77 in pairs(v76) do
        for v78, v79 in pairs(v77) do
            if type(v79) == "boolean" then
                v77[v78] = false
            else
                v77[v78] = v77[v78] * 0
            end
        end
    end
    if u20 then
        u48 = 0
    end
end
local u81 = {}
local u82 = nil
local u83 = nil
local function u89(p84, p85) --[[Anonymous function at line 302]]
    --[[
    Upvalues:
        [1] = u82
        [2] = u43
        [3] = u48
        [4] = u81
    --]]
    local v86 = p84.UserInputType == Enum.UserInputType.Touch
    assert(v86)
    local v87 = p84.UserInputState == Enum.UserInputState.Begin
    assert(v87)
    if u82 == nil and (u43(p84.Position) and not p85) then
        u82 = p84
    else
        if not p85 then
            local v88 = u48 + 1
            u48 = math.max(0, v88)
        end
        u81[p84] = p85
    end
end
local function u94(p90, _) --[[Anonymous function at line 322]]
    --[[
    Upvalues:
        [1] = u82
        [2] = u81
        [3] = u83
        [4] = u48
    --]]
    local v91 = p90.UserInputType == Enum.UserInputType.Touch
    assert(v91)
    local v92 = p90.UserInputState == Enum.UserInputState.End
    assert(v92)
    if p90 == u82 then
        u82 = nil
    end
    if u81[p90] == false then
        u83 = nil
        local v93 = u48 - 1
        u48 = math.max(0, v93)
    end
    u81[p90] = nil
end
local function u106(p95, p96) --[[Anonymous function at line 341]]
    --[[
    Upvalues:
        [1] = u82
        [2] = u81
        [3] = u52
        [4] = u83
    --]]
    local v97 = p95.UserInputType == Enum.UserInputType.Touch
    assert(v97)
    local v98 = p95.UserInputState == Enum.UserInputState.Change
    assert(v98)
    if p95 == u82 then
        return
    else
        if u81[p95] == nil then
            u81[p95] = p96
        end
        local v99 = {}
        for v100, v101 in pairs(u81) do
            if not v101 then
                table.insert(v99, v100)
            end
        end
        if #v99 == 1 and u81[p95] == false then
            local v102 = p95.Delta
            local v103 = u52
            v103.Move = v103.Move + Vector2.new(v102.X, v102.Y)
        end
        if #v99 == 2 then
            local v104 = (v99[1].Position - v99[2].Position).Magnitude
            if u83 then
                local v105 = u52
                v105.Pinch = v105.Pinch + (v104 - u83)
            end
            u83 = v104
        else
            u83 = nil
        end
    end
end
local function u107() --[[Anonymous function at line 385]]
    --[[
    Upvalues:
        [1] = u81
        [2] = u82
        [3] = u83
        [4] = u17
        [5] = u48
    --]]
    u81 = {}
    u82 = nil
    u83 = nil
    if u17 then
        u48 = 0
    end
end
local function u112(p108, p109, p110, p111) --[[Anonymous function at line 395]]
    --[[
    Upvalues:
        [1] = u51
    --]]
    if not p111 then
        u51.Wheel = p108
        u51.Pan = p109
        u51.Pinch = -p110
    end
end
local function u116(p113, p114) --[[Anonymous function at line 403]]
    --[[
    Upvalues:
        [1] = u89
        [2] = u48
    --]]
    if p113.UserInputType == Enum.UserInputType.Touch then
        u89(p113, p114)
    elseif p113.UserInputType == Enum.UserInputType.MouseButton2 and not p114 then
        local v115 = u48 + 1
        u48 = math.max(0, v115)
    end
end
local function u120(p117, p118) --[[Anonymous function at line 412]]
    --[[
    Upvalues:
        [1] = u106
        [2] = u51
    --]]
    if p117.UserInputType == Enum.UserInputType.Touch then
        u106(p117, p118)
    elseif p117.UserInputType == Enum.UserInputType.MouseMovement then
        local v119 = p117.Delta
        u51.Movement = Vector2.new(v119.X, v119.Y)
    end
end
local function u124(p121, p122) --[[Anonymous function at line 421]]
    --[[
    Upvalues:
        [1] = u94
        [2] = u48
    --]]
    if p121.UserInputType == Enum.UserInputType.Touch then
        u94(p121, p122)
    elseif p121.UserInputType == Enum.UserInputType.MouseButton2 then
        local v123 = u48 - 1
        u48 = math.max(0, v123)
    end
end
local u125 = false
function v46.setInputEnabled(p126) --[[Anonymous function at line 432]]
    --[[
    Upvalues:
        [1] = u125
        [2] = u80
        [3] = u107
        [4] = u1
        [5] = u68
        [6] = u10
        [7] = u71
        [8] = u6
        [9] = u75
        [10] = u73
        [11] = u47
        [12] = u2
        [13] = u116
        [14] = u120
        [15] = u124
        [16] = u112
        [17] = u17
    --]]
    if u125 ~= p126 then
        u125 = p126
        u80()
        u107()
        if u125 then
            u1:BindActionAtPriority("RbxCameraThumbstick", u68, false, u10, Enum.KeyCode.Thumbstick2)
            u1:BindActionAtPriority("RbxCameraKeypress", u71, false, u10, Enum.KeyCode.Left, Enum.KeyCode.Right, Enum.KeyCode.I, Enum.KeyCode.O)
            if u6.VREnabled then
                u1:BindAction("RbxCameraGamepadReset", u75, false, Enum.KeyCode.ButtonL3)
            end
            u1:BindAction("RbxCameraGamepadZoom", u73, false, Enum.KeyCode.ButtonR3)
            local v127 = u47
            local v128 = u2.InputBegan
            local v129 = u116
            table.insert(v127, v128:Connect(v129))
            local v130 = u47
            local v131 = u2.InputChanged
            local v132 = u120
            table.insert(v130, v131:Connect(v132))
            local v133 = u47
            local v134 = u2.InputEnded
            local v135 = u124
            table.insert(v133, v134:Connect(v135))
            local v136 = u47
            local v137 = u2.PointerAction
            local v138 = u112
            table.insert(v136, v137:Connect(v138))
            if u17 then
                local v139 = u47
                local v140 = game:GetService("GuiService").MenuOpened
                local v141 = u107
                table.insert(v139, v140:connect(v141))
                return
            end
        else
            u1:UnbindAction("RbxCameraThumbstick")
            u1:UnbindAction("RbxCameraMouseMove")
            u1:UnbindAction("RbxCameraMouseWheel")
            u1:UnbindAction("RbxCameraKeypress")
            u1:UnbindAction("RbxCameraGamepadZoom")
            if u6.VREnabled then
                u1:UnbindAction("RbxCameraGamepadReset")
            end
            for _, v142 in pairs(u47) do
                v142:Disconnect()
            end
            u47 = {}
        end
    end
end
function v46.getInputEnabled() --[[Anonymous function at line 504]]
    --[[
    Upvalues:
        [1] = u125
    --]]
    return u125
end
function v46.resetInputForFrameEnd() --[[Anonymous function at line 508]]
    --[[
    Upvalues:
        [1] = u51
        [2] = u52
    --]]
    u51.Movement = Vector2.new()
    u52.Move = Vector2.new()
    u52.Pinch = 0
    u51.Wheel = 0
    u51.Pan = Vector2.new()
    u51.Pinch = 0
end
u2.WindowFocused:Connect(u80)
u2.WindowFocusReleased:Connect(u80)
local u143 = false
local u144 = false
local u145 = 0
function v46.getHoldPan() --[[Anonymous function at line 529]]
    --[[
    Upvalues:
        [1] = u143
    --]]
    return u143
end
function v46.getTogglePan() --[[Anonymous function at line 533]]
    --[[
    Upvalues:
        [1] = u144
    --]]
    return u144
end
function v46.getPanning() --[[Anonymous function at line 537]]
    --[[
    Upvalues:
        [1] = u144
        [2] = u143
    --]]
    return u144 or u143
end
function v46.setTogglePan(p146) --[[Anonymous function at line 541]]
    --[[
    Upvalues:
        [1] = u144
    --]]
    u144 = p146
end
local u147 = false
local u148 = nil
local u149 = nil
function v46.enableCameraToggleInput() --[[Anonymous function at line 549]]
    --[[
    Upvalues:
        [1] = u147
        [2] = u143
        [3] = u144
        [4] = u148
        [5] = u149
        [6] = u23
        [7] = u145
        [8] = u24
        [9] = u2
    --]]
    if not u147 then
        u147 = true
        u143 = false
        u144 = false
        if u148 then
            u148:Disconnect()
        end
        if u149 then
            u149:Disconnect()
        end
        u148 = u23:Connect(function() --[[Anonymous function at line 566]]
            --[[
            Upvalues:
                [1] = u143
                [2] = u145
            --]]
            u143 = true
            u145 = tick()
        end)
        u149 = u24:Connect(function() --[[Anonymous function at line 571]]
            --[[
            Upvalues:
                [1] = u143
                [2] = u145
                [3] = u144
                [4] = u2
            --]]
            u143 = false
            if tick() - u145 < 0.3 and (u144 or u2:GetMouseDelta().Magnitude < 2) then
                u144 = not u144
            end
        end)
    end
end
function v46.disableCameraToggleInput() --[[Anonymous function at line 579]]
    --[[
    Upvalues:
        [1] = u147
        [2] = u148
        [3] = u149
    --]]
    if u147 then
        u147 = false
        if u148 then
            u148:Disconnect()
            u148 = nil
        end
        if u149 then
            u149:Disconnect()
            u149 = nil
        end
    end
end
return v46