-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\ByteNet\dataTypes\array-ModuleScript.lua
local v1 = require(script.Parent.Parent.process.bufferWriter)
require(script.Parent.Parent.types)
local u2 = v1.u16
return function(p3) --[[Anonymous function at line 9]]
    --[[
    Upvalues:
        [1] = u2
    --]]
    local u4 = p3.write
    local u5 = p3.read
    return {
        ["read"] = function(p6, p7) --[[Function name: read, line 14]]
            --[[
            Upvalues:
                [1] = u5
            --]]
            local v8 = buffer.readu16(p6, p7)
            local v9 = p7 + 2
            local v10 = {}
            for _ = 1, v8 do
                local v11, v12 = u5(p6, v9)
                table.insert(v10, v11)
                v9 = v9 + v12
            end
            return v10, v9 - p7
        end,
        ["write"] = function(p13) --[[Function name: write, line 28]]
            --[[
            Upvalues:
                [1] = u2
                [2] = u4
            --]]
            local v14 = #p13
            u2(v14)
            for v15 = 1, v14 do
                u4(p13[v15])
            end
        end
    }
end