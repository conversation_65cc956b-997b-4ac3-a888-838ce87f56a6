-- Full Path: -Grow-a-Garden-\StarterPlayer\StarterCharacterScripts\HRP_Rotate-LocalScript.lua
local v1 = game.Players.LocalPlayer
local u2 = v1.Character or v1.CharacterAdded:Wait()
local u3 = u2:Wait<PERSON><PERSON><PERSON><PERSON><PERSON>("HumanoidRootPart")
local v4 = game:GetService("RunService")
local u5 = game:GetService("UserInputService")
local u6 = nil
local u7 = nil
local v8 = game.Workspace:FindFirstChild("Player_Orientation_References") or Instance.new("Folder", game.Workspace)
v8.Name = "Player_Orientation_References"
local u9 = game.ReplicatedStorage.REF:Clone()
u9.Parent = v8
u9.Anchored = true
u9.CanCollide = false
u9.Name = v1.Name
u9.Transparency = 1
local v10 = Instance.new("Attachment", u9)
local v11 = Instance.new("Attachment", u3)
local u12 = Instance.new("AlignOrientation", u9)
u12.Attachment0 = v11
u12.Attachment1 = v10
u12.MaxAngularVelocity = 3000000000
u12.MaxTorque = 3000000000
u12.Enabled = false
if not u5.TouchEnabled then
    v4.RenderStepped:Connect(function() --[[Anonymous function at line 31]]
        --[[
        Upvalues:
            [1] = u6
            [2] = u5
            [3] = u3
            [4] = u12
            [5] = u9
        --]]
        if u6 == true then
            local v13 = workspace.CurrentCamera
            local v14 = u5:GetMouseLocation()
            local v15 = v13:ViewportPointToRay(v14.X, v14.Y)
            local v16 = v15.Origin
            local v17 = v15.Direction
            local v18 = v16 + v17 * ((u3.Position.Y - v16.Y) / v17.Y)
            local v19 = u3.Position
            local v20 = (v18 - v19).Unit
            u12.Enabled = true
            u9.Position = v19
            u9.CFrame = CFrame.new(v19, v19 + v20)
        end
    end)
    u2.ChildAdded:Connect(function(p21) --[[Anonymous function at line 54]]
        if p21:IsA("Tool") then
            string.find(p21.Name, "Watering")
        end
    end)
    u2.ChildRemoved:Connect(function(_) --[[Anonymous function at line 60]]
        --[[
        Upvalues:
            [1] = u2
            [2] = u6
            [3] = u12
        --]]
        if u2:FindFirstChildWhichIsA("Tool") == nil then
            u6 = false
            u12.Enabled = false
        end
    end)
    u5.InputBegan:Connect(function(p22) --[[Anonymous function at line 67]]
        --[[
        Upvalues:
            [1] = u7
        --]]
        if p22.UserInputType == Enum.UserInputType.MouseButton1 then
            u7 = true
        end
    end)
    u5.InputEnded:Connect(function(p23) --[[Anonymous function at line 73]]
        --[[
        Upvalues:
            [1] = u7
            [2] = u12
        --]]
        if p23.UserInputType == Enum.UserInputType.MouseButton1 then
            u7 = false
            u12.Enabled = false
        end
    end)
end