-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\Icon\Elements\Widget-ModuleScript.lua
return function(u1, u2) --[[Anonymous function at line 6]]
    local u3 = Instance.new("Frame")
    u3:SetAttribute("WidgetUID", u1.UID)
    u3.Name = "Widget"
    u3.BackgroundTransparency = 1
    u3.Visible = true
    u3.ZIndex = 20
    u3.Active = false
    u3.ClipsDescendants = true
    local u4 = Instance.new("Frame")
    u4.Name = "IconButton"
    u4.Visible = true
    u4.ZIndex = 2
    u4.BorderSizePixel = 0
    u4.Parent = u3
    u4.ClipsDescendants = true
    u4.Active = false
    u1.deselected:Connect(function() --[[Anonymous function at line 25]]
        --[[
        Upvalues:
            [1] = u4
        --]]
        u4.ClipsDescendants = true
    end)
    u1.selected:Connect(function() --[[Anonymous function at line 28]]
        --[[
        Upvalues:
            [1] = u1
            [2] = u4
        --]]
        task.defer(function() --[[Anonymous function at line 29]]
            --[[
            Upvalues:
                [1] = u1
                [2] = u4
            --]]
            u1.resizingComplete:Once(function() --[[Anonymous function at line 30]]
                --[[
                Upvalues:
                    [1] = u1
                    [2] = u4
                --]]
                if u1.isSelected then
                    u4.ClipsDescendants = false
                end
            end)
        end)
    end)
    local v5 = Instance.new("UICorner")
    v5:SetAttribute("Collective", "IconCorners")
    v5.Parent = u4
    local u6 = require(script.Parent.Menu)(u1)
    local u7 = u6.MenuUIListLayout
    local u8 = u6.MenuGap
    u6.Parent = u4
    local u9 = Instance.new("Frame")
    u9.Name = "IconSpot"
    u9.BackgroundColor3 = Color3.fromRGB(225, 225, 225)
    u9.BackgroundTransparency = 0.9
    u9.Visible = true
    u9.AnchorPoint = Vector2.new(0, 0.5)
    u9.ZIndex = 5
    u9.Parent = u6
    v5:Clone().Parent = u9
    local v10 = u9:Clone()
    v10.Name = "IconOverlay"
    v10.BackgroundColor3 = Color3.fromRGB(255, 255, 255)
    v10.ZIndex = u9.ZIndex + 1
    v10.Size = UDim2.new(1, 0, 1, 0)
    v10.Position = UDim2.new(0, 0, 0, 0)
    v10.AnchorPoint = Vector2.new(0, 0)
    v10.Visible = false
    v10.Parent = u9
    local u11 = Instance.new("TextButton")
    u11:SetAttribute("CorrespondingIconUID", u1.UID)
    u11.Name = "ClickRegion"
    u11.BackgroundTransparency = 1
    u11.Visible = true
    u11.Text = ""
    u11.ZIndex = 20
    u11.Selectable = true
    u11.SelectionGroup = true
    u11.Parent = u9
    require(script.Parent.Parent.Features.Gamepad).registerButton(u11)
    v5:Clone().Parent = u11
    local u12 = Instance.new("Frame")
    u12.Name = "Contents"
    u12.BackgroundTransparency = 1
    u12.Size = UDim2.fromScale(1, 1)
    u12.Parent = u9
    local u13 = Instance.new("UIListLayout")
    u13.Name = "ContentsList"
    u13.FillDirection = Enum.FillDirection.Horizontal
    u13.VerticalAlignment = Enum.VerticalAlignment.Center
    u13.SortOrder = Enum.SortOrder.LayoutOrder
    u13.VerticalFlex = Enum.UIFlexAlignment.SpaceEvenly
    u13.Padding = UDim.new(0, 3)
    u13.Parent = u12
    local u14 = Instance.new("Frame")
    u14.Name = "PaddingLeft"
    u14.LayoutOrder = 1
    u14.ZIndex = 5
    u14.BorderColor3 = Color3.fromRGB(0, 0, 0)
    u14.BackgroundTransparency = 1
    u14.BorderSizePixel = 0
    u14.BackgroundColor3 = Color3.fromRGB(255, 255, 255)
    u14.Parent = u12
    local u15 = Instance.new("Frame")
    u15.Name = "PaddingCenter"
    u15.LayoutOrder = 3
    u15.ZIndex = 5
    u15.Size = UDim2.new(0, 0, 1, 0)
    u15.BorderColor3 = Color3.fromRGB(0, 0, 0)
    u15.BackgroundTransparency = 1
    u15.BorderSizePixel = 0
    u15.BackgroundColor3 = Color3.fromRGB(255, 255, 255)
    u15.Parent = u12
    local u16 = Instance.new("Frame")
    u16.Name = "PaddingRight"
    u16.LayoutOrder = 5
    u16.ZIndex = 5
    u16.BorderColor3 = Color3.fromRGB(0, 0, 0)
    u16.BackgroundTransparency = 1
    u16.BorderSizePixel = 0
    u16.BackgroundColor3 = Color3.fromRGB(255, 255, 255)
    u16.Parent = u12
    local u17 = Instance.new("Frame")
    u17.Name = "IconLabelContainer"
    u17.LayoutOrder = 4
    u17.ZIndex = 3
    u17.AnchorPoint = Vector2.new(0, 0.5)
    u17.Size = UDim2.new(0, 0, 0.5, 0)
    u17.BackgroundTransparency = 1
    u17.Position = UDim2.new(0.5, 0, 0.5, 0)
    u17.Parent = u12
    local u18 = Instance.new("TextLabel")
    local u19 = workspace.CurrentCamera.ViewportSize.X + 200
    u18.Name = "IconLabel"
    u18.LayoutOrder = 4
    u18.ZIndex = 15
    u18.AnchorPoint = Vector2.new(0, 0)
    u18.Size = UDim2.new(0, u19, 1, 0)
    u18.ClipsDescendants = false
    u18.BackgroundTransparency = 1
    u18.Position = UDim2.fromScale(0, 0)
    u18.RichText = true
    u18.TextColor3 = Color3.fromRGB(255, 255, 255)
    u18.TextXAlignment = Enum.TextXAlignment.Left
    u18.Text = ""
    u18.TextWrapped = true
    u18.TextWrap = true
    u18.TextScaled = false
    u18.Active = false
    u18.AutoLocalize = true
    u18.Parent = u17
    local u20 = Instance.new("ImageLabel")
    u20.Name = "IconImage"
    u20.LayoutOrder = 2
    u20.ZIndex = 15
    u20.AnchorPoint = Vector2.new(0, 0.5)
    u20.Size = UDim2.new(0, 0, 0.5, 0)
    u20.BackgroundTransparency = 1
    u20.Position = UDim2.new(0, 11, 0.5, 0)
    u20.ScaleType = Enum.ScaleType.Stretch
    u20.Active = false
    u20.Parent = u12
    local v21 = v5:Clone()
    v21:SetAttribute("Collective", nil)
    v21.CornerRadius = UDim.new(0, 0)
    v21.Name = "IconImageCorner"
    v21.Parent = u20
    local u22 = game:GetService("TweenService")
    local u23 = 0
    local function v61(_) --[[Anonymous function at line 184]]
        --[[
        Upvalues:
            [1] = u1
            [2] = u18
            [3] = u20
            [4] = u17
            [5] = u14
            [6] = u15
            [7] = u16
            [8] = u4
            [9] = u13
            [10] = u12
            [11] = u3
            [12] = u19
            [13] = u6
            [14] = u9
            [15] = u7
            [16] = u8
            [17] = u22
            [18] = u11
            [19] = u23
            [20] = u2
        --]]
        task.defer(function() --[[Anonymous function at line 191]]
            --[[
            Upvalues:
                [1] = u1
                [2] = u18
                [3] = u20
                [4] = u17
                [5] = u14
                [6] = u15
                [7] = u16
                [8] = u4
                [9] = u13
                [10] = u12
                [11] = u3
                [12] = u19
                [13] = u6
                [14] = u9
                [15] = u7
                [16] = u8
                [17] = u22
                [18] = u11
                [19] = u23
                [20] = u2
            --]]
            local v24 = u1.indicator
            if v24 then
                v24 = v24.Visible
            end
            local v25 = v24 or u18.Text ~= ""
            local v26
            if u20.Image == "" then
                v26 = false
            else
                v26 = u20.Image ~= nil
            end
            local _ = Enum.HorizontalAlignment.Center
            local v27 = UDim2.fromScale(1, 1)
            if v26 and not v25 then
                u17.Visible = false
                u20.Visible = true
                u14.Visible = false
                u15.Visible = false
                u16.Visible = false
            elseif v26 or not v25 then
                if v26 and v25 then
                    u17.Visible = true
                    u20.Visible = true
                    u14.Visible = true
                    u15.Visible = not v24
                    u16.Visible = not v24
                    local _ = Enum.HorizontalAlignment.Left
                end
            else
                u17.Visible = true
                u20.Visible = false
                u14.Visible = true
                u15.Visible = false
                u16.Visible = true
            end
            u4.Size = v27
            local v28 = u13.Padding.Offset
            local v29 = u18.TextBounds.X
            u17.Size = UDim2.new(0, v29, u18.Size.Y.Scale, 0)
            local v30 = v28
            for _, v31 in pairs(u12:GetChildren()) do
                if v31:IsA("GuiObject") and v31.Visible == true then
                    v30 = v30 + ((v31:GetAttribute("TargetWidth") or v31.AbsoluteSize.X) + v28)
                end
            end
            local v32 = u3:GetAttribute("MinimumWidth")
            local v33 = u3:GetAttribute("MinimumHeight")
            local v34 = u3:GetAttribute("BorderSize")
            local v35 = u19
            local v36 = math.clamp(v30, v32, v35)
            local v37 = 0
            local v38 = #u1.menuIcons > 0
            if v38 then
                v38 = u1.isSelected
            end
            if v38 then
                for _, v39 in pairs(u6:GetChildren()) do
                    if v39 ~= u9 and (v39:IsA("GuiObject") and v39.Visible) then
                        v37 = v37 + ((v39:GetAttribute("TargetWidth") or v39.AbsoluteSize.X) + u7.Padding.Offset)
                    end
                end
                if not u9.Visible then
                    local v40 = u9
                    v36 = v36 - ((v40:GetAttribute("TargetWidth") or v40.AbsoluteSize.X) + u7.Padding.Offset * 2 + v34)
                end
                v37 = v37 - v34 * 0.5
                v36 = v36 + (v37 - v34 * 0.75)
            end
            local v41 = u8
            if v38 then
                v38 = u9.Visible
            end
            v41.Visible = v38
            local v42 = u3:GetAttribute("DesiredWidth")
            if v42 then
                if v36 >= v42 then
                    v42 = v36
                end
            else
                v42 = v36
            end
            u1.updateMenu:Fire()
            local v43 = v42 - v37
            local v44 = math.max(v43, v32) - v34 * 2
            local v45 = u6:GetAttribute("MenuWidth")
            if v45 then
                v45 = v45 + v44 + u7.Padding.Offset + 10
            end
            if v45 then
                local v46 = u6:GetAttribute("MaxWidth")
                if v46 then
                    v45 = math.max(v46, v32)
                end
                u6:SetAttribute("MenuCanvasWidth", v42)
                if v45 >= v42 then
                    v45 = v42
                end
            else
                v45 = v42
            end
            local v47 = Enum.EasingStyle.Quint
            local v48 = Enum.EasingDirection.Out
            local v49 = u9
            local v50 = v49:GetAttribute("TargetWidth") or v49.AbsoluteSize.X
            local v51 = u9.AbsoluteSize.X
            local v52 = math.max(v44, v50, v51)
            local v53 = u3
            local v54 = v53:GetAttribute("TargetWidth") or v53.AbsoluteSize.X
            local v55 = u3.AbsoluteSize.X
            local v56 = math.max(v45, v54, v55)
            local v57 = TweenInfo.new(v52 / 750, v47, v48)
            local v58 = TweenInfo.new(v56 / 750, v47, v48)
            u22:Create(u9, v57, {
                ["Position"] = UDim2.new(0, v34, 0.5, 0),
                ["Size"] = UDim2.new(0, v44, 1, -v34 * 2)
            }):Play()
            u22:Create(u11, v57, {
                ["Size"] = UDim2.new(0, v44, 1, 0)
            }):Play()
            local v59 = UDim2.fromOffset(v45, v33)
            if u3.Size.Y.Offset ~= v33 then
                u3.Size = v59
            end
            u3:SetAttribute("TargetWidth", v59.X.Offset)
            u22:Create(u3, v58, {
                ["Size"] = v59
            }):Play()
            u23 = u23 + 1
            for v60 = 1, v58.Time * 100 do
                task.delay(v60 / 100, function() --[[Anonymous function at line 303]]
                    --[[
                    Upvalues:
                        [1] = u2
                        [2] = u1
                    --]]
                    u2.iconChanged:Fire(u1)
                end)
            end
            task.delay(v58.Time - 0.2, function() --[[Anonymous function at line 307]]
                --[[
                Upvalues:
                    [1] = u23
                    [2] = u1
                --]]
                u23 = u23 - 1
                task.defer(function() --[[Anonymous function at line 309]]
                    --[[
                    Upvalues:
                        [1] = u23
                        [2] = u1
                    --]]
                    if u23 == 0 then
                        u1.resizingComplete:Fire()
                    end
                end)
            end)
            u1:updateParent()
        end)
    end
    local u62 = require(script.Parent.Parent.Utility).createStagger(0.01, v61)
    local u63 = true
    u1:setBehaviour("IconLabel", "Text", u62)
    u1:setBehaviour("IconLabel", "FontFace", function(p64) --[[Anonymous function at line 322]]
        --[[
        Upvalues:
            [1] = u18
            [2] = u62
            [3] = u63
        --]]
        if u18.FontFace ~= p64 then
            task.spawn(function() --[[Anonymous function at line 327]]
                --[[
                Upvalues:
                    [1] = u62
                    [2] = u63
                --]]
                u62()
                if u63 then
                    u63 = false
                    for _ = 1, 10 do
                        task.wait(1)
                        u62()
                    end
                end
            end)
        end
    end)
    local function v68() --[[Anonymous function at line 349]]
        --[[
        Upvalues:
            [1] = u3
            [2] = u1
            [3] = u9
            [4] = u6
            [5] = u8
            [6] = u7
            [7] = u62
        --]]
        task.defer(function() --[[Anonymous function at line 350]]
            --[[
            Upvalues:
                [1] = u3
                [2] = u1
                [3] = u9
                [4] = u6
                [5] = u8
                [6] = u7
                [7] = u62
            --]]
            local v65 = u3:GetAttribute("BorderSize")
            local v66 = u1.alignment
            local v67
            if u9.Visible == false then
                v67 = 0
            elseif v66 == "Right" then
                v67 = -v65 or v65
            else
                v67 = v65
            end
            u6.Position = UDim2.new(0, v67, 0, 0)
            u8.Size = UDim2.fromOffset(v65, 0)
            u7.Padding = UDim.new(0, 0)
            u62()
        end)
    end
    u1:setBehaviour("Widget", "BorderSize", v68)
    u1:setBehaviour("IconSpot", "Visible", v68)
    u1.startMenuUpdate:Connect(u62)
    u1.updateSize:Connect(u62)
    u1:setBehaviour("ContentsList", "HorizontalAlignment", u62)
    u1:setBehaviour("Widget", "Visible", u62)
    u1:setBehaviour("Widget", "DesiredWidth", u62)
    u1:setBehaviour("Widget", "MinimumWidth", u62)
    u1:setBehaviour("Widget", "MinimumHeight", u62)
    u1:setBehaviour("Indicator", "Visible", u62)
    u1:setBehaviour("IconImageRatio", "AspectRatio", u62)
    u1:setBehaviour("IconImage", "Image", function(p69) --[[Anonymous function at line 371]]
        --[[
        Upvalues:
            [1] = u20
            [2] = u62
        --]]
        local v70 = tonumber(p69) and "http://www.roblox.com/asset/?id=" .. p69 or (p69 or "")
        if u20.Image ~= v70 then
            u62()
        end
        return v70
    end)
    u1.alignmentChanged:Connect(function(p71) --[[Anonymous function at line 378]]
        --[[
        Upvalues:
            [1] = u7
            [2] = u3
            [3] = u1
            [4] = u9
            [5] = u6
            [6] = u8
            [7] = u62
        --]]
        local v72 = p71 == "Center" and "Left" or p71
        u7.HorizontalAlignment = Enum.HorizontalAlignment[v72]
        task.defer(function() --[[Anonymous function at line 350]]
            --[[
            Upvalues:
                [1] = u3
                [2] = u1
                [3] = u9
                [4] = u6
                [5] = u8
                [6] = u7
                [7] = u62
            --]]
            local v73 = u3:GetAttribute("BorderSize")
            local v74 = u1.alignment
            local v75
            if u9.Visible == false then
                v75 = 0
            elseif v74 == "Right" then
                v75 = -v73 or v73
            else
                v75 = v73
            end
            u6.Position = UDim2.new(0, v75, 0, 0)
            u8.Size = UDim2.fromOffset(v73, 0)
            u7.Padding = UDim.new(0, 0)
            u62()
        end)
    end)
    local u76 = Instance.new("NumberValue")
    u76.Name = "IconImageScale"
    u76.Parent = u20
    u76:GetPropertyChangedSignal("Value"):Connect(function() --[[Anonymous function at line 389]]
        --[[
        Upvalues:
            [1] = u20
            [2] = u76
        --]]
        u20.Size = UDim2.new(u76.Value, 0, u76.Value, 0)
    end)
    local v77 = Instance.new("UIAspectRatioConstraint")
    v77.Name = "IconImageRatio"
    v77.AspectType = Enum.AspectType.FitWithinMaxSize
    v77.DominantAxis = Enum.DominantAxis.Height
    v77.Parent = u20
    local v78 = Instance.new("UIGradient")
    v78.Name = "IconGradient"
    v78.Enabled = true
    v78.Parent = u4
    local v79 = Instance.new("UIGradient")
    v79.Name = "IconSpotGradient"
    v79.Enabled = true
    v79.Parent = u9
    return u3
end