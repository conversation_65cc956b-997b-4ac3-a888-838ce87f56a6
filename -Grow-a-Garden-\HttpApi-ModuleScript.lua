-- Full Path: -Grow-a-Garden-\\HttpApi-ModuleScript.lua
local u1 = game:GetService("RunService")
local u2 = require(script.Parent.Validation)
local u3 = require(script.Parent.Version)
local u4 = require(script.HashLib)
local u5 = {
    ["protocol"] = "https",
    ["hostName"] = "api.gameanalytics.com",
    ["version"] = "v2",
    ["remoteConfigsVersion"] = "v1",
    ["initializeUrlPath"] = "init",
    ["eventsUrlPath"] = "events",
    ["EGAHTTPApiResponse"] = {
        ["NoResponse"] = 0,
        ["BadResponse"] = 1,
        ["RequestTimeout"] = 2,
        ["JsonEncodeFailed"] = 3,
        ["JsonDecodeFailed"] = 4,
        ["InternalServerError"] = 5,
        ["BadRequest"] = 6,
        ["Unauthorized"] = 7,
        ["UnknownResponseCode"] = 8,
        ["Ok"] = 9,
        ["Created"] = 10
    }
}
local u6 = game:GetService("HttpService")
local u7 = require(script.Parent.Logger)
local u8 = (u1:IsStudio() and "https" or u5.protocol) .. "://" .. (u1:IsStudio() and "sandbox-" or "") .. u5.hostName .. "/" .. u5.version
local u9 = (u1:IsStudio() and "https" or u5.protocol) .. "://" .. (u1:IsStudio() and "sandbox-" or "") .. u5.hostName .. "/remote_configs/" .. u5.remoteConfigsVersion
local function u14(p10, p11) --[[Anonymous function at line 66]]
    --[[
    Upvalues:
        [1] = u7
        [2] = u5
    --]]
    local v12 = p10.StatusCode
    local v13 = p10.Body
    if not v13 or #v13 == 0 then
        u7:d(p11 .. " request. failed. Might be no connection. Status code: " .. tostring(v12))
        return u5.EGAHTTPApiResponse.NoResponse
    end
    if v12 == 200 then
        return u5.EGAHTTPApiResponse.Ok
    end
    if v12 == 201 then
        return u5.EGAHTTPApiResponse.Created
    end
    if v12 == 0 or v12 == 401 then
        u7:d(p11 .. " request. 401 - Unauthorized.")
        return u5.EGAHTTPApiResponse.Unauthorized
    end
    if v12 == 400 then
        u7:d(p11 .. " request. 400 - Bad Request.")
        return u5.EGAHTTPApiResponse.BadRequest
    end
    if v12 ~= 500 then
        return u5.EGAHTTPApiResponse.UnknownResponseCode
    end
    u7:d(p11 .. " request. 500 - Internal Server Error.")
    return u5.EGAHTTPApiResponse.InternalServerError
end
function u5.initRequest(p15, p16, p17, p18, p19, p20) --[[Anonymous function at line 93]]
    --[[
    Upvalues:
        [1] = u9
        [2] = u5
        [3] = u1
        [4] = u8
        [5] = u7
        [6] = u6
        [7] = u3
        [8] = u4
        [9] = u14
        [10] = u2
    --]]
    local u21 = u9 .. "/" .. u5.initializeUrlPath .. "?game_key=" .. p16 .. "&interval_seconds=0&configs_hash=" .. (p19.ConfigsHash or "")
    if u1:IsStudio() then
        u21 = u8 .. "/5c6bcb5402204249437fb5a7a80a4959/" .. p15.initializeUrlPath
    end
    u7:d("Sending \'init\' URL: " .. u21)
    local u22 = u6:JSONEncode({
        ["user_id"] = tostring(p20) .. p19.CustomUserId,
        ["sdk_version"] = "roblox " .. u3.SdkVersion,
        ["os_version"] = p19.OS,
        ["platform"] = p19.Platform,
        ["build"] = p18,
        ["session_num"] = p19.Sessions,
        ["random_salt"] = p19.Sessions
    }):gsub("\"country_code\":\"unknown\"", "\"country_code\":null")
    local v23 = u22
    local u24
    if p17 then
        local v25 = u4.hmac(u4.sha256, u1:IsStudio() and "16813a12f718bc5c620f56944e1abc3ea13ccbac" or p17, v23, true)
        u24 = u4.base64_encode(v25)
    else
        u7:w("Error encoding, invalid SecretKey")
        u24 = nil
    end
    u7:d("init payload: " .. u22)
    local u26 = nil
    local v28, v29 = pcall(function() --[[Anonymous function at line 108]]
        --[[
        Upvalues:
            [1] = u26
            [2] = u6
            [3] = u21
            [4] = u24
            [5] = u22
        --]]
        local v27 = {
            ["Url"] = u21,
            ["Method"] = "POST",
            ["Headers"] = {
                ["Authorization"] = u24,
                ["Content-Type"] = "application/json"
            },
            ["Body"] = u22
        }
        u26 = u6:RequestAsync(v27)
    end)
    if not v28 then
        u7:d("Failed Init Call. error: " .. v29)
        return {
            ["statusCode"] = u5.EGAHTTPApiResponse.UnknownResponseCode,
            ["body"] = nil
        }
    end
    u7:d("init request content: " .. u26.Body)
    local v30 = u14(u26, "Init")
    if v30 ~= u5.EGAHTTPApiResponse.Ok and (v30 ~= u5.EGAHTTPApiResponse.Created and v30 ~= u5.EGAHTTPApiResponse.BadRequest) then
        u7:d("Failed Init Call. URL: " .. u21 .. ", JSONString: " .. u22 .. ", Authorization: " .. u24)
        return {
            ["statusCode"] = v30,
            ["body"] = nil
        }
    end
    local u31 = nil
    if not pcall(function() --[[Anonymous function at line 142]]
        --[[
        Upvalues:
            [1] = u31
            [2] = u6
            [3] = u26
        --]]
        u31 = u6:JSONDecode(u26.Body)
    end) then
        u7:d("Failed Init Call. Json decoding failed: " .. v29)
        return {
            ["statusCode"] = u5.EGAHTTPApiResponse.JsonDecodeFailed,
            ["body"] = nil
        }
    end
    if v30 ~= u5.EGAHTTPApiResponse.BadRequest then
        return u2:validateAndCleanInitRequestResponse(u31, v30 == u5.EGAHTTPApiResponse.Created) and {
            ["statusCode"] = v30,
            ["body"] = u31
        } or {
            ["statusCode"] = u5.EGAHTTPApiResponse.BadResponse,
            ["body"] = nil
        }
    end
    u7:d("Failed Init Call. Bad request. Response: " .. u26.Body)
    return {
        ["statusCode"] = v30,
        ["body"] = nil
    }
end
function u5.sendEventsInArray(p32, p33, p34, p35) --[[Anonymous function at line 179]]
    --[[
    Upvalues:
        [1] = u7
        [2] = u8
        [3] = u1
        [4] = u6
        [5] = u4
        [6] = u5
        [7] = u14
    --]]
    if p35 and #p35 ~= 0 then
        local u36 = u8 .. "/" .. p33 .. "/" .. p32.eventsUrlPath
        if u1:IsStudio() then
            u36 = u8 .. "/5c6bcb5402204249437fb5a7a80a4959/" .. p32.eventsUrlPath
        end
        u7:d("Sending \'events\' URL: " .. u36)
        local u37 = u6:JSONEncode(p35):gsub("\"country_code\":\"unknown\"", "\"country_code\":null")
        local v38 = u37
        local u39
        if p34 then
            local v40 = u4.hmac(u4.sha256, u1:IsStudio() and "16813a12f718bc5c620f56944e1abc3ea13ccbac" or p34, v38, true)
            u39 = u4.base64_encode(v40)
        else
            u7:w("Error encoding, invalid SecretKey")
            u39 = nil
        end
        local u41 = nil
        local v43, v44 = pcall(function() --[[Anonymous function at line 199]]
            --[[
            Upvalues:
                [1] = u41
                [2] = u6
                [3] = u36
                [4] = u39
                [5] = u37
            --]]
            local v42 = {
                ["Url"] = u36,
                ["Method"] = "POST",
                ["Headers"] = {
                    ["Authorization"] = u39,
                    ["Content-Type"] = "application/json"
                },
                ["Body"] = u37
            }
            u41 = u6:RequestAsync(v42)
        end)
        if not v43 then
            u7:d("Failed Events Call. error: " .. v44)
            return {
                ["statusCode"] = u5.EGAHTTPApiResponse.UnknownResponseCode,
                ["body"] = nil
            }
        end
        u7:d("body: " .. u41.Body)
        local v45 = u14(u41, "Events")
        if v45 ~= u5.EGAHTTPApiResponse.Ok and (v45 ~= u5.EGAHTTPApiResponse.Created and v45 ~= u5.EGAHTTPApiResponse.BadRequest) then
            u7:d("Failed Events Call. URL: " .. u36 .. ", JSONString: " .. u37 .. ", Authorization: " .. u39)
            return {
                ["statusCode"] = v45,
                ["body"] = nil
            }
        end
        local u46 = nil
        pcall(function() --[[Anonymous function at line 233]]
            --[[
            Upvalues:
                [1] = u46
                [2] = u6
                [3] = u41
            --]]
            u46 = u6:JSONDecode(u41.Body)
        end)
        if not u46 then
            u7:d("Failed Events Call. Json decoding failed")
            return {
                ["statusCode"] = u5.EGAHTTPApiResponse.JsonDecodeFailed,
                ["body"] = nil
            }
        end
        if v45 ~= u5.EGAHTTPApiResponse.BadRequest then
            return {
                ["statusCode"] = u5.EGAHTTPApiResponse.Ok,
                ["body"] = u46
            }
        end
        u7:d("Failed Events Call. Bad request. Response: " .. u41.Body)
        return {
            ["statusCode"] = v45,
            ["body"] = nil
        }
    end
    u7:d("sendEventsInArray called with missing eventArray")
end
return u5