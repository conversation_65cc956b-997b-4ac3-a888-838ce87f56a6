-- Full Path: -Grow-a-Garden-\ReplicatedStorage\CmdrClient\Types\Command-ModuleScript.lua
local u1 = require(script.Parent.Parent.Shared.Util)
return function(u2) --[[Anonymous function at line 3]]
    --[[
    Upvalues:
        [1] = u1
    --]]
    local v7 = {
        ["Transform"] = function(p3) --[[Function name: Transform, line 5]]
            --[[
            Upvalues:
                [1] = u1
                [2] = u2
            --]]
            return u1.MakeFuzzyFinder(u2:GetCommandNames())(p3)
        end,
        ["Validate"] = function(p4) --[[Function name: Validate, line 11]]
            return #p4 > 0, "No command with that name could be found."
        end,
        ["Autocomplete"] = function(p5) --[[Function name: Autocomplete, line 15]]
            return p5
        end,
        ["Parse"] = function(p6) --[[Function name: Parse, line 19]]
            return p6[1]
        end
    }
    u2:RegisterType("command", v7)
    u2:RegisterType("commands", u1.MakeListableType(v7))
end