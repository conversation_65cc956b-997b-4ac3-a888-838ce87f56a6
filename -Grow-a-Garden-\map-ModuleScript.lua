-- Full Path: -Grow-a-Garden-\\map-ModuleScript.lua
local v1 = require(script.Parent.Parent.process.bufferWriter)
require(script.Parent.Parent.types)
local u2 = v1.u16
return function(u3, u4) --[[Anonymous function at line 7]]
    --[[
    Upvalues:
        [1] = u2
    --]]
    local u5 = u3.write
    local u6 = u4.write
    return {
        ["read"] = function(p7, p8) --[[Function name: read, line 16]]
            --[[
            Upvalues:
                [1] = u3
                [2] = u4
            --]]
            local v9 = buffer.readu16(p7, p8)
            local v10 = p8 + 2
            local v11 = {}
            for _ = 1, v9 do
                local v12, v13 = u3.read(p7, v10)
                local v14 = v10 + v13
                local v15, v16 = u4.read(p7, v14)
                v10 = v14 + v16
                v11[v12] = v15
            end
            return v11, v10 - p8
        end,
        ["write"] = function(p17) --[[Function name: write, line 38]]
            --[[
            Upvalues:
                [1] = u2
                [2] = u5
                [3] = u6
            --]]
            local v18 = 0
            for _ in p17 do
                v18 = v18 + 1
            end
            u2(v18)
            for v19, v20 in p17 do
                u5(v19)
                u6(v20)
            end
        end
    }
end