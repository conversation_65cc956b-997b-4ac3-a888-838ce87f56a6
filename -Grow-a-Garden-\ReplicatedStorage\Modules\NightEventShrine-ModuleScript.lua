-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\NightEventShrine-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
local u2 = game:GetService("CollectionService")
require(v1.Modules.UpdateService)
local u3 = workspace.NightEvent:FindFirstChild("NightEventShrine", true) or v1.Modules.UpdateService:FindFirstChild("NightEventShrine", true)
local u4 = workspace.NightEvent.GroundBreak
local u5 = require(game.ReplicatedStorage.Code.CameraShaker)
local u6 = u3:GetPivot()
local u7 = u3:GetPivot() * CFrame.new(0, -15, 0)
u3:PivotTo(u7)
for _, v8 in u3:GetDescendants() do
    if v8:IsA("BasePart") and not u2:HasTag(v8, "KeepTransparency") then
        v8.Transparency = 1
        v8.CanCollide = 1
    end
end
local u9 = false
local u11 = u5.new(Enum.RenderPriority.Camera.Value, function(p10) --[[Anonymous function at line 26]]
    workspace.CurrentCamera.CFrame = workspace.CurrentCamera.CFrame * p10
end)
u11:Start()
local function u18() --[[Anonymous function at line 33]]
    --[[
    Upvalues:
        [1] = u9
        [2] = u3
        [3] = u2
        [4] = u4
        [5] = u11
        [6] = u5
        [7] = u7
        [8] = u6
    --]]
    if not u9 then
        for _, v12 in u3:GetDescendants() do
            if v12:IsA("BasePart") and not u2:HasTag(v12, "KeepTransparency") then
                v12.Transparency = 0
                v12.CanCollide = 1
            end
        end
        u9 = true
        for _, v13 in u4:GetChildren() do
            v13.Enabled = true
        end
        local v14 = u11:ShakeSustain(u5.Presets.Vibration)
        local v15 = 0
        while v15 < 4 and u9 == true do
            local v16 = (workspace.CurrentCamera.CFrame.Position - u4.Position).Magnitude
            v14:SetScaleMagnitude(1 - math.clamp(v16, 0, 75) / 75)
            v15 = v15 + game:GetService("RunService").Heartbeat:Wait()
            u3:PivotTo(u7:Lerp(u6, v15 / 4) * CFrame.new(Random.new():NextUnitVector() * 0.1))
        end
        u11:StopSustained(1)
        if u9 then
            u3:PivotTo(u6)
            for _, v17 in u4:GetChildren() do
                v17.Enabled = false
            end
        end
    end
end
local function u25() --[[Anonymous function at line 83]]
    --[[
    Upvalues:
        [1] = u9
        [2] = u3
        [3] = u2
        [4] = u4
        [5] = u11
        [6] = u5
        [7] = u6
        [8] = u7
    --]]
    if u9 ~= false then
        for _, v19 in u3:GetDescendants() do
            if v19:IsA("BasePart") and not u2:HasTag(v19, "KeepTransparency") then
                v19.Transparency = 0
                v19.CanCollide = 1
            end
        end
        for _, v20 in u4:GetChildren() do
            v20.Enabled = true
        end
        u9 = false
        local v21 = u11:ShakeSustain(u5.Presets.Vibration)
        local v22 = 0
        while v22 < 4 and not u9 do
            local v23 = (workspace.CurrentCamera.CFrame.Position - u4.Position).Magnitude
            v21:SetScaleMagnitude(1 - math.clamp(v23, 0, 75) / 75)
            v22 = v22 + game:GetService("RunService").Heartbeat:Wait()
            u3:PivotTo(u6:Lerp(u7, v22 / 4) * CFrame.new(Random.new():NextUnitVector() * 0.1))
        end
        u11:StopSustained(1)
        if not u9 then
            u3:PivotTo(u7)
            for _, v24 in u4:GetChildren() do
                v24.Enabled = false
            end
        end
    end
end
workspace:GetAttributeChangedSignal("NightEvent"):Connect(function() --[[Anonymous function at line 135]]
    --[[
    Upvalues:
        [1] = u18
        [2] = u25
    --]]
    if workspace:GetAttribute("NightEvent") then
        u18()
    else
        u25()
    end
end)
if workspace:GetAttribute("NightEvent") then
    u18()
end
return {}