-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\ABTestExperiments\Experiments\HoldToCollectExperiment-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
local u2 = game:GetService("UserInputService")
local u3 = game:GetService("GuiService")
require(v1.Modules.ABTestExperiments.ABTestTypes)
local v4 = {
    ["RemoteConfig"] = "HoldToCollect",
    ["Disabled"] = false,
    ["DefaultState"] = false
}
local v7 = {
    [true] = {
        ["Client"] = function(p5, _) --[[Function name: Client, line 17]]
            --[[
            Upvalues:
                [1] = u2
                [2] = u3
            --]]
            local v6 = u2.TouchEnabled and not u2.KeyboardEnabled
            if v6 then
                v6 = not u3:IsTenFootInterface()
            end
            if v6 then
                p5:SetAttribute("AB_HoldToCollect", true)
            end
        end
    }
}
v4.States = v7
return v4