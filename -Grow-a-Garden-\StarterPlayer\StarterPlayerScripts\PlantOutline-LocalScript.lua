-- Full Path: -Grow-a-Garden-\StarterPlayer\StarterPlayerScripts\PlantOutline-LocalScript.lua
game:GetService("UserInputService")
game:GetService("CollectionService")
local v1 = game:GetService("ReplicatedStorage")
game:GetService("TweenService")
local u2 = game:GetService("RunService")
local v3 = game:GetService("ProximityPromptService")
local v4 = require(v1.Modules.MutationHandler)
local u5 = game.Players.LocalPlayer:WaitForChild("PlayerGui"):WaitForChild("FruitMutation_UI"):WaitForChild("Frame")
local u6 = u5:Wait<PERSON><PERSON><PERSON>hild("FruitName")
local u7 = u5:WaitFor<PERSON>hild("FruitMutation")
v1:WaitFor<PERSON>hild("GameEvents")
require(v1.Modules.GetFarm)
local u8 = require(v1.Data.SeedData)
local u9 = require(v1.Data.EasterData)
local u10 = require(v1.Item_Module)
local _ = game.Players.LocalPlayer
local u11 = workspace.CurrentCamera
local u12 = script:Wait<PERSON>or<PERSON>hild("Highlight")
local u13 = nil
local u14 = v4:GetMutations()
local u15 = {}
v3.PromptShown:Connect(function(p16, _) --[[Anonymous function at line 64]]
    --[[
    Upvalues:
        [1] = u15
        [2] = u13
        [3] = u12
        [4] = u5
        [5] = u8
        [6] = u9
        [7] = u10
        [8] = u6
        [9] = u2
        [10] = u14
        [11] = u7
    --]]
    local v17 = p16:FindFirstAncestorWhichIsA("Model")
    if v17 and v17:HasTag("Harvestable") then
        for _, v18 in pairs(u15) do
            v18:Disconnect()
        end
        table.clear(u15)
        u13 = v17
        if u12.Adornee ~= v17 then
            u12.FillTransparency = 1
        end
        u12.Adornee = v17
        u5.Visible = true
        local u19 = v17.Name
        local v20 = u8[u19] or u9[u19]
        if v20 then
            v20 = v20.SeedRarity
        end
        local v21
        if v20 then
            v21 = u10.Return_Rarity_Data(v20)
        else
            v21 = v20
        end
        if v20 == "Prismatic" then
            u6.Text = "\240\159\140\136"
            local u22 = u6
            if u15[u22] then
                u15[u22]:Disconnect()
            end
            local u23 = 0
            local u24 = "\240\159\140\136"
            u15[u22] = u2.RenderStepped:Connect(function(p25) --[[Anonymous function at line 47]]
                --[[
                Upvalues:
                    [1] = u23
                    [2] = u19
                    [3] = u22
                    [4] = u24
                --]]
                u23 = (u23 + p25 * 0.2) % 1
                local v26 = Color3.fromHSV(u23, 1, 1)
                local v27 = string.format("#%02X%02X%02X", v26.R * 255, v26.G * 255, v26.B * 255)
                u22.Text = u24:gsub("\240\159\140\136", (string.format("<font color=\"#%s\">%s</font>", v27, u19)))
            end)
        elseif v21 then
            local v28 = v21[2]
            local v29 = string.format("#%02X%02X%02X", v28.R * 255, v28.G * 255, v28.B * 255)
            u6.Text = string.format("<font color=\"%s\">%s</font>", v29, u19)
        else
            u6.Text = u19
        end
        local v30 = nil
        local v31 = nil
        local v32 = v17:FindFirstChild("Variant")
        local v33
        if v32 and v32:IsA("StringValue") then
            v33 = v32.Value
            if v33 == "" or v33 == "Normal" then
                v33 = v30
            elseif v33 == "Rainbow" then
                v31 = nil
            elseif v33 == "Gold" then
                v31 = "#FFD700"
            else
                v31 = "#FFFFFF"
            end
        else
            v33 = v30
        end
        local v34 = {}
        for v35, v36 in pairs(u14) do
            if v17:GetAttribute(v35) then
                local v37 = {
                    ["Name"] = v35
                }
                local v38
                if v36.Color then
                    local v39 = v36.Color
                    v38 = string.format("#%02X%02X%02X", v39.R * 255, v39.G * 255, v39.B * 255) or "#FFFFFF"
                else
                    v38 = "#FFFFFF"
                end
                v37.Color = v38
                table.insert(v34, v37)
            end
        end
        local v40 = {}
        if v33 then
            if v33 == "Rainbow" then
                table.insert(v40, "\240\159\140\136")
            else
                local v41 = string.format
                table.insert(v40, v41("<font color=\"%s\">%s</font>", v31, v33))
            end
        end
        for _, v42 in ipairs(v34) do
            local v43 = string.format
            local v44 = v42.Color
            local v45 = v42.Name
            table.insert(v40, v43("<font color=\"%s\">%s</font>", v44, v45))
        end
        if #v40 > 0 then
            local u46 = table.concat(v40, " <font color=\"#FFFFFF\">+</font> ")
            u7.Text = u46
            u7.Visible = true
            if v33 == "Rainbow" then
                local u47 = u7
                if u15[u47] then
                    u15[u47]:Disconnect()
                end
                local u48 = 0
                local u49 = "Rainbow"
                u15[u47] = u2.RenderStepped:Connect(function(p50) --[[Anonymous function at line 47]]
                    --[[
                    Upvalues:
                        [1] = u48
                        [2] = u49
                        [3] = u47
                        [4] = u46
                    --]]
                    u48 = (u48 + p50 * 0.2) % 1
                    local v51 = Color3.fromHSV(u48, 1, 1)
                    local v52 = string.format("#%02X%02X%02X", v51.R * 255, v51.G * 255, v51.B * 255)
                    u47.Text = u46:gsub("\240\159\140\136", (string.format("<font color=\"#%s\">%s</font>", v52, u49)))
                end)
            end
        else
            u7.Visible = false
        end
        if #v34 > 0 then
            local v53 = v34[1].Color
            u12.FillColor = Color3.fromHex(v53)
            return
        elseif v31 then
            u12.FillColor = Color3.fromHex(v31)
        else
            u12.FillColor = Color3.new(1, 1, 1)
        end
    else
        return
    end
end)
v3.PromptHidden:Connect(function(p54) --[[Anonymous function at line 161]]
    --[[
    Upvalues:
        [1] = u12
        [2] = u5
        [3] = u13
        [4] = u15
    --]]
    local v55 = p54:FindFirstAncestorWhichIsA("Model")
    if v55 and (v55:HasTag("Harvestable") and u12.Adornee == v55) then
        u12.Adornee = nil
        u5.Visible = false
        u13 = nil
        for _, v56 in pairs(u15) do
            v56:Disconnect()
        end
        table.clear(u15)
    end
end)
u2.RenderStepped:Connect(function() --[[Anonymous function at line 173]]
    --[[
    Upvalues:
        [1] = u13
        [2] = u5
        [3] = u11
    --]]
    if not u13 then
        u5.Visible = false
        return
    end
    local v57 = nil
    for _, v58 in u13:GetDescendants() do
        if v58:IsA("ProximityPrompt") and v58.Enabled then
            v57 = v58
            break
        end
    end
    if v57 and v57.Parent:IsA("BasePart") then
        local v59, v60 = u11:WorldToViewportPoint(v57.Parent.Position)
        if v60 then
            u5.Position = UDim2.new(0, v59.X, 0, v59.Y + 50)
            u5.Visible = true
        else
            u5.Visible = false
        end
    else
        u5.Visible = false
        return
    end
end)