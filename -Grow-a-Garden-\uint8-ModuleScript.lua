-- Full Path: -Grow-a-Garden-\\uint8-ModuleScript.lua
require(script.Parent.Parent.types)
local u3 = {
    ["write"] = require(script.Parent.Parent.process.bufferWriter).u8,
    ["read"] = function(p1, p2) --[[Function name: read, line 9]]
        return buffer.readu8(p1, p2), 1
    end
}
return function() --[[Anonymous function at line 14]]
    --[[
    Upvalues:
        [1] = u3
    --]]
    return u3
end