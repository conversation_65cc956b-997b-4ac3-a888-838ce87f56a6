-- Full Path: -Grow-a-Garden-\ReplicatedStorage\CmdrClient\Types\PlayerId-ModuleScript.lua
local u1 = require(script.Parent.Parent.Shared.Util)
local u2 = game:GetService("Players")
local u3 = {}
local u13 = {
    ["DisplayName"] = "Full Player Name",
    ["Prefixes"] = "# integer",
    ["Transform"] = function(p4) --[[Function name: Transform, line 27]]
        --[[
        Upvalues:
            [1] = u1
            [2] = u2
        --]]
        return p4, u1.MakeFuzzyFinder(u2:GetPlayers())(p4)
    end,
    ["ValidateOnce"] = function(p5) --[[Function name: ValidateOnce, line 33]]
        --[[
        Upvalues:
            [1] = u3
            [2] = u2
        --]]
        local v6
        if u3[p5] then
            v6 = u3[p5]
        elseif u2:FindFirstChild(p5) then
            u3[p5] = u2[p5].UserId
            v6 = u2[p5].UserId
        else
            local v7
            v7, v6 = pcall(u2.GetUserIdFromNameAsync, u2, p5)
            if v7 then
                u3[p5] = v6
            else
                v6 = nil
            end
        end
        return v6 ~= nil, "No player with that name could be found."
    end,
    ["Autocomplete"] = function(_, p8) --[[Function name: Autocomplete, line 37]]
        --[[
        Upvalues:
            [1] = u1
        --]]
        return u1.GetNames(p8)
    end,
    ["Parse"] = function(p9) --[[Function name: Parse, line 41]]
        --[[
        Upvalues:
            [1] = u3
            [2] = u2
        --]]
        if u3[p9] then
            return u3[p9]
        end
        if u2:FindFirstChild(p9) then
            u3[p9] = u2[p9].UserId
            return u2[p9].UserId
        end
        local v10, v11 = pcall(u2.GetUserIdFromNameAsync, u2, p9)
        if not v10 then
            return nil
        end
        u3[p9] = v11
        return v11
    end,
    ["Default"] = function(p12) --[[Function name: Default, line 45]]
        return p12.Name
    end,
    ["ArgumentOperatorAliases"] = {
        ["me"] = ".",
        ["all"] = "*",
        ["others"] = "**",
        ["random"] = "?"
    }
}
return function(p14) --[[Anonymous function at line 57]]
    --[[
    Upvalues:
        [1] = u13
        [2] = u1
    --]]
    p14:RegisterType("playerId", u13)
    p14:RegisterType("playerIds", u1.MakeListableType(u13, {
        ["Prefixes"] = "# integers"
    }))
end