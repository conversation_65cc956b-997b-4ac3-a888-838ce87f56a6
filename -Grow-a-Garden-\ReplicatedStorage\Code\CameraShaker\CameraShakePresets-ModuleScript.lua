-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Code\CameraShaker\CameraShakePresets-ModuleScript.lua
local u1 = require(script.Parent.CameraShakeInstance)
local u10 = {
    ["Bump"] = function() --[[Function name: Bump, line 26]]
        --[[
        Upvalues:
            [1] = u1
        --]]
        local v2 = u1.new(2.5, 4, 0.1, 0.75)
        v2.PositionInfluence = Vector3.new(0.15, 0.15, 0.15)
        v2.RotationInfluence = Vector3.new(1, 1, 1)
        return v2
    end,
    ["Damage"] = function() --[[Function name: Damage, line 33]]
        --[[
        Upvalues:
            [1] = u1
        --]]
        local v3 = u1.new(1.5, 4, 0.1, 0.75)
        v3.PositionInfluence = Vector3.new(0.15, 0.15, 0.15)
        v3.RotationInfluence = Vector3.new(1, 1, 1)
        return v3
    end,
    ["Explosion"] = function() --[[Function name: Explosion, line 42]]
        --[[
        Upvalues:
            [1] = u1
        --]]
        local v4 = u1.new(5, 10, 0, 1.5)
        v4.PositionInfluence = Vector3.new(0.25, 0.25, 0.25)
        v4.RotationInfluence = Vector3.new(4, 1, 1)
        return v4
    end,
    ["Earthquake"] = function() --[[Function name: Earthquake, line 52]]
        --[[
        Upvalues:
            [1] = u1
        --]]
        local v5 = u1.new(0.6, 3.5, 2, 10)
        v5.PositionInfluence = Vector3.new(0.25, 0.25, 0.25)
        v5.RotationInfluence = Vector3.new(1, 1, 4)
        return v5
    end,
    ["BadTrip"] = function() --[[Function name: BadTrip, line 62]]
        --[[
        Upvalues:
            [1] = u1
        --]]
        local v6 = u1.new(10, 0.15, 5, 10)
        v6.PositionInfluence = Vector3.new(0, 0, 0.15)
        v6.RotationInfluence = Vector3.new(2, 1, 4)
        return v6
    end,
    ["HandheldCamera"] = function() --[[Function name: HandheldCamera, line 72]]
        --[[
        Upvalues:
            [1] = u1
        --]]
        local v7 = u1.new(1, 0.25, 2, 10)
        v7.PositionInfluence = Vector3.new(0, 0, 0)
        v7.RotationInfluence = Vector3.new(3, 3, 0.75)
        return v7
    end,
    ["Vibration"] = function() --[[Function name: Vibration, line 82]]
        --[[
        Upvalues:
            [1] = u1
        --]]
        local v8 = u1.new(0.4, 20, 2, 2)
        v8.PositionInfluence = Vector3.new(0.15, 0.15, 0.15)
        v8.RotationInfluence = Vector3.new(1.25, 0, 4)
        return v8
    end,
    ["RoughDriving"] = function() --[[Function name: RoughDriving, line 92]]
        --[[
        Upvalues:
            [1] = u1
        --]]
        local v9 = u1.new(1, 2, 1, 1)
        v9.PositionInfluence = Vector3.new(0, 0, 0)
        v9.RotationInfluence = Vector3.new(1, 1, 1)
        return v9
    end
}
return setmetatable({}, {
    ["__index"] = function(_, p11) --[[Function name: __index, line 104]]
        --[[
        Upvalues:
            [1] = u10
        --]]
        local v12 = u10[p11]
        if type(v12) == "function" then
            return v12()
        end
        error("No preset found with index \"" .. p11 .. "\"")
    end
})