-- Full Path: -Grow-a-Garden-\\ParticleEffects-ModuleScript.lua
local u1 = {}
u1.__index = u1
local u2 = {
    ["EffectInstance"] = nil,
    ["EmitDivider"] = 1,
    ["CurrentScale"] = 1,
    ["OriginalScale"] = {}
}
local u3 = game:GetService("TweenService")
local u4 = game:GetService("RunService")
local u5 = require(script.Parent:WaitForChild("TableUtil"))
local function u12(p6, p7) --[[Anonymous function at line 26]]
    if p7 == 0 then
        return p6.Keypoints[1].Value
    end
    if p7 == 1 then
        return p6.Keypoints[#p6.Keypoints].Value
    end
    for v8 = 1, #p6.Keypoints - 1 do
        local v9 = p6.Keypoints[v8]
        local v10 = p6.Keypoints[v8 + 1]
        if v9.Time <= p7 and p7 < v10.Time then
            local v11 = (p7 - v9.Time) / (v10.Time - v9.Time)
            return Color3.new((v10.Value.R - v9.Value.R) * v11 + v9.Value.R, (v10.Value.G - v9.Value.G) * v11 + v9.Value.G, (v10.Value.B - v9.Value.B) * v11 + v9.Value.B)
        end
    end
end
function u1.new(p13) --[[Anonymous function at line 54]]
    --[[
    Upvalues:
        [1] = u5
        [2] = u2
        [3] = u1
    --]]
    local v14 = u5.Reconcile(p13, u2)
    local v15 = {
        ["EffectInstance"] = v14.EffectInstance:Clone() or Instance.new("Part"),
        ["EmitDivider"] = v14.EmitDivider or 1,
        ["CurrentScale"] = v14.CurrentScale or 1,
        ["OriginalScale"] = v14.OriginalScale or {}
    }
    local v16 = u1
    return setmetatable(v15, v16)
end
function u1.SetCFrame(p17, p18) --[[Anonymous function at line 66]]
    if not p17.EffectInstance then
        return false
    end
    if p17.EffectInstance:IsA("BasePart") or (p17.EffectInstance:IsA("Part") or p17.EffectInstance:IsA("MeshPart")) then
        p17.EffectInstance.CFrame = p18
    elseif p17.EffectInstance:IsA("Model") and p17.EffectInstance.PrimaryPart then
        p17.EffectInstance:SetPrimaryPartCFrame(p18)
    end
    return true
end
function u1.AttachTo(p19, p20) --[[Anonymous function at line 78]]
    local v21 = Instance.new("WeldConstraint")
    p19.EffectInstance.Massless = true
    p19.EffectInstance.CanCollide = false
    v21.Part0 = p20
    v21.Part1 = p19.EffectInstance
    v21.Parent = p19.EffectInstance
    return v21
end
function u1.Play(u22, p23) --[[Anonymous function at line 92]]
    if not u22.EffectInstance then
        return false
    end
    local v24 = u22.EffectInstance:GetDescendants()
    local v25 = u22.EffectInstance
    table.insert(v24, v25)
    local u26 = p23 or {}
    for _, u27 in ipairs(v24) do
        if u27:IsA("ParticleEmitter") then
            local u28 = u27:GetAttribute("EmitCount") or 1
            local u29 = u27:GetAttribute("EmitRepeat") or 1
            local u30 = u27:GetAttribute("EmitDelay") or 0
            if u30 > 0 then
                task.delay(u30, function() --[[Anonymous function at line 107]]
                    --[[
                    Upvalues:
                        [1] = u29
                        [2] = u26
                        [3] = u27
                        [4] = u28
                        [5] = u22
                        [6] = u30
                    --]]
                    for _ = 1, u29 do
                        if not (table.find(u26, u27) or table.find(u26, u27.Name)) then
                            local v31 = u27
                            local v32 = u28 / (u22.EmitDivider or 1)
                            v31:Emit((math.ceil(v32)))
                        end
                        task.wait(u30)
                    end
                end)
            else
                for _ = 1, u29 do
                    if not (table.find(u26, u27) or table.find(u26, u27.Name)) then
                        local v33 = u28 / u22.EmitDivider
                        u27:Emit((math.ceil(v33)))
                    end
                end
            end
        end
    end
    return true
end
function u1.Emit(p34, p35, p36) --[[Anonymous function at line 128]]
    if not p34.EffectInstance then
        return false
    end
    local v37 = p36 or {}
    local v38 = p35 or 1
    for _, v39 in ipairs(p34.EffectInstance:GetDescendants()) do
        if v39:IsA("ParticleEmitter") and not (table.find(v37, v39) or table.find(v37, v39.Name)) then
            local v40 = v38 / p34.EmitDivider
            v39:Emit((math.ceil(v40)))
        end
    end
    return true
end
function u1.EmitForSeconds(p41, p42, p43) --[[Anonymous function at line 145]]
    if not p41.EffectInstance then
        return false
    end
    local v44 = p43 or {}
    local u45 = {}
    local v46 = p42 or 1
    for _, u47 in ipairs(p41.EffectInstance:GetDescendants()) do
        if u47:IsA("ParticleEmitter") then
            local v48 = u47:GetAttribute("EmitDelay") or 0
            if not (table.find(v44, u47) or table.find(v44, u47.Name)) then
                if v48 > 0 then
                    task.delay(v48, function() --[[Anonymous function at line 158]]
                        --[[
                        Upvalues:
                            [1] = u47
                        --]]
                        u47.Enabled = true
                    end)
                else
                    u47.Enabled = true
                end
                table.insert(u45, u47)
            end
        end
    end
    task.delay(v46, function() --[[Anonymous function at line 169]]
        --[[
        Upvalues:
            [1] = u45
        --]]
        for _, v49 in ipairs(u45) do
            if v49 then
                v49.Enabled = false
            end
        end
    end)
    return true
end
function u1.Toggle(p50, p51, p52) --[[Anonymous function at line 179]]
    --[[
    Upvalues:
        [1] = u3
    --]]
    if not p50.EffectInstance then
        return false
    end
    local v53 = p52 or {}
    for _, v54 in ipairs(p50.EffectInstance:GetDescendants()) do
        if v54:IsA("ParticleEmitter") then
            if not (table.find(v53, v54) or table.find(v53, v54.Name)) then
                local v55
                if p51 == nil then
                    v55 = not v54.Enabled
                else
                    v55 = p51
                end
                v54.Enabled = v55
            end
        elseif v54:IsA("Beam") then
            v54.Enabled = false
        elseif v54:IsA("PointLight") or v54:IsA("SpotLight") then
            u3:Create(v54, TweenInfo.new(1), {
                ["Brightness"] = 0
            }):Play()
        end
    end
    return true
end
function u1.SetOriginalScale(p56, p57) --[[Anonymous function at line 200]]
    p56.OriginalScale[p57] = {}
    p56.OriginalScale[p57].Size = p57.Size
    p56.OriginalScale[p57].Speed = p57.Speed
    p56.OriginalScale[p57].Acceleration = p57.Acceleration
end
function u1.SetScale(p58, p59, p60, p61) --[[Anonymous function at line 207]]
    if not (p58.EffectInstance and p58.OriginalScale) then
        return false
    end
    p58.CurrentScale = p59
    local v62 = p60 or {}
    for _, v63 in ipairs(p58.EffectInstance:GetDescendants()) do
        if v63:IsA("ParticleEmitter") then
            if not (table.find(v62, v63) or table.find(v62, v63.Name)) then
                if not p58.OriginalScale[v63] or p61 then
                    p58:SetOriginalScale(v63)
                end
                local v64 = {}
                for _, v65 in ipairs(p58.OriginalScale[v63].Size.Keypoints) do
                    local v66 = NumberSequenceKeypoint.new
                    local v67 = v65.Time
                    local v68 = v65.Value * p59
                    local v69 = v65.Envelope * p59
                    table.insert(v64, v66(v67, v68, v69))
                end
                local v70 = NumberSequence.new(v64)
                local v71 = NumberRange.new(p58.OriginalScale[v63].Speed.Min * p59, p58.OriginalScale[v63].Speed.Max * p59)
                local v72 = p58.OriginalScale[v63].Acceleration * p59
                v63.Size = v70
                v63.Speed = v71
                v63.Acceleration = v72
            end
        elseif v63:IsA("Attachment") then
            v63.Position = v63.Position * p59
        elseif v63:IsA("Beam") then
            v63.CurveSize0 = v63.CurveSize0 * p59
            v63.CurveSize1 = v63.CurveSize1 * p59
            v63.Width0 = v63.Width0 * p59
            v63.Width1 = v63.Width1 * p59
        end
    end
    return true
end
function u1.SetColor(p73, p74, p75) --[[Anonymous function at line 250]]
    if not p73.EffectInstance then
        return false
    end
    local v76 = p75 or {}
    for _, v77 in ipairs(p73.EffectInstance:GetDescendants()) do
        if v77:IsA("ParticleEmitter") and not (table.find(v76, v77) or table.find(v76, v77.Name)) then
            v77.Color = p74
        end
    end
    return true
end
function u1.SetSize(p78, p79, p80) --[[Anonymous function at line 266]]
    if not p78.EffectInstance then
        return false
    end
    local v81 = p80 or {}
    for _, v82 in ipairs(p78.EffectInstance:GetDescendants()) do
        if v82:IsA("ParticleEmitter") and not (table.find(v81, v82) or table.find(v81, v82.Name)) then
            v82.Size = p79
        end
    end
    return true
end
function u1.SetDelayTimeScale(p83, p84, p85) --[[Anonymous function at line 282]]
    if not p83.EffectInstance then
        return false
    end
    local v86 = p85 or {}
    for _, v87 in ipairs(p83.EffectInstance:GetDescendants()) do
        if v87:IsA("ParticleEmitter") and (not table.find(v86, v87) and (not table.find(v86, v87.Name) and v87:GetAttribute("EmitDelay"))) then
            v87:SetAttribute("EmitDelay", v87:GetAttribute("EmitDelay") * (1 / p84))
        end
    end
    return true
end
function u1.TweenScale(u88, u89, u90, p91) --[[Anonymous function at line 299]]
    --[[
    Upvalues:
        [1] = u3
        [2] = u4
    --]]
    local u92 = p91 or {}
    task.spawn(function() --[[Anonymous function at line 302]]
        --[[
        Upvalues:
            [1] = u88
            [2] = u90
            [3] = u89
            [4] = u3
            [5] = u92
            [6] = u4
        --]]
        local v93 = os.clock()
        local v94 = u88.CurrentScale
        u88.CurrentScale = u90
        repeat
            local v95 = (os.clock() - v93) / u89.Time
            local v96 = u3:GetValue(math.clamp(v95, 0, 1), u89.EasingStyle, u89.EasingDirection)
            u88:SetScale(v94 + (u90 - v94) * v96, u92)
            u4.Heartbeat:Wait()
        until os.clock() - v93 > u89.Time or not u88.EffectInstance
        if u88.EffectInstance then
            u88:SetScale(u90, u92)
        end
    end)
end
function u1.TweenColor(p97, u98, u99, p100) --[[Anonymous function at line 325]]
    --[[
    Upvalues:
        [1] = u12
        [2] = u3
        [3] = u4
    --]]
    local v101 = p100 or {}
    local u102 = {}
    local u103 = {}
    for _, v104 in ipairs(p97.EffectInstance:GetDescendants()) do
        if v104:IsA("ParticleEmitter") and not (table.find(v101, v104) or table.find(v101, v104.Name)) then
            table.insert(u102, v104)
        end
    end
    local function u117(p105) --[[Anonymous function at line 338]]
        --[[
        Upvalues:
            [1] = u102
            [2] = u103
            [3] = u99
            [4] = u12
        --]]
        for _, v106 in ipairs(u102) do
            if v106 then
                if not u103[v106] then
                    u103[v106] = v106.Color
                end
                local v107 = {}
                for _, v108 in ipairs(u99.Keypoints) do
                    local v109 = u12(u103[v106], v108.Time)
                    local v110 = v108.Value
                    local v111 = v109.R + (v110.R - v109.R) * p105
                    local v112 = v109.G + (v110.G - v109.G) * p105
                    local v113 = v109.B + (v110.B - v109.B) * p105
                    local v114 = ColorSequenceKeypoint.new
                    local v115 = v108.Time
                    local v116 = Color3.new
                    table.insert(v107, v114(v115, v116(v111, v112, v113)))
                end
                v106.Color = ColorSequence.new(v107)
            end
        end
    end
    task.spawn(function() --[[Anonymous function at line 359]]
        --[[
        Upvalues:
            [1] = u98
            [2] = u3
            [3] = u117
            [4] = u4
        --]]
        local v118 = os.clock()
        repeat
            local v119 = (os.clock() - v118) / u98.Time
            u117((u3:GetValue(math.clamp(v119, 0, 1), u98.EasingStyle, u98.EasingDirection)))
            u4.Heartbeat:Wait()
        until os.clock() - v118 > u98.Time
        u117(1)
    end)
end
function u1.IncrementDelay(p120, p121, p122) --[[Anonymous function at line 376]]
    local v123 = p122 or {}
    for _, v124 in ipairs(p120.EffectInstance:GetDescendants()) do
        if v124:IsA("ParticleEmitter") and (not table.find(v123, v124) and (not table.find(v123, v124.Name) and v124:GetAttribute("EmitDelay"))) then
            v124:SetAttribute("EmitDelay", v124:GetAttribute("EmitDelay") + p121)
        end
    end
end
function u1.UnLock(p125, p126) --[[Anonymous function at line 389]]
    local v127 = p126 or {}
    for _, v128 in ipairs(p125.EffectInstance:GetDescendants()) do
        if v128:IsA("ParticleEmitter") and not (table.find(v127, v128) or table.find(v127, v128.Name)) then
            v128.LockedToPart = false
        end
    end
end
function u1.Destroy(p129) --[[Anonymous function at line 401]]
    if p129.EffectInstance then
        p129.EffectInstance:Destroy()
    end
    table.clear(p129)
    setmetatable(p129, nil)
end
return u1