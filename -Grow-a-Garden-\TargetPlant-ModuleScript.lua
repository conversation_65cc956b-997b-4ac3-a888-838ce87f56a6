-- Full Path: -Grow-a-Garden-\\TargetPlant-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
local u2 = require(v1.Modules.PetServices.ActivePetsService)
local u3 = require(v1.Modules.GeneralTargettingService)
return {
    ["Activate"] = function(u4) --[[Function name: Activate, line 9]]
        --[[
        Upvalues:
            [1] = u3
            [2] = u2
        --]]
        u3:CreateTargetingHandler({
            ["OnConfirm"] = function(p5) --[[Function name: OnConfirm, line 11]]
                --[[
                Upvalues:
                    [1] = u2
                    [2] = u4
                --]]
                print("Confirmed", p5)
                u2:SetPetState(u4:GetAttribute("UUID"), "TargetPlant", {
                    ["TargetedPlant"] = p5
                })
            end,
            ["OnCancelled"] = function(...) --[[Function name: OnCancelled, line 18]]
                print("Cancelled")
            end,
            ["TargetType"] = "Plant",
            ["Targeter"] = u4
        })
    end
}