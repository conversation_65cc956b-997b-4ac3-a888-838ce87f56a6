-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\PetServices\PetActionUserInterfaceService\PetActionsHandlers\PickUp-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
local v2 = game:GetService("Players")
local u3 = require(v1.Modules.PetServices.PetsService)
local u4 = v2.LocalPlayer
return {
    ["Verifier"] = function(p5) --[[Function name: Verifier, line 9]]
        --[[
        Upvalues:
            [1] = u4
        --]]
        return p5:GetAttribute("OWNER") == u4.Name
    end,
    ["Activate"] = function(p6) --[[Function name: Activate, line 14]]
        --[[
        Upvalues:
            [1] = u3
        --]]
        u3:UnequipPet(p6:GetAttribute("UUID"))
    end
}