-- Full Path: -Grow-a-Garden-\\PackTest-LocalScript.lua
for _, u1 in game:GetDescendants() do
    if u1.ClassName == "RemoteEvent" then
        u1.OnClientEvent:Connect(function(...) --[[Anonymous function at line 3]]
            --[[
            Upvalues:
                [1] = u1
            --]]
            print(u1.Name, ...)
        end)
    elseif u1:IsA("RemoteFunction") then
        function u1.OnClientInvoke() --[[Anonymous function at line 7]]
            --[[
            Upvalues:
                [1] = u1
            --]]
            print(u1.Name)
        end
    end
end
local u2 = {}
task.delay(10, function() --[[Anonymous function at line 16]]
    --[[
    Upvalues:
        [1] = u2
    --]]
    for _, v3 in game:GetDescendants() do
        for u4 in v3:GetAttributes() do
            v3:GetAttributeChangedSignal(u4):Connect(function() --[[Anonymous function at line 23]]
                --[[
                Upvalues:
                    [1] = u2
                    [2] = u4
                --]]
                u2[u4] = (u2[u4] or 0) + 1
            end)
        end
    end
end)
task.spawn(function() --[[Anonymous function at line 33]]
    --[[
    Upvalues:
        [1] = u2
    --]]
    while true do
        task.wait(0.25)
        if next(u2) then
            print(u2)
        end
        table.clear(u2)
    end
end)