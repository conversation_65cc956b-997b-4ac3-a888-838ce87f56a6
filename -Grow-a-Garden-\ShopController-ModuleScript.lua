-- Full Path: -Grow-a-Garden-\\ShopController-ModuleScript.lua
local u1 = game:GetService("MarketplaceService")
local u2 = game:GetService("ReplicatedStorage")
local u3 = game:GetService("PolicyService")
game:GetService("SoundService")
local u4 = game:GetService("RunService")
local v5 = game:GetService("Players")
local u6 = require(u2.Data.InfinitePackData)
require(u2.Data.SeedPackData)
local u7 = require(u2.Data.SeedData)
local u8 = require(u2.Modules.MarketController)
local u9 = require(u2.Modules.GiftController)
local u10 = require(u2.Modules.GuiController)
require(u2.Modules.DataService)
local u11 = require(u2.Modules.NumberUtil)
local u12 = require(u2.Modules.RetryPcall)
local u13 = require(u2.Modules.FastTween)
local u14 = require(u2.Modules.Remotes)
local u15 = require(u2.Modules.Notification)
local u16 = require(u2.Data.ExoticPackData)
require(u2.Modules.InventoryService)
require(u2.Modules.CountDictionary)
local u17 = require(u2.Modules.UpdateService)
local u18 = require(u2.Modules.DataService)
local u19 = require(u2.Comma_Module)
local v20 = require(u2.Modules.Trove)
local u21 = v5.LocalPlayer
local u22 = u21.PlayerGui
local u23 = u22.Shop_UI
local u24 = {}
local u25 = {}
local u26 = 0
function u24._updateNotifications(_) --[[Anonymous function at line 38]]
    --[[
    Upvalues:
        [1] = u22
        [2] = u25
    --]]
    u22.Hud_UI.SideBtns.Shop.NotifyHolder.Notify.Visible = next(u25) ~= nil
end
function u24.Notify(_) --[[Anonymous function at line 42]]
    --[[
    Upvalues:
        [1] = u26
        [2] = u25
        [3] = u24
    --]]
    local v27 = u26 + 1
    u26 = v27
    local v28 = u25
    table.insert(v28, v27)
    u24:_updateNotifications()
    return v27
end
function u24.ClearNotification(_, p29) --[[Anonymous function at line 50]]
    --[[
    Upvalues:
        [1] = u25
        [2] = u24
    --]]
    if not p29 then
        return false
    end
    local v30 = table.find(u25, p29)
    if not v30 then
        return true
    end
    table.remove(u25, v30)
    u24:_updateNotifications()
    return true
end
local u31 = v20.new()
function u24._showExoticPack(_, u32) --[[Anonymous function at line 66]]
    --[[
    Upvalues:
        [1] = u31
        [2] = u10
        [3] = u23
        [4] = u12
        [5] = u3
        [6] = u21
        [7] = u7
        [8] = u4
        [9] = u8
        [10] = u1
        [11] = u19
        [12] = u9
        [13] = u11
    --]]
    u31:Clean()
    local v33 = u10:GetStateForGui(u23)
    local u34 = u23.Frame.ScrollingFrame.ExoticPack
    u34.Visible = false
    local v35, v36 = u12(30, 12, function() --[[Anonymous function at line 74]]
        --[[
        Upvalues:
            [1] = u3
            [2] = u21
        --]]
        return u3:GetPolicyInfoForPlayerAsync(u21)
    end)
    if v35 then
        if not v36.ArePaidRandomItemsRestricted then
            u34.Visible = true
            u31:Add(function() --[[Anonymous function at line 88]]
                --[[
                Upvalues:
                    [1] = u34
                --]]
                u34.Visible = false
            end)
            for v37, v38 in u32.Items do
                local v39
                if u32.BiggerTemplateIndex <= v37 then
                    v39 = u34.List.UIListLayout.TemplateBigger
                elseif u32.MidTemplateIndex <= v37 or #u32.Items <= 5 then
                    v39 = u34.List.UIListLayout.TemplateMid
                else
                    v39 = u34.List.UIListLayout.TemplateSmaller
                end
                local u40 = u31:Clone(v39)
                local v41 = u40.Odds
                local v42 = v38.Chance * 100
                v41.Text = ("%*%%"):format(math.floor(v42) / 100)
                if v38.Icon then
                    u40.Vector.Image = v38.Icon
                    u40.Vector.Visible = true
                else
                    local v43 = u7[v38.RewardId]
                    if v43 and v43.Asset then
                        u40.Vector.Visible = true
                        u40.Vector.Image = v43.Asset
                    end
                end
                if u32.BiggerTemplateIndex <= v37 then
                    local u44 = nil
                    u31:Add(v33.Visible:Observe(function(p45) --[[Anonymous function at line 115]]
                        --[[
                        Upvalues:
                            [1] = u44
                            [2] = u4
                            [3] = u40
                        --]]
                        if p45 then
                            if not u44 then
                                u44 = u4.PostSimulation:Connect(function() --[[Anonymous function at line 119]]
                                    --[[
                                    Upvalues:
                                        [1] = u40
                                    --]]
                                    u40.Odds.TextColor3 = Color3.fromHSV(os.clock() % 5 / 5, 1, 1)
                                end)
                                return
                            end
                        elseif u44 then
                            u44:Disconnect()
                            u44 = nil
                        end
                    end))
                    u31:Add(function() --[[Anonymous function at line 131]]
                        --[[
                        Upvalues:
                            [1] = u44
                        --]]
                        if u44 then
                            u44:Disconnect()
                            u44 = nil
                        end
                    end)
                end
                u40.Parent = u34.List
            end
            u8:RemovePriceLabel(u34.Buttons.Buy1)
            u8:SetPriceLabel(u34.Buttons.Buy1, u32.Products[1])
            u34.Buttons.Buy1.Amount.Text = ("1 %*"):format(u32.Display)
            u31:Add(u34.Buttons.Buy1.Activated:Connect(function() --[[Anonymous function at line 145]]
                --[[
                Upvalues:
                    [1] = u8
                    [2] = u32
                --]]
                u8:PromptPurchase(u32.Products[1], Enum.InfoType.Product)
            end))
            u8:RemovePriceLabel(u34.Buttons.Buy3)
            u8:SetPriceLabel(u34.Buttons.Buy3, u32.Products[3])
            u34.Buttons.Buy3.Amount.Text = ("3 %*"):format(u32.DisplayPlural)
            u31:Add(u34.Buttons.Buy3.Activated:Connect(function() --[[Anonymous function at line 152]]
                --[[
                Upvalues:
                    [1] = u8
                    [2] = u32
                --]]
                u8:PromptPurchase(u32.Products[3], Enum.InfoType.Product)
            end))
            u8:RemovePriceLabel(u34.Buttons.Buy10)
            u8:SetPriceLabel(u34.Buttons.Buy10, u32.Products[10])
            u34.Buttons.Buy10.Amount.Text = ("10 %*"):format(u32.DisplayPlural)
            u31:Add(u34.Buttons.Buy10.Activated:Connect(function() --[[Anonymous function at line 159]]
                --[[
                Upvalues:
                    [1] = u8
                    [2] = u32
                --]]
                u8:PromptPurchase(u32.Products[10], Enum.InfoType.Product)
            end))
            u31:Add(task.spawn(function() --[[Anonymous function at line 163]]
                --[[
                Upvalues:
                    [1] = u34
                    [2] = u12
                    [3] = u1
                    [4] = u32
                    [5] = u19
                --]]
                u34.Buttons.Buy10.TextLabel.Visible = false
                local v46, v47 = u12(30, 12, function() --[[Anonymous function at line 166]]
                    --[[
                    Upvalues:
                        [1] = u1
                        [2] = u32
                    --]]
                    return u1:GetProductInfo(u32.Products[1], Enum.InfoType.Product)
                end)
                if v46 then
                    u34.Buttons.Buy10.TextLabel.Text = u19.Comma(v47.PriceInRobux * 10)
                    u34.Buttons.Buy10.TextLabel.Visible = true
                end
            end))
            u31:Add(u34.Buttons.Gift1.Activated:Connect(function() --[[Anonymous function at line 178]]
                --[[
                Upvalues:
                    [1] = u9
                    [2] = u32
                --]]
                u9:PromptGiftFromGiftId(u32.GiftProducts[1])
            end))
            u31:Add(u34.Buttons.Gift3.Activated:Connect(function() --[[Anonymous function at line 182]]
                --[[
                Upvalues:
                    [1] = u9
                    [2] = u32
                --]]
                u9:PromptGiftFromGiftId(u32.GiftProducts[3])
            end))
            u31:Add(u34.Buttons.Gift10.Activated:Connect(function() --[[Anonymous function at line 186]]
                --[[
                Upvalues:
                    [1] = u9
                    [2] = u32
                --]]
                u9:PromptGiftFromGiftId(u32.GiftProducts[10])
            end))
            u31:Add(task.spawn(function() --[[Anonymous function at line 191]]
                --[[
                Upvalues:
                    [1] = u32
                    [2] = u34
                    [3] = u11
                --]]
                while true do
                    local v48 = u32.EndTime.UnixTimestamp - workspace:GetServerTimeNow()
                    u34.Timer.Text = u11.autoColon(v48)
                    u34.Timer.Visible = v48 > 0
                    u34.TimerHeader.Visible = v48 > 0
                    task.wait(1)
                end
            end))
            u34.Title.Text = u32.Title
            u34.Title.TitleShadow.Text = u32.Title
            u34.Vector.Image = u32.Vector
            u34.Vector.Position = u32.VectorPosition
            u34.Vector.Size = u32.VectorSize
        end
    else
        return
    end
end
function u24._initInfinitePack(_) --[[Anonymous function at line 209]]
    --[[
    Upvalues:
        [1] = u23
        [2] = u18
        [3] = u6
        [4] = u13
        [5] = u8
        [6] = u14
        [7] = u11
    --]]
    local u49 = u23.Frame.ScrollingFrame.Infinite
    local u50 = u49.INF.Holder
    local u51 = u50.Template
    local u52 = nil
    local u53 = nil
    local u54 = Instance.new("NumberValue")
    local function u69() --[[Anonymous function at line 225]]
        --[[
        Upvalues:
            [1] = u18
            [2] = u6
            [3] = u53
            [4] = u13
            [5] = u54
            [6] = u50
            [7] = u51
            [8] = u8
            [9] = u14
        --]]
        local u55 = u18:GetData()
        if u55 then
            local v56 = u55.InfinitePack.Depth
            local v57 = u6.GetRewards(u55.InfinitePack.Day, v56 + 6)
            u53 = v57
            u13(u54, TweenInfo.new(0.2), {
                ["Value"] = v56
            })
            for _, u58 in u50:GetChildren() do
                local v59 = u58.Name
                local v60 = tonumber(v59)
                if v60 and v60 <= v56 then
                    task.delay(0.2, function() --[[Anonymous function at line 243]]
                        --[[
                        Upvalues:
                            [1] = u58
                        --]]
                        u58:Destroy()
                    end)
                end
            end
            local v61 = #v57 - 6 + 1
            for u62 = math.max(v61, 1), #v57 do
                local v63 = v57[u62]
                local u64 = u50:FindFirstChild((tostring(u62)))
                if not u64 then
                    u64 = u51:Clone()
                    u64.Name = tostring(u62)
                    u64.Position = UDim2.fromOffset(u62, 0)
                    u64.LayoutOrder = u62
                    u64.ZIndex = #v57 - u62
                    u64.Position = UDim2.fromOffset(u50.AbsoluteSize.Y * (u62 - 1), 0)
                    u64.Visible = true
                    local u65 = u6.GetProductFor(u62)
                    local u66 = task.spawn(function() --[[Anonymous function at line 262]]
                        --[[
                        Upvalues:
                            [1] = u65
                            [2] = u64
                            [3] = u8
                        --]]
                        if u65 == 0 then
                            u64.TextButton.Text = "FREE"
                        else
                            u8:SetPriceLabel(u64.TextButton, u65)
                        end
                    end)
                    u64.Destroying:Once(function() --[[Anonymous function at line 270]]
                        --[[
                        Upvalues:
                            [1] = u66
                        --]]
                        if coroutine.status(u66) == "suspended" then
                            pcall(task.cancel, u66)
                        end
                    end)
                    u64.TextButton.Activated:Connect(function() --[[Anonymous function at line 276]]
                        --[[
                        Upvalues:
                            [1] = u55
                            [2] = u18
                            [3] = u62
                            [4] = u14
                        --]]
                        u55 = u18:GetData()
                        if u55.InfinitePack.Depth + 1 == u62 then
                            u14.InfinitePack.Claim.send()
                        end
                    end)
                    if v63 then
                        u64.ImageLabel.Image = v63.Icon
                    end
                    u64.Parent = u50
                end
                u64.ZIndex = #v57 - u62
                local v67 = u64.TextButton
                local v68
                if u62 == u55.InfinitePack.Depth + 1 then
                    v68 = Color3.new(0, 1, 0)
                else
                    v68 = Color3.new(0.27451, 0.27451, 0.27451)
                end
                v67.BackgroundColor3 = v68
            end
        end
    end
    local function v70() --[[Anonymous function at line 299]]
        --[[
        Upvalues:
            [1] = u50
            [2] = u54
        --]]
        u50.Position = UDim2.fromOffset(-u50.AbsoluteSize.Y * u54.Value, 0)
    end
    local u71 = nil
    local function v75() --[[Anonymous function at line 304]]
        --[[
        Upvalues:
            [1] = u71
            [2] = u50
            [3] = u54
        --]]
        if u71 and coroutine.status(u71) == "suspended" then
            task.cancel(u71)
        end
        u71 = task.delay(0.1, function() --[[Anonymous function at line 309]]
            --[[
            Upvalues:
                [1] = u50
                [2] = u54
                [3] = u71
            --]]
            u50.Position = UDim2.fromOffset(-u50.AbsoluteSize.Y * u54.Value, 0)
            for _, v72 in u50:GetChildren() do
                local v73 = v72.Name
                local v74 = tonumber(v73)
                if v74 then
                    v72.Position = UDim2.fromOffset(u50.AbsoluteSize.Y * (v74 - 1), 0)
                end
            end
            u71 = nil
        end)
    end
    local function u79() --[[Anonymous function at line 325]]
        --[[
        Upvalues:
            [1] = u18
            [2] = u52
            [3] = u50
            [4] = u69
        --]]
        local v76 = u18:GetData()
        if v76 then
            if u52 ~= v76.InfinitePack.Day then
                for _, v77 in u50:GetChildren() do
                    if v77:IsA("GuiObject") then
                        local v78 = v77.Name
                        if tonumber(v78) then
                            v77:Destroy()
                        end
                    end
                end
                u52 = v76.InfinitePack.Day
                u69()
            end
        else
            return
        end
    end
    u54.Changed:Connect(v70)
    task.spawn(v70)
    u50:GetPropertyChangedSignal("AbsoluteSize"):Connect(v75)
    task.spawn(v75)
    task.spawn(function() --[[Anonymous function at line 360]]
        --[[
        Upvalues:
            [1] = u79
            [2] = u18
            [3] = u69
        --]]
        task.spawn(u79)
        local v80 = u18
        assert(v80:GetPathSignal("InfinitePack/@")):Connect(function() --[[Anonymous function at line 363]]
            --[[
            Upvalues:
                [1] = u79
                [2] = u69
            --]]
            u79()
            u69()
        end)
    end)
    task.spawn(function() --[[Anonymous function at line 369]]
        --[[
        Upvalues:
            [1] = u11
            [2] = u49
        --]]
        while true do
            local v81 = workspace:GetServerTimeNow()
            local v82 = (v81 // 86400 + 1) * 86400 - v81
            local v83 = u11.compactFormat(v82)
            u49.Timer.Text = v83
            u49.Timer.TimerShadow.Text = v83
            task.wait(1)
        end
    end)
end
function u24._initSheckles(_) --[[Anonymous function at line 388]]
    --[[
    Upvalues:
        [1] = u23
        [2] = u8
        [3] = u9
    --]]
    for _, v84 in u23.Frame.ScrollingFrame.Sheckles.List:GetChildren() do
        if v84:IsA("GuiObject") then
            local v85 = v84.Name
            local u86 = tonumber(v85)
            if u86 then
                v84.Buttons.Buy.Activated:Connect(function() --[[Anonymous function at line 399]]
                    --[[
                    Upvalues:
                        [1] = u8
                        [2] = u86
                    --]]
                    u8:PromptPurchase(u86, Enum.InfoType.Product)
                end)
                v84.Buttons.Gift.Activated:Connect(function() --[[Anonymous function at line 403]]
                    --[[
                    Upvalues:
                        [1] = u9
                        [2] = u86
                    --]]
                    u9:PromptGiftFromNormalId(u86)
                end)
                u8:SetPriceLabel(v84.Buttons.Buy, u86)
            end
        end
    end
end
local u87 = {
    ["EggSlot"] = {
        3277519303,
        3278155980,
        3278156201,
        3278156304,
        3278156396
    },
    ["PetEquipped"] = {
        3277519374,
        3278155251,
        3278155396,
        3278155529,
        3278155672
    }
}
function u24._initPetProducts(_) --[[Anonymous function at line 427]]
    --[[
    Upvalues:
        [1] = u23
        [2] = u18
        [3] = u87
        [4] = u8
        [5] = u15
    --]]
    for _, u88 in u23.Frame.ScrollingFrame.PetProducts.List:GetChildren() do
        if u88:IsA("GuiObject") and u88.Name ~= "Free" then
            local u89 = u88:FindFirstChild("DataType").Value
            local v90 = ("PetsData/%*"):format(u89)
            local u91 = u18:GetData().PetsData[u89]
            local u92 = u87[u88.Name][u91 + 1]
            local u93 = 0
            local function v94() --[[Anonymous function at line 441]]
                --[[
                Upvalues:
                    [1] = u93
                    [2] = u91
                    [3] = u18
                    [4] = u89
                    [5] = u92
                    [6] = u87
                    [7] = u88
                    [8] = u8
                --]]
                u93 = u93 + 1
                u91 = u18:GetData().PetsData[u89]
                u92 = u87[u88.Name][u91 + 1]
                if u91 < 5 then
                    u8:SetPriceLabel(u88.Buttons.Buy, u92)
                else
                    u88.Buttons.Buy.Text = "MAX!"
                end
                u88.Amount.Text = ("%*/5 Extra!"):format(u91)
            end
            u18:GetPathSignal(v90):Connect(v94)
            v94()
            u88.Buttons.Buy.Activated:Connect(function() --[[Anonymous function at line 459]]
                --[[
                Upvalues:
                    [1] = u91
                    [2] = u15
                    [3] = u18
                    [4] = u89
                    [5] = u92
                    [6] = u87
                    [7] = u88
                    [8] = u8
                --]]
                if u91 >= 5 then
                    u15:CreateNotification("Maximum Slots Purchased!")
                else
                    u91 = u18:GetData().PetsData[u89]
                    u92 = u87[u88.Name][u91 + 1]
                    u8:PromptPurchase(u92, Enum.InfoType.Product)
                end
            end)
        end
    end
end
function u24.Start(_) --[[Anonymous function at line 470]]
    --[[
    Upvalues:
        [1] = u10
        [2] = u23
        [3] = u17
        [4] = u2
        [5] = u24
        [6] = u16
    --]]
    local u95 = u10:GetStateForGui(u23)
    u10:UsePopupAnims(u23)
    u23.Frame.Close.Activated:Connect(function() --[[Anonymous function at line 475]]
        --[[
        Upvalues:
            [1] = u10
            [2] = u23
        --]]
        u10:Close(u23)
    end)
    task.spawn(function() --[[Anonymous function at line 479]]
        --[[
        Upvalues:
            [1] = u17
            [2] = u2
            [3] = u24
            [4] = u16
            [5] = u95
        --]]
        local v96 = u17:IsHiddenFromUpdate("Shop Comestic Crate")
        if v96 then
            local v97 = require(u2.Data.PetRegistry)
            local v98 = {}
            for v99, v100 in v97.PetEggs["Exotic Bug Egg"].RarityData.Items do
                local v101 = {
                    ["Chance"] = v100.ItemOdd,
                    ["RewardId"] = v99,
                    ["Icon"] = v97.PetList[v99] and (v97.PetList[v99].Icon or "") or ""
                }
                table.insert(v98, v101)
            end
            table.sort(v98, function(p102, p103) --[[Anonymous function at line 493]]
                local v104 = p102.Chance
                local v105 = p103.Chance
                if v104 == v105 then
                    return p102.RewardId > p103.RewardId
                else
                    return v105 < v104
                end
            end)
            local u106 = {
                ["Products"] = {
                    [1] = 3278426597,
                    [3] = 3277814538,
                    [10] = 3277814625
                },
                ["GiftProducts"] = {
                    [1] = 3277815193,
                    [3] = 3277815350,
                    [10] = 3277815654
                },
                ["Title"] = "EXOTIC BUG EGG!",
                ["Vector"] = "rbxassetid://84874852713842",
                ["VectorPosition"] = UDim2.fromScale(0.092, 0.646),
                ["VectorSize"] = UDim2.fromScale(0.276, 0.885),
                ["Display"] = "Egg",
                ["DisplayPlural"] = "Eggs",
                ["Items"] = v98,
                ["BiggerTemplateIndex"] = 4,
                ["MidTemplateIndex"] = 4,
                ["EndTime"] = DateTime.fromUniversalTime(2025, 5, 24, 14)
            }
            xpcall(function() --[[Anonymous function at line 529]]
                --[[
                Upvalues:
                    [1] = u24
                    [2] = u106
                --]]
                u24:_showExoticPack(u106)
            end, warn)
        end
        while u17:IsHiddenFromUpdate("Shop Comestic Crate") do
            task.wait(1)
        end
        task.spawn(u24._showExoticPack, u24, u16)
        if v96 and not u95.Visible:Get() then
            local u107 = u24:Notify()
            local u108 = nil
            u108 = u95.Visible:Listen(function(p109) --[[Anonymous function at line 543]]
                --[[
                Upvalues:
                    [1] = u107
                    [2] = u24
                    [3] = u108
                --]]
                if p109 then
                    if u107 then
                        u24:ClearNotification(u107)
                    end
                    if u108 then
                        u108:Disconnect()
                        u108 = nil
                    end
                end
            end)
        end
    end)
    task.spawn(u24._initInfinitePack, u24)
    task.spawn(u24._initSheckles, u24)
    task.spawn(u24._initPetProducts, u24)
end
task.spawn(u24.Start, u24)
return u24