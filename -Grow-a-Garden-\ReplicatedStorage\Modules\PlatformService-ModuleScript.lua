-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\PlatformService-ModuleScript.lua
local u1 = game:GetService("UserInputService")
local v2 = require(script:Wait<PERSON><PERSON><PERSON><PERSON><PERSON>("Signal"))
local u3 = {
    ["PC"] = {
        Enum.UserInputType.Keyboard,
        Enum.UserInputType.MouseButton1,
        Enum.UserInputType.MouseButton2,
        Enum.UserInputType.MouseButton3,
        Enum.UserInputType.MouseWheel,
        Enum.UserInputType.MouseMovement
    },
    ["Mobile"] = { Enum.UserInputType.Touch },
    ["Console"] = {
        Enum.UserInputType.Gamepad1,
        Enum.UserInputType.Gamepad2,
        Enum.UserInputType.Gamepad3,
        Enum.UserInputType.Gamepad4,
        Enum.UserInputType.Gamepad5,
        Enum.UserInputType.Gamepad6,
        Enum.UserInputType.Gamepad7,
        Enum.UserInputType.Gamepad8,
        Enum.KeyCode.ButtonX,
        Enum.KeyCode.ButtonA,
        Enum.KeyCode.ButtonB,
        Enum.KeyCode.ButtonY,
        Enum.KeyCode.ButtonL1,
        Enum.KeyCode.ButtonL2,
        Enum.KeyCode.ButtonL3,
        Enum.KeyCode.ButtonSelect,
        Enum.KeyCode.ButtonStart,
        Enum.KeyCode.ButtonR1,
        Enum.KeyCode.ButtonR2,
        Enum.KeyCode.ButtonR3
    }
}
local u4 = v2.new()
local u5 = {
    ["CurrentPlatform"] = "PC",
    ["PlatformChanged"] = u4
}
local u6 = nil
function u5.GetPlatform(_) --[[Anonymous function at line 51]]
    --[[
    Upvalues:
        [1] = u5
        [2] = u1
        [3] = u3
        [4] = u6
    --]]
    local v7 = u5.CurrentPlatform
    local v8 = u1:GetLastInputType()
    for v9, v10 in u3 do
        if table.find(v10, v8) or table.find(v10, u6) then
            u5.CurrentPlatform = v9
            return v9
        end
    end
    return v7
end
u1.InputChanged:Connect(function(_) --[[Anonymous function at line 66]]
    --[[
    Upvalues:
        [1] = u5
        [2] = u4
    --]]
    local v11 = u5.CurrentPlatform
    local v12 = u5:GetPlatform()
    if v11 ~= v12 then
        u4:Fire(v12)
    end
end)
u1.InputBegan:Connect(function(p13) --[[Anonymous function at line 72]]
    --[[
    Upvalues:
        [1] = u6
        [2] = u5
        [3] = u4
    --]]
    u6 = p13.KeyCode
    local v14 = u5.CurrentPlatform
    local v15 = u5:GetPlatform()
    if v14 ~= v15 then
        u4:Fire(v15)
    end
end)
return u5