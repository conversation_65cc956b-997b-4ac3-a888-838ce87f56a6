-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\GetVector2ToWorld-ModuleScript.lua
game:GetService("UserInputService")
local u1 = game:GetService("GuiService")
local u2 = workspace.CurrentCamera
workspace:GetPropertyChangedSignal("CurrentCamera"):Connect(function() --[[Anonymous function at line 6]]
    --[[
    Upvalues:
        [1] = u2
    --]]
    u2 = workspace.CurrentCamera
end)
return function(p3, p4) --[[Function name: GetMouseToWorld, line 10]]
    --[[
    Upvalues:
        [1] = u1
        [2] = u2
    --]]
    local v5 = u1:GetGuiInset()
    local v6 = u2:ScreenPointToRay(p3.X + v5.X, p3.Y - v5.Y)
    return workspace:Raycast(v6.Origin, v6.Direction * (p4 or 1000), RaycastParams)
end