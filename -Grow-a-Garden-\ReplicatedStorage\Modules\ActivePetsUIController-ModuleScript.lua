-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\ActivePetsUIController-ModuleScript.lua
local v1 = game:GetService("Players")
local u2 = game:GetService("TweenService")
local u3 = game:GetService("ReplicatedStorage")
local u4 = v1.LocalPlayer
local v5 = u4:WaitForChild("PlayerGui")
local v6 = require(u3.Modules.GuiController)
local u7 = require(u3.Modules.PetServices.PetUtilities)
local u8 = require(u3.Modules.PetServices.ActivePetsService)
local u9 = require(u3.Modules.PetServices.PetsService)
local u10 = require(u3.Modules.Notification)
local u11 = require(u3.Data.PetRegistry)
local u12 = u11.PetList
local v13 = u3:WaitForChild("GameEvents"):WaitForChild("RefreshActivePetsUI")
local u14 = v5:WaitForChild("ActivePetUI")
local u15 = u14.Frame:WaitForChild("Main")
local u16 = u15:WaitFor<PERSON>hild("Header").EXIT_BUTTON.SENSOR
local u17 = u15.ScrollingFrame
local u18 = u17:WaitForChild("PetTemplate")
local u19 = u14.Frame:WaitForChild("Opener")
local u20 = u19:WaitForChild("SENSOR")
local u21 = u19:WaitForChild("OPEN_BUTTON")
local u22 = {}
local u23 = game:GetService("UserInputService").TouchEnabled
local u24 = false
local u25 = os.clock()
local u26 = UDim2.new(2, 0, 0.5, 0)
local u27 = UDim2.new(0.88, 0, 0.5, 0)
local u28 = u23 and UDim2.new(1, 0, 0.4, 0) or UDim2.new(1, 0, 0.5, 0)
local u29 = u23 and UDim2.new(0, 0, 0.4, 0) or UDim2.new(0, 0, 0.5, 0)
local u30 = TweenInfo.new(0.35, Enum.EasingStyle.Quad, Enum.EasingDirection.Out)
local u31 = TweenInfo.new(0.25, Enum.EasingStyle.Quad, Enum.EasingDirection.In)
function u22.Refresh(u32) --[[Anonymous function at line 60]]
    --[[
    Upvalues:
        [1] = u17
        [2] = u18
        [3] = u3
        [4] = u15
        [5] = u7
        [6] = u4
        [7] = u12
        [8] = u11
        [9] = u10
        [10] = u8
        [11] = u9
        [12] = u19
    --]]
    for _, v33 in u17:GetChildren() do
        if v33:IsA("Frame") and v33 ~= u18 then
            v33:Destroy()
        end
    end
    local v34 = require(u3.Modules.DataService):GetData()
    local v35 = v34.PetsData.MutableStats.MaxEquippedPets
    local v36 = v34.PetsData.EquippedPets or {}
    u15.Header.TITLE.Text = ("Active Pets: %*/%*"):format(#v36, v35)
    local v37 = u7:GetPetsSortedByAge(u4, 0, false, true)
    for _, u38 in v37 do
        local v39 = u12[u38.PetType]
        local v40 = u38.PetData
        local v41 = u18:Clone()
        v41.Visible = true
        v41.Name = u38.UUID
        v41.PET_NAME.Text = v40.Name or "Unnamed"
        v41.PET_TYPE.Text = u38.PetType or "Unknown"
        v41.PET_AGE.Text = ("Age: %*"):format(v40.Level)
        v41.PET_IMAGE.Image = v39.Icon or ""
        local v42 = v39.DefaultHunger
        local v43 = (v40.Hunger or 0) / v42
        local v44 = math.clamp(v43, 0, 1)
        v41.PetStats.HUNGER.HUNGER_BAR.Size = UDim2.fromScale(v44, 1)
        local v45 = u11.PetConfig.XP_CONFIG.MAX_LEVEL
        local v46 = u7:GetCurrentLevelXPCost(v40.Level)
        local v47 = v45 <= v40.Level
        local v48 = v40.LevelProgress or 0
        local v49
        if v47 then
            v49 = 1
        else
            local v50 = v48 / v46
            v49 = math.clamp(v50, 0, 1)
        end
        v41.PetStats.XP.LEVEL_PROGRESS_BAR.Size = UDim2.fromScale(v49, 1)
        local v51 = v41.PetStats.FEED.Inner.SENSOR
        local v52 = v41.PetStats.VIEW.Inner.SENSOR
        local v53 = v41.PetStats.PICKUP.Inner.SENSOR
        v51.MouseButton1Click:Connect(function() --[[Anonymous function at line 110]]
            --[[
            Upvalues:
                [1] = u4
                [2] = u10
                [3] = u8
                [4] = u38
                [5] = u32
            --]]
            local v54 = u4.Character
            if v54 then
                local v55 = v54:FindFirstChildWhichIsA("Tool")
                if not v55 then
                    return u10:CreateNotification("You are not holding a fruit!")
                end
                if not v55:HasTag("FruitTool") then
                    return u10:CreateNotification("You are not holding a fruit!")
                end
                u8:Feed(u38.UUID)
                task.delay(0.5, function() --[[Anonymous function at line 118]]
                    --[[
                    Upvalues:
                        [1] = u32
                    --]]
                    u32:Refresh()
                end)
            end
        end)
        v52.MouseButton1Click:Connect(function() --[[Anonymous function at line 123]] end)
        v53.MouseButton1Click:Connect(function() --[[Anonymous function at line 127]]
            --[[
            Upvalues:
                [1] = u9
                [2] = u38
                [3] = u17
                [4] = u3
                [5] = u15
                [6] = u32
            --]]
            u9:UnequipPet(u38.UUID)
            local v56 = u17:FindFirstChild(u38.UUID)
            if v56 then
                v56:Destroy()
            end
            local v57 = require(u3.Modules.DataService):GetData()
            local v58 = v57.PetsData.MutableStats
            local v59 = v57.PetsData.EquippedPets or {}
            u15.Header.TITLE.Text = ("Active Pets: %*/%*"):format(#v59, v58.MaxEquippedPets)
            if #v59 == 0 then
                u32:Close()
            end
        end)
        v41.Parent = u17
    end
    local v60 = #v37 > 0
    u19.Visible = v60
    if not v60 then
        u32:Close()
    end
end
task.spawn(function() --[[Anonymous function at line 160]]
    --[[
    Upvalues:
        [1] = u24
        [2] = u22
    --]]
    while true do
        repeat
            task.wait(5)
        until u24
        u22:Refresh()
    end
end)
function u22.Toggle(p61) --[[Anonymous function at line 169]]
    --[[
    Upvalues:
        [1] = u24
        [2] = u27
        [3] = u26
        [4] = u29
        [5] = u28
        [6] = u19
        [7] = u15
        [8] = u14
        [9] = u30
        [10] = u31
        [11] = u2
        [12] = u21
    --]]
    u24 = not u24
    local v62 = u24 and u27 or u26
    local v63 = u24 and u29 or u28
    local v64 = u24 and ">>" or "<<"
    u19.Parent = u24 and u15 or u14.Frame
    u2:Create(u15, u24 and u30 or u31, {
        ["Position"] = v62
    }):Play()
    u19.Position = v63
    u21.Text = v64
    if u24 then
        p61:Refresh()
    end
end
task.spawn(function() --[[Anonymous function at line 188]]
    --[[
    Upvalues:
        [1] = u24
        [2] = u25
        [3] = u22
    --]]
    while true do
        repeat
            task.wait(1)
        until u24 and os.clock() - u25 > 6
        u22:Close()
    end
end)
function u22.Open(p65) --[[Anonymous function at line 197]]
    --[[
    Upvalues:
        [1] = u24
    --]]
    if not u24 then
        p65:Toggle()
    end
end
function u22.Close(p66) --[[Anonymous function at line 203]]
    --[[
    Upvalues:
        [1] = u24
    --]]
    if u24 then
        p66:Toggle()
    end
end
function u22.Start(u67) --[[Anonymous function at line 209]]
    --[[
    Upvalues:
        [1] = u15
        [2] = u26
        [3] = u18
        [4] = u19
        [5] = u16
        [6] = u25
        [7] = u28
        [8] = u21
        [9] = u20
        [10] = u23
    --]]
    u15.Position = u26
    u18.Visible = false
    u19.Visible = false
    u16.MouseButton1Click:Connect(function() --[[Anonymous function at line 215]]
        --[[
        Upvalues:
            [1] = u25
            [2] = u67
        --]]
        u25 = os.clock()
        u67:Close()
    end)
    u19.Position = u28
    u21.Text = "<<"
    u20.MouseButton1Click:Connect(function() --[[Anonymous function at line 223]]
        --[[
        Upvalues:
            [1] = u25
            [2] = u67
        --]]
        u25 = os.clock()
        u67:Toggle()
    end)
    u15.InputBegan:Connect(function() --[[Anonymous function at line 228]]
        --[[
        Upvalues:
            [1] = u25
        --]]
        u25 = os.clock()
    end)
    if u23 then
        u19.Size = UDim2.new(0, 30, 0, 30)
    else
        u19.Size = UDim2.new(0, 40, 0, 40)
    end
    u67:Refresh()
end
v6:GetStateForGui(u14).Visible:Observe(function(p68) --[[Anonymous function at line 243]]
    --[[
    Upvalues:
        [1] = u22
    --]]
    if not p68 then
        u22:Close()
    end
end)
v13.OnClientEvent:Connect(function() --[[Anonymous function at line 249]]
    --[[
    Upvalues:
        [1] = u22
    --]]
    u22:Refresh()
end)
u22:Start()
return u22