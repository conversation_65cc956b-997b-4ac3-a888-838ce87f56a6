-- Full Path: -Grow-a-Garden-\ReplicatedStorage\GameAnalytics\GameAnalytics\Logger-ModuleScript.lua
return {
    ["_infoLogEnabled"] = false,
    ["_infoLogAdvancedEnabled"] = false,
    ["_debugEnabled"] = game:GetService("RunService"):IsStudio(),
    ["setDebugLog"] = function(p1, p2) --[[Function name: setDebugLog, line 10]]
        p1._debugEnabled = p2
    end,
    ["setInfoLog"] = function(p3, p4) --[[Function name: setInfoLog, line 14]]
        p3._infoLogEnabled = p4
    end,
    ["setVerboseLog"] = function(p5, p6) --[[Function name: setVerboseLog, line 18]]
        p5._infoLogAdvancedEnabled = p6
    end,
    ["i"] = function(p7, p8) --[[Function name: i, line 22]]
        if p7._infoLogEnabled then
            local v9 = "Info/GameAnalytics: " .. p8
            print(v9)
        end
    end,
    ["w"] = function(_, p10) --[[Function name: w, line 38]]
        local v11 = "Warning/GameAnalytics: " .. p10
        warn(v11)
    end,
    ["e"] = function(_, u12) --[[Function name: e, line 50]]
        task.spawn(function() --[[Anonymous function at line 51]]
            --[[
            Upvalues:
                [1] = u12
            --]]
            local v13 = "Error/GameAnalytics: " .. u12
            error(v13, 0)
        end)
    end,
    ["d"] = function(p14, p15) --[[Function name: d, line 64]]
        if p14._debugEnabled then
            local v16 = "Debug/GameAnalytics: " .. p15
            print(v16)
        end
    end,
    ["ii"] = function(p17, p18) --[[Function name: ii, line 80]]
        if p17._infoLogAdvancedEnabled then
            local v19 = "Verbose/GameAnalytics: " .. p18
            print(v19)
        end
    end
}