-- Full Path: -Grow-a-Garden-\\ButtonController-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
local v2 = game:GetService("SoundService")
local u3 = game:GetService("Players")
local u4 = require(v1.Modules.Trove)
local u5 = require(v1.Modules.Observers)
local u6 = require(v1.Modules.DeviceController)
local u7 = v2.Hover
local u8 = v2.Click
local v13 = {
    ["Start"] = function(_) --[[Function name: Start, line 15]]
        --[[
        Upvalues:
            [1] = u5
            [2] = u4
            [3] = u6
            [4] = u7
            [5] = u8
            [6] = u3
        --]]
        u5.observeTag("Button", function(u9) --[[Anonymous function at line 16]]
            --[[
            Upvalues:
                [1] = u4
                [2] = u6
                [3] = u7
                [4] = u8
            --]]
            if not u9:IsA("GuiButton") then
                return nil
            end
            local v10 = u4.new()
            local u11 = v10:Extend()
            v10:Add(u6:Observe(function(p12) --[[Anonymous function at line 24]]
                --[[
                Upvalues:
                    [1] = u11
                    [2] = u9
                    [3] = u7
                --]]
                u11:Clean()
                if p12 == "PC" then
                    u11:Add(u9.MouseEnter:Connect(function() --[[Anonymous function at line 28]]
                        --[[
                        Upvalues:
                            [1] = u7
                        --]]
                        u7.PlaybackSpeed = 1 + math.random(-15, 15) / 100
                        u7.TimePosition = 0
                        u7.Playing = true
                    end))
                end
            end))
            v10:Add(u9.Activated:Connect(function() --[[Anonymous function at line 36]]
                --[[
                Upvalues:
                    [1] = u8
                --]]
                u8.PlaybackSpeed = 1 + math.random(-15, 15) / 100
                u8.TimePosition = 0
                u8.Playing = true
            end))
            return v10:WrapClean()
        end, { workspace, u3.LocalPlayer.PlayerGui })
    end
}
task.spawn(v13.Start, v13)
return v13