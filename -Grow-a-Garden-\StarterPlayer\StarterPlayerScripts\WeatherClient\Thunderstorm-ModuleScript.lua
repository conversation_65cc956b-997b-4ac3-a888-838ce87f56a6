-- Full Path: -Grow-a-Garden-\StarterPlayer\StarterPlayerScripts\WeatherClient\Thunderstorm-ModuleScript.lua
local v1 = {}
local u2 = game.ReplicatedStorage.RainParticle
local u3 = Random.new()
local u4 = workspace.CurrentCamera
local u5 = RaycastParams.new()
u5.FilterDescendantsInstances = { workspace.Terrain, workspace }
u5.FilterType = Enum.RaycastFilterType.Include
local u6 = {}
local u7 = false
local function u11(p8) --[[Anonymous function at line 19]]
    --[[
    Upvalues:
        [1] = u2
        [2] = u6
    --]]
    local v9 = {
        ["particle"] = u2:Clone(),
        ["position"] = p8,
        ["spawnTime"] = os.clock(),
        ["visible"] = false,
        ["lastupdate"] = 0
    }
    game.TweenService:Create(v9.particle, TweenInfo.new(0.7), {
        ["Transparency"] = 0.2
    }):Play()
    local v10 = u6
    table.insert(v10, v9)
end
local u12 = script.Sky
local u13 = require(game.ReplicatedStorage.Modules.SkyboxManager)
u13.AddSkybox(u12)
local u14 = game.Lighting.ColorCorrection:Clone()
u14.Name = script.Name
u14.Parent = game.Lighting
local function u22() --[[Anonymous function at line 70]]
    --[[
    Upvalues:
        [1] = u7
        [2] = u13
        [3] = u12
        [4] = u14
        [5] = u6
        [6] = u3
        [7] = u4
        [8] = u11
    --]]
    u7 = true
    u13.UpdateSkybox(u12, 2)
    game.TweenService:Create(game.Lighting, TweenInfo.new(3), {
        ["Ambient"] = Color3.fromRGB(170, 237, 255),
        ["ExposureCompensation"] = 0.45,
        ["Brightness"] = 0.5
    }):Play()
    game.TweenService:Create(u14, TweenInfo.new(3), {
        ["Brightness"] = 0.1,
        ["TintColor"] = Color3.fromRGB(221, 248, 255)
    }):Play()
    game.Workspace.Terrain.Clouds.Enabled = true
    game.TweenService:Create(game.Workspace.Terrain.Clouds, TweenInfo.new(3), {
        ["Cover"] = 0.925,
        ["Density"] = 0.183
    }):Play()
    task.spawn(function() --[[Anonymous function at line 95]]
        --[[
        Upvalues:
            [1] = u7
            [2] = u6
            [3] = u3
            [4] = u4
            [5] = u11
            [6] = u13
            [7] = u12
            [8] = u14
        --]]
        while u7 do
            task.wait(0)
            if #u6 <= 77 then
                for _ = 1, 3 do
                    local v15 = u3:NextNumber(10, 180)
                    local v16 = 2 * v15
                    local v17 = u4.FieldOfView / 2
                    local v18 = math.rad(v17)
                    local v19 = v16 * math.tan(v18)
                    local v20 = v19 * (u4.ViewportSize.X / u4.ViewportSize.Y)
                    local v21 = u4.CFrame * CFrame.new(u3:NextNumber(-v20 / 2, v20 / 2), u3:NextNumber(-v19 / 2, v19 / 2 + 20), -v15)
                    if not workspace:Raycast(v21.Position, Vector3.new(0, 150, 0)) then
                        u11(v21.Position)
                    end
                end
            end
        end
        u13.UpdateSkybox(u12, 0)
        game.TweenService:Create(u14, TweenInfo.new(3), {
            ["Brightness"] = 0,
            ["TintColor"] = Color3.fromRGB(255, 255, 255)
        }):Play()
        game.TweenService:Create(game.Lighting, TweenInfo.new(3), {
            ["Ambient"] = game.Lighting:GetAttribute("DefaultAmbient"),
            ["ExposureCompensation"] = game.Lighting:GetAttribute("DefaultExposure"),
            ["Brightness"] = game.Lighting:GetAttribute("DefaultBrightness")
        }):Play()
        game.TweenService:Create(game.Workspace.Terrain.Clouds, TweenInfo.new(3), {
            ["Cover"] = 0,
            ["Density"] = 0
        }):Play()
        task.delay(3, function() --[[Anonymous function at line 161]]
            game.Workspace.Terrain.Clouds.Enabled = false
        end)
    end)
end
workspace:GetAttributeChangedSignal("Thunderstorm"):Connect(function() --[[Anonymous function at line 168]]
    --[[
    Upvalues:
        [1] = u22
        [2] = u7
    --]]
    if workspace:GetAttribute("Thunderstorm") then
        u22()
    else
        u7 = false
    end
end)
local u23 = game.ReplicatedStorage.RainSplash:Clone()
u23.Parent = workspace.WeatherVisuals
task.spawn(function() --[[Anonymous function at line 179]]
    --[[
    Upvalues:
        [1] = u6
        [2] = u5
        [3] = u4
        [4] = u23
        [5] = u3
    --]]
    while true do
        local v24 = game:GetService("RunService").RenderStepped:Wait()
        local v25 = os.clock()
        local v26 = v24 * 1.5
        local v27 = v24 * 5
        local v28 = math.min(1, v27)
        local v29 = {}
        local v30 = {}
        for v31, v32 in u6 do
            local _ = v25 - v32.spawnTime + v28
            local v33 = v32.position
            local v34 = -20 * v28
            local v35 = Vector3.new(0, v34, 0)
            local v36 = workspace:Spherecast(v33, 0.15, v35, u5)
            local v37
            if v36 then
                v33 = v36.Position
                v37 = true
            else
                v32.position = v33 + v35
                v37 = nil
            end
            local v38, v39 = u4:WorldToScreenPoint(v33)
            local v40 = v32.visible
            local v41 = (u4.CFrame.Position - v33).Magnitude / 120
            local v42 = v41 * v41
            local v43 = 1 / math.random(60, 120)
            local v44 = v26 * v42 + 0.016666666666666666
            if v41 > 1.5 then
                v32.particle:Destroy()
                table.remove(u6, v31)
            elseif v25 - v32.lastupdate + v43 > v44 then
                v32.lastupdate = v25
                if v39 and v38.Z < 200 then
                    v32.visible = true
                    local v45 = v32.particle
                    table.insert(v29, v45)
                    local v46 = CFrame.new(v33, u4.CFrame.Position) * CFrame.Angles(1.5707963267948966, 0, 0)
                    table.insert(v30, v46)
                else
                    v32.visible = false
                end
                if v32.visible ~= v40 then
                    if v32.visible then
                        v32.particle.Parent = workspace.WeatherVisuals
                    else
                        v32.particle.Parent = nil
                    end
                end
                if v37 then
                    v32.particle:Destroy()
                    u23.CFrame = CFrame.new(v33)
                    u23.Attachment.ParticleEmitter:Emit(u3:NextInteger(1, 2))
                    table.remove(u6, v31)
                elseif os.clock() - v32.spawnTime > 7 then
                    game.Debris:AddItem(v32.particle, 0.3)
                    game.TweenService:Create(v32.particle, TweenInfo.new(0.3), {
                        ["Transparency"] = 1
                    }):Play()
                    table.remove(u6, v31)
                end
            end
        end
        debug.profilebegin("Weather_" .. script.Name)
        workspace:BulkMoveTo(v29, v30, Enum.BulkMoveMode.FireCFrameChanged)
        debug.profileend()
    end
end)
if workspace:GetAttribute("Thunderstorm") then
    task.defer(u22)
else
    u7 = false
end
local v47 = require(game.ReplicatedStorage.Code.CameraShaker)
local u48 = require(game.ReplicatedStorage.Code.LightningBolt)
local u50 = v47.new(Enum.RenderPriority.Camera.Value, function(p49) --[[Anonymous function at line 275]]
    workspace.CurrentCamera.CFrame = workspace.CurrentCamera.CFrame * p49
end)
local function u57(p51, p52) --[[Anonymous function at line 279]]
    --[[
    Upvalues:
        [1] = u48
        [2] = u50
        [3] = u14
    --]]
    local v53 = u48.new(p51, p52, 30)
    v53.Thickness = 1.5
    v53.Color = Color3.fromRGB(139, 205, 255)
    v53.Frequency = 15
    v53.MinThicknessMultiplier = 0.1
    v53.MaxThicknessMultiplier = 1
    task.wait(0.65)
    u50:Shake(u50.Presets.Explosion)
    local v54 = game.ReplicatedStorage.LightningHit:Clone()
    v54.CFrame = CFrame.new(p52.WorldPosition)
    local v55 = workspace:Raycast(v54.CFrame.p, Vector3.new(-0, -50, -0))
    if v55 and v55.Position then
        v54.CFrame = CFrame.new(v55.Position)
    end
    v54.Parent = workspace.WeatherVisuals
    u14.Brightness = 0.3
    task.delay(0.1, function() --[[Anonymous function at line 314]]
        --[[
        Upvalues:
            [1] = u14
        --]]
        u14.Brightness = 0.1
    end)
    for _, v56 in pairs(v54:GetDescendants()) do
        if v56:IsA("ParticleEmitter") then
            v56:Emit(v56:GetAttribute("EmitCount"))
        elseif v56:IsA("Sound") then
            v56:Play()
        elseif v56:IsA("PointLight") then
            game.TweenService:Create(v56, TweenInfo.new(3), {
                ["Range"] = 0
            }):Play()
        end
    end
    v53:DestroyDissipate(0.7)
    game.Debris:AddItem(v54, 3)
end
game:GetService("ReplicatedStorage"):WaitForChild("GameEvents").LightningBolt.OnClientEvent:Connect(function(p58) --[[Anonymous function at line 335]]
    --[[
    Upvalues:
        [1] = u57
    --]]
    local u59 = Instance.new("Attachment")
    local u60 = Instance.new("Attachment")
    u59.Parent = workspace.Terrain
    u60.Parent = workspace.Terrain
    u60.WorldPosition = p58 + Vector3.new(0, 200, 0)
    u59.WorldPosition = p58
    u57(u60, u59)
    task.delay(5, function() --[[Anonymous function at line 347]]
        --[[
        Upvalues:
            [1] = u60
            [2] = u59
        --]]
        u60:Destroy()
        u59:Destroy()
    end)
end)
return v1