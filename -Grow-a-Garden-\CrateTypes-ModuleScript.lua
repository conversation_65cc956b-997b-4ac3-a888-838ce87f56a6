-- Full Path: -Grow-a-Garden-\\CrateTypes-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
game:GetService("ServerStorage")
local v2 = v1.Assets.Models.EggModels
local u3 = require(script.Parent.Parent.Shared.Util)
require(v1.Data.PetEggData)
local u4 = {}
for _, v5 in v2:GetChildren() do
    local v6 = v5.Name
    table.insert(u4, v6)
end
local u11 = {
    ["Transform"] = function(p7) --[[Function name: Transform, line 17]]
        --[[
        Upvalues:
            [1] = u3
            [2] = u4
        --]]
        return u3.MakeFuzzyFinder(u4)(p7)
    end,
    ["Validate"] = function(p8) --[[Function name: Validate, line 23]]
        return #p8 > 0, "No seeds could be found."
    end,
    ["Autocomplete"] = function(p9) --[[Function name: Autocomplete, line 27]]
        --[[
        Upvalues:
            [1] = u3
        --]]
        return u3.GetNames(p9)
    end,
    ["Parse"] = function(p10) --[[Function name: Parse, line 31]]
        return p10[1]
    end,
    ["Default"] = function(_) --[[Function name: Default, line 35]]
        return "Common"
    end,
    ["ArgumentOperatorAliases"] = {
        ["me"] = ".",
        ["all"] = "*",
        ["others"] = "**",
        ["random"] = "?"
    }
}
return function(p12) --[[Anonymous function at line 47]]
    --[[
    Upvalues:
        [1] = u11
    --]]
    p12:RegisterType("eggname", u11)
end