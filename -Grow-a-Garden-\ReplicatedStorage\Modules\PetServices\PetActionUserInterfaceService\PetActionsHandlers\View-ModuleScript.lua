-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\PetServices\PetActionUserInterfaceService\PetActionsHandlers\View-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
require(v1.Modules.PetServices.ActivePetsService)
local u2 = require(v1.Modules.PetServices.PetCardUserInterfaceService)
local u3 = require(v1.Modules.ActivePetsUIController)
return {
    ["Activate"] = function(p4) --[[Function name: Activate, line 8]]
        --[[
        Upvalues:
            [1] = u2
            [2] = u3
        --]]
        u2:SetTarget(p4)
        u2:Toggle(true)
        u3:Close()
    end
}