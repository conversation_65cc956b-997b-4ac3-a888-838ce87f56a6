-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Gift_Notification\Holder\Frame\Decline\LocalScript_3-LocalScript.lua
local u1 = game.SoundService.Hover
local u2 = game.SoundService.Click
script.Parent.MouseEnter:Connect(function() --[[Anonymous function at line 3]]
    --[[
    Upvalues:
        [1] = u1
    --]]
    u1.PlaybackSpeed = 1 + math.random(-15, 15) / 100
    u1.Playing = true
    u1.TimePosition = 0
end)
script.Parent.MouseButton1Click:Connect(function() --[[Anonymous function at line 8]]
    --[[
    Upvalues:
        [1] = u2
    --]]
    u2.PlaybackSpeed = 1 + math.random(-15, 15) / 100
    u2.Playing = true
    u2.TimePosition = 0
end)