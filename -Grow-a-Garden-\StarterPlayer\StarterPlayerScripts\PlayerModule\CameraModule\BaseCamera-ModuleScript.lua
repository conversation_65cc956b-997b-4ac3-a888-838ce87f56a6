-- Full Path: -Grow-a-Garden-\StarterPlayer\StarterPlayerScripts\PlayerModule\CameraModule\BaseCamera-ModuleScript.lua
local v1 = game:GetService("Players")
local u2 = game:GetService("UserInputService")
local u3 = game:GetService("VRService")
local u4 = UserSettings():GetService("UserGameSettings")
local v5 = script.Parent.Parent:WaitForChild("CommonUtils")
local u6 = require(v5:WaitForChild("ConnectionUtil"))
local v7 = require(v5:WaitForChild("FlagUtil"))
local u8 = require(script.Parent:WaitForChild("CameraUtils"))
local u9 = require(script.Parent:WaitForChild("ZoomController"))
local u10 = require(script.Parent:WaitForChild("CameraToggleStateController"))
local u11 = require(script.Parent:WaitFor<PERSON>hild("CameraInput"))
local u12 = require(script.Parent:WaitFor<PERSON>hild("CameraUI"))
local u13 = v1.LocalPlayer
local v14, v15 = pcall(function() --[[Anonymous function at line 24]]
    return UserSettings():IsUserFeatureEnabled("UserFixGamepadMaxZoom")
end)
local u16 = v14 and v15
local u17 = v7.getUserFlag("UserFixCameraCameraCharacterUpdates")
Vector2.new(0, 0)
local u18 = {
    ["CHARACTER_ADDED"] = "CHARACTER_ADDED",
    ["CAMERA_MODE_CHANGED"] = "CAMERA_MODE_CHANGED",
    ["CAMERA_MIN_DISTANCE_CHANGED"] = "CAMERA_MIN_DISTANCE_CHANGED",
    ["CAMERA_MAX_DISTANCE_CHANGED"] = "CAMERA_MAX_DISTANCE_CHANGED"
}
local u19 = {}
u19.__index = u19
function u19.new() --[[Anonymous function at line 80]]
    --[[
    Upvalues:
        [1] = u19
        [2] = u6
        [3] = u13
        [4] = u17
        [5] = u4
    --]]
    local v20 = u19
    local v21 = setmetatable({}, v20)
    v21._connections = u6.new()
    v21.gamepadZoomLevels = { 0, 10, 20 }
    v21.FIRST_PERSON_DISTANCE_THRESHOLD = 1
    v21.cameraType = nil
    v21.cameraMovementMode = nil
    v21.lastCameraTransform = nil
    v21.lastUserPanCamera = tick()
    v21.humanoidRootPart = nil
    v21.humanoidCache = {}
    v21.lastSubject = nil
    v21.lastSubjectPosition = Vector3.new(0, 5, 0)
    v21.lastSubjectCFrame = CFrame.new(v21.lastSubjectPosition)
    local v22 = u13.CameraMinZoomDistance
    local v23 = u13.CameraMaxZoomDistance
    v21.currentSubjectDistance = math.clamp(12.5, v22, v23)
    v21.inFirstPerson = false
    v21.inMouseLockedMode = false
    v21.portraitMode = false
    v21.isSmallTouchScreen = false
    v21.resetCameraAngle = true
    v21.enabled = false
    if not u17 then
        v21.PlayerGui = nil
    end
    v21.cameraChangedConn = nil
    v21.viewportSizeChangedConn = nil
    v21.shouldUseVRRotation = false
    v21.VRRotationIntensityAvailable = false
    v21.lastVRRotationIntensityCheckTime = 0
    v21.lastVRRotationTime = 0
    v21.vrRotateKeyCooldown = {}
    v21.cameraTranslationConstraints = Vector3.new(1, 1, 1)
    v21.humanoidJumpOrigin = nil
    v21.trackingHumanoid = nil
    v21.cameraFrozen = false
    v21.subjectStateChangedConn = nil
    v21.gamepadZoomPressConnection = nil
    v21.mouseLockOffset = Vector3.new(0, 0, 0)
    u4:SetCameraYInvertVisible()
    u4:SetGamepadCameraSensitivityVisible()
    return v21
end
function u19.GetModuleName(_) --[[Anonymous function at line 149]]
    return "BaseCamera"
end
function u19._setUpConfigurations(u24) --[[Anonymous function at line 153]]
    --[[
    Upvalues:
        [1] = u18
        [2] = u13
        [3] = u17
    --]]
    u24._connections:trackConnection(u18.CHARACTER_ADDED, u13.CharacterAdded:Connect(function(p25) --[[Anonymous function at line 154]]
        --[[
        Upvalues:
            [1] = u24
        --]]
        u24:OnCharacterAdded(p25)
    end))
    if u17 then
        u24.humanoidRootPart = nil
    elseif u13.Character then
        u24:OnCharacterAdded(u13.Character)
    end
    u24._connections:trackConnection(u18.CAMERA_MODE_CHANGED, u13:GetPropertyChangedSignal("CameraMode"):Connect(function() --[[Anonymous function at line 165]]
        --[[
        Upvalues:
            [1] = u24
        --]]
        u24:OnPlayerCameraPropertyChange()
    end))
    u24._connections:trackConnection(u18.CAMERA_MIN_DISTANCE_CHANGED, u13:GetPropertyChangedSignal("CameraMinZoomDistance"):Connect(function() --[[Anonymous function at line 168]]
        --[[
        Upvalues:
            [1] = u24
        --]]
        u24:OnPlayerCameraPropertyChange()
    end))
    u24._connections:trackConnection(u18.CAMERA_MAX_DISTANCE_CHANGED, u13:GetPropertyChangedSignal("CameraMaxZoomDistance"):Connect(function() --[[Anonymous function at line 171]]
        --[[
        Upvalues:
            [1] = u24
        --]]
        u24:OnPlayerCameraPropertyChange()
    end))
    u24:OnPlayerCameraPropertyChange()
end
function u19.OnCharacterAdded(u26, p27) --[[Anonymous function at line 177]]
    --[[
    Upvalues:
        [1] = u17
        [2] = u2
        [3] = u13
    --]]
    u26.resetCameraAngle = u26.resetCameraAngle or u26:GetEnabled()
    u26.humanoidRootPart = nil
    if not u17 and u2.TouchEnabled then
        u26.PlayerGui = u13:WaitForChild("PlayerGui")
        for _, v28 in ipairs(p27:GetChildren()) do
            if v28:IsA("Tool") then
                u26.isAToolEquipped = true
            end
        end
        p27.ChildAdded:Connect(function(p29) --[[Anonymous function at line 190]]
            --[[
            Upvalues:
                [1] = u26
            --]]
            if p29:IsA("Tool") then
                u26.isAToolEquipped = true
            end
        end)
        p27.ChildRemoved:Connect(function(p30) --[[Anonymous function at line 195]]
            --[[
            Upvalues:
                [1] = u26
            --]]
            if p30:IsA("Tool") then
                u26.isAToolEquipped = false
            end
        end)
    end
end
function u19.GetHumanoidRootPart(p31) --[[Anonymous function at line 204]]
    --[[
    Upvalues:
        [1] = u13
    --]]
    local v32 = (not p31.humanoidRootPart and u13.Character and true or false) and u13.Character:FindFirstChildOfClass("Humanoid")
    if v32 then
        p31.humanoidRootPart = v32.RootPart
    end
    return p31.humanoidRootPart
end
function u19.GetBodyPartToFollow(_, p33, _) --[[Anonymous function at line 216]]
    if p33:GetState() == Enum.HumanoidStateType.Dead then
        local v34 = p33.Parent
        if v34 and v34:IsA("Model") then
            return v34:FindFirstChild("Head") or p33.RootPart
        end
    end
    return p33.RootPart
end
function u19.GetSubjectCFrame(p35) --[[Anonymous function at line 228]]
    local v36 = p35.lastSubjectCFrame
    local v37 = workspace.CurrentCamera
    if v37 then
        v37 = v37.CameraSubject
    end
    if not v37 then
        return v36
    end
    if v37:IsA("Humanoid") then
        local v38 = v37:GetState() == Enum.HumanoidStateType.Dead
        local v39 = v37.CameraOffset
        if p35:GetIsMouseLocked() then
            v39 = Vector3.new()
        end
        local v40 = v37.RootPart
        if v38 and (v37.Parent and v37.Parent:IsA("Model")) then
            v40 = v37.Parent:FindFirstChild("Head") or v40
        end
        if v40 and v40:IsA("BasePart") then
            local v41
            if v37.RigType == Enum.HumanoidRigType.R15 then
                if v37.AutomaticScalingEnabled then
                    v41 = Vector3.new(0, 1.5, 0)
                    local v42 = v37.RootPart
                    if v40 == v42 then
                        local v43 = (v42.Size.Y - (Vector3.new(2, 2, 1)).Y) / 2
                        v41 = v41 + Vector3.new(0, v43, 0)
                    end
                else
                    v41 = Vector3.new(0, 2, 0)
                end
            else
                v41 = Vector3.new(0, 1.5, 0)
            end
            v36 = v40.CFrame * CFrame.new((v38 and Vector3.new(0, 0, 0) or v41) + v39)
        end
    elseif v37:IsA("BasePart") then
        v36 = v37.CFrame
    elseif v37:IsA("Model") then
        if v37.PrimaryPart then
            v36 = v37:GetPrimaryPartCFrame()
        else
            v36 = CFrame.new()
        end
    end
    if v36 then
        p35.lastSubjectCFrame = v36
    end
    return v36
end
function u19.GetSubjectVelocity(_) --[[Anonymous function at line 302]]
    local v44 = workspace.CurrentCamera
    if v44 then
        v44 = v44.CameraSubject
    end
    if not v44 then
        return Vector3.new(0, 0, 0)
    end
    if v44:IsA("BasePart") then
        return v44.Velocity
    end
    if v44:IsA("Humanoid") then
        local v45 = v44.RootPart
        if v45 then
            return v45.Velocity
        end
    else
        local v46 = v44:IsA("Model") and v44.PrimaryPart
        if v46 then
            return v46.Velocity
        end
    end
    return Vector3.new(0, 0, 0)
end
function u19.GetSubjectRotVelocity(_) --[[Anonymous function at line 331]]
    local v47 = workspace.CurrentCamera
    if v47 then
        v47 = v47.CameraSubject
    end
    if not v47 then
        return Vector3.new(0, 0, 0)
    end
    if v47:IsA("BasePart") then
        return v47.RotVelocity
    end
    if v47:IsA("Humanoid") then
        local v48 = v47.RootPart
        if v48 then
            return v48.RotVelocity
        end
    else
        local v49 = v47:IsA("Model") and v47.PrimaryPart
        if v49 then
            return v49.RotVelocity
        end
    end
    return Vector3.new(0, 0, 0)
end
function u19.StepZoom(p50) --[[Anonymous function at line 360]]
    --[[
    Upvalues:
        [1] = u11
        [2] = u9
    --]]
    local v51 = p50.currentSubjectDistance
    local v52 = u11.getZoomDelta()
    if math.abs(v52) > 0 then
        local v53
        if v52 > 0 then
            local v54 = v51 + v52 * (v51 * 0.5 + 1)
            local v55 = p50.FIRST_PERSON_DISTANCE_THRESHOLD
            v53 = math.max(v54, v55)
        else
            local v56 = (v51 + v52) / (1 - v52 * 0.5)
            v53 = math.max(v56, 0.5)
        end
        p50:SetCameraToSubjectDistance(v53 < p50.FIRST_PERSON_DISTANCE_THRESHOLD and 0.5 or v53)
    end
    return u9.GetZoomRadius()
end
function u19.GetSubjectPosition(p57) --[[Anonymous function at line 385]]
    local v58 = p57.lastSubjectPosition
    local v59 = game.Workspace.CurrentCamera
    if v59 then
        v59 = v59.CameraSubject
    end
    if not v59 then
        return nil
    end
    if v59:IsA("Humanoid") then
        local v60 = v59:GetState() == Enum.HumanoidStateType.Dead
        local v61 = v59.CameraOffset
        if p57:GetIsMouseLocked() then
            v61 = Vector3.new()
        end
        local v62 = v59.RootPart
        if v60 and (v59.Parent and v59.Parent:IsA("Model")) then
            v62 = v59.Parent:FindFirstChild("Head") or v62
        end
        if v62 and v62:IsA("BasePart") then
            local v63
            if v59.RigType == Enum.HumanoidRigType.R15 then
                if v59.AutomaticScalingEnabled then
                    v63 = Vector3.new(0, 1.5, 0)
                    if v62 == v59.RootPart then
                        local v64 = v59.RootPart.Size.Y / 2 - (Vector3.new(2, 2, 1)).Y / 2
                        v63 = v63 + Vector3.new(0, v64, 0)
                    end
                else
                    v63 = Vector3.new(0, 2, 0)
                end
            else
                v63 = Vector3.new(0, 1.5, 0)
            end
            v58 = v62.CFrame.p + v62.CFrame:vectorToWorldSpace((v60 and Vector3.new(0, 0, 0) or v63) + v61)
        end
    elseif v59:IsA("VehicleSeat") then
        v58 = v59.CFrame.p + v59.CFrame:vectorToWorldSpace(Vector3.new(0, 5, 0))
    elseif v59:IsA("SkateboardPlatform") then
        v58 = v59.CFrame.p + Vector3.new(0, 5, 0)
    elseif v59:IsA("BasePart") then
        v58 = v59.CFrame.p
    elseif v59:IsA("Model") then
        if v59.PrimaryPart then
            v58 = v59:GetPrimaryPartCFrame().p
        else
            v58 = v59:GetModelCFrame().p
        end
    end
    p57.lastSubject = v59
    p57.lastSubjectPosition = v58
    return v58
end
function u19.OnViewportSizeChanged(p65) --[[Anonymous function at line 462]]
    --[[
    Upvalues:
        [1] = u2
    --]]
    local v66 = game.Workspace.CurrentCamera.ViewportSize
    p65.portraitMode = v66.X < v66.Y
    local v67 = u2.TouchEnabled
    if v67 then
        v67 = v66.Y < 500 and true or v66.X < 700
    end
    p65.isSmallTouchScreen = v67
end
function u19.OnCurrentCameraChanged(u68) --[[Anonymous function at line 470]]
    --[[
    Upvalues:
        [1] = u2
    --]]
    if u2.TouchEnabled then
        if u68.viewportSizeChangedConn then
            u68.viewportSizeChangedConn:Disconnect()
            u68.viewportSizeChangedConn = nil
        end
        local v69 = game.Workspace.CurrentCamera
        if v69 then
            u68:OnViewportSizeChanged()
            u68.viewportSizeChangedConn = v69:GetPropertyChangedSignal("ViewportSize"):Connect(function() --[[Anonymous function at line 481]]
                --[[
                Upvalues:
                    [1] = u68
                --]]
                u68:OnViewportSizeChanged()
            end)
        end
    end
    if u68.cameraSubjectChangedConn then
        u68.cameraSubjectChangedConn:Disconnect()
        u68.cameraSubjectChangedConn = nil
    end
    local v70 = game.Workspace.CurrentCamera
    if v70 then
        u68.cameraSubjectChangedConn = v70:GetPropertyChangedSignal("CameraSubject"):Connect(function() --[[Anonymous function at line 495]]
            --[[
            Upvalues:
                [1] = u68
            --]]
            u68:OnNewCameraSubject()
        end)
        u68:OnNewCameraSubject()
    end
end
function u19.OnPlayerCameraPropertyChange(p71) --[[Anonymous function at line 502]]
    p71:SetCameraToSubjectDistance(p71.currentSubjectDistance)
end
function u19.InputTranslationToCameraAngleChange(_, p72, p73) --[[Anonymous function at line 507]]
    return p72 * p73
end
function u19.GamepadZoomPress(p74) --[[Anonymous function at line 513]]
    --[[
    Upvalues:
        [1] = u13
        [2] = u16
    --]]
    local v75 = p74:GetCameraToSubjectDistance()
    local v76 = u13.CameraMaxZoomDistance
    for v77 = #p74.gamepadZoomLevels, 1, -1 do
        local v78 = p74.gamepadZoomLevels[v77]
        if v76 >= v78 then
            if v78 < u13.CameraMinZoomDistance then
                v78 = u13.CameraMinZoomDistance
                if u16 and v76 == v78 then
                    break
                end
            end
            if not u16 and v76 == v78 then
                break
            end
            if v78 + (v76 - v78) / 2 < v75 then
                p74:SetCameraToSubjectDistance(v78)
                return
            end
            v76 = v78
        end
    end
    p74:SetCameraToSubjectDistance(p74.gamepadZoomLevels[#p74.gamepadZoomLevels])
end
function u19.Enable(p79, p80) --[[Anonymous function at line 558]]
    if p79.enabled ~= p80 then
        p79.enabled = p80
        p79:OnEnabledChanged()
    end
end
function u19.OnEnabledChanged(u81) --[[Anonymous function at line 566]]
    --[[
    Upvalues:
        [1] = u11
        [2] = u13
    --]]
    if u81.enabled then
        u81:_setUpConfigurations()
        u11.setInputEnabled(true)
        u81.gamepadZoomPressConnection = u11.gamepadZoomPress:Connect(function() --[[Anonymous function at line 572]]
            --[[
            Upvalues:
                [1] = u81
            --]]
            u81:GamepadZoomPress()
        end)
        if u13.CameraMode == Enum.CameraMode.LockFirstPerson then
            u81.currentSubjectDistance = 0.5
            if not u81.inFirstPerson then
                u81:EnterFirstPerson()
            end
        end
        if u81.cameraChangedConn then
            u81.cameraChangedConn:Disconnect()
            u81.cameraChangedConn = nil
        end
        u81.cameraChangedConn = workspace:GetPropertyChangedSignal("CurrentCamera"):Connect(function() --[[Anonymous function at line 584]]
            --[[
            Upvalues:
                [1] = u81
            --]]
            u81:OnCurrentCameraChanged()
        end)
        u81:OnCurrentCameraChanged()
    else
        u81._connections:disconnectAll()
        u11.setInputEnabled(false)
        if u81.gamepadZoomPressConnection then
            u81.gamepadZoomPressConnection:Disconnect()
            u81.gamepadZoomPressConnection = nil
        end
        u81:Cleanup()
    end
end
function u19.GetEnabled(p82) --[[Anonymous function at line 602]]
    return p82.enabled
end
function u19.Cleanup(p83) --[[Anonymous function at line 606]]
    --[[
    Upvalues:
        [1] = u8
    --]]
    if p83.subjectStateChangedConn then
        p83.subjectStateChangedConn:Disconnect()
        p83.subjectStateChangedConn = nil
    end
    if p83.viewportSizeChangedConn then
        p83.viewportSizeChangedConn:Disconnect()
        p83.viewportSizeChangedConn = nil
    end
    if p83.cameraChangedConn then
        p83.cameraChangedConn:Disconnect()
        p83.cameraChangedConn = nil
    end
    p83.lastCameraTransform = nil
    p83.lastSubjectCFrame = nil
    u8.restoreMouseBehavior()
end
function u19.UpdateMouseBehavior(p84) --[[Anonymous function at line 627]]
    --[[
    Upvalues:
        [1] = u4
        [2] = u12
        [3] = u11
        [4] = u10
        [5] = u8
    --]]
    local v85 = u4.ComputerMovementMode == Enum.ComputerMovementMode.ClickToMove
    if p84.isCameraToggle and v85 == false then
        u12.setCameraModeToastEnabled(true)
        u11.enableCameraToggleInput()
        u10(p84.inFirstPerson)
        return
    else
        u12.setCameraModeToastEnabled(false)
        u11.disableCameraToggleInput()
        if p84.inFirstPerson or p84.inMouseLockedMode then
            u8.setRotationTypeOverride(Enum.RotationType.CameraRelative)
            u8.setMouseBehaviorOverride(Enum.MouseBehavior.LockCenter)
            return
        else
            u8.restoreRotationType()
            if u11.getRotationActivated() then
                u8.setMouseBehaviorOverride(Enum.MouseBehavior.LockCurrentPosition)
            else
                u8.restoreMouseBehavior()
            end
        end
    end
end
function u19.UpdateForDistancePropertyChange(p86) --[[Anonymous function at line 655]]
    p86:SetCameraToSubjectDistance(p86.currentSubjectDistance)
end
function u19.SetCameraToSubjectDistance(p87, p88) --[[Anonymous function at line 661]]
    --[[
    Upvalues:
        [1] = u13
        [2] = u9
    --]]
    local v89 = p87.currentSubjectDistance
    if u13.CameraMode == Enum.CameraMode.LockFirstPerson then
        p87.currentSubjectDistance = 0.5
        if not p87.inFirstPerson then
            p87:EnterFirstPerson()
        end
    else
        local v90 = u13.CameraMinZoomDistance
        local v91 = u13.CameraMaxZoomDistance
        local v92 = math.clamp(p88, v90, v91)
        if v92 < 1 then
            p87.currentSubjectDistance = 0.5
            if not p87.inFirstPerson then
                p87:EnterFirstPerson()
            end
        else
            p87.currentSubjectDistance = v92
            if p87.inFirstPerson then
                p87:LeaveFirstPerson()
            end
        end
    end
    local v93 = u9.SetZoomParameters
    local v94 = p87.currentSubjectDistance
    local v95 = p88 - v89
    v93(v94, (math.sign(v95)))
    return p87.currentSubjectDistance
end
function u19.SetCameraType(p96, p97) --[[Anonymous function at line 695]]
    p96.cameraType = p97
end
function u19.GetCameraType(p98) --[[Anonymous function at line 700]]
    return p98.cameraType
end
function u19.SetCameraMovementMode(p99, p100) --[[Anonymous function at line 705]]
    p99.cameraMovementMode = p100
end
function u19.GetCameraMovementMode(p101) --[[Anonymous function at line 709]]
    return p101.cameraMovementMode
end
function u19.SetIsMouseLocked(p102, p103) --[[Anonymous function at line 713]]
    p102.inMouseLockedMode = p103
end
function u19.GetIsMouseLocked(p104) --[[Anonymous function at line 717]]
    return p104.inMouseLockedMode
end
function u19.SetMouseLockOffset(p105, p106) --[[Anonymous function at line 721]]
    p105.mouseLockOffset = p106
end
function u19.GetMouseLockOffset(p107) --[[Anonymous function at line 725]]
    return p107.mouseLockOffset
end
function u19.InFirstPerson(p108) --[[Anonymous function at line 729]]
    return p108.inFirstPerson
end
function u19.EnterFirstPerson(p109) --[[Anonymous function at line 733]]
    p109.inFirstPerson = true
    p109:UpdateMouseBehavior()
end
function u19.LeaveFirstPerson(p110) --[[Anonymous function at line 738]]
    p110.inFirstPerson = false
    p110:UpdateMouseBehavior()
end
function u19.GetCameraToSubjectDistance(p111) --[[Anonymous function at line 744]]
    return p111.currentSubjectDistance
end
function u19.GetMeasuredDistanceToFocus(_) --[[Anonymous function at line 751]]
    local v112 = game.Workspace.CurrentCamera
    if v112 then
        return (v112.CoordinateFrame.p - v112.Focus.p).magnitude
    else
        return nil
    end
end
function u19.GetCameraLookVector(_) --[[Anonymous function at line 759]]
    return game.Workspace.CurrentCamera and game.Workspace.CurrentCamera.CFrame.LookVector or Vector3.new(0, 0, 1)
end
function u19.CalculateNewLookCFrameFromArg(p113, p114, p115) --[[Anonymous function at line 763]]
    local v116 = p114 or p113:GetCameraLookVector()
    local v117 = v116.Y
    local v118 = math.asin(v117)
    local v119 = p115.Y
    local v120 = v118 + -1.3962634015954636
    local v121 = v118 + 1.3962634015954636
    local v122 = math.clamp(v119, v120, v121)
    local v123 = Vector2.new(p115.X, v122)
    local v124 = CFrame.new(Vector3.new(0, 0, 0), v116)
    return CFrame.Angles(0, -v123.X, 0) * v124 * CFrame.Angles(-v123.Y, 0, 0)
end
function u19.CalculateNewLookVectorFromArg(p125, p126, p127) --[[Anonymous function at line 773]]
    return p125:CalculateNewLookCFrameFromArg(p126, p127).LookVector
end
function u19.CalculateNewLookVectorVRFromArg(p128, p129) --[[Anonymous function at line 778]]
    local v130 = ((p128:GetSubjectPosition() - game.Workspace.CurrentCamera.CFrame.p) * Vector3.new(1, 0, 1)).unit
    local v131 = Vector2.new(p129.X, 0)
    local v132 = CFrame.new(Vector3.new(0, 0, 0), v130)
    return ((CFrame.Angles(0, -v131.X, 0) * v132 * CFrame.Angles(-v131.Y, 0, 0)).LookVector * Vector3.new(1, 0, 1)).unit
end
function u19.GetHumanoid(p133) --[[Anonymous function at line 788]]
    --[[
    Upvalues:
        [1] = u13
    --]]
    local v134 = u13
    if v134 then
        v134 = u13.Character
    end
    if not v134 then
        return nil
    end
    local v135 = p133.humanoidCache[u13]
    if v135 and v135.Parent == v134 then
        return v135
    end
    p133.humanoidCache[u13] = nil
    local v136 = v134:FindFirstChildOfClass("Humanoid")
    if v136 then
        p133.humanoidCache[u13] = v136
    end
    return v136
end
function u19.GetHumanoidPartToFollow(_, p137, p138) --[[Anonymous function at line 806]]
    if p138 == Enum.HumanoidStateType.Dead then
        local v139 = p137.Parent
        if v139 then
            return v139:FindFirstChild("Head") or p137.Torso
        else
            return p137.Torso
        end
    else
        return p137.Torso
    end
end
function u19.OnNewCameraSubject(p140) --[[Anonymous function at line 820]]
    if p140.subjectStateChangedConn then
        p140.subjectStateChangedConn:Disconnect()
        p140.subjectStateChangedConn = nil
    end
end
function u19.IsInFirstPerson(p141) --[[Anonymous function at line 827]]
    return p141.inFirstPerson
end
function u19.Update(_, _) --[[Anonymous function at line 831]]
    error("BaseCamera:Update() This is a virtual function that should never be getting called.", 2)
end
function u19.GetCameraHeight(p142) --[[Anonymous function at line 835]]
    --[[
    Upvalues:
        [1] = u3
    --]]
    return (not u3.VREnabled or p142.inFirstPerson) and 0 or 0.25881904510252074 * p142.currentSubjectDistance
end
return u19