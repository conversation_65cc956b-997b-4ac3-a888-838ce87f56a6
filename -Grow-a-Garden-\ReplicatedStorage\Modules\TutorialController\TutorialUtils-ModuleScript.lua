-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\TutorialController\TutorialUtils-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
local u2 = game:GetService("RunService")
local v3 = game:GetService("Players")
local u4 = require(v1.Modules.GetFarm)
local u5 = require(v1.Modules.FastTween)
local u6 = v3.LocalPlayer
local u7 = u6.PlayerGui.Tutorial_UI
local u8 = u7.Pointer
local function u13() --[[Anonymous function at line 14]]
    --[[
    Upvalues:
        [1] = u6
    --]]
    local v9 = {}
    for _, v10 in u6.Backpack:GetChildren() do
        if v10:IsA("Tool") then
            table.insert(v9, v10)
        end
    end
    local v11 = u6.Character
    local v12
    if v11 then
        v12 = v11:FindFirstChildWhichIsA("Tool")
    else
        v12 = nil
    end
    if v12 then
        table.insert(v9, v12)
    end
    return v9
end
local u14 = false
return {
    ["getTools"] = u13,
    ["waitForSeed"] = function() --[[Function name: waitForSeed, line 30]]
        --[[
        Upvalues:
            [1] = u13
        --]]
        while true do
            for _, v15 in u13() do
                if string.find(v15.Name, "Seed") then
                    return true
                end
            end
            task.wait(0.1)
        end
    end,
    ["waitUntilSellableItem"] = function() --[[Function name: waitUntilSellableItem, line 42]]
        --[[
        Upvalues:
            [1] = u13
        --]]
        while true do
            for _, v16 in u13() do
                if v16:FindFirstChild("Item_String") then
                    return true
                end
            end
            task.wait(0.1)
        end
    end,
    ["waitUntilNoSellableItems"] = function() --[[Function name: waitUntilNoSellableItems, line 54]]
        --[[
        Upvalues:
            [1] = u13
        --]]
        while true do
            local v17 = false
            for _, v18 in u13() do
                if v18:FindFirstChild("Item_String") then
                    v17 = true
                end
            end
            if not v17 then
                return true
            end
            task.wait(0.1)
        end
    end,
    ["waitForFarm"] = function() --[[Function name: waitForFarm, line 71]]
        --[[
        Upvalues:
            [1] = u4
            [2] = u6
        --]]
        local v19 = nil
        while true do
            v19 = v19 or u4(u6)
            if v19 then
                break
            end
            task.wait(0.1)
        end
        return v19
    end,
    ["waitUntilDistance"] = function(p20, p21) --[[Function name: waitUntilDistance, line 85]]
        --[[
        Upvalues:
            [1] = u6
        --]]
        while not u6.Character or u6:DistanceFromCharacter(p20) > p21 do
            task.wait(0.1)
        end
    end,
    ["waitUntilSeedEquipped"] = function() --[[Function name: waitUntilSeedEquipped, line 95]]
        --[[
        Upvalues:
            [1] = u6
        --]]
        while true do
            local v22 = u6.Character
            if v22 then
                v22 = v22:FindFirstChildWhichIsA("Tool")
            end
            if v22 and v22:GetAttribute("ItemType") == "Seed" then
                return
            end
            task.wait(0.1)
        end
    end,
    ["pointToUI"] = function(u23, p24, p25) --[[Function name: pointToUI, line 108]]
        --[[
        Upvalues:
            [1] = u14
            [2] = u8
            [3] = u7
            [4] = u5
            [5] = u2
        --]]
        while u14 do
            task.wait()
        end
        u14 = true
        local function v27() --[[Anonymous function at line 115]]
            --[[
            Upvalues:
                [1] = u23
                [2] = u8
            --]]
            local v26 = u23()
            u8.Position = UDim2.fromOffset(v26.X, v26.Y)
        end
        task.spawn(v27)
        u8.Parent = p24 or u7
        u8.UIScale.Scale = p25 or 1
        u8.Visible = true
        u5(u8, TweenInfo.new(0.3), {
            ["ImageTransparency"] = 0
        })
        local u28 = u5(u8.UIScale, TweenInfo.new(1, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut, -1, true), {
            ["Scale"] = (p25 or 1) * 1.1
        })
        local u29 = u2.PreRender:Connect(v27)
        return function() --[[Anonymous function at line 130]]
            --[[
            Upvalues:
                [1] = u29
                [2] = u5
                [3] = u8
                [4] = u28
                [5] = u7
                [6] = u14
            --]]
            if u29.Connected then
                u29:Disconnect()
                u5(u8, TweenInfo.new(0.3), {
                    ["ImageTransparency"] = 1
                }).Completed:Wait()
                u28:Cancel()
                u8.Visible = false
                u8.Parent = u7
                u14 = false
            end
        end
    end
}