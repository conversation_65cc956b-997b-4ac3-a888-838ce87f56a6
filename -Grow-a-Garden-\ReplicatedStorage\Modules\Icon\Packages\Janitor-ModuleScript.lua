-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\Icon\Packages\Janitor-ModuleScript.lua
local u1 = game:GetService("RunService")
local u2 = u1.Heartbeat
local u3 = newproxy(true)
getmetatable(u3).__tostring = function() --[[Anonymous function at line 33]]
    return "IndicesReference"
end
local u4 = newproxy(true)
getmetatable(u4).__tostring = function() --[[Anonymous function at line 38]]
    return "LinkToInstanceIndex"
end
local u5 = {
    ["IGNORE_MEMORY_DEBUG"] = true,
    ["ClassName"] = "Janitor",
    ["__index"] = {
        ["CurrentlyCleaning"] = true,
        [u3] = nil
    }
}
local u6 = {
    ["function"] = true,
    ["Promise"] = "cancel",
    ["RBXScriptConnection"] = "Disconnect"
}
function u5.new() --[[Anonymous function at line 64]]
    --[[
    Upvalues:
        [1] = u3
        [2] = u5
    --]]
    local v7 = {
        ["CurrentlyCleaning"] = false,
        [u3] = nil
    }
    local v8 = u5
    return setmetatable(v7, v8)
end
function u5.Is(p9) --[[Anonymous function at line 76]]
    --[[
    Upvalues:
        [1] = u5
    --]]
    local v10
    if type(p9) == "table" then
        v10 = getmetatable(p9) == u5
    else
        v10 = false
    end
    return v10
end
u5.is = u5.Is
function u5.__index.Add(p11, p12, p13, p14) --[[Anonymous function at line 89]]
    --[[
    Upvalues:
        [1] = u3
        [2] = u6
    --]]
    if p14 then
        p11:Remove(p14)
        local v15 = p11[u3]
        if not v15 then
            v15 = {}
            p11[u3] = v15
        end
        v15[p14] = p12
    end
    local v16 = typeof(p12)
    local v17 = p13 or (u6[v16 == "table" and string.match(tostring(p12), "Promise") and "Promise" or v16] or "Destroy")
    if type(p12) ~= "function" and not p12[v17] then
        warn(string.format("Object %s doesn\'t have method %s, are you sure you want to add it? Traceback: %s", tostring(p12), tostring(v17), debug.traceback(nil, 2)))
    end
    p11[p12] = { v17, (debug.traceback("")) }
    return p12
end
u5.__index.Give = u5.__index.Add
function u5.__index.AddPromise(p18, u19) --[[Anonymous function at line 125]]
    --[[
    Upvalues:
        [1] = u1
    --]]
    local v20
    if u1:IsRunning() then
        v20 = require(game:GetService("ReplicatedStorage").Framework).modules.Promise
    else
        v20 = nil
    end
    if not v20 then
        return u19
    end
    if not v20.is(u19) then
        error(string.format("Invalid argument #1 to \'Janitor:AddPromise\' (Promise expected, got %s (%s))", typeof(u19), (tostring(u19))))
    end
    if u19:getStatus() ~= v20.Status.Started then
        return u19
    end
    local v21 = newproxy(false)
    local v24 = p18:Add(v20.new(function(p22, _, p23) --[[Anonymous function at line 133]]
        --[[
        Upvalues:
            [1] = u19
        --]]
        if not p23(function() --[[Anonymous function at line 134]]
            --[[
            Upvalues:
                [1] = u19
            --]]
            u19:cancel()
        end) then
            p22(u19)
        end
    end), "cancel", v21)
    v24:finallyCall(p18.Remove, p18, v21)
    return v24
end
u5.__index.GivePromise = u5.__index.AddPromise
function u5.__index.AddObject(p25, p26) --[[Anonymous function at line 155]]
    --[[
    Upvalues:
        [1] = u1
    --]]
    local v27 = newproxy(false)
    local v28
    if u1:IsRunning() then
        v28 = require(game:GetService("ReplicatedStorage").Framework).modules.Promise
    else
        v28 = nil
    end
    if not (v28 and v28.is(p26)) then
        return p25:Add(p26, false, v27), v27
    end
    if p26:getStatus() ~= v28.Status.Started then
        return p26
    end
    local v29 = p25:Add(v28.resolve(p26), "cancel", v27)
    v29:finallyCall(p25.Remove, p25, v27)
    return v29, v27
end
u5.__index.GiveObject = u5.__index.AddObject
function u5.__index.Remove(p30, p31) --[[Anonymous function at line 178]]
    --[[
    Upvalues:
        [1] = u3
    --]]
    local v32 = p30[u3]
    local v33 = v32 and v32[p31]
    if v33 then
        local v34 = p30[v33]
        if v34 then
            v34 = v34[1]
        end
        if v34 then
            if v34 == true then
                v33()
            else
                local v35 = v33[v34]
                if v35 then
                    v35(v33)
                end
            end
            p30[v33] = nil
        end
        v32[p31] = nil
    end
    return p30
end
function u5.__index.Get(p36, p37) --[[Anonymous function at line 212]]
    --[[
    Upvalues:
        [1] = u3
    --]]
    local v38 = p36[u3]
    if v38 then
        return v38[p37]
    end
end
function u5.__index.Cleanup(p39) --[[Anonymous function at line 223]]
    --[[
    Upvalues:
        [1] = u3
    --]]
    if not p39.CurrentlyCleaning then
        p39.CurrentlyCleaning = nil
        for v40, v41 in next, p39 do
            if v40 ~= u3 then
                local v42 = type(v40)
                if v42 == "string" or v42 == "number" then
                    p39[v40] = nil
                else
                    local v43 = v41[1]
                    local v44 = v41[2]
                    if v43 == true then
                        local v45, v46 = pcall(v40)
                        if not v45 then
                            local v47 = debug.traceback("", 3)
                            warn("-------- Janitor Error --------" .. "\n" .. tostring(v46) .. "\n" .. v47 .. "" .. v44)
                        end
                    else
                        local v48 = v40[v43]
                        if v48 then
                            local v49, v50 = pcall(v48, v40)
                            local v51
                            if typeof(v40) == "Instance" then
                                v51 = v48 == "Destroy"
                            else
                                v51 = false
                            end
                            if not (v49 or v51) then
                                local v52 = debug.traceback("", 3)
                                warn("-------- Janitor Error --------" .. "\n" .. tostring(v50) .. "\n" .. v52 .. "" .. v44)
                            end
                        end
                    end
                    p39[v40] = nil
                end
            end
        end
        local v53 = p39[u3]
        if v53 then
            for v54 in next, v53 do
                v53[v54] = nil
            end
            p39[u3] = {}
        end
        p39.CurrentlyCleaning = false
    end
end
u5.__index.Clean = u5.__index.Cleanup
function u5.__index.Destroy(p55) --[[Anonymous function at line 283]]
    p55:Cleanup()
end
u5.__call = u5.__index.Cleanup
local u56 = {
    ["Connected"] = true
}
u56.__index = u56
function u56.Disconnect(p57) --[[Anonymous function at line 297]]
    if p57.Connected then
        p57.Connected = false
        p57.Connection:Disconnect()
    end
end
function u56.__tostring(p58) --[[Anonymous function at line 304]]
    local v59 = p58.Connected
    return "Disconnect<" .. tostring(v59) .. ">"
end
function u5.__index.LinkToInstance(u60, p61, p62) --[[Anonymous function at line 314]]
    --[[
    Upvalues:
        [1] = u4
        [2] = u56
        [3] = u2
    --]]
    local u63 = nil
    local v64 = p62 and newproxy(false) or u4
    local u65 = p61.Parent == nil
    local v66 = u56
    local u67 = setmetatable({}, v66)
    local function v69(_, p68) --[[Anonymous function at line 320]]
        --[[
        Upvalues:
            [1] = u67
            [2] = u65
            [3] = u2
            [4] = u63
            [5] = u60
        --]]
        u65 = u67.Connected and p68 == nil
        if u65 then
            coroutine.wrap(function() --[[Anonymous function at line 326]]
                --[[
                Upvalues:
                    [1] = u2
                    [2] = u67
                    [3] = u63
                    [4] = u60
                    [5] = u65
                --]]
                u2:Wait()
                if u67.Connected then
                    if u63.Connected then
                        while u65 and (u63.Connected and u67.Connected) do
                            u2:Wait()
                        end
                        if u67.Connected and u65 then
                            u60:Cleanup()
                        end
                    else
                        u60:Cleanup()
                    end
                else
                    return
                end
            end)()
        end
    end
    local u70 = p61.AncestryChanged:Connect(v69)
    u67.Connection = u70
    if u65 then
        local v71 = p61.Parent
        if u67.Connected then
            if v71 == nil then
                u65 = true
            else
                u65 = false
            end
            if u65 then
                coroutine.wrap(function() --[[Anonymous function at line 326]]
                    --[[
                    Upvalues:
                        [1] = u2
                        [2] = u67
                        [3] = u70
                        [4] = u60
                        [5] = u65
                    --]]
                    u2:Wait()
                    if u67.Connected then
                        if u70.Connected then
                            while u65 and (u70.Connected and u67.Connected) do
                                u2:Wait()
                            end
                            if u67.Connected and u65 then
                                u60:Cleanup()
                            end
                        else
                            u60:Cleanup()
                        end
                    else
                        return
                    end
                end)()
            end
        end
    end
    return u60:Add(u67, "Disconnect", v64)
end
function u5.__index.LinkToInstances(p72, ...) --[[Anonymous function at line 362]]
    --[[
    Upvalues:
        [1] = u5
    --]]
    local v73 = u5.new()
    for _, v74 in ipairs({ ... }) do
        v73:Add(p72:LinkToInstance(v74, true), "Disconnect")
    end
    return v73
end
for v75, v76 in next, u5.__index do
    local v77 = string.lower(v75)
    local v78 = string.sub(v77, 1, 1) .. string.sub(v75, 2)
    u5.__index[v78] = v76
end
return u5