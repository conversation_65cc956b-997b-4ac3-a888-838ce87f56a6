-- Full Path: -Grow-a-Garden-\\RichText-ModuleScript.lua
local u1 = {}
local u2 = {
    ["Color"] = "TextColor3",
    ["StrokeColor"] = "TextStrokeColor3",
    ["ImageColor"] = "ImageColor3"
}
u1.ColorShortcuts = {}
u1.ColorShortcuts.White = Color3.new(1, 1, 1)
u1.ColorShortcuts.Black = Color3.new(0, 0, 0)
u1.ColorShortcuts.Red = Color3.new(1, 0.4, 0.4)
u1.ColorShortcuts.Green = Color3.new(0.4, 1, 0.4)
u1.ColorShortcuts.Blue = Color3.new(0.4, 0.4, 1)
u1.ColorShortcuts.Cyan = Color3.new(0.4, 0.85, 1)
u1.ColorShortcuts.Orange = Color3.new(1, 0.5, 0.2)
u1.ColorShortcuts.Yellow = Color3.new(1, 0.9, 0.2)
u1.ImageShortcuts = {}
u1.ImageShortcuts.Eggplant = 639588687
u1.ImageShortcuts.Thinking = 955646496
u1.ImageShortcuts.Sad = 947900188
u1.ImageShortcuts.Happy = 414889555
u1.ImageShortcuts.Despicable = 711674643
local u3 = {
    ["ContainerHorizontalAlignment"] = "Center",
    ["ContainerVerticalAlignment"] = "Center",
    ["TextYAlignment"] = "Center",
    ["TextScaled"] = true,
    ["TextScaleRelativeTo"] = "Frame",
    ["TextScale"] = 0.6,
    ["TextSize"] = 20,
    ["Font"] = "SourceSans",
    ["TextColor3"] = "White",
    ["TextStrokeColor3"] = "Black",
    ["TextTransparency"] = 0,
    ["TextStrokeTransparency"] = 0,
    ["BackgroundTransparency"] = 1,
    ["BorderSizePixel"] = 0,
    ["ImageColor3"] = "White",
    ["ImageTransparency"] = 0,
    ["ImageRectOffset"] = "0,0",
    ["ImageRectSize"] = "0,0",
    ["AnimateStepTime"] = 0,
    ["AnimateStepGrouping"] = "Letter",
    ["AnimateStepFrequency"] = 4,
    ["AnimateYield"] = 0,
    ["AnimateStyle"] = "Appear",
    ["AnimateStyleTime"] = 0.5,
    ["AnimateStyleNumPeriods"] = 3,
    ["AnimateStyleAmplitude"] = 0.5
}
local u33 = {
    ["Appear"] = function(p4) --[[Function name: Appear, line 226]]
        p4.Visible = true
    end,
    ["Fade"] = function(p5, p6, p7) --[[Function name: Fade, line 230]]
        p5.Visible = true
        if p5:IsA("TextLabel") then
            p5.TextTransparency = 1 - p6 * (1 - p7.TextTransparency)
        elseif p5:IsA("ImageLabel") then
            p5.ImageTransparency = 1 - p6 * (1 - p7.ImageTransparency)
        end
    end,
    ["Wiggle"] = function(p8, p9, p10) --[[Function name: Wiggle, line 239]]
        p8.Visible = true
        local v11 = p10.InitialSize.Y.Offset * (1 - p9) * p10.AnimateStyleAmplitude
        local v12 = p10.InitialPosition
        local v13 = UDim2.new
        local v14 = p9 * 3.141592653589793 * 2 * p10.AnimateStyleNumPeriods
        p8.Position = v12 + v13(0, 0, 0, math.sin(v14) * v11 / 2)
    end,
    ["FallDown"] = function(p15, p16, p17) --[[Function name: FallDown, line 245]]
        p15.Visible = true
        local v18 = p17.InitialSize.Y.Offset * (1 - p16) * p17.AnimateStyleAmplitude
        p15.Position = p17.InitialPosition + UDim2.new(0, 0, 0, v18 * -1)
        p15.TextTransparency = 1 - p16
    end,
    ["Swing"] = function(p19, p20, p21) --[[Function name: Swing, line 252]]
        p19.Visible = true
        local v22 = 90 * (1 - p20) * p21.AnimateStyleAmplitude
        local v23 = p20 * 3.141592653589793 * 2 * p21.AnimateStyleNumPeriods
        p19.Rotation = math.sin(v23) * v22
    end,
    ["Spin"] = function(p24, p25, p26) --[[Function name: Spin, line 258]]
        p24.Visible = true
        p24.Position = p26.InitialPosition + UDim2.new(0, p26.InitialSize.X.Offset / 2, 0, p26.InitialSize.Y.Offset / 2)
        p24.AnchorPoint = Vector2.new(0.5, 0.5)
        p24.Rotation = p25 * p26.AnimateStyleNumPeriods * 360
    end,
    ["Rainbow"] = function(p27, p28, p29) --[[Function name: Rainbow, line 265]]
        p27.Visible = true
        local v30 = Color3.fromHSV(p28 * p29.AnimateStyleNumPeriods % 1, 1, 1)
        if p27:IsA("TextLabel") then
            local v31 = getColorFromString(p29.TextColor3)
            p27.TextColor3 = Color3.new(v30.r + p28 * (v31.r - v30.r), v30.g + p28 * (v31.g - v30.g), v30.b + p28 * (v31.b - v30.b))
        else
            local v32 = getColorFromString(p29.ImageColor3)
            p27.ImageColor3 = Color3.new(v30.r + p28 * (v32.r - v30.r), v30.g + p28 * (v32.g - v30.g), v30.b + p28 * (v32.b - v30.b))
        end
    end
}
local u34 = game:GetService("TextService")
local u35 = game:GetService("RunService")
local u36 = 0
function getLayerCollector(p37)
    if p37 then
        if p37:IsA("LayerCollector") then
            return p37
        elseif p37 and p37.Parent then
            return getLayerCollector(p37.Parent)
        else
            return nil
        end
    else
        return nil
    end
end
function shallowCopy(p38)
    local v39 = {}
    for v40, v41 in pairs(p38) do
        v39[v40] = v41
    end
    return v39
end
function getColorFromString(p42)
    --[[
    Upvalues:
        [1] = u1
    --]]
    if u1.ColorShortcuts[p42] then
        return u1.ColorShortcuts[p42]
    end
    local v43, v44, v45 = p42:match("(%d+),(%d+),(%d+)")
    return Color3.new(v43 / 255, v44 / 255, v45 / 255)
end
function getVector2FromString(p46)
    local v47, v48 = p46:match("(%d+),(%d+)")
    return Vector2.new(v47, v48)
end
function setHorizontalAlignment(p49, p50)
    if p50 == "Left" then
        p49.AnchorPoint = Vector2.new(0, 0)
        p49.Position = UDim2.new(0, 0, 0, 0)
        return
    elseif p50 == "Center" then
        p49.AnchorPoint = Vector2.new(0.5, 0)
        p49.Position = UDim2.new(0.5, 0, 0, 0)
    elseif p50 == "Right" then
        p49.AnchorPoint = Vector2.new(1, 0)
        p49.Position = UDim2.new(1, 0, 0, 0)
    end
end
function u1.New(_, u51, p52, p53, p54, p55) --[[Anonymous function at line 331]]
    --[[
    Upvalues:
        [1] = u2
        [2] = u3
        [3] = u34
        [4] = u1
        [5] = u36
        [6] = u35
        [7] = u33
    --]]
    for _, v56 in pairs(u51:GetChildren()) do
        v56:Destroy()
    end
    local u57 = p54 == nil and true or p54
    local u58 = {}
    local u59 = {}
    if p55 then
        p52 = p55.Text
        p53 = p55.StartingProperties
    end
    local u60 = {}
    local u61 = {}
    local u62 = {}
    local u63 = 0
    local u64 = false
    local u65 = Instance.new("TextLabel")
    local u66 = Instance.new("ImageLabel")
    local u67 = getLayerCollector(u51)
    u65.AutoLocalize = false
    local u68 = nil
    local u69 = nil
    local function u73(p70, p71) --[[Anonymous function at line 359]]
        --[[
        Upvalues:
            [1] = u2
            [2] = u59
            [3] = u58
            [4] = u68
            [5] = u60
            [6] = u3
            [7] = u69
        --]]
        local v72 = u2[p70] or p70
        if p71 == "/" then
            if u59[v72] then
                p71 = u59[v72]
            else
                warn("Attempt to default <" .. v72 .. "> to value with no default")
            end
        end
        if tonumber(p71) then
            p71 = tonumber(p71)
        elseif p71 == "false" or p71 == "true" then
            p71 = p71 == "true"
        end
        u58[v72] = p71
        if not u68(v72, p71) then
            if v72 == "ContainerHorizontalAlignment" and u60[#u60] then
                setHorizontalAlignment(u60[#u60].Container, p71)
            elseif not u3[v72] then
                if v72 ~= "Img" then
                    return false
                end
                u69(p71)
            end
        end
        return true
    end
    u68 = function(u74, p75, p76) --[[Function name: applyProperty, line 390]]
        --[[
        Upvalues:
            [1] = u65
            [2] = u66
        --]]
        local u77 = nil
        local v78 = false
        for _, u79 in pairs(p76 and { p76 } or { u65, u66 }) do
            if pcall(function() --[[Anonymous function at line 394]]
                --[[
                Upvalues:
                    [1] = u77
                    [2] = u79
                    [3] = u74
                --]]
                local v80 = u79[u74]
                u77 = typeof(v80)
            end) then
                if u77 == "Color3" then
                    u79[u74] = getColorFromString(p75)
                elseif u77 == "Vector2" then
                    u79[u74] = getVector2FromString(p75)
                else
                    u79[u74] = p75
                end
                v78 = true
            end
        end
        return v78
    end
    local u81 = u68
    for v82, v83 in pairs(u3) do
        u73(v82, v83)
        u59[u2[v82] or v82] = u58[u2[v82] or v82]
    end
    for v84, v85 in pairs(p53 or {}) do
        u73(v84, v85)
        u59[u2[v84] or v84] = u58[u2[v84] or v84]
    end
    if p55 then
        u58 = p55.OverflowPickupProperties
        for v86, v87 in pairs(u58) do
            u73(v86, v87)
        end
    end
    local u88 = 0
    local function u97() --[[Anonymous function at line 443]]
        --[[
        Upvalues:
            [1] = u60
            [2] = u88
            [3] = u57
            [4] = u58
            [5] = u67
            [6] = u51
            [7] = u64
            [8] = u61
            [9] = u63
        --]]
        local v89 = u60[#u60]
        if v89 then
            u88 = u88 + v89.Size.Y.Offset
            if not u57 then
                local v90 = u88
                local v91
                if u58.TextScaled == true then
                    local v92 = nil
                    if u58.TextScaleRelativeTo == "Screen" then
                        v92 = u67.AbsoluteSize.Y
                    elseif u58.TextScaleRelativeTo == "Frame" then
                        v92 = u51.AbsoluteSize.Y
                    end
                    local v93 = u58.TextScale * v92
                    v91 = math.min(v93, 100)
                else
                    v91 = u58.TextSize
                end
                if v90 + v91 > u51.AbsoluteSize.Y then
                    u64 = true
                    return
                end
            end
        end
        local v94 = Instance.new("Frame")
        v94.Name = string.format("Line%03d", #u60 + 1)
        v94.Size = UDim2.new(0, 0, 0, 0)
        v94.BackgroundTransparency = 1
        local v95 = Instance.new("Frame", v94)
        v95.Name = "Container"
        v95.Size = UDim2.new(0, 0, 0, 0)
        v95.BackgroundTransparency = 1
        setHorizontalAlignment(v95, u58.ContainerHorizontalAlignment)
        v94.Parent = u51
        local v96 = u60
        table.insert(v96, v94)
        u61[#u60] = {}
        u63 = 0
    end
    u97()
    local function u109(p98, p99, p100, p101) --[[Anonymous function at line 476]]
        --[[
        Upvalues:
            [1] = u60
            [2] = u58
            [3] = u63
            [4] = u51
            [5] = u61
            [6] = u97
            [7] = u62
        --]]
        local v102 = u60[#u60]
        local v103 = u58.TextYAlignment
        local v104 = tostring(v103)
        if v104 == "Top" then
            p98.Position = UDim2.new(0, u63, 0, 0)
            p98.AnchorPoint = Vector2.new(0, 0)
        elseif v104 == "Center" then
            p98.Position = UDim2.new(0, u63, 0.5, 0)
            p98.AnchorPoint = Vector2.new(0, 0.5)
        elseif v104 == "Bottom" then
            p98.Position = UDim2.new(0, u63, 1, 0)
            p98.AnchorPoint = Vector2.new(0, 1)
        end
        u63 = u63 + p100
        if u63 > u51.AbsoluteSize.X and u63 ~= p100 then
            p98:Destroy()
            local v105 = u61[#u60][#u61[#u60]]
            if v105:IsA("TextLabel") and v105.Text == " " then
                v102.Container.Size = UDim2.new(0, u63 - p100 - v105.Size.X.Offset, 1, 0)
                v105:Destroy()
                table.remove(u61[#u60])
            end
            u97()
            p101()
        else
            p98.Size = UDim2.new(0, p100, 0, p99)
            v102.Container.Size = UDim2.new(0, u63, 1, 0)
            local v106 = UDim2.new
            local v107 = v102.Size.Y.Offset
            v102.Size = v106(1, 0, 0, (math.max(v107, p99)))
            p98.Name = string.format("Group%03d", #u61[#u60] + 1)
            p98.Parent = v102.Container
            local v108 = u61[#u60]
            table.insert(v108, p98)
            u62[p98] = shallowCopy(u58)
            u62[p98].InitialSize = p98.Size
            u62[p98].InitialPosition = p98.Position
            u62[p98].InitialAnchorPoint = p98.AnchorPoint
            u58.AnimateYield = 0
        end
    end
    local function u123(u110) --[[Anonymous function at line 516]]
        --[[
        Upvalues:
            [1] = u97
            [2] = u63
            [3] = u58
            [4] = u67
            [5] = u51
            [6] = u34
            [7] = u65
            [8] = u62
            [9] = u109
            [10] = u64
            [11] = u123
        --]]
        if u110 == "\n" then
            u97()
            return
        elseif u110 ~= " " or u63 ~= 0 then
            local v111
            if u58.TextScaled == true then
                local v112 = nil
                if u58.TextScaleRelativeTo == "Screen" then
                    v112 = u67.AbsoluteSize.Y
                elseif u58.TextScaleRelativeTo == "Frame" then
                    v112 = u51.AbsoluteSize.Y
                end
                local v113 = u58.TextScale * v112
                v111 = math.min(v113, 100)
            else
                v111 = u58.TextSize
            end
            local v114 = u34:GetTextSize(u110, v111, u65.Font, Vector2.new(u67.AbsoluteSize.X, v111)).X
            local v115 = u65:Clone()
            v115.TextScaled = false
            v115.TextSize = v111
            v115.Text = u110
            v115.TextTransparency = 1
            v115.TextStrokeTransparency = 1
            v115.TextWrapped = false
            local v116 = 1
            local v117 = 0
            for v118, v119 in utf8.graphemes(u110) do
                local v120 = string.sub(u110, v118, v119)
                local v121 = u34:GetTextSize(v120, v111, u65.Font, Vector2.new(u67.AbsoluteSize.X, v111)).X
                local v122 = u65:Clone()
                v122.Text = v120
                v122.TextScaled = false
                v122.TextSize = v111
                v122.Position = UDim2.new(0, v117, 0, 0)
                v122.Size = UDim2.new(0, v121 + 1, 0, v111)
                v122.Name = string.format("Char%03d", v116)
                v122.Parent = v115
                v122.Visible = false
                u62[v122] = shallowCopy(u58)
                u62[v122].InitialSize = v122.Size
                u62[v122].InitialPosition = v122.Position
                u62[v122].InitialAnchorPoint = v122.AnchorPoint
                v117 = v117 + v121
                v116 = v116 + 1
            end
            u109(v115, v111, v114, function() --[[Anonymous function at line 555]]
                --[[
                Upvalues:
                    [1] = u64
                    [2] = u123
                    [3] = u110
                --]]
                if not u64 then
                    u123(u110)
                end
            end)
        end
    end
    u69 = function(u124) --[[Function name: printImage, line 558]]
        --[[
        Upvalues:
            [1] = u58
            [2] = u67
            [3] = u51
            [4] = u66
            [5] = u1
            [6] = u109
            [7] = u64
            [8] = u69
        --]]
        local v125
        if u58.TextScaled == true then
            local v126 = nil
            if u58.TextScaleRelativeTo == "Screen" then
                v126 = u67.AbsoluteSize.Y
            elseif u58.TextScaleRelativeTo == "Frame" then
                v126 = u51.AbsoluteSize.Y
            end
            local v127 = u58.TextScale * v126
            v125 = math.min(v127, 100)
        else
            v125 = u58.TextSize
        end
        local v128 = u66:Clone()
        if u1.ImageShortcuts[u124] then
            local v129 = u1.ImageShortcuts[u124]
            v128.Image = typeof(v129) == "number" and "rbxassetid://" .. u1.ImageShortcuts[u124] or u1.ImageShortcuts[u124]
        else
            v128.Image = "rbxassetid://" .. u124
        end
        v128.Size = UDim2.new(0, v125, 0, v125)
        v128.Visible = false
        u109(v128, v125, v125, function() --[[Anonymous function at line 572]]
            --[[
            Upvalues:
                [1] = u64
                [2] = u69
                [3] = u124
            --]]
            if not u64 then
                u69(u124)
            end
        end)
    end
    local function v134(p130) --[[Anonymous function at line 575]]
        --[[
        Upvalues:
            [1] = u73
            [2] = u123
        --]]
        for _, v131 in pairs(p130) do
            local v132, v133 = string.match(v131, "<(.+)=(.+)>")
            if v132 and v133 then
                if not u73(v132, v133) then
                    warn("Could not apply markup: ", v131)
                end
            else
                u123(v131)
            end
        end
    end
    local v135 = #p52
    local v136 = {}
    local v137
    if p55 then
        v137 = p55.OverflowPickupIndex
    else
        v137 = 1
    end
    while true do
        if not v137 or v137 > v135 then
            v138 = v137
            break
        end
        local v138, v139 = string.find(p52, "<.->", v137)
        local v140, v141 = string.find(p52, "[ \t\n]", v137)
        local v142
        if v138 and (v139 and (not v140 or v138 < v140)) then
            v142 = nil
        else
            v138 = v140 or v135 + 1
            v139 = v141 or v135 + 1
            v142 = true
        end
        local v143
        if v137 < v138 then
            local v144 = v138 - 1
            v143 = string.sub(p52, v137, v144) or nil
        else
            v143 = nil
        end
        local v145
        if v138 <= v135 then
            v145 = string.sub(p52, v138, v139) or nil
        else
            v145 = nil
        end
        table.insert(v136, v143)
        if v142 then
            v134(v136)
            if u64 then
                v138 = v137
                break
            end
            v134({ v145 })
            if u64 then
                break
            end
            v136 = {}
        else
            table.insert(v136, v145)
        end
        v137 = v139 + 1
    end
    if not u64 then
        v134(v136)
    end
    local v146 = Instance.new("UIListLayout")
    v146.HorizontalAlignment = u58.ContainerHorizontalAlignment
    v146.VerticalAlignment = u58.ContainerVerticalAlignment
    v146.Parent = u51
    local v147 = u51.AbsoluteSize.X
    local v148 = 0
    local v149 = 0
    for _, v150 in pairs(u60) do
        v148 = v148 + v150.Size.Y.Offset
        local v151 = v150.Container
        local v152 = nil
        local v153 = nil
        if v151.AnchorPoint.X == 0 then
            v152 = v151.Position.X.Offset
            v153 = v151.Size.X.Offset
        elseif v151.AnchorPoint.X == 0.5 then
            v152 = v150.AbsoluteSize.X / 2 - v151.Size.X.Offset / 2
            v153 = v150.AbsoluteSize.X / 2 + v151.Size.X.Offset / 2
        elseif v151.AnchorPoint.X == 1 then
            v152 = v150.AbsoluteSize.X - v151.Size.X.Offset
            v153 = v150.AbsoluteSize.X
        end
        v147 = math.min(v147, v152)
        v149 = math.max(v149, v153)
    end
    u36 = u36 + 1
    local u154 = false
    local u155 = false
    local u156 = false
    local u157 = "TextAnimation" .. u36
    local u158 = {}
    local function u166() --[[Anonymous function at line 673]]
        --[[
        Upvalues:
            [1] = u155
            [2] = u158
            [3] = u154
            [4] = u35
            [5] = u157
            [6] = u33
        --]]
        if u155 and #u158 == 0 or u154 then
            u154 = true
            u35:UnbindFromRenderStep(u157)
            u158 = {}
        else
            local v159 = tick()
            for v160 = #u158, 1, -1 do
                local v161 = u158[v160]
                local v162 = v161.Settings
                local v163 = u33[v162.AnimateStyle]
                if not v163 then
                    warn("No animation style found for: ", v162.AnimateStyle, ", defaulting to Appear")
                    v163 = u33.Appear
                end
                local v164 = (v159 - v161.Start) / v162.AnimateStyleTime
                local v165 = math.min(v164, 1)
                v163(v161.Char, v165, v162)
                if v165 >= 1 then
                    table.remove(u158, v160)
                end
            end
        end
    end
    local function u170(p167) --[[Anonymous function at line 698]]
        --[[
        Upvalues:
            [1] = u62
            [2] = u81
        --]]
        p167.Position = u62[p167].InitialPosition
        p167.Size = u62[p167].InitialSize
        p167.AnchorPoint = u62[p167].InitialAnchorPoint
        for v168, v169 in pairs(u62[p167]) do
            u81(v168, v169, p167)
        end
    end
    local function u174(p171, p172) --[[Anonymous function at line 707]]
        --[[
        Upvalues:
            [1] = u170
        --]]
        p171.Visible = p172
        for _, v173 in pairs(p171:GetChildren()) do
            v173.Visible = p172
            if p172 then
                u170(v173)
            end
        end
        if p172 and p171:IsA("ImageLabel") then
            u170(p171)
        end
    end
    local function u203(p175) --[[Anonymous function at line 720]]
        --[[
        Upvalues:
            [1] = u154
            [2] = u35
            [3] = u157
            [4] = u166
            [5] = u61
            [6] = u174
            [7] = u158
            [8] = u156
            [9] = u62
            [10] = u155
        --]]
        u154 = false
        u35:BindToRenderStep(u157, Enum.RenderPriority.Last.Value, u166)
        local v176 = nil
        local v177 = nil
        local _ = nil
        local v178 = nil
        for _, v179 in pairs(u61) do
            for _, v180 in pairs(v179) do
                u174(v180, false)
            end
        end
        for _, v181 in pairs(u61) do
            for _, v182 in pairs(v181) do
                local v183 = u62[v182]
                v176 = (v183.AnimateStepGrouping ~= v178 or v183.AnimateStepFrequency ~= v177) and 0 or v176
                v178 = v183.AnimateStepGrouping
                local v184 = v183.AnimateStepTime
                local v185 = v183.AnimateStepFrequency
                if v183.AnimateYield > 0 then
                    wait(v183.AnimateYield)
                end
                local v186
                if v178 == "Word" or v178 == "All" then
                    local v187
                    if v182:IsA("TextLabel") then
                        v182.Visible = true
                        v187 = v184
                        v177 = v185
                        for _, v188 in pairs(v182:GetChildren()) do
                            local v189 = u158
                            local v190 = {
                                ["Char"] = v188,
                                ["Settings"] = u62[v188],
                                ["Start"] = tick()
                            }
                            table.insert(v189, v190)
                        end
                    else
                        local v191 = u158
                        local v192 = {
                            ["Char"] = v182,
                            ["Settings"] = v183,
                            ["Start"] = tick()
                        }
                        table.insert(v191, v192)
                        v187 = v184
                        v177 = v185
                    end
                    if v178 == "Word" then
                        v186 = v176 + 1
                        if u156 or (v186 % v177 ~= 0 or v187 < 0) then
                            v176 = v186
                        else
                            local v193 = v187 > 0 and v187 or nil
                            wait(v193)
                            v176 = v186
                        end
                    end
                elseif v178 == "Letter" then
                    if v182:IsA("TextLabel") then
                        v182.Visible = true
                        local _ = v182.Text
                        local v194 = v184
                        v177 = v185
                        local v195 = 1
                        while true do
                            local v196 = v182:FindFirstChild(string.format("Char%03d", v195))
                            if not v196 then
                                break
                            end
                            local v197 = u158
                            local v198 = {
                                ["Char"] = v196,
                                ["Settings"] = u62[v196],
                                ["Start"] = tick()
                            }
                            table.insert(v197, v198)
                            v186 = v176 + 1
                            if not u156 and (v186 % v177 == 0 and v194 >= 0) then
                                local v199
                                if v194 > 0 then
                                    v199 = v194 or nil
                                else
                                    v199 = nil
                                end
                                wait(v199)
                            end
                            if u154 then
                                return
                            end
                            v195 = v195 + 1
                            v176 = v186
                        end
                    else
                        local v200 = u158
                        local v201 = {
                            ["Char"] = v182,
                            ["Settings"] = v183,
                            ["Start"] = tick()
                        }
                        table.insert(v200, v201)
                        v186 = v176 + 1
                        if u156 or (v186 % v185 ~= 0 or v184 < 0) then
                            v176 = v186
                            v177 = v185
                        else
                            local v202 = v184 > 0 and v184 or nil
                            wait(v202)
                            v176 = v186
                            v177 = v185
                        end
                    end
                else
                    warn("Invalid step grouping: ", v178)
                    v177 = v185
                end
                if u154 then
                    return
                end
            end
        end
        u155 = true
        if p175 then
            while #u158 > 0 do
                u35.RenderStepped:Wait()
            end
        end
    end
    local v204 = {
        ["Overflown"] = u64,
        ["OverflowPickupIndex"] = v138,
        ["StartingProperties"] = p53,
        ["OverflowPickupProperties"] = u58,
        ["Text"] = p52
    }
    if p55 then
        p55.NextTextObject = v204
    end
    v204.ContentSize = Vector2.new(v149 - v147, v148)
    function v204.Animate(p205, p206) --[[Anonymous function at line 841]]
        --[[
        Upvalues:
            [1] = u203
        --]]
        if p206 then
            u203()
        else
            coroutine.wrap(u203)()
        end
        if p205.NextTextObject then
            p205.NextTextObject:Animate(p206)
        end
    end
    function v204.Show(p207, p208) --[[Anonymous function at line 852]]
        --[[
        Upvalues:
            [1] = u156
            [2] = u154
            [3] = u61
            [4] = u174
        --]]
        if p208 then
            u156 = true
        else
            u154 = true
            for _, v209 in pairs(u61) do
                for _, v210 in pairs(v209) do
                    u174(v210, true)
                end
            end
        end
        if p207.NextTextObject then
            p207.NextTextObject:Show(p208)
        end
    end
    function v204.Hide(p211) --[[Anonymous function at line 868]]
        --[[
        Upvalues:
            [1] = u154
            [2] = u61
            [3] = u174
        --]]
        u154 = true
        for _, v212 in pairs(u61) do
            for _, v213 in pairs(v212) do
                u174(v213, false)
            end
        end
        if p211.NextTextObject then
            p211.NextTextObject:Hide()
        end
    end
    return v204
end
function u1.ContinueOverflow(_, p214, p215) --[[Anonymous function at line 884]]
    --[[
    Upvalues:
        [1] = u1
    --]]
    return u1:New(p214, nil, nil, false, p215)
end
return u1