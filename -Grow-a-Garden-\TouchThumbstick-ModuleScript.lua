-- Full Path: -Grow-a-Garden-\\TouchThumbstick-ModuleScript.lua
game:GetService("Players")
local u1 = game:GetService("GuiService")
local u2 = game:GetService("UserInputService")
UserSettings():GetService("UserGameSettings")
local u3 = require(script.Parent:Wait<PERSON><PERSON><PERSON><PERSON><PERSON>("BaseCharacterController"))
local u4 = setmetatable({}, u3)
u4.__index = u4
function u4.new() --[[Anonymous function at line 20]]
    --[[
    Upvalues:
        [1] = u3
        [2] = u4
    --]]
    local v5 = u3.new()
    local v6 = u4
    local v7 = setmetatable(v5, v6)
    v7.isFollowStick = false
    v7.thumbstickFrame = nil
    v7.moveTouchObject = nil
    v7.onTouchMovedConn = nil
    v7.onTouchEndedConn = nil
    v7.screenPos = nil
    v7.stickImage = nil
    v7.thumbstickSize = nil
    return v7
end
function u4.Enable(p8, p9, p10) --[[Anonymous function at line 35]]
    if p9 == nil then
        return false
    end
    local v11 = p9 and true or false
    if p8.enabled == v11 then
        return true
    end
    p8.moveVector = Vector3.new(0, 0, 0)
    p8.isJumping = false
    if v11 then
        if not p8.thumbstickFrame then
            p8:Create(p10)
        end
        p8.thumbstickFrame.Visible = true
    else
        p8.thumbstickFrame.Visible = false
        p8:OnInputEnded()
    end
    p8.enabled = v11
end
function u4.OnInputEnded(p12) --[[Anonymous function at line 56]]
    p12.thumbstickFrame.Position = p12.screenPos
    p12.stickImage.Position = UDim2.new(0, p12.thumbstickFrame.Size.X.Offset / 2 - p12.thumbstickSize / 4, 0, p12.thumbstickFrame.Size.Y.Offset / 2 - p12.thumbstickSize / 4)
    p12.moveVector = Vector3.new(0, 0, 0)
    p12.isJumping = false
    p12.thumbstickFrame.Position = p12.screenPos
    p12.moveTouchObject = nil
end
function u4.Create(u13, u14) --[[Anonymous function at line 65]]
    --[[
    Upvalues:
        [1] = u2
        [2] = u1
    --]]
    if u13.thumbstickFrame then
        u13.thumbstickFrame:Destroy()
        u13.thumbstickFrame = nil
        if u13.onTouchMovedConn then
            u13.onTouchMovedConn:Disconnect()
            u13.onTouchMovedConn = nil
        end
        if u13.onTouchEndedConn then
            u13.onTouchEndedConn:Disconnect()
            u13.onTouchEndedConn = nil
        end
        if u13.absoluteSizeChangedConn then
            u13.absoluteSizeChangedConn:Disconnect()
            u13.absoluteSizeChangedConn = nil
        end
    end
    u13.thumbstickFrame = Instance.new("Frame")
    u13.thumbstickFrame.Name = "ThumbstickFrame"
    u13.thumbstickFrame.Active = true
    u13.thumbstickFrame.Visible = false
    u13.thumbstickFrame.BackgroundTransparency = 1
    local u15 = Instance.new("ImageLabel")
    u15.Name = "OuterImage"
    u15.Image = "rbxasset://textures/ui/TouchControlsSheet.png"
    u15.ImageRectOffset = Vector2.new()
    u15.ImageRectSize = Vector2.new(220, 220)
    u15.BackgroundTransparency = 1
    u15.Position = UDim2.new(0, 0, 0, 0)
    u13.stickImage = Instance.new("ImageLabel")
    u13.stickImage.Name = "StickImage"
    u13.stickImage.Image = "rbxasset://textures/ui/TouchControlsSheet.png"
    u13.stickImage.ImageRectOffset = Vector2.new(220, 0)
    u13.stickImage.ImageRectSize = Vector2.new(111, 111)
    u13.stickImage.BackgroundTransparency = 1
    u13.stickImage.ZIndex = 2
    local function v19() --[[Anonymous function at line 105]]
        --[[
        Upvalues:
            [1] = u14
            [2] = u13
            [3] = u15
        --]]
        local v16 = u14.AbsoluteSize.X
        local v17 = u14.AbsoluteSize.Y
        local v18 = math.min(v16, v17) <= 500
        u13.thumbstickSize = v18 and 70 or 120
        u13.screenPos = v18 and UDim2.new(0, u13.thumbstickSize / 2 - 10, 1, -u13.thumbstickSize - 20) or UDim2.new(0, u13.thumbstickSize / 2, 1, -u13.thumbstickSize * 1.75)
        u13.thumbstickFrame.Size = UDim2.new(0, u13.thumbstickSize, 0, u13.thumbstickSize)
        u13.thumbstickFrame.Position = u13.screenPos
        u15.Size = UDim2.new(0, u13.thumbstickSize, 0, u13.thumbstickSize)
        u13.stickImage.Size = UDim2.new(0, u13.thumbstickSize / 2, 0, u13.thumbstickSize / 2)
        u13.stickImage.Position = UDim2.new(0, u13.thumbstickSize / 2 - u13.thumbstickSize / 4, 0, u13.thumbstickSize / 2 - u13.thumbstickSize / 4)
    end
    v19()
    u13.absoluteSizeChangedConn = u14:GetPropertyChangedSignal("AbsoluteSize"):Connect(v19)
    u15.Parent = u13.thumbstickFrame
    u13.stickImage.Parent = u13.thumbstickFrame
    local u20 = nil
    local function u27(p21) --[[Anonymous function at line 145]]
        --[[
        Upvalues:
            [1] = u20
            [2] = u13
        --]]
        local v22 = Vector2.new(p21.X - u20.X, p21.Y - u20.Y)
        local v23 = v22.magnitude
        local v24 = u13.thumbstickFrame.AbsoluteSize.X / 2
        if u13.isFollowStick and v24 < v23 then
            local v25 = v22.unit * v24
            u13.thumbstickFrame.Position = UDim2.new(0, p21.X - u13.thumbstickFrame.AbsoluteSize.X / 2 - v25.X, 0, p21.Y - u13.thumbstickFrame.AbsoluteSize.Y / 2 - v25.Y)
        else
            local v26 = math.min(v23, v24)
            v22 = v22.unit * v26
        end
        u13.stickImage.Position = UDim2.new(0, v22.X + u13.stickImage.AbsoluteSize.X / 2, 0, v22.Y + u13.stickImage.AbsoluteSize.Y / 2)
    end
    u13.thumbstickFrame.InputBegan:Connect(function(p28) --[[Anonymous function at line 162]]
        --[[
        Upvalues:
            [1] = u13
            [2] = u20
        --]]
        if not u13.moveTouchObject and (p28.UserInputType == Enum.UserInputType.Touch and p28.UserInputState == Enum.UserInputState.Begin) then
            u13.moveTouchObject = p28
            u13.thumbstickFrame.Position = UDim2.new(0, p28.Position.X - u13.thumbstickFrame.Size.X.Offset / 2, 0, p28.Position.Y - u13.thumbstickFrame.Size.Y.Offset / 2)
            u20 = Vector2.new(u13.thumbstickFrame.AbsolutePosition.X + u13.thumbstickFrame.AbsoluteSize.X / 2, u13.thumbstickFrame.AbsolutePosition.Y + u13.thumbstickFrame.AbsoluteSize.Y / 2)
            Vector2.new(p28.Position.X - u20.X, p28.Position.Y - u20.Y)
        end
    end)
    u13.onTouchMovedConn = u2.TouchMoved:Connect(function(p29, _) --[[Anonymous function at line 177]]
        --[[
        Upvalues:
            [1] = u13
            [2] = u20
            [3] = u27
        --]]
        if p29 == u13.moveTouchObject then
            u20 = Vector2.new(u13.thumbstickFrame.AbsolutePosition.X + u13.thumbstickFrame.AbsoluteSize.X / 2, u13.thumbstickFrame.AbsolutePosition.Y + u13.thumbstickFrame.AbsoluteSize.Y / 2)
            local v30 = Vector2.new(p29.Position.X - u20.X, p29.Position.Y - u20.Y) / (u13.thumbstickSize / 2)
            local v31 = v30.magnitude
            local v32
            if v31 < 0.05 then
                v32 = Vector3.new()
            else
                local v33 = v30.unit
                local v34 = (v31 - 0.05) / 0.95
                local v35 = v33 * math.min(1, v34)
                local v36 = v35.X
                local v37 = v35.Y
                v32 = Vector3.new(v36, 0, v37)
            end
            u13.moveVector = v32
            u27(p29.Position)
        end
    end)
    u13.onTouchEndedConn = u2.TouchEnded:Connect(function(p38, _) --[[Anonymous function at line 187]]
        --[[
        Upvalues:
            [1] = u13
        --]]
        if p38 == u13.moveTouchObject then
            u13:OnInputEnded()
        end
    end)
    u1.MenuOpened:Connect(function() --[[Anonymous function at line 193]]
        --[[
        Upvalues:
            [1] = u13
        --]]
        if u13.moveTouchObject then
            u13:OnInputEnded()
        end
    end)
    u13.thumbstickFrame.Parent = u14
end
return u4