-- Full Path: -Grow-a-Garden-\\bool-ModuleScript.lua
local v1 = require(script.Parent.Parent.process.bufferWriter)
require(script.Parent.Parent.types)
local u4 = {
    ["read"] = function(p2, p3) --[[Function name: read, line 11]]
        return buffer.readu8(p2, p3) == 1, 1
    end,
    ["write"] = v1.bool
}
return function() --[[Anonymous function at line 18]]
    --[[
    Upvalues:
        [1] = u4
    --]]
    return u4
end