-- Full Path: -Grow-a-Garden-\\Variant1-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
local u2 = game:GetService("Players").LocalPlayer
local u3 = require(v1.Modules.Notification)
require(v1.Modules.GetFarm)
local u4 = require(v1.Modules.TutorialController.TutorialUtils)
local u5 = require(v1.Arrow_Module)
return function() --[[Anonymous function at line 14]]
    --[[
    Upvalues:
        [1] = u5
        [2] = u2
        [3] = u3
        [4] = u4
    --]]
    local u6 = {}
    local u24 = task.spawn(function() --[[Anonymous function at line 29]]
        --[[
        Upvalues:
            [1] = u5
            [2] = u2
            [3] = u6
            [4] = u3
            [5] = u4
        --]]
        local v7 = workspace.Tutorial_Points.Tutorial_Point_1.Position
        local u8 = u5.GenerateArrow(u2, v7, math.random(1, 1000000))
        local v9 = u6
        table.insert(v9, u8)
        local function v11() --[[Anonymous function at line 20]]
            --[[
            Upvalues:
                [1] = u6
                [2] = u8
                [3] = u5
            --]]
            local v10 = table.find(u6, u8)
            if v10 then
                u5.Remove_Arrow(u8)
                table.remove(u6, v10)
            end
        end
        local v12 = task.delay(5, function() --[[Anonymous function at line 31]]
            --[[
            Upvalues:
                [1] = u3
            --]]
            u3:CreateNotification("Go buy your first seed!")
        end)
        u4.waitForSeed()
        if coroutine.status(v12) == "suspended" then
            task.cancel(v12)
        end
        v11()
        local v13 = u4.waitForFarm()
        u3:CreateNotification("Go plant your seed!")
        local v14 = (v13.Spawn_Point.CFrame * CFrame.new(0, 0, -10)).Position
        local u15 = u5.GenerateArrow(u2, v14, math.random(1, 1000000))
        local v16 = u6
        table.insert(v16, u15)
        local function v18() --[[Anonymous function at line 20]]
            --[[
            Upvalues:
                [1] = u6
                [2] = u15
                [3] = u5
            --]]
            local v17 = table.find(u6, u15)
            if v17 then
                u5.Remove_Arrow(u15)
                table.remove(u6, v17)
            end
        end
        u4.waitUntilSellableItem()
        v18()
        u3:CreateNotification("Go sell your plant!")
        local v19 = workspace.Tutorial_Points.Tutorial_Point_2.Position
        local u20 = u5.GenerateArrow(u2, v19, math.random(1, 1000000))
        local v21 = u6
        table.insert(v21, u20)
        local function v23() --[[Anonymous function at line 20]]
            --[[
            Upvalues:
                [1] = u6
                [2] = u20
                [3] = u5
            --]]
            local v22 = table.find(u6, u20)
            if v22 then
                u5.Remove_Arrow(u20)
                table.remove(u6, v22)
            end
        end
        u4.waitUntilNoSellableItems()
        v23()
    end)
    return function() --[[Anonymous function at line 58]]
        --[[
        Upvalues:
            [1] = u24
            [2] = u6
            [3] = u5
        --]]
        if coroutine.status(u24) == "suspended" then
            task.cancel(u24)
        end
        for _, v25 in u6 do
            u5.Remove_Arrow(v25)
        end
        table.clear(u6)
    end
end