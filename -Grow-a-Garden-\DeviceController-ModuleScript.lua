-- Full Path: -Grow-a-Garden-\\DeviceController-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
local u2 = game:GetService("UserInputService")
local u10 = {
    ["DeviceChanged"] = require(v1.Modules.Signal).new(),
    ["GetCurrentDevice"] = function(_) --[[Function name: GetCurrentDevice, line 13]]
        --[[
        Upvalues:
            [1] = u2
        --]]
        local v3 = u2:GetLastInputType()
        return v3 == Enum.UserInputType.Touch and "Touch" or ((v3 == Enum.UserInputType.Keyboard or string.find(v3.Name, "Mouse", 1, true)) and "PC" or (string.find(v3.Name, "Gamepad", 1, true) and "Gamepad" or "PC"))
    end,
    ["Observe"] = function(_, u4) --[[Function name: Observe, line 26]]
        --[[
        Upvalues:
            [1] = u10
        --]]
        local u5 = u10.DeviceChanged:Connect(function() --[[Anonymous function at line 27]]
            --[[
            Upvalues:
                [1] = u4
                [2] = u10
            --]]
            task.spawn(u4, u10:GetCurrentDevice())
        end)
        task.spawn(u4, u10:GetCurrentDevice())
        return function() --[[Anonymous function at line 33]]
            --[[
            Upvalues:
                [1] = u5
            --]]
            u5:Disconnect()
        end
    end,
    ["Start"] = function(_) --[[Function name: Start, line 38]]
        --[[
        Upvalues:
            [1] = u10
            [2] = u2
        --]]
        local u6 = nil
        local function v8() --[[Anonymous function at line 40]]
            --[[
            Upvalues:
                [1] = u10
                [2] = u6
            --]]
            local v7 = u10:GetCurrentDevice()
            if v7 ~= u6 then
                u6 = v7
                u10.DeviceChanged:Fire(v7)
            end
        end
        u2.LastInputTypeChanged:Connect(v8)
        local v9 = u10:GetCurrentDevice()
        if v9 ~= u6 then
            u6 = v9
            u10.DeviceChanged:Fire(v9)
        end
    end
}
task.spawn(u10.Start, u10)
return u10