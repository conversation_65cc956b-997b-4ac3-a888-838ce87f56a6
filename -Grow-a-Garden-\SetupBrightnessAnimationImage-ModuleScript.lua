-- Full Path: -Grow-a-Garden-\\SetupBrightnessAnimationImage-ModuleScript.lua
local u1 = game:GetService("TweenService")
return function(p2) --[[Function name: SetupBrightnessAnimation, line 9]]
    --[[
    Upvalues:
        [1] = u1
    --]]
    local v3 = p2:FindFirstChild("InletTexture", true)
    if not p2:Get<PERSON>ttribute("DefaultColor") then
        p2:<PERSON><PERSON><PERSON><PERSON>bute("DefaultColor", v3.ImageColor3)
    end
    local v4 = p2:GetAttribute("DefaultColor")
    local v5, v6, v7 = v4:ToHSV()
    local v8 = v7 + 0.1
    local v9 = math.clamp(v8, 0, 1)
    local v10 = Color3.fromHSV(v5, v6, v9)
    local v11 = p2:FindFirstChild("SENSOR", true)
    p2:FindFirstChild("UIScale", true)
    local u12 = u1:Create(v3, TweenInfo.new(0.25), {
        ["ImageColor3"] = v10
    })
    local u13 = u1:Create(v3, TweenInfo.new(0.25), {
        ["ImageColor3"] = v4
    })
    v11.MouseEnter:Connect(function() --[[Anonymous function at line 29]]
        --[[
        Upvalues:
            [1] = u12
        --]]
        u12:Play()
    end)
    v11.MouseLeave:Connect(function() --[[Anonymous function at line 33]]
        --[[
        Upvalues:
            [1] = u13
        --]]
        u13:Play()
    end)
    return v11
end