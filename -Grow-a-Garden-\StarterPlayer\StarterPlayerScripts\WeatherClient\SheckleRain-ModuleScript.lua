-- Full Path: -Grow-a-Garden-\StarterPlayer\StarterPlayerScripts\WeatherClient\SheckleRain-ModuleScript.lua
local v1 = {}
local u2 = game.ReplicatedStorage.SheckleParticle
local u3 = Random.new()
local u4 = workspace.CurrentCamera
local u5 = RaycastParams.new()
u5.FilterDescendantsInstances = { workspace.Terrain, workspace }
u5.FilterType = Enum.RaycastFilterType.Include
local u6 = {}
local u7 = false
local function u11(p8) --[[Anonymous function at line 19]]
    --[[
    Upvalues:
        [1] = u2
        [2] = u6
    --]]
    local v9 = {
        ["particle"] = u2:Clone(),
        ["position"] = p8,
        ["spawnTime"] = os.clock(),
        ["visible"] = false,
        ["lastupdate"] = 0
    }
    game.TweenService:Create(v9.particle, TweenInfo.new(0.7), {
        ["Transparency"] = 0.2
    }):Play()
    local v10 = u6
    table.insert(v10, v9)
end
local u12 = game.ReplicatedStorage.SheckleRainEmitter:Clone()
u12.Parent = workspace.WeatherVisuals
local u13 = script.Sky
local u14 = require(game.ReplicatedStorage.Modules.SkyboxManager)
u14.AddSkybox(u13)
local u15 = game.Lighting.ColorCorrection:Clone()
u15.Name = script.Name
u15.Parent = game.Lighting
local function u23() --[[Anonymous function at line 76]]
    --[[
    Upvalues:
        [1] = u7
        [2] = u12
        [3] = u14
        [4] = u13
        [5] = u15
        [6] = u6
        [7] = u3
        [8] = u4
        [9] = u11
    --]]
    u7 = true
    u12.ParticleEmitter.Enabled = true
    u14.UpdateSkybox(u13, 2)
    game.TweenService:Create(game.Lighting, TweenInfo.new(3), {
        ["Ambient"] = Color3.fromRGB(255, 206, 138),
        ["ExposureCompensation"] = 0.4,
        ["Brightness"] = 0.7
    }):Play()
    game.TweenService:Create(u15, TweenInfo.new(3), {
        ["Brightness"] = 0.1,
        ["TintColor"] = Color3.fromRGB(252, 255, 193)
    }):Play()
    game.Workspace.Terrain.Clouds.Enabled = true
    game.TweenService:Create(game.Workspace.Terrain.Clouds, TweenInfo.new(3), {
        ["Cover"] = 0.624,
        ["Density"] = 1,
        ["Color"] = Color3.fromRGB(255, 245, 167)
    }):Play()
    task.spawn(function() --[[Anonymous function at line 101]]
        --[[
        Upvalues:
            [1] = u7
            [2] = u6
            [3] = u3
            [4] = u4
            [5] = u11
            [6] = u14
            [7] = u13
            [8] = u12
            [9] = u15
        --]]
        while u7 do
            task.wait(0.05)
            if #u6 <= 400 then
                for _ = 1, 3 do
                    local v16 = u3:NextNumber(10, 180)
                    local v17 = 2 * v16
                    local v18 = u4.FieldOfView / 2
                    local v19 = math.rad(v18)
                    local v20 = v17 * math.tan(v19)
                    local v21 = v20 * (u4.ViewportSize.X / u4.ViewportSize.Y)
                    local v22 = u4.CFrame * CFrame.new(u3:NextNumber(-v21 / 2, v21 / 2), u3:NextNumber(-v20 / 2, v20 / 2 + 20), -v16)
                    if not workspace:Raycast(v22.Position, Vector3.new(0, 150, 0)) then
                        u11(v22.Position)
                    end
                end
            end
        end
        u14.UpdateSkybox(u13, 0)
        u12.ParticleEmitter.Enabled = false
        game.TweenService:Create(u15, TweenInfo.new(3), {
            ["Brightness"] = 0,
            ["TintColor"] = Color3.fromRGB(255, 255, 255)
        }):Play()
        game.TweenService:Create(game.Lighting, TweenInfo.new(3), {
            ["Ambient"] = game.Lighting:GetAttribute("DefaultAmbient"),
            ["ExposureCompensation"] = game.Lighting:GetAttribute("DefaultExposure"),
            ["Brightness"] = game.Lighting:GetAttribute("DefaultBrightness")
        }):Play()
        game.TweenService:Create(game.Workspace.Terrain.Clouds, TweenInfo.new(3), {
            ["Cover"] = 0,
            ["Density"] = 0
        }):Play()
        task.delay(3, function() --[[Anonymous function at line 171]]
            game.Workspace.Terrain.Clouds.Enabled = false
        end)
    end)
end
workspace:GetAttributeChangedSignal("SheckleRain"):Connect(function() --[[Anonymous function at line 182]]
    --[[
    Upvalues:
        [1] = u23
        [2] = u7
    --]]
    if workspace:GetAttribute("SheckleRain") then
        u23()
    else
        u7 = false
    end
end)
local u24 = game.ReplicatedStorage.SheckleSplash:Clone()
u24.Parent = workspace.WeatherVisuals
task.spawn(function() --[[Anonymous function at line 194]]
    --[[
    Upvalues:
        [1] = u6
        [2] = u5
        [3] = u4
        [4] = u24
        [5] = u3
    --]]
    while true do
        local v25 = game:GetService("RunService").RenderStepped:Wait()
        local v26 = os.clock()
        local v27 = v25 * 3
        local v28 = v25 * 5
        local v29 = math.min(1, v28)
        local v30 = {}
        local v31 = {}
        for v32, v33 in u6 do
            local _ = v26 - v33.spawnTime + v29
            local v34 = v33.position
            local v35 = -5 * v29
            local v36 = Vector3.new(0, v35, 0)
            local v37 = workspace:Spherecast(v34, 0.15, v36, u5)
            local v38
            if v37 then
                v34 = v37.Position
                v38 = true
            else
                v33.position = v34 + v36
                v38 = nil
            end
            local v39, v40 = u4:WorldToScreenPoint(v34)
            local v41 = v33.visible
            local v42 = (u4.CFrame.Position - v34).Magnitude / 120
            local v43 = v42 * v42
            local v44 = 1 / math.random(60, 120)
            local v45 = v27 * v43 + 0.016666666666666666
            if v42 > 1.5 then
                v33.particle:Destroy()
                table.remove(u6, v32)
            elseif v26 - v33.lastupdate + v44 > v45 then
                v33.lastupdate = v26
                if v40 and v39.Z < 200 then
                    v33.visible = true
                    local v46 = v33.particle
                    table.insert(v30, v46)
                    local v47 = CFrame.new(v34, u4.CFrame.Position) * CFrame.Angles(1.5707963267948966, 0, 0)
                    table.insert(v31, v47)
                else
                    v33.visible = false
                end
                if v33.visible ~= v41 then
                    if v33.visible then
                        v33.particle.Parent = workspace.WeatherVisuals
                    else
                        v33.particle.Parent = nil
                    end
                end
                if v38 then
                    v33.particle:Destroy()
                    u24.CFrame = CFrame.new(v34)
                    u24.Attachment.ParticleEmitter:Emit(u3:NextInteger(1, 2))
                    table.remove(u6, v32)
                elseif os.clock() - v33.spawnTime > 7 then
                    game.Debris:AddItem(v33.particle, 0.3)
                    game.TweenService:Create(v33.particle, TweenInfo.new(0.3), {
                        ["Transparency"] = 1
                    }):Play()
                    table.remove(u6, v32)
                end
            end
        end
        debug.profilebegin("Weather_" .. script.Name)
        workspace:BulkMoveTo(v30, v31, Enum.BulkMoveMode.FireCFrameChanged)
        debug.profileend()
    end
end)
if workspace:GetAttribute("SheckleRain") then
    task.defer(u23)
else
    u7 = false
end
return v1