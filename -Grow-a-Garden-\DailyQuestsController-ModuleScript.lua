-- Full Path: -Grow-a-Garden-\\DailyQuestsController-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
local v2 = game:GetService("Players")
require(v1.Modules.Trove)
local u3 = require(v1.Modules.Remotes)
local u4 = require(v1.Modules.NumberUtil)
local u5 = require(v1.Modules.DataService)
local u6 = require(v1.Modules.GuiController)
local u7 = require(v1.Modules.QuestsController)
local u8 = v2.LocalPlayer
local u9 = u8.PlayerGui:WaitForChild("DailyQuests_UI")
local u10 = u9:WaitForChild("Frame"):WaitForChild("ScrollingFrame")
local u11 = u10:WaitForChild("ClaimHolder")
local u12 = u11:WaitForChild("Frame"):WaitForChild("Claim")
local u13 = u11:WaitForChild("Frame"):WaitFor<PERSON>hild("Claim2")
local u14 = u11:WaitForChild("Frame"):WaitForChild("Complete")
local u15 = u11:WaitForChild("Frame"):WaitForChild("Claimed")
local u16 = u10:WaitForChild("Holder")
u16.Parent = script
local v17 = {}
local u18 = false
function v17.Start(_) --[[Anonymous function at line 50]]
    --[[
    Upvalues:
        [1] = u6
        [2] = u9
        [3] = u5
        [4] = u7
        [5] = u16
        [6] = u10
        [7] = u15
        [8] = u12
        [9] = u13
        [10] = u14
        [11] = u11
        [12] = u18
        [13] = u8
        [14] = u3
        [15] = u4
    --]]
    u6:UsePopupAnims(u9)
    u9.Frame.Frame.ExitButton.Activated:Connect(function() --[[Anonymous function at line 53]]
        --[[
        Upvalues:
            [1] = u6
            [2] = u9
        --]]
        u6:Close(u9)
    end)
    local u19 = {}
    local function v45() --[[Anonymous function at line 68]]
        --[[
        Upvalues:
            [1] = u5
            [2] = u19
            [3] = u7
            [4] = u16
            [5] = u10
            [6] = u15
            [7] = u12
            [8] = u13
            [9] = u14
            [10] = u11
            [11] = u18
            [12] = u8
        --]]
        local v20 = u5:GetData()
        if not v20 then
            return
        end
        if not (v20.DailyQuests and v20.DailyQuests.ContainerId) then
            for v21 in u19 do
                local v22 = u19[v21]
                if v22 then
                    v22:Destroy()
                    u19[v21] = nil
                end
            end
            table.clear(u19)
            return
        end
        local v23 = u7:GetContainerFromId(v20.DailyQuests.ContainerId)
        if not v23 then
            return
        end
        local v24 = true
        for v25, v26 in v23.Quests do
            if not v26.Completed then
                v24 = false
            end
            local v27 = u7:GetQuest(v26.Type)
            if v27 then
                local v28 = u19[v26.Id]
                if not v28 then
                    v28 = u16:Clone()
                    v28.LayoutOrder = v25 + 1
                    v28.Parent = u10
                    u19[v26.Id] = v28
                end
                local v29 = v27:Display(v26.Progress, v26.Target, v26.Arguments)
                v28.QuestFrame.Title.Text = v29.Title
                local v30 = v28.QuestFrame.Frame.Fill
                local v31 = UDim2.fromScale
                local v32 = v26.Progress / v26.Target
                v30.Size = v31(math.clamp(v32, 0, 1), 1)
                v28.QuestFrame.Frame.Progress.Text = v26.Progress >= v26.Target and "Completed!" or v29.Bar
            end
        end
        for v33 in u19 do
            local v34 = false
            for _, v35 in v23.Quests do
                if v35.Id == v33 then
                    v34 = true
                    break
                end
            end
            if not v34 then
                local v36 = u19[v33]
                if v36 then
                    v36:Destroy()
                    u19[v33] = nil
                end
            end
        end
        u15.Visible = v23.Redeemed
        local v37 = u12
        local v38
        if v24 then
            v38 = not v23.Redeemed
        else
            v38 = v24
        end
        v37.Visible = v38
        local v39 = u13
        local v40 = not v24
        if v40 then
            v40 = not v23.Redeemed
        end
        v39.Visible = v40
        local v41 = u14
        local v42 = not v24
        if v42 then
            v42 = not v23.Redeemed
        end
        v41.Visible = v42
        local v43 = v23.Rewards[1]
        local v44
        if v43 then
            v44 = u7:GetRewardInfo(v43.Type)
        else
            v44 = nil
        end
        if v44 then
            u11:WaitForChild("Frame"):WaitForChild("Title").Text = v44:Display(v43)
        end
        if v24 and not (v23.Redeemed or (u18 or u8:GetAttribute("FirstTimePlayer"))) then
            u18 = true
            require(game.ReplicatedStorage.Modules.Notification):CreateNotification("All your quests are completed you can now claim your prize!")
        elseif not v24 then
            u18 = false
        end
    end
    u12.Activated:Connect(function() --[[Anonymous function at line 162]]
        --[[
        Upvalues:
            [1] = u3
        --]]
        u3.DailyQuests.Claim.send()
    end)
    task.spawn(v45)
    local v46 = u5:GetPathSignal("DailyQuests/@")
    assert(v46, "Failed to listen for Daily Quests"):Connect(v45)
    local v47 = u5:GetPathSignal("QuestContainers/@")
    assert(v47, "Failed to listen for Quest Containers"):Connect(v45)
    task.spawn(function() --[[Anonymous function at line 170]]
        --[[
        Upvalues:
            [1] = u9
            [2] = u4
        --]]
        while true do
            local v48 = workspace:GetServerTimeNow()
            local v49 = ((v48 + 14400) // 86400 + 1) * 86400 - 14400
            u9.Frame.Resets.Text = ("Resets in %*"):format((u4.compactFormat(v49 - v48)))
            task.wait(1)
        end
    end)
end
task.spawn(v17.Start, v17)
return v17