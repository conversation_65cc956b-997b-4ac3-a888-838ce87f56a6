-- Full Path: -Grow-a-Garden-\\DefaultData-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
require(v1.Data.QuestData.Types)
local v2 = require(v1.Data.SettingsRegistry)
local v3 = {
    ["Backpack"] = {},
    ["PlantedObjects"] = {},
    ["SavedFruit"] = {},
    ["LastSaveTime"] = 0,
    ["FirstTimeUser"] = true,
    ["Migrated"] = {},
    ["Sheckles"] = 20,
    ["LevelData"] = {},
    ["ClaimedCodes"] = {
        ["FREE_PANDA_PET_UPDATE"] = false
    },
    ["SeedStock"] = {
        ["Seed"] = 0,
        ["Stocks"] = {},
        ["ForcedSeed"] = nil,
        ["ForcedSeedEndTimestamp"] = nil
    },
    ["GearStock"] = {
        ["Gear"] = 0,
        ["Stocks"] = {},
        ["ForcedGear"] = nil,
        ["ForcedGearEndTimestamp"] = nil
    },
    ["PetEggStock"] = {
        ["Egg"] = 0,
        ["Stocks"] = {},
        ["ForcedEgg"] = nil,
        ["ForcedEggEndTimestamp"] = nil
    },
    ["CosmeticStock"] = {
        ["CosmeticSeed"] = 0,
        ["CrateStocks"] = {},
        ["ItemStocks"] = {},
        ["ForcedCosmetic"] = nil,
        ["ForcedCosmeticEndTimestamp"] = nil
    },
    ["EasterStock"] = {
        ["Easter"] = 0,
        ["Stocks"] = {},
        ["ForcedEaster"] = nil,
        ["ForcedEasterEndTimestamp"] = nil,
        ["AfterUpdateRegeneration"] = nil
    },
    ["EventShopStock"] = {
        ["ShopSeed"] = 0,
        ["Stocks"] = {},
        ["ForcedEventShop"] = nil,
        ["ForcedEventShopEndTimestamp"] = nil,
        ["AfterUpdateRegeneration"] = nil
    },
    ["NightEventShopStock"] = {
        ["ShopSeed"] = 0,
        ["Stocks"] = {},
        ["ForcedEventShop"] = nil,
        ["ForcedEventShopEndTimestamp"] = nil
    },
    ["DailyQuests"] = nil,
    ["SavedObjects"] = {},
    ["InventoryData"] = {},
    ["QuestContainers"] = {},
    ["BadgeData"] = {}
}
local v4 = {
    ["MutableStats"] = {
        ["MaxEquippedPets"] = 3,
        ["MaxPetsInInventory"] = 60,
        ["MaxEggsInFarm"] = 3
    },
    ["PurchasedEquipSlots"] = 0,
    ["PurchasedEggSlots"] = 0,
    ["EquippedPets"] = {},
    ["PetInventory"] = {
        ["Counter"] = 0,
        ["Data"] = {}
    }
}
v3.PetsData = v4
v3.NightQuestData = {
    ["Experience"] = 0,
    ["ClaimedRewardIndices"] = {}
}
local v5 = {
    ["RequiredPlantInfo"] = {
        ["RequiredPlant"] = nil,
        ["RequiredPlantSize"] = nil,
        ["RequiredPlantVariant"] = nil,
        ["RequiredPlantMutation"] = nil
    },
    ["SeedPackGiverDiscovered"] = false,
    ["Progression"] = 1,
    ["ProgressionSeedPacks"] = 0
}
v3.SeedPackGiverInfo = v5
v3.RotatingEasterShop = {
    ["Unlocked"] = false,
    ["TotalWeightSubmitted"] = 0,
    ["LastRegenerationTime"] = 0,
    ["GeneratedItems"] = {}
}
v3.InfinitePack = {
    ["Depth"] = 0,
    ["Day"] = 0
}
v3.StarterPack = {
    ["Started"] = false,
    ["ContainerId"] = nil,
    ["Timer"] = nil,
    ["ShowTimer"] = nil
}
v3.TotalRobuxSpent = 0
v3.Transactions = {}
local v6 = {
    ["Inventory"] = {},
    ["History"] = {
        ["Sent"] = {},
        ["Received"] = {}
    }
}
v3.Gifts = v6
v3.Settings = {
    ["Textures"] = v2.Textures.SettingsData.DefaultValue,
    ["ShowDetails"] = v2.ShowDetails.SettingsData.DefaultValue,
    ["VisualEffects"] = v2.VisualEffects.SettingsData.DefaultValue,
    ["GearShopButton"] = v2.GearShopButton.SettingsData.DefaultValue,
    ["PetShopButton"] = v2.PetShopButton.SettingsData.DefaultValue,
    ["RecieveGifts"] = v2.RecieveGifts.SettingsData.DefaultValue,
    ["PetUpdateRate"] = v2.PetUpdateRate.SettingsData.DefaultValue,
    ["PlantableCollisions"] = v2.PlantableCollisions.SettingsData.DefaultValue
}
v3.DefaultSettingsAlreadySet = {}
local v7 = {
    ["Inventory"] = {},
    ["MutableStats"] = {
        ["MaxEquippedCosmetics"] = 0,
        ["MaxCosmeticsInInventory"] = 0,
        ["MaxCratesInFarm"] = 0
    },
    ["Equipped"] = {}
}
v3.CosmeticData = v7
v3.ExpansionsData = {
    ["Unlocked"] = {},
    ["ExpansionTimers"] = {},
    ["CanSeeExpansions"] = false
}
v3.PlayerFlags = {
    ["ResetCosmetic"] = false,
    ["ResetStats"] = false
}
return v3