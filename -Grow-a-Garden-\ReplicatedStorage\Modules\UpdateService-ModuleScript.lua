-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\UpdateService-ModuleScript.lua
local u1 = game:GetService("ReplicatedFirst")
game:GetService("RunService")
local u2 = game:GetService("Players").LocalPlayer
local u3 = {}
local u4 = {}
local u5 = false
local u6 = workspace.Interaction.UpdateItems:GetChildren()
local v9 = {
    ["IsHiddenFromUpdate"] = function(_, p7) --[[Function name: IsHiddenFromUpdate, line 24]]
        --[[
        Upvalues:
            [1] = u3
            [2] = u1
        --]]
        for _, v8 in u3 do
            if v8 == p7 and workspace:GetServerTimeNow() <= u1.GlobalUpdateTime.Value then
                return true
            end
        end
        return false
    end,
    ["GetUpdateTime"] = function(_) --[[Function name: GetUpdateTime, line 34]]
        --[[
        Upvalues:
            [1] = u1
        --]]
        return u1.GlobalUpdateTime.Value
    end,
    ["GetRemainingTimeUntilUpdate"] = function(_) --[[Function name: GetRemainingTimeUntilUpdate, line 38]]
        --[[
        Upvalues:
            [1] = u1
        --]]
        return u1.GlobalUpdateTime.Value - workspace:GetServerTimeNow()
    end,
    ["IsUpdateDone"] = function(_) --[[Function name: IsUpdateDone, line 42]]
        --[[
        Upvalues:
            [1] = u5
        --]]
        return u5
    end
}
task.spawn(function() --[[Anonymous function at line 55]]
    --[[
    Upvalues:
        [1] = u1
        [2] = u4
        [3] = u5
        [4] = u6
        [5] = u2
    --]]
    if workspace:GetServerTimeNow() >= u1.GlobalUpdateTime.Value then
        for _, v10 in u4 do
            v10:Destroy()
        end
        u5 = true
    else
        for _, v11 in u6 do
            v11.Parent = script
        end
        while task.wait(1) and workspace:GetServerTimeNow() < u1.GlobalUpdateTime.Value do

        end
        local v12
        if u2 then
            v12 = u2:GetAttribute("FirstTimePlayer")
        else
            v12 = nil
        end
        for _, v13 in u6 do
            if not v12 then
                v13.Parent = workspace.Interaction.UpdateItems
            end
        end
        for _, v14 in u4 do
            v14:Destroy()
        end
        u5 = true
    end
end)
return v9