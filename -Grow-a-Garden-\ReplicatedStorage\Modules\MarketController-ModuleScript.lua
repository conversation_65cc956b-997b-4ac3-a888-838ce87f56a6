-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\MarketController-ModuleScript.lua
local u1 = game:GetService("MarketplaceService")
local v2 = game:GetService("ReplicatedStorage")
game:GetService("SoundService")
local v3 = game:GetService("Players")
local u4 = require(v2.Modules.Notification)
local u5 = require(v2.Modules.RetryPcall)
local u6 = require(v2.Modules.Observers)
local u7 = require(v2.Comma_Module)
local u8 = require(v2.Modules.Remotes)
local v9 = require(v2.Modules.Signal)
require(v2.Modules.IsDev)
local u10 = v3.LocalPlayer
local u11 = u10.PlayerGui
local u12 = {
    ["PromptProductPurchaseInitiated"] = v9.new()
}
local u13 = {}
local function u22(p14) --[[Anonymous function at line 31]]
    --[[
    Upvalues:
        [1] = u12
        [2] = u7
    --]]
    if p14:IsA("ProximityPrompt") then
        p14.ObjectText = "???"
    else
        p14.Text = "???"
    end
    local v15 = p14:GetAttribute("ProductId")
    if v15 == nil then
        return
    else
        local v16, v17 = u12:GetProductInfo(v15, Enum.InfoType.Product)
        if v16 then
            local v18 = p14:GetAttribute("Format") or ":robux::value:"
            local v19 = string.gsub(v18, ":robux:", "\238\128\130")
            local v20 = string.gsub(v19, ":value:", u7.Comma(v17.PriceInRobux))
            local v21 = string.gsub(v20, ":rawvalue:", v17.PriceInRobux)
            if p14:IsA("ProximityPrompt") then
                p14.ObjectText = v21
            else
                p14.Text = v21
            end
        elseif p14:IsA("ProximityPrompt") then
            p14.ObjectText = "Failed to load"
        else
            p14.Text = "Failed to load"
        end
    end
end
local function u27(p23) --[[Anonymous function at line 65]]
    --[[
    Upvalues:
        [1] = u13
        [2] = u22
    --]]
    for _, v24 in u13 do
        if v24.instance == p23 then
            return
        end
    end
    local v25
    if p23:IsA("TextLabel") or p23:IsA("TextButton") then
        v25 = p23:FindFirstAncestorWhichIsA("LayerCollector")
    else
        v25 = nil
    end
    if v25 and not v25.Enabled then
        local v26 = u13
        table.insert(v26, {
            ["screenGui"] = v25,
            ["instance"] = p23
        })
    else
        task.spawn(u22, p23)
    end
end
local function u30(p28) --[[Anonymous function at line 86]]
    --[[
    Upvalues:
        [1] = u13
    --]]
    for v29 = #u13, 1, -1 do
        if u13[v29].instance == p28 then
            table.remove(u13, v29)
        end
    end
end
local u31 = {}
local u32 = 0
function u12.GetProductInfo(_, u33, u34) --[[Anonymous function at line 108]]
    --[[
    Upvalues:
        [1] = u31
        [2] = u32
        [3] = u5
        [4] = u1
    --]]
    while true do
        local v35 = u31[tostring(u33)]
        if not (v35 and v35.fetching) then
            break
        end
        task.wait(1)
    end
    local v36 = u31[tostring(u33)]
    if v36 and (not v36.fetching and v36.success) then
        return true, v36.data
    else
        u32 = u32 + 1
        task.delay(60, function() --[[Anonymous function at line 124]]
            --[[
            Upvalues:
                [1] = u32
            --]]
            u32 = u32 - 1
        end)
        while u32 > 36 do
            task.wait(1)
        end
        u31[tostring(u33)] = {
            ["fetching"] = true,
            ["timestamp"] = workspace:GetServerTimeNow()
        }
        local v37, v38 = u5(15, 30, function() --[[Anonymous function at line 139]]
            --[[
            Upvalues:
                [1] = u1
                [2] = u33
                [3] = u34
            --]]
            return u1:GetProductInfo(u33, u34)
        end)
        if v37 and v38 ~= nil then
            u31[tostring(u33)] = {
                ["fetching"] = false,
                ["timestamp"] = workspace:GetServerTimeNow(),
                ["success"] = true,
                ["data"] = v38
            }
            return true, v38
        else
            u31[tostring(u33)] = nil
            return false, nil
        end
    end
end
function u12.PromptPurchase(_, p39, p40) --[[Anonymous function at line 159]]
    --[[
    Upvalues:
        [1] = u4
        [2] = u8
        [3] = u12
        [4] = u10
        [5] = u1
    --]]
    if p40 == Enum.InfoType.Product then
        if workspace:GetAttribute("AllowFakePurchases") then
            u4:CreateNotification((("Invoking fake purchase for \"%*\""):format(p39)))
            task.wait(0.5)
            u8.Market.FakePurchase.send(p39)
            task.wait(1)
        else
            u12.PromptProductPurchaseInitiated:Fire(u10, p39)
            u1:PromptProductPurchase(u10, p39)
        end
    elseif p40 == Enum.InfoType.GamePass then
        u1:PromptGamePassPurchase(u10, p39)
        return
    elseif p40 == Enum.InfoType.Asset then
        u1:PromptPurchase(u10, p39)
    elseif p40 == Enum.InfoType.Bundle then
        u1:PromptBundlePurchase(u10, p39)
    end
end
function u12.RemovePriceLabel(_, p41) --[[Anonymous function at line 179]]
    p41:RemoveTag("PriceLabel")
end
function u12.SetPriceLabel(_, p42, p43, _) --[[Anonymous function at line 183]]
    --[[
    Upvalues:
        [1] = u27
    --]]
    p42:SetAttribute("Format", ":robux::value:")
    p42:SetAttribute("ProductId", p43)
    p42:AddTag("PriceLabel")
    u27(p42)
end
function u12.Start(_) --[[Anonymous function at line 190]]
    --[[
    Upvalues:
        [1] = u8
        [2] = u12
        [3] = u6
        [4] = u27
        [5] = u30
        [6] = u13
        [7] = u22
        [8] = u11
    --]]
    u8.Market.PromptPurchase.listen(function(p44) --[[Anonymous function at line 191]]
        --[[
        Upvalues:
            [1] = u12
        --]]
        local v45 = nil
        if p44.type == 1 then
            v45 = Enum.InfoType.Product
        elseif p44.type == 2 then
            v45 = Enum.InfoType.GamePass
        elseif p44.type == 3 then
            v45 = Enum.InfoType.Asset
        elseif p44.type == 4 then
            v45 = Enum.InfoType.Bundle
        end
        if v45 then
            u12:PromptPurchase(p44.id, v45)
        end
    end)
    u6.observeTag("PriceLabel", function(u46) --[[Anonymous function at line 210]]
        --[[
        Upvalues:
            [1] = u6
            [2] = u27
            [3] = u30
        --]]
        if u46:IsA("TextButton") or (u46:IsA("TextLabel") or u46:IsA("ProximityPrompt")) then
            return u6.observeAttribute(u46, "ProductId", function(_) --[[Anonymous function at line 215]]
                --[[
                Upvalues:
                    [1] = u27
                    [2] = u46
                    [3] = u30
                --]]
                u27(u46)
                return function() --[[Anonymous function at line 218]]
                    --[[
                    Upvalues:
                        [1] = u30
                        [2] = u46
                    --]]
                    u30(u46)
                end
            end)
        else
            return nil
        end
    end)
    local function v50(u47) --[[Anonymous function at line 224]]
        --[[
        Upvalues:
            [1] = u13
            [2] = u22
        --]]
        if u47:IsA("LayerCollector") then
            u47:GetPropertyChangedSignal("Enabled"):Connect(function() --[[Anonymous function at line 229]]
                --[[
                Upvalues:
                    [1] = u47
                    [2] = u13
                    [3] = u22
                --]]
                if u47.Enabled then
                    for v48 = #u13, 1, -1 do
                        local v49 = u13[v48]
                        if v49.screenGui == u47 then
                            task.spawn(u22, v49.instance)
                        end
                    end
                end
            end)
        end
    end
    u11.ChildAdded:Connect(v50)
    for _, u51 in u11:GetChildren() do
        if u51:IsA("LayerCollector") then
            u51:GetPropertyChangedSignal("Enabled"):Connect(function() --[[Anonymous function at line 229]]
                --[[
                Upvalues:
                    [1] = u51
                    [2] = u13
                    [3] = u22
                --]]
                if u51.Enabled then
                    for v52 = #u13, 1, -1 do
                        local v53 = u13[v52]
                        if v53.screenGui == u51 then
                            task.spawn(u22, v53.instance)
                        end
                    end
                end
            end)
        end
    end
end
task.spawn(u12.Start, u12)
return u12