-- Full Path: -Grow-a-Garden-\\SeedPackController-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
local u2 = game:GetService("TweenService")
local u3 = game:GetService("SoundService")
local u4 = game:GetService("RunService")
local v5 = game:GetService("Players")
local u6 = require(v1.Modules.WeightRandom)
local u7 = require(v1.Data.SeedPackData)
local u8 = require(v1.Item_Module)
local u9 = require(v1.Data.SeedData)
local u10 = require(v1.Modules.Remotes)
local u11 = require(v1.Modules.FastTween)
local u12 = require(v1.Modules.Shake)
local u13 = v5.LocalPlayer.PlayerGui:WaitForChild("RollCrate_UI")
local u14 = u13.Frame
local u15 = u14.Rolled
local u16 = u14.Section.Spinner
local u17 = table.create(150)
local u18 = false
local v19 = {}
local u20 = false
local u21 = false
local function u24(p22, p23) --[[Anonymous function at line 42]]
    --[[
    Upvalues:
        [1] = u14
        [2] = u11
    --]]
    if p22 then
        if p23 then
            u14.Position = UDim2.fromScale(0.5, 0.5)
        else
            u14.Position = UDim2.fromScale(0.5, 1.5)
            u11(u14, TweenInfo.new(0.5, Enum.EasingStyle.Quint, Enum.EasingDirection.Out), {
                ["Position"] = UDim2.fromScale(0.5, 0.5)
            })
        end
    elseif p23 then
        u14.Position = UDim2.fromScale(0.5, 1.5)
    else
        u14.Position = UDim2.fromScale(0.5, 0.5)
        u11(u14, TweenInfo.new(0.5, Enum.EasingStyle.Quint, Enum.EasingDirection.In), {
            ["Position"] = UDim2.fromScale(0.5, 1.5)
        })
    end
end
local function u29(p25, p26) --[[Anonymous function at line 64]]
    --[[
    Upvalues:
        [1] = u15
        [2] = u11
    --]]
    if p25 then
        if p26 then
            u15.ImageTransparency = 0
            u15.Label.TextTransparency = 0
            u15.Label.UIStroke.Transparency = 0
        else
            local v27 = TweenInfo.new(0.5, Enum.EasingStyle.Quint, Enum.EasingDirection.Out)
            u15.ImageTransparency = 1
            u15.Label.TextTransparency = 1
            u15.Label.UIStroke.Transparency = 1
            u11(u15, v27, {
                ["ImageTransparency"] = 0
            })
            u11(u15.Label, v27, {
                ["TextTransparency"] = 0
            })
            u11(u15.Label.UIStroke, v27, {
                ["Transparency"] = 0
            })
        end
    elseif p26 then
        u15.ImageTransparency = 1
        u15.Label.TextTransparency = 1
        u15.Label.UIStroke.Transparency = 1
    else
        local v28 = TweenInfo.new(0.5, Enum.EasingStyle.Quint, Enum.EasingDirection.Out)
        u15.ImageTransparency = 0
        u15.Label.TextTransparency = 0
        u15.Label.UIStroke.Transparency = 0
        u11(u15, v28, {
            ["ImageTransparency"] = 1
        })
        u11(u15.Label, v28, {
            ["TextTransparency"] = 1
        })
        u11(u15.Label.UIStroke, v28, {
            ["Transparency"] = 1
        })
    end
end
local function u77(p30) --[[Anonymous function at line 116]]
    --[[
    Upvalues:
        [1] = u21
        [2] = u13
        [3] = u24
        [4] = u14
        [5] = u7
        [6] = u15
        [7] = u6
        [8] = u17
        [9] = u9
        [10] = u8
        [11] = u3
        [12] = u16
        [13] = u2
        [14] = u11
        [15] = u12
        [16] = u4
        [17] = u29
    --]]
    local v31 = u21
    u13.Enabled = true
    u24(true, v31)
    u14.Visible = true
    local v32 = Random.new()
    local v33 = u7.Packs[p30.seedPackType]
    local v34 = (p30.seedPackType == "RainbowSeedSackPremium" or p30.seedPackType == "RainbowSeedSackBasic") and "Rainbow" or (string.find(p30.seedPackType, "Night") and "Night" or "Normal")
    local v35 = v33.Items
    local v36 = v35[p30.resultIndex]
    local v37 = 140
    local v38 = false
    for _, v39 in u14.Decorations:GetChildren() do
        v39.Visible = v39.Name == v34
    end
    for _, v40 in u15.Decorations:GetChildren() do
        v40.Visible = v40.Name == v34
    end
    local v41 = v36.Type == "Pack" and (v36.RewardId == "RainbowSeedSackPremium" or v36.RewardId == "RainbowSeedSackBasic") and true or v38
    local v42 = nil
    local v43 = {}
    for v44 = 1, 150 do
        local v45
        if v44 == v37 then
            v45 = v36
        else
            v45 = u6.array(v35, v32)
        end
        if v45 then
            local v46 = u17[v44]
            local v47 = v45.Type == "Pack" and (v45.RewardId == "RainbowSeedSackPremium" or v45.RewardId == "RainbowSeedSackBasic") and "Custom_RainbowCrate" or v34
            local v48 = nil
            for _, v49 in v46:GetChildren() do
                local v50 = v49.Name == v47
                v49.Visible = v50
                if v50 then
                    v48 = v49
                end
            end
            if v48 then
                if v44 == v37 then
                    v42 = v48
                end
                if (v45.Type == "Seed" or v45.Type == "RainbowSeed") and u9[v45.RewardId] then
                    local v51 = u9[v45.RewardId]
                    local v52 = u8.Return_Rarity_Data(v51.SeedRarity)
                    if v52 then
                        v48.Rarity.Text = v52[1]
                        v48.Rarity.TextColor3 = v52[2]
                    end
                    v48.Vector.Visible = true
                    v48.Vector.Image = v51.Asset
                elseif v45.Type == "Pack" then
                    v48.Vector.Image = v45.Icon or ""
                    v48.Vector.Visible = true
                else
                    warn((("Seed data not found for %*"):format(v45.RewardId)))
                end
                v48.Label.Text = u7:GetTextDisplayForItem(v45)
            end
        else
            v37 = v37 - 1
        end
    end
    local u53 = Instance.new("NumberValue")
    u53.Value = 5
    local u54 = nil
    local u55 = nil
    local function u60() --[[Anonymous function at line 218]]
        --[[
        Upvalues:
            [1] = u53
            [2] = u54
            [3] = u55
            [4] = u3
            [5] = u21
            [6] = u16
        --]]
        local v56 = u53.Value
        local v57 = v56 // 1
        if v57 ~= u54 then
            if u54 and (not u55 or os.clock() - u55 > 0.05) then
                local v58 = u3
                local v59
                if u21 then
                    v59 = u3["Seed Pack"].RainbowTick
                else
                    v59 = u3["Seed Pack"].NormalTick
                end
                v58:PlayLocalSound(v59)
                u55 = os.clock()
            end
            u54 = v57
        end
        u16.Position = UDim2.new(0.5, -v56 * u16.AbsoluteSize.Y, 0.5, 0)
    end
    local v61 = u16:GetPropertyChangedSignal("AbsoluteSize"):Connect(u60)
    local v62 = u53.Changed:Connect(u60)
    task.spawn(u60)
    local u63 = v37 - 0.5 + (math.random() - 0.5) * 0.5
    local v64 = u2:Create(u53, TweenInfo.new(5, Enum.EasingStyle.Quint, Enum.EasingDirection.Out), {
        ["Value"] = u63
    })
    v64:Play()
    local u65 = false
    local u66 = coroutine.running()
    local function v67() --[[Anonymous function at line 248]]
        --[[
        Upvalues:
            [1] = u65
            [2] = u66
        --]]
        if not u65 then
            u65 = true
            if coroutine.status(u66) == "suspended" then
                task.spawn(u66)
            end
        end
    end
    local v68 = u14.Skip.Activated:Once(v67)
    local v69 = v64.Completed:Once(v67)
    if not u65 then
        coroutine.yield()
    end
    v68:Disconnect()
    v69:Disconnect()
    v64:Cancel()
    v64:Destroy()
    v62:Disconnect()
    v61:Disconnect()
    task.defer(function() --[[Anonymous function at line 276]]
        --[[
        Upvalues:
            [1] = u53
            [2] = u63
            [3] = u60
        --]]
        u53.Value = u63
        u60()
        u53:Destroy()
    end)
    if v41 and v42 then
        u3["Seed Pack"].RainbowReward:Play()
        local v70 = u11(v42.Vector, TweenInfo.new(0.1, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut, -1, true), {
            ["ImageColor3"] = Color3.new(1, 1, 1)
        })
        local v71 = u11(v42, TweenInfo.new(0.1, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut, -1, true), {
            ["ImageColor3"] = Color3.new(1, 1, 1)
        })
        task.wait(0.3)
        local u72 = u12.new()
        u72.FadeInTime = 0.5
        u72.FadeOutTime = 1.5
        u72.Frequency = 0.08
        u72.Amplitude = 15
        u72.Sustain = true
        u72.PositionInfluence = Vector3.new(1, 1, 0)
        u72.RotationInfluence = Vector3.new(0, 0, 0)
        local u73 = nil
        u73 = u4.PostSimulation:Connect(function() --[[Anonymous function at line 307]]
            --[[
            Upvalues:
                [1] = u72
                [2] = u14
                [3] = u73
            --]]
            local v74, _, v75 = u72:Update()
            u14.Position = UDim2.fromScale(0.5, 0.5) + UDim2.fromOffset(v74.X, v74.Y)
            if v75 then
                u73:Disconnect()
            end
        end)
        task.wait(1.2)
        u13.Fade.BackgroundTransparency = 0
        u11(u13.Fade, TweenInfo.new(1.5, Enum.EasingStyle.Sine, Enum.EasingDirection.Out, 0, false, 0.2), {
            ["BackgroundTransparency"] = 1
        })
        v70:Cancel()
        v71:Cancel()
        task.delay(0.3, function() --[[Anonymous function at line 326]]
            --[[
            Upvalues:
                [1] = u72
            --]]
            u72:StopSustain()
        end)
    else
        u3["Seed Pack"].Reward:Play()
        u15.Label.Text = ("x1 %*"):format((u7:GetTextDisplayForItem(v36)))
        u29(true)
        task.wait(2)
        u24(false)
        task.wait(0.5)
        u29(false)
    end
    u14.Visible = false
    u13.Enabled = false
    for _, v76 in v43 do
        if type(v76) == "function" then
            task.spawn(v76)
        end
    end
    if v31 then
        u21 = false
    end
    if v41 then
        u21 = true
    end
end
function v19.Spin(_, p78) --[[Anonymous function at line 363]]
    --[[
    Upvalues:
        [1] = u18
        [2] = u20
        [3] = u77
        [4] = u10
    --]]
    while not u18 do
        task.wait()
    end
    while u20 do
        task.wait()
    end
    u20 = true
    xpcall(u77, warn, p78)
    u20 = false
    u10.SeedPack.SpinFinished.send()
end
function v19.Start(u79) --[[Anonymous function at line 379]]
    --[[
    Upvalues:
        [1] = u13
        [2] = u10
        [3] = u16
        [4] = u17
        [5] = u18
    --]]
    u13.Frame.Visible = false
    u13.Frame.Position = UDim2.fromScale(0.5, 1.5)
    u13.Frame.Rolled.ImageTransparency = 1
    u13.Frame.Rolled.Label.TextTransparency = 1
    u13.Frame.Rolled.Label.UIStroke.Transparency = 1
    for _, u80 in u13.Frame.Rolled.Decorations:GetChildren() do
        u80.ImageTransparency = u13.Frame.Rolled.ImageTransparency
        u13.Frame.Rolled:GetPropertyChangedSignal("ImageTransparency"):Connect(function() --[[Anonymous function at line 390]]
            --[[
            Upvalues:
                [1] = u80
                [2] = u13
            --]]
            u80.ImageTransparency = u13.Frame.Rolled.ImageTransparency
        end)
    end
    u10.SeedPack.Result.listen(function(p81) --[[Anonymous function at line 395]]
        --[[
        Upvalues:
            [1] = u79
        --]]
        u79:Spin(p81)
    end)
    for v82 = 1, 150 do
        local v83 = u16.UIListLayout.Template:Clone()
        v83.Name = tostring(v82)
        v83.LayoutOrder = v82
        v83.Parent = u16
        u17[v82] = v83
        if v82 % 10 == 0 then
            task.wait(0.1)
        end
    end
    u18 = true
end
task.spawn(v19.Start, v19)
return v19