-- Full Path: -Grow-a-Garden-\\TableUtils-ModuleScript.lua
local function u5(p1) --[[Anonymous function at line 2]]
    --[[
    Upvalues:
        [1] = u5
    --]]
    if typeof(p1) ~= "table" then
        return p1
    end
    local v2 = {}
    for v3, v4 in pairs(p1) do
        v2[v3] = u5(v4)
    end
    return v2
end
local function u12(p6, p7) --[[Anonymous function at line 13]]
    --[[
    Upvalues:
        [1] = u12
    --]]
    if typeof(p6) ~= "table" or typeof(p7) ~= "table" then
        return p6 == p7
    end
    for v8, v9 in pairs(p6) do
        if not u12(v9, p7[v8]) then
            return false
        end
    end
    for v10, v11 in pairs(p7) do
        if not u12(v11, p6[v10]) then
            return false
        end
    end
    return true
end
return {
    ["deepCopy"] = u5,
    ["deepEquals"] = u12,
    ["getDictionaryLength"] = function(p13) --[[Function name: getDictionaryLength, line 30]]
        local v14 = 0
        for _, _ in p13 do
            v14 = v14 + 1
        end
        return v14
    end
}