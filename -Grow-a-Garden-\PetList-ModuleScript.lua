-- Full Path: -Grow-a-Garden-\\PetList-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage"):Wait<PERSON><PERSON><PERSON>hil<PERSON>("Assets")
local v2 = v1:<PERSON><PERSON><PERSON><PERSON>hil<PERSON>("Models"):<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("PetAssets")
local v3 = v1:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("Animations"):<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("PetAnimations")
local v4 = {
    ["Dog"] = {
        ["Description"] = "Digging Buddy: Occasionally digs up a random seed",
        ["Actions"] = {},
        ["Model"] = v2:<PERSON><PERSON><PERSON><PERSON><PERSON>hil<PERSON>("Dog"),
        ["DefaultHunger"] = 1000,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966),
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://135018170520317",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 10,
        ["SellPrice"] = 55000,
        ["Rarity"] = "Common",
        ["Passives"] = { "Digging Buddy" },
        ["YHeightScaler"] = 0.4,
        ["Animations"] = {
            ["Idle"] = v3.Dog.Idle,
            ["Walk"] = v3.Dog.Walk,
            ["Dig"] = v3.Dog.Dig
        },
        ["States"] = {}
    },
    ["Golden Lab"] = {
        ["Description"] = "Digging Friend: Occasionally digs up a random seed at a higher chance",
        ["Actions"] = {},
        ["Model"] = v2:FindFirstChild("Dog"),
        ["Variant"] = "Golden Lab",
        ["DefaultHunger"] = 1200,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966),
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://99376934607716",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 10,
        ["SellPrice"] = 55000,
        ["Rarity"] = "Common",
        ["Passives"] = { "Digging Friend" },
        ["YHeightScaler"] = 0.4,
        ["Animations"] = {
            ["Idle"] = v3.Dog.Idle,
            ["Walk"] = v3.Dog.Walk,
            ["Dig"] = v3.Dog.Dig
        },
        ["States"] = {}
    },
    ["Bunny"] = {
        ["Description"] = "Carrot Chomper: Runs to carrots, eats them, and grants bonus sheckles (more than normal value)",
        ["Actions"] = {},
        ["Model"] = v2:FindFirstChild("Bunny"),
        ["DefaultHunger"] = 1100,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966),
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://85830855120751",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 9,
        ["SellPrice"] = 55000,
        ["Rarity"] = "Common",
        ["Passives"] = { "Carrot Chomper" },
        ["YHeightScaler"] = 0.4,
        ["Animations"] = {
            ["Idle"] = v3.Bunny.Idle,
            ["Walk"] = v3.Bunny.Walk,
            ["Chomp"] = v3.Bunny.Chomp
        },
        ["States"] = {}
    },
    ["Black Bunny"] = {
        ["Description"] = "Carrot Devourer: Runs to carrots, eats them, and grants bonus sheckles (more than normal value)",
        ["Actions"] = {},
        ["Model"] = v2:FindFirstChild("Bunny"),
        ["Variant"] = "Black Bunny",
        ["DefaultHunger"] = 1300,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966),
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://86614624778104",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 9,
        ["SellPrice"] = 165000,
        ["Rarity"] = "Uncommon",
        ["Passives"] = { "Carrot Chomper" },
        ["YHeightScaler"] = 0.4,
        ["Animations"] = {
            ["Idle"] = v3.Bunny.Idle,
            ["Walk"] = v3.Bunny.Walk,
            ["Chomp"] = v3.Bunny.Chomp
        },
        ["States"] = {}
    },
    ["Cat"] = {
        ["Description"] = "Cat Nap: Cat naps in a random spot in your farm, emitting an aura that boosts nearby fruit size",
        ["Actions"] = {},
        ["Model"] = v2:FindFirstChild("Cat"),
        ["DefaultHunger"] = 1400,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966),
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://136444015144013",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 12,
        ["SellPrice"] = 165000,
        ["Rarity"] = "Uncommon",
        ["Passives"] = { "Cat Nap" },
        ["YHeightScaler"] = 0.4,
        ["Animations"] = {
            ["Idle"] = v3.Cat.Idle,
            ["Walk"] = v3.Cat.Walk,
            ["Nap"] = v3.Cat.Nap
        },
        ["States"] = {}
    },
    ["Orange Tabby"] = {
        ["Description"] = "Orange Tabby: Orange Tabby naps in a random spot in your farm, emitting an aura that boosts nearby fruit size",
        ["Actions"] = {},
        ["Model"] = v2:FindFirstChild("Cat"),
        ["Variant"] = "Orange Tabby",
        ["DefaultHunger"] = 1500,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966),
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://103360220936666",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 12,
        ["SellPrice"] = 660000,
        ["Rarity"] = "Common",
        ["Passives"] = { "Lazy Nap" },
        ["YHeightScaler"] = 0.4,
        ["Animations"] = {
            ["Idle"] = v3.Cat.Idle,
            ["Walk"] = v3.Cat.Walk,
            ["Nap"] = v3.Cat.Nap
        },
        ["States"] = {}
    },
    ["Deer"] = {
        ["Description"] = "Forester: When harvesting berry plants, there is a chance the fruit will remain",
        ["Actions"] = {},
        ["Model"] = v2:FindFirstChild("Deer"),
        ["DefaultHunger"] = 2500,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966),
        ["ModelScalePerLevel"] = 0.009,
        ["Icon"] = "rbxassetid://91926785467809",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 12,
        ["SellPrice"] = 165000,
        ["Rarity"] = "Uncommon",
        ["Passives"] = { "Forester" },
        ["YHeightScaler"] = 0.65,
        ["Animations"] = {
            ["Idle"] = v3.Deer.Idle,
            ["Walk"] = v3.Deer.Walk,
            ["Stomp"] = v3.Deer.Stomp
        },
        ["States"] = {}
    },
    ["Spotted Deer"] = {
        ["Description"] = "Forester: When harvesting berry plants, there is a chance the fruit will remain",
        ["Actions"] = {},
        ["Model"] = v2:FindFirstChild("Spotted Deer"),
        ["DefaultHunger"] = 2500,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966),
        ["ModelScalePerLevel"] = 0.009,
        ["Icon"] = "rbxassetid://126439207915258",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 12,
        ["SellPrice"] = 660000,
        ["Rarity"] = "Rare",
        ["Passives"] = { "Spotted Forester" },
        ["YHeightScaler"] = 0.65,
        ["Animations"] = {
            ["Idle"] = v3.Deer.Idle,
            ["Walk"] = v3.Deer.Walk,
            ["Stomp"] = v3.Deer.Stomp
        },
        ["States"] = {}
    },
    ["Monkey"] = {
        ["Description"] = "Cheeky Refund: 3% chance to get your fruit back when you sell it",
        ["Actions"] = {},
        ["Model"] = v2:FindFirstChild("Monkey"),
        ["DefaultHunger"] = 7400,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966),
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://113881196210664",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 10,
        ["SellPrice"] = 660000,
        ["Rarity"] = "Rare",
        ["Passives"] = { "Cheeky Refund" },
        ["YHeightScaler"] = 0.4,
        ["Animations"] = {
            ["Idle"] = v3.Monkey.Idle,
            ["Walk"] = v3.Monkey.Walk,
            ["Steal"] = v3.Monkey.Steal
        },
        ["States"] = {}
    },
    ["Silver Monkey"] = {
        ["Description"] = "Cheeky Refund: 3% chance to get your fruit back when you sell it",
        ["Actions"] = {},
        ["Model"] = v2:FindFirstChild("Monkey"),
        ["Variant"] = "Silver Monkey",
        ["DefaultHunger"] = 8000,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966),
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://136985272620600",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 10,
        ["SellPrice"] = 3300000,
        ["Rarity"] = "Legendary",
        ["Passives"] = { "Premium Cheeky Refund" },
        ["YHeightScaler"] = 0.4,
        ["Animations"] = {
            ["Idle"] = v3.Monkey.Idle,
            ["Walk"] = v3.Monkey.Walk,
            ["Steal"] = v3.Monkey.Steal
        },
        ["States"] = {}
    },
    ["Chicken"] = {
        ["Description"] = "Eggcelerator: Decreases the time needed to hatch other eggs",
        ["Actions"] = {},
        ["Model"] = v2:FindFirstChild("Chicken"),
        ["DefaultHunger"] = 3400,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966),
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://108080824427369",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 10,
        ["SellPrice"] = 165000,
        ["Rarity"] = "Uncommon",
        ["Passives"] = { "Eggcelerator" },
        ["YHeightScaler"] = 0.15,
        ["Animations"] = {
            ["Idle"] = v3.Chicken.Idle,
            ["Walk"] = v3.Chicken.Walk
        },
        ["States"] = {}
    },
    ["Rooster"] = {
        ["Description"] = "Eggcelerator: Decreases the time needed to hatch other eggs",
        ["Actions"] = {},
        ["Model"] = v2:FindFirstChild("Rooster"),
        ["DefaultHunger"] = 4000,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966),
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://137107493326109",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 10,
        ["SellPrice"] = 650000,
        ["Rarity"] = "Rare",
        ["Passives"] = { "Better Eggcelerator" },
        ["YHeightScaler"] = 0.15,
        ["Animations"] = {
            ["Idle"] = v3.Chicken.Idle,
            ["Walk"] = v3.Chicken.Walk
        },
        ["States"] = {}
    },
    ["Pig"] = {
        ["Description"] = "Fertilizer Frenzy: Occasionally releases a fertilizing AOE boosting plant size and mutation chance",
        ["Actions"] = {},
        ["Model"] = v2:FindFirstChild("Pig"),
        ["DefaultHunger"] = 5000,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966),
        ["ModelScalePerLevel"] = 0.008,
        ["Icon"] = "rbxassetid://134476443266448",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 8,
        ["SellPrice"] = 650000,
        ["Rarity"] = "Uncommon",
        ["Passives"] = { "Fertilizer Frenzy" },
        ["YHeightScaler"] = 0.6,
        ["Animations"] = {
            ["Idle"] = v3.Pig.Idle,
            ["Walk"] = v3.Pig.Walk,
            ["Frenzy"] = v3.Pig.Frenzy
        },
        ["States"] = {}
    },
    ["Turtle"] = {
        ["Description"] = "Turtle Tinkerer: Slowing aura that makes sprinklers last longer",
        ["Actions"] = {},
        ["Model"] = v2:FindFirstChild("Turtle"),
        ["DefaultHunger"] = 10000,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966),
        ["ModelScalePerLevel"] = 0.004,
        ["Icon"] = "rbxassetid://92906330087175",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 5,
        ["SellPrice"] = 3300000,
        ["Rarity"] = "Legendary",
        ["Passives"] = { "Turtle Tinkerer" },
        ["YHeightScaler"] = -0.32,
        ["Animations"] = {
            ["Idle"] = v3.Turtle.Idle,
            ["Walk"] = v3.Turtle.Walk
        },
        ["States"] = {}
    },
    ["Cow"] = {
        ["Description"] = "Milk of the Land: Fertilizing aura that boosts nearby plant growth speed ",
        ["Actions"] = {},
        ["Model"] = v2:FindFirstChild("Cow"),
        ["DefaultHunger"] = 9500,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966),
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://118832676475537",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 8,
        ["SellPrice"] = 3300000,
        ["Rarity"] = "Legendary",
        ["YHeightScaler"] = 0.5,
        ["Passives"] = { "Milk of the Land" },
        ["Animations"] = {
            ["Idle"] = v3.Cow.Idle,
            ["Walk"] = v3.Cow.Walk
        },
        ["States"] = {}
    },
    ["Snail"] = {
        ["Description"] = "Slow and Steady: Increased lucky harvest chance",
        ["Actions"] = {},
        ["Model"] = v2:FindFirstChild("Snail"),
        ["DefaultHunger"] = 12000,
        ["WeldOffset"] = CFrame.Angles(0, 1.5707963267948966, 3.141592653589793),
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://80970021440625",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 3,
        ["SellPrice"] = 60000000,
        ["Rarity"] = "Mythical",
        ["Passives"] = { "Slow and Steady" },
        ["YHeightScaler"] = 0,
        ["Animations"] = {
            ["Idle"] = v3.Snail.Idle,
            ["Walk"] = v3.Snail.Walk
        },
        ["States"] = {}
    },
    ["Giant Ant"] = {
        ["Description"] = "For the Blue Colony: Small chance to duplicate harvested plant & Candy Harvester: Increased chance to duplicate harvested candy type plant",
        ["Actions"] = {},
        ["Model"] = v2:FindFirstChild("Giant Ant"),
        ["DefaultHunger"] = 18000,
        ["WeldOffset"] = CFrame.Angles(0, 1.5707963267948966, 3.141592653589793),
        ["ModelScalePerLevel"] = 0.003,
        ["Icon"] = "rbxassetid://71413253805996",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 5,
        ["SellPrice"] = 60000000,
        ["Rarity"] = "Mythical",
        ["Passives"] = { "For the Blue Colony", "Candy Harvester" },
        ["YHeightScaler"] = 0.4,
        ["Animations"] = {
            ["Idle"] = v3.Ant.Idle,
            ["Walk"] = v3.Ant.Walk,
            ["Grab"] = v3.Ant.Grab
        }
    },
    ["Dragonfly"] = {
        ["Description"] = "Transmutation: Every now and then turns a fruit to gold",
        ["Actions"] = {},
        ["Model"] = v2:FindFirstChild("Dragonfly"),
        ["DefaultHunger"] = 100000,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 0),
        ["ModelScalePerLevel"] = 0.008,
        ["Icon"] = "rbxassetid://118484611393651",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Flight",
        ["MovementSpeed"] = 9,
        ["SellPrice"] = 60000000,
        ["Rarity"] = "Divine",
        ["Passives"] = { "Transmutation" },
        ["YHeightScaler"] = 0.4,
        ["Animations"] = {
            ["Idle"] = v3.Dragonfly.Idle,
            ["Walk"] = v3.Dragonfly.Walk
        }
    },
    ["Polar Bear"] = {
        ["Description"] = "Polar Express: Occasionally sets a random nearby fruit cold, turning it into Chilled with a small chance for Frozen",
        ["Actions"] = {},
        ["Model"] = v2:FindFirstChild("Polar Bear"),
        ["DefaultHunger"] = 20000,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966),
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://72209118254193",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 8,
        ["SellPrice"] = 3300000,
        ["Rarity"] = "Legendary",
        ["Passives"] = { "Polar Express" },
        ["YHeightScaler"] = 0.35,
        ["Animations"] = {
            ["Idle"] = v3.Bear.Idle,
            ["Walk"] = v3.Bear.Walk,
            ["Roar"] = v3.Bear.Roar
        }
    },
    ["Panda"] = {
        ["Description"] = "Bamboozle: Waddles to bamboo, eats it, and grants bonus sheckles (more than normal value)",
        ["Actions"] = {},
        ["Model"] = v2:FindFirstChild("Panda"),
        ["DefaultHunger"] = 20000,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966),
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://107090327345246",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 8,
        ["SellPrice"] = 165000,
        ["Rarity"] = "Legendary",
        ["Passives"] = { "Bamboozle" },
        ["YHeightScaler"] = 0.35,
        ["Animations"] = {
            ["Idle"] = v3.Panda.Idle,
            ["Walk"] = v3.Panda.Walk,
            ["Chomp"] = v3.Panda.Chomp
        }
    },
    ["Sea Otter"] = {
        ["Description"] = "Water Spray: Water\'s plants randomly like a watering can",
        ["Actions"] = {},
        ["Model"] = v2:FindFirstChild("Sea Otter"),
        ["DefaultHunger"] = 30000,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966),
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://94422445572440",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 9,
        ["SellPrice"] = 3300000,
        ["Rarity"] = "Legendary",
        ["Passives"] = { "Water Spray" },
        ["YHeightScaler"] = -0.25,
        ["Animations"] = {
            ["Idle"] = v3.Otter.Idle,
            ["Walk"] = v3.Otter.Walk,
            ["Spray"] = v3.Otter.Spray
        }
    },
    ["Caterpillar"] = {
        ["Description"] = "Leaf Lover Passive: Boost nearby Leafy plants growth rate",
        ["Actions"] = {},
        ["Model"] = v2:FindFirstChild("Caterpillar"),
        ["DefaultHunger"] = 25000,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966),
        ["ModelScalePerLevel"] = 0.0065,
        ["Icon"] = "rbxassetid://119651461526366",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 6,
        ["SellPrice"] = 60000000,
        ["Rarity"] = "Mythical",
        ["Passives"] = { "Leaf Lover" },
        ["YHeightScaler"] = -0.55,
        ["Animations"] = {
            ["Idle"] = v3.Caterpillar.Idle,
            ["Walk"] = v3.Caterpillar.Walk
        }
    },
    ["Praying Mantis"] = {
        ["Description"] = "Zen Zone: Prays, then gives plants in AOE Buff that increases the chance of gold fruit from plants",
        ["Actions"] = {},
        ["Model"] = v2:FindFirstChild("Praying Mantis"),
        ["DefaultHunger"] = 55000,
        ["WeldOffset"] = CFrame.Angles(0, 1.5707963267948966, 3.141592653589793),
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://121485029406440",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 8,
        ["SellPrice"] = 60000000,
        ["Rarity"] = "Mythical",
        ["Passives"] = { "Zen Zone" },
        ["YHeightScaler"] = 0.1,
        ["Animations"] = {
            ["Idle"] = v3.Mantis.Idle,
            ["Walk"] = v3.Mantis.Walk,
            ["Pray"] = v3.Mantis.Pray
        }
    },
    ["Hedgehog"] = {
        ["Description"] = "Prickly Lover: Makes prickly fruit grow bigger",
        ["Actions"] = {},
        ["Model"] = v2:FindFirstChild("Hedgehog"),
        ["DefaultHunger"] = 30000,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966),
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://83544966481425",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 7,
        ["SellPrice"] = 1000000,
        ["Rarity"] = "Rare",
        ["Passives"] = { "Prickly Lover" },
        ["YHeightScaler"] = 0.1,
        ["Animations"] = {
            ["Idle"] = v3.Hedgehog.Idle,
            ["Walk"] = v3.Hedgehog.Walk,
            ["Curl"] = v3.Hedgehog.Curl,
            ["CurlLoop"] = v3.Hedgehog.CurlLoop
        }
    },
    ["Kiwi"] = {
        ["Description"] = "Nocturnal Nursery: Occasionally reduces the hatch time of the egg with the most hatch time left",
        ["Actions"] = {},
        ["Model"] = v2:FindFirstChild("Kiwi"),
        ["DefaultHunger"] = 50000,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966),
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://104651906442347",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 8,
        ["SellPrice"] = 1000000,
        ["Rarity"] = "Rare",
        ["Passives"] = { "Nocturnal Nursery" },
        ["YHeightScaler"] = 0.9,
        ["Animations"] = {
            ["Idle"] = v3.Kiwi.Idle,
            ["Walk"] = v3.Kiwi.Walk,
            ["Nurse"] = v3.Kiwi.Nurse
        }
    },
    ["Mole"] = {
        ["Description"] = "Treasure Hunter: Will occasionally dig down to find gear or sheckles",
        ["Actions"] = {},
        ["Model"] = v2:FindFirstChild("Mole"),
        ["DefaultHunger"] = 50000,
        ["WeldOffset"] = CFrame.Angles(0, 1.5707963267948966, 0),
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://79089804794269",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 6,
        ["SellPrice"] = 2000000,
        ["Rarity"] = "Legendary",
        ["Passives"] = { "Treasure Hunter" },
        ["YHeightScaler"] = 0.1,
        ["Animations"] = {
            ["Idle"] = v3.Mole.Idle,
            ["Walk"] = v3.Mole.Walk,
            ["DigDown"] = v3.Mole.DigDown,
            ["DigUp"] = v3.Mole.DigUp,
            ["DigWalk"] = v3.Mole.DigWalk
        }
    },
    ["Frog"] = {
        ["Description"] = "Croak: Will occasionally advance a nearby plant\'s growth by 24 hours",
        ["Actions"] = {},
        ["Model"] = v2:FindFirstChild("Frog"),
        ["DefaultHunger"] = 50000,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966),
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://96930166899467",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 8,
        ["SellPrice"] = 3000000,
        ["Rarity"] = "Legendary",
        ["Passives"] = { "Croak" },
        ["YHeightScaler"] = 0.1,
        ["Animations"] = {
            ["Idle"] = v3.Frog.Idle,
            ["Walk"] = v3.Frog.Walk,
            ["Croak"] = v3.Frog.Croak
        }
    },
    ["Echo Frog"] = {
        ["Description"] = "Echo Croak: Will occasionally advance a nearby plant\'s growth by 24 hours",
        ["Actions"] = {},
        ["Model"] = v2:FindFirstChild("Echo Frog"),
        ["DefaultHunger"] = 50000,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966),
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://102271225890686",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 8,
        ["SellPrice"] = 5000000,
        ["Rarity"] = "Mythical",
        ["Passives"] = { "Echo Croak" },
        ["YHeightScaler"] = 0.1,
        ["Animations"] = {
            ["Idle"] = v3.Frog.Idle,
            ["Walk"] = v3.Frog.Walk,
            ["Croak"] = v3.Frog.Croak
        }
    },
    ["Owl"] = {
        ["Description"] = "Prince of the Night: Grants bonus experience per second gain to all active pets.",
        ["Actions"] = {},
        ["Model"] = v2:FindFirstChild("Owl"),
        ["DefaultHunger"] = 50000,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966),
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://138016343005291",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 8,
        ["SellPrice"] = 8000000,
        ["Rarity"] = "Mythical",
        ["Passives"] = { "Prince of the Night", "Movement Variation" },
        ["YHeightScaler"] = 0.5,
        ["Animations"] = {
            ["Idle"] = v3.Owl.Idle,
            ["Walk"] = v3.Owl.Walk,
            ["Fly"] = v3.Owl.Fly,
            ["FlyUp"] = v3.Owl.FlyUp,
            ["FlyDown"] = v3.Owl.FlyDown
        }
    },
    ["Night Owl"] = {
        ["Description"] = "King of the Night: Grants bonus experience per second gain to all active pets.",
        ["Actions"] = {},
        ["Model"] = v2:FindFirstChild("Night Owl"),
        ["DefaultHunger"] = 50000,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966),
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://101760640498094",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 8,
        ["SellPrice"] = 10000000,
        ["Rarity"] = "Divine",
        ["Passives"] = { "King of the Night", "Movement Variation" },
        ["YHeightScaler"] = 0.5,
        ["Animations"] = {
            ["Idle"] = v3.Owl.Idle,
            ["Walk"] = v3.Owl.Walk,
            ["Fly"] = v3.Owl.Fly,
            ["FlyUp"] = v3.Owl.FlyUp,
            ["FlyDown"] = v3.Owl.FlyDown
        }
    },
    ["Raccoon"] = {
        ["Description"] = "Rascal: Occasionally steals (duplicates) fruit from other player\'s plot and hands it to you",
        ["Actions"] = {},
        ["Model"] = v2:FindFirstChild("Raccoon"),
        ["DefaultHunger"] = 45000,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966),
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://136232391555861",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 16,
        ["SellPrice"] = 10000000,
        ["Rarity"] = "Divine",
        ["Passives"] = { "Rascal" },
        ["YHeightScaler"] = 0.1,
        ["Animations"] = {
            ["Idle"] = v3.Raccoon.Idle,
            ["Walk"] = v3.Raccoon.Walk,
            ["Steal"] = v3.Raccoon.Steal
        }
    },
    ["Grey Mouse"] = {
        ["Description"] = "Whisker Wisdom: Occasionally gains bonus experience & Scamper: Increase player movement speed",
        ["Actions"] = {},
        ["Model"] = v2:FindFirstChild("Grey Mouse"),
        ["DefaultHunger"] = 15000,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966),
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://116310390398341",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 10,
        ["SellPrice"] = 4400000,
        ["Rarity"] = "Mythical",
        ["Passives"] = { "Whisker Wisdom", "Scamper" },
        ["YHeightScaler"] = 0.4,
        ["Animations"] = {
            ["Idle"] = v3.Mouse.Idle,
            ["Walk"] = v3.Mouse.Walk,
            ["Think"] = v3.Mouse.Think
        },
        ["States"] = {}
    },
    ["Squirrel"] = {
        ["Description"] = "Seed Stash: Planting seeds have a small chance to not be consumed. Rarer plants have less chance",
        ["Actions"] = {},
        ["Model"] = v2:FindFirstChild("Squirrel"),
        ["DefaultHunger"] = 15000,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966),
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://96950434895806",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 8,
        ["SellPrice"] = 5500000,
        ["Rarity"] = "Mythical",
        ["Passives"] = { "Seed Stash" },
        ["YHeightScaler"] = 0.4,
        ["Animations"] = {
            ["Idle"] = v3.Squirrel.Idle,
            ["Walk"] = v3.Squirrel.Walk
        },
        ["States"] = {}
    },
    ["Brown Mouse"] = {
        ["Description"] = "Whiskier Wisdom: Occasionally gains bonus experience & Cheese Hop: Increase player jump height",
        ["Actions"] = {},
        ["Model"] = v2:FindFirstChild("Grey Mouse"),
        ["Variant"] = "Brown Mouse",
        ["DefaultHunger"] = 15000,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966),
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://94641319183999",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 9,
        ["SellPrice"] = 5500000,
        ["Rarity"] = "Mythical",
        ["Passives"] = { "Whiskier Wisdom", "Cheese Hop" },
        ["YHeightScaler"] = 0.4,
        ["Animations"] = {
            ["Idle"] = v3.Mouse.Idle,
            ["Walk"] = v3.Mouse.Walk,
            ["Think"] = v3.Mouse.Think
        },
        ["States"] = {}
    },
    ["Red Giant Ant"] = {
        ["Description"] = "For the Red Colony: Small chance to duplicate harvested plant & Fruit Harvester: Increased chance to duplicate harvested fruit type plant",
        ["Actions"] = {},
        ["Model"] = v2:FindFirstChild("Giant Ant"),
        ["Variant"] = "Red Giant Ant",
        ["DefaultHunger"] = 15000,
        ["WeldOffset"] = CFrame.Angles(0, 1.5707963267948966, 3.141592653589793),
        ["ModelScalePerLevel"] = 0.003,
        ["Icon"] = "rbxassetid://89449712431551",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 5,
        ["SellPrice"] = 6500000,
        ["Rarity"] = "Mythical",
        ["Passives"] = { "For the Red Colony", "Fruit Harvester" },
        ["YHeightScaler"] = 0.4,
        ["Animations"] = {
            ["Idle"] = v3.Ant.Idle,
            ["Walk"] = v3.Ant.Walk,
            ["Grab"] = v3.Ant.Grab
        }
    },
    ["Red Fox"] = {
        ["Description"] = "Every <Cooldown>m, goes to another player\'s plot and tries to steal a seed from a random plant. The rarer the plant, the harder it is to succeed!",
        ["Actions"] = {},
        ["Model"] = v2:FindFirstChild("Red Fox"),
        ["DefaultHunger"] = 35000,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966),
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://116662854190616",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 16,
        ["SellPrice"] = 7500000,
        ["Rarity"] = "Mythical",
        ["Passives"] = { "Scoundrel" },
        ["YHeightScaler"] = 0.1,
        ["Animations"] = {
            ["Idle"] = v3.Fox.Idle,
            ["Walk"] = v3.Fox.Walk,
            ["Steal"] = v3.Fox.Steal
        }
    },
    ["Chicken Zombie"] = {
        ["Description"] = "Zombify: Occasionally has a chance to zombify a nearby random fruit & Eggcelerator: Decreases the time needed to hatch other eggs",
        ["Actions"] = {},
        ["Model"] = v2:FindFirstChild("Chicken Zombie"),
        ["DefaultHunger"] = 35000,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966),
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://108581559611673",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 8,
        ["SellPrice"] = 600000,
        ["Rarity"] = "Mythical",
        ["Passives"] = { "Zombify", "Eggcelerator" },
        ["YHeightScaler"] = 0.4,
        ["Animations"] = {
            ["Idle"] = v3.ChickenZombie.Idle,
            ["Walk"] = v3.ChickenZombie.Walk,
            ["Zombify"] = v3.ChickenZombie.Zombify
        },
        ["States"] = {}
    },
    ["Blood Hedgehog"] = {
        ["Description"] = "Sanguine Spike: Makes prickly fruit have increased variant chance and grow bigger",
        ["Actions"] = {},
        ["Model"] = v2:FindFirstChild("Blood Hedgehog"),
        ["DefaultHunger"] = 30000,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966),
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://76471191139414",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 7,
        ["SellPrice"] = 33000000,
        ["Rarity"] = "Legendary",
        ["Passives"] = { "Sanguine Spike", "Prickly Blessing" },
        ["YHeightScaler"] = 0.1,
        ["Animations"] = {
            ["Idle"] = v3.Hedgehog.Idle,
            ["Walk"] = v3.Hedgehog.Walk,
            ["Curl"] = v3.Hedgehog.Curl,
            ["CurlLoop"] = v3.Hedgehog.CurlLoop
        }
    },
    ["Blood Kiwi"] = {
        ["Description"] = "Crimson Cradle: Occasionally reduces the egg hatch time and boosts egg hatch speed",
        ["Actions"] = {},
        ["Model"] = v2:FindFirstChild("Blood Kiwi"),
        ["DefaultHunger"] = 45000,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966),
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://87343374343285",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 8,
        ["SellPrice"] = 30000000,
        ["Rarity"] = "Mythical",
        ["Passives"] = { "Crimson Cradle", "Better Eggcelerator" },
        ["YHeightScaler"] = 0.9,
        ["Animations"] = {
            ["Idle"] = v3.Kiwi.Idle,
            ["Walk"] = v3.Kiwi.Walk,
            ["Nurse"] = v3.Kiwi.Nurse
        }
    },
    ["Blood Owl"] = {
        ["Description"] = "Monarch of Midnight: Grants bonus experience per second gain to all active pets",
        ["Actions"] = {},
        ["Model"] = v2:FindFirstChild("Blood Owl"),
        ["DefaultHunger"] = 50000,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966),
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://81262783747840",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 8,
        ["SellPrice"] = 70000000,
        ["Rarity"] = "Divine",
        ["Passives"] = { "Monarch of Midnight", "Movement Variation" },
        ["YHeightScaler"] = 0.5,
        ["Animations"] = {
            ["Idle"] = v3.Owl.Idle,
            ["Walk"] = v3.Owl.Walk,
            ["Fly"] = v3.Owl.Fly,
            ["FlyUp"] = v3.Owl.FlyUp,
            ["FlyDown"] = v3.Owl.FlyDown
        }
    },
    ["Moon Cat"] = {
        ["Description"] = "Moon Nap: Moon cat naps in a random spot in your farm, and boosts nearby fruit size & Moon Harvest: Grants chance for Night type plants to replant when harvested ",
        ["Actions"] = {},
        ["Model"] = v2:FindFirstChild("Cat"),
        ["Variant"] = "Moon Cat",
        ["DefaultHunger"] = 2400,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966),
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://72392850111255",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 10,
        ["SellPrice"] = 25000000,
        ["Rarity"] = "Legendary",
        ["Passives"] = { "Moon Nap", "Moon Harvest" },
        ["YHeightScaler"] = 0.4,
        ["Animations"] = {
            ["Idle"] = v3.Cat.Idle,
            ["Walk"] = v3.Cat.Walk,
            ["Nap"] = v3.Cat.Nap
        },
        ["States"] = {}
    },
    ["Firefly"] = {
        ["Description"] = "Lightning Bug: Occasionally strikes a random nearby fruit, with a small chance of turning it Shocked",
        ["Actions"] = {},
        ["Model"] = v2:FindFirstChild("Firefly"),
        ["DefaultHunger"] = 25000,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966),
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://131310748087635",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Flight",
        ["MovementSpeed"] = 10,
        ["SellPrice"] = 3300000,
        ["Rarity"] = "Mythical",
        ["Passives"] = { "Lightning Bug" },
        ["YHeightScaler"] = 0.5,
        ["Animations"] = {
            ["Idle"] = v3.Firefly.Idle,
            ["Walk"] = v3.Firefly.Walk
        }
    },
    ["Red Dragon"] = {
        ["Description"] = "Scorched Soil: Occasionally sets a random nearby fruit ablaze, turning it into Burnt",
        ["Actions"] = {},
        ["Model"] = v2:FindFirstChild("Red Dragon"),
        ["DefaultHunger"] = 10000,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 0),
        ["ModelScalePerLevel"] = 0.004,
        ["Icon"] = "rbxassetid://140223014467344",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Flight",
        ["MovementSpeed"] = 10,
        ["SellPrice"] = 100000,
        ["Rarity"] = "Common",
        ["Passives"] = { "Scorched Soil" },
        ["YHeightScaler"] = 0.2,
        ["Animations"] = {}
    }
}
for v5, v6 in v4 do
    if not v6.Model then
        warn(v5, "has no attached model defaulting to Dog model")
        v6.Model = v2:WaitForChild("Dog")
    end
    v6.YHeightScaler = v6.YHeightScaler or 0
    v6.States = v6.States or {}
end
return v4