-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\Settings\SettingsReflectionService-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
local v2 = game:GetService("Players")
local u3 = game:GetService("SoundService")
local u4 = game:GetService("TweenService")
local u5 = v2.LocalPlayer
local v6 = require(v1.Modules.DataService)
local v7 = require(v1.Data.SettingsRegistry)
local u8 = require(v1.Modules.Settings.SettingsService)
require(v1.Modules.CreateTagHandler)
local u9 = require(v1.Modules.DetailsController)
require(v1.Modules.userprint)("Perthyz")
local v14 = {
    ["GearShopButton"] = function(p10) --[[Anonymous function at line 64]]
        --[[
        Upvalues:
            [1] = u5
        --]]
        u5.PlayerGui.Teleport_UI.Frame.Gear.Visible = p10
    end,
    ["PetShopButton"] = function(p11) --[[Anonymous function at line 67]]
        --[[
        Upvalues:
            [1] = u5
        --]]
        u5.PlayerGui.Teleport_UI.Frame.Pets.Visible = p11
    end,
    ["Audio"] = function(p12) --[[Anonymous function at line 70]]
        --[[
        Upvalues:
            [1] = u4
            [2] = u3
        --]]
        u4:Create(u3.Music, TweenInfo.new(2), {
            ["Volume"] = p12 and 1 or 0
        }):Play()
    end,
    ["ShowDetails"] = function(p13) --[[Anonymous function at line 75]]
        --[[
        Upvalues:
            [1] = u9
        --]]
        if u9:IsHardDeleted() then
            return
        elseif p13 == "None" then
            u9:SetDetailLevel("None")
            return
        elseif p13 == "Less" then
            u9:SetDetailLevel("Less")
        elseif p13 == "All" then
            u9:SetDetailLevel("All")
        end
    end
}
local v15 = {}
for u16 in v7 do
    local u17 = v14[u16]
    if u17 then
        v6:GetPathSignal((("Settings/%*"):format(u16))):Connect(function() --[[Anonymous function at line 90]]
            --[[
            Upvalues:
                [1] = u17
                [2] = u8
                [3] = u16
            --]]
            u17(u8:GetSetting(u16))
        end)
        if u16 ~= "ShowDetails" then
            u17((u8:GetSetting(u16)))
        end
    end
end
return v15