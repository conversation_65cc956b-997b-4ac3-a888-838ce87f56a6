-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Give_Gear-ModuleScript.lua
local v1 = game:GetService("ServerStorage")
local v2 = game:GetService("ReplicatedStorage")
local v3 = game:GetService("ServerScriptService")
local u4 = game:GetService("Players")
game:GetService("HttpService")
v1:WaitForChild("SaveableTools")
v1:<PERSON><PERSON><PERSON><PERSON>hil<PERSON>("Gears")
local v5 = v2:WaitForChild("ObjectModels")
require(v2.Data.SaveableToolsData)
require(v2.Modules.StringUtils)
local u6 = require(v3.Modules.InventoryService)
local u37 = {
    ["Watering Can"] = function(p7, _, p8) --[[Anonymous function at line 23]]
        --[[
        Upvalues:
            [1] = u6
        --]]
        local v9 = p8 or 1
        local v10 = u6:Find(p7, "Watering Can")
        local _, v11 = next(v10)
        if v11 then
            local v12 = v11.ItemData
            v12.Uses = v12.Uses + v9
        else
            u6:CreateItem(p7, "Watering Can", {
                ["Uses"] = v9
            })
        end
    end,
    ["Trowel"] = function(p13, _, p14) --[[Anonymous function at line 37]]
        --[[
        Upvalues:
            [1] = u6
        --]]
        local v15 = p14 or 1
        local v16 = u6:Find(p13, "Trowel")
        local _, v17 = next(v16)
        if v17 then
            local v18 = v17.ItemData
            v18.Uses = v18.Uses + v15
        else
            u6:CreateItem(p13, "Trowel", {
                ["Uses"] = v15
            })
        end
    end,
    ["Recall Wrench"] = function(p19, _, p20) --[[Anonymous function at line 51]]
        --[[
        Upvalues:
            [1] = u6
        --]]
        local v21 = p20 or 1
        local v22 = u6:Find(p19, "Recall Wrench")
        local _, v23 = next(v22)
        if v23 then
            local v24 = v23.ItemData
            v24.Uses = v24.Uses + v21
        else
            u6:CreateItem(p19, "Recall Wrench", {
                ["Uses"] = v21
            })
        end
    end,
    ["Favorite Tool"] = function(p25, _, p26) --[[Anonymous function at line 65]]
        --[[
        Upvalues:
            [1] = u6
        --]]
        local v27 = p26 or 1
        local v28 = u6:Find(p25, "Favorite Tool")
        local _, v29 = next(v28)
        if v29 then
            local v30 = v29.ItemData
            v30.Uses = v30.Uses + v27
        else
            u6:CreateItem(p25, "Favorite Tool", {
                ["Uses"] = v27
            })
        end
    end,
    ["Harvest Tool"] = function(p31, _, p32) --[[Anonymous function at line 79]]
        --[[
        Upvalues:
            [1] = u6
        --]]
        local v33 = p32 or 1
        local v34 = u6:Find(p31, "Harvest Tool")
        local _, v35 = next(v34)
        if v35 then
            local v36 = v35.ItemData
            v36.Uses = v36.Uses + v33
        else
            u6:CreateItem(p31, "Harvest Tool", {
                ["Uses"] = v33
            })
        end
    end
}
local v38 = {}
for _, v39 in v5:GetChildren() do
    local u40 = v39.Name
    local v41 = u40:find("Sprinkler")
    local v42 = u40:find("Lightning Rod")
    local v43 = u40:find("Night Staff")
    local v44 = u40:find("Star Caller")
    if v41 then
        u37[u40] = function(p45, u46, p47) --[[Anonymous function at line 108]]
            --[[
            Upvalues:
                [1] = u6
            --]]
            local v48 = p47 or 1
            local v50 = u6:Find(p45, "Sprinkler", function(p49) --[[Anonymous function at line 115]]
                --[[
                Upvalues:
                    [1] = u46
                --]]
                return p49.ItemName and p49.ItemName == u46 and true or false
            end)
            local _, v51 = next(v50)
            if v51 then
                if not v51.ItemData.Uses then
                    v51.ItemData.Uses = 1
                end
                local v52 = v51.ItemData
                v52.Uses = v52.Uses + v48
            else
                u6:CreateItem(p45, "Sprinkler", {
                    ["ItemName"] = u46,
                    ["Uses"] = v48
                })
            end
        end
    elseif v42 then
        u37[u40] = function(p53) --[[Anonymous function at line 136]]
            --[[
            Upvalues:
                [1] = u6
                [2] = u40
            --]]
            u6:CreateItem(p53, "Lightning Rod", {
                ["ItemName"] = u40
            })
        end
    elseif v43 then
        u37[u40] = function(p54, p55, p56) --[[Anonymous function at line 142]]
            --[[
            Upvalues:
                [1] = u6
            --]]
            for _ = 1, p56 do
                u6:CreateItem(p54, "Night Staff", {
                    ["ItemName"] = p55
                })
            end
        end
    elseif v44 then
        u37[u40] = function(p57, p58) --[[Anonymous function at line 150]]
            --[[
            Upvalues:
                [1] = u6
            --]]
            u6:CreateItem(p57, "Star Caller", {
                ["ItemName"] = p58
            })
        end
    end
end
function v38.Give_Gear(p59, p60, p61) --[[Anonymous function at line 162]]
    --[[
    Upvalues:
        [1] = u4
        [2] = u37
    --]]
    while not (p59.Character and p59.Character.PrimaryPart) do
        if not p59:IsDescendantOf(u4) then
            return
        end
        task.wait()
    end
    local v62 = u37[p60]
    if v62 then
        v62(p59, p60, p61)
    end
end
return v38