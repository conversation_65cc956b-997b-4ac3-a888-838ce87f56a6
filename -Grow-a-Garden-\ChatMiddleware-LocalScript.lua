-- Full Path: -Grow-a-Garden-\\ChatMiddleware-LocalScript.lua
local v1 = game:GetService("TextChatService")
game:GetService("ReplicatedStorage")
local u2 = game:GetService("Players")
local u3 = game:GetService("UserService")
local u4 = {
    ["RankMap"] = {
        [255] = "<font color=\"rgb(255,80,80)\"><b>[OWNER]</b></font> %s",
        [254] = "<font color=\"rgb(255,165,00)\"><b>[OWNER]</b></font> %s",
        [200] = "<font color=\"rgb(255,165,00)\"><b>[DEV]</b></font> %s",
        [7] = "<font color=\"rgb(255,165,00)\"><b>[QA-LEAD]</b></font> %s"
    },
    ["AnnouncementFormat"] = "<font color=\"rgb(255,165,0)\"><b>%s:</b> %s</font>"
}
function v1.OnIncomingMessage(p5) --[[Anonymous function at line 21]]
    --[[
    Upvalues:
        [1] = u2
        [2] = u4
        [3] = u3
    --]]
    local v6 = Instance.new("TextChatMessageProperties")
    local v7 = p5.TextSource
    local _ = p5.Text
    local v8 = p5.TextSource
    if v8 then
        v8 = p5.TextSource.UserId
    end
    if v8 then
        v8 = u2:GetPlayerByUserId(v8)
    end
    if v8 then
        if v8 then
            local _ = u4.RankMap[v8:GetRankInGroup(35789249)]
        end
        if v7 then
            local v9 = u3:GetUserInfosByUserIdsAsync({ p5.TextSource.UserId })[1]
            if not v9 then
                return
            end
            local v10 = u2:GetPlayerByUserId(p5.TextSource.UserId)
            if not v10 then
                return
            end
            local v11 = u4.RankMap[v10:GetRankInGroup(35789249)]
            p5.PrefixText = ("%*%*"):format(v11 and v11:format(v10.DisplayName) or v10.DisplayName, v9.HasVerifiedBadge and "\238\128\128" or "")
        end
    end
    return v6
end