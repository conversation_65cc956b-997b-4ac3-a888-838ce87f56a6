-- Full Path: -Grow-a-Garden-\\LocalScript_53-LocalScript.lua
local v1 = game:GetService("Players")
local u2 = game:GetService("TweenService")
local u3 = v1.LocalPlayer
local u4 = script.Parent
local function u6() --[[Anonymous function at line 8]]
    --[[
    Upvalues:
        [1] = u3
        [2] = u2
        [3] = u4
    --]]
    local v5 = {
        ["GroupTransparency"] = u3:GetAttribute("LowMemoryWarning") and 0 or 1
    }
    u2:Create(u4, TweenInfo.new(1.5), v5):Play()
end
u3:GetAttributeChangedSignal("LowMemoryWarning"):Connect(function() --[[Anonymous function at line 16]]
    --[[
    Upvalues:
        [1] = u6
    --]]
    u6()
end)
u6()