-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\FriendGiftLocal-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
local v2 = game:GetService("Players")
local v3 = v1.GameEvents
local u4 = game:GetService("StarterGui")
local v5 = v3.FriendGiftEvent
local u6 = {}
local u7 = v2.LocalPlayer
local u8 = u7.PlayerGui:WaitForChild("Friend_Notification", 3)
local u9 = u8.Frame.Frame.Background.Holder.BodyLabel
local v10 = u8.Frame.Frame.Background.Holder.Frame.Accept
local v11 = u8.Frame.Frame.Background.Holder.Frame.Decline
local u12 = {}
local u13 = nil
function u6.DismissCurrent(_) --[[Anonymous function at line 40]]
    --[[
    Upvalues:
        [1] = u12
        [2] = u13
        [3] = u9
        [4] = u8
    --]]
    print("EVENT FIRED 1")
    local v14 = table.find(u12, u13)
    if v14 then
        table.remove(u12, v14)
    end
    u13 = nil
    if #u12 > 0 then
        print("Trying to show next")
        print("EVENT FIRED 5")
        if not u13 then
            local v15 = u12[1]
            u13 = v15
            print("EVENT FIRED 6")
            u9.Text = ("Friend request from\n%*"):format(v15.Name)
            print("EVENT FIRED 7")
            u8.Show_VAL.Value = true
        end
    else
        print("Hiding")
        u8.Show_VAL.Value = false
        return
    end
end
function u6.AcceptCurrent(_) --[[Anonymous function at line 59]]
    --[[
    Upvalues:
        [1] = u13
        [2] = u6
        [3] = u4
    --]]
    print("EVENT FIRED 2")
    local v16 = u13
    u6:DismissCurrent()
    if v16 then
        u4:SetCore("PromptSendFriendRequest", v16)
    end
end
v5.OnClientEvent:Connect(function(p17) --[[Anonymous function at line 70]]
    --[[
    Upvalues:
        [1] = u7
        [2] = u12
        [3] = u13
        [4] = u9
        [5] = u8
    --]]
    print("EVENT FIRED 3")
    if p17:IsFriendsWith(u7.UserId) then
        return
    else
        if not table.find(u12, p17) then
            local v18 = u12
            table.insert(v18, p17)
        end
        print("EVENT FIRED 5")
        if not u13 then
            local v19 = u12[1]
            u13 = v19
            print("EVENT FIRED 6")
            u9.Text = ("Friend request from\n%*"):format(v19.Name)
            print("EVENT FIRED 7")
            u8.Show_VAL.Value = true
        end
    end
end)
v10.MouseButton1Click:Connect(function() --[[Anonymous function at line 83]]
    --[[
    Upvalues:
        [1] = u6
    --]]
    u6:AcceptCurrent()
end)
v11.MouseButton1Click:Connect(function() --[[Anonymous function at line 87]]
    --[[
    Upvalues:
        [1] = u6
    --]]
    u6:DismissCurrent()
end)
return u6