-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\ReplicationClass\TableListener\Signal\Immediate-ModuleScript.lua
local u1 = {}
u1.__index = u1
local u2 = {}
u2.__index = u2
local u3 = nil
local function u6(p4, ...) --[[Anonymous function at line 48]]
    --[[
    Upvalues:
        [1] = u3
    --]]
    local v5 = u3
    u3 = nil
    p4(...)
    u3 = v5
end
local function u7() --[[Anonymous function at line 57]]
    --[[
    Upvalues:
        [1] = u3
        [2] = u6
    --]]
    u3 = coroutine.running()
    while true do
        u6(coroutine.yield())
    end
end
function u1.new() --[[Anonymous function at line 71]]
    --[[
    Upvalues:
        [1] = u1
    --]]
    local v8 = u1
    return setmetatable({
        ["_active"] = true,
        ["_head"] = nil
    }, v8)
end
function u1.Is(p9) --[[Anonymous function at line 93]]
    --[[
    Upvalues:
        [1] = u1
    --]]
    local v10
    if typeof(p9) == "table" then
        v10 = getmetatable(p9) == u1
    else
        v10 = false
    end
    return v10
end
function u1.IsActive(p11) --[[Anonymous function at line 110]]
    return p11._active == true
end
function u1.Connect(p12, p13) --[[Anonymous function at line 132]]
    --[[
    Upvalues:
        [1] = u2
    --]]
    local v14 = typeof(p13) == "function"
    assert(v14, "Must be function")
    if p12._active ~= true then
        local v15 = u2
        return setmetatable({
            ["Connected"] = false,
            ["_node"] = nil
        }, v15)
    end
    local v16 = p12._head
    local v17 = {
        ["_signal"] = p12,
        ["_connection"] = nil,
        ["_handler"] = p13,
        ["_next"] = v16,
        ["_prev"] = nil
    }
    if v16 ~= nil then
        v16._prev = v17
    end
    p12._head = v17
    local v18 = u2
    local v19 = setmetatable({
        ["Connected"] = true,
        ["_node"] = v17
    }, v18)
    v17._connection = v19
    return v19
end
function u1.Once(p20, u21) --[[Anonymous function at line 194]]
    local v22 = typeof(u21) == "function"
    assert(v22, "Must be function")
    local u23 = nil
    u23 = p20:Connect(function(...) --[[Anonymous function at line 204]]
        --[[
        Upvalues:
            [1] = u23
            [2] = u21
        --]]
        u23:Disconnect()
        u21(...)
    end)
    return u23
end
u1.ConnectOnce = u1.Once
function u1.Wait(p24) --[[Anonymous function at line 226]]
    local u25 = coroutine.running()
    local u26 = nil
    u26 = p24:Connect(function(...) --[[Anonymous function at line 231]]
        --[[
        Upvalues:
            [1] = u26
            [2] = u25
        --]]
        u26:Disconnect()
        task.spawn(u25, ...)
    end)
    return coroutine.yield()
end
function u1.Fire(p27, ...) --[[Anonymous function at line 256]]
    --[[
    Upvalues:
        [1] = u3
        [2] = u7
    --]]
    local v28 = p27._head
    while v28 ~= nil do
        if v28._connection ~= nil then
            if u3 == nil then
                task.spawn(u7)
            end
            task.spawn(u3, v28._handler, ...)
        end
        v28 = v28._next
    end
end
function u1.DisconnectAll(p29) --[[Anonymous function at line 287]]
    local v30 = p29._head
    while v30 ~= nil do
        local v31 = v30._connection
        if v31 ~= nil then
            v31.Connected = false
            v31._node = nil
            v30._connection = nil
        end
        v30 = v30._next
    end
    p29._head = nil
end
function u1.Destroy(p32) --[[Anonymous function at line 316]]
    if p32._active == true then
        p32:DisconnectAll()
        p32._active = false
    end
end
function u2.Disconnect(p33) --[[Anonymous function at line 339]]
    if p33.Connected == true then
        p33.Connected = false
        local v34 = p33._node
        local v35 = v34._prev
        local v36 = v34._next
        if v36 ~= nil then
            v36._prev = v35
        end
        if v35 == nil then
            v34._signal._head = v36
        else
            v35._next = v36
        end
        v34._connection = nil
        p33._node = nil
    end
end
u2.Destroy = u2.Disconnect
return u1