-- Full Path: -Grow-a-Garden-\\Vector-ModuleScript.lua
local u1 = require(script.Parent.Parent.Shared.Util)
local function v4(p2, p3) --[[Anonymous function at line 3]]
    if p2 == nil then
        return false, ("Invalid or missing number at position %d in Vector type."):format(p3)
    else
        return true
    end
end
local u5 = u1.MakeSequenceType({
    ["ValidateEach"] = v4,
    ["TransformEach"] = tonumber,
    ["Constructor"] = Vector3.new,
    ["Length"] = 3
})
local u6 = u1.MakeSequenceType({
    ["ValidateEach"] = v4,
    ["TransformEach"] = tonumber,
    ["Constructor"] = Vector2.new,
    ["Length"] = 2
})
return function(p7) --[[Anonymous function at line 25]]
    --[[
    Upvalues:
        [1] = u5
        [2] = u1
        [3] = u6
    --]]
    p7:RegisterType("vector3", u5)
    p7:RegisterType("vector3s", u1.MakeListableType(u5))
    p7:RegisterType("vector2", u6)
    p7:RegisterType("vector2s", u1.MakeListableType(u6))
end