-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\ByteNet\packets\definePacket-ModuleScript.lua
require(script.Parent.Parent.types)
local u1 = require(script.Parent.packet)
return function(u2) --[[Anonymous function at line 12]]
    --[[
    Upvalues:
        [1] = u1
    --]]
    return function(p3) --[[Anonymous function at line 13]]
        --[[
        Upvalues:
            [1] = u1
            [2] = u2
        --]]
        return u1(u2, p3)
    end
end