-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\NightQuestHelper-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
game:GetService("ServerScriptService")
local u2 = require(v1.Data.NightQuestRewardData)
local u3 = require(v1.Data.SeedData)
local v4 = {}
local u5 = {
    ["Common"] = 1,
    ["Uncommon"] = 2,
    ["Rare"] = 3,
    ["Legendary"] = 4,
    ["Mythical"] = 5,
    ["Divine"] = 6,
    ["Prismatic"] = 7
}
local u6 = {
    ["Normal"] = 0,
    ["Gold"] = 2,
    ["Rainbow"] = 3
}
function v4.GetFruitExperienceValue(_, p7) --[[Anonymous function at line 25]]
    --[[
    Upvalues:
        [1] = u3
        [2] = u5
        [3] = u6
    --]]
    local v8 = p7:FindFirstChild("Item_String")
    if not v8 then
        return warn("NightQuestHelper:GetFruitExperienceValue | Cannot find Item Name!")
    end
    local v9 = u3[v8.Value]
    if not v9 then
        warn((("NightQuestHelper:GetFruitExperienceValue | Unable to find seed %*!"):format(v8)))
        return 1
    end
    local v10 = u5[v9.SeedRarity]
    if not v10 then
        warn((("NightQuestHelper:GetFruitExperienceValue | Unable to find rarity %*!"):format(v9.SeedRarity)))
        return 1
    end
    local v11 = p7:FindFirstChild("Variant")
    if not v11 then
        return warn("NightQuestHelper:GetFruitExperienceValue | Unable to find Variant!")
    end
    local v12 = u6[v11.Value]
    if not v12 then
        warn("NightQuestHelper:GetFruitExperienceValue | Variant Value Cannot be found!")
        v12 = 1
    end
    local v13 = v10 + v12
    if p7:GetAttribute("Bloodlit") then
        v13 = v13 * 1.5
    end
    return math.ceil(v13)
end
function v4.IsQuestFinished(_, p14) --[[Anonymous function at line 61]]
    --[[
    Upvalues:
        [1] = u2
    --]]
    return #p14.ClaimedRewardIndices >= #u2
end
function v4.ReturnRewardData(_) --[[Anonymous function at line 65]]
    --[[
    Upvalues:
        [1] = u2
    --]]
    return u2
end
function v4.GetNextQuestReward(_, p15) --[[Anonymous function at line 69]]
    --[[
    Upvalues:
        [1] = u2
    --]]
    for v16, v17 in u2 do
        if not table.find(p15.ClaimedRewardIndices, v16) and p15.Experience < v17.RequiredExperience then
            return v17, v16
        end
    end
end
function v4.GetUnclaimedQuestRewards(_, p18) --[[Anonymous function at line 86]]
    --[[
    Upvalues:
        [1] = u2
    --]]
    local v19 = {}
    for v20, v21 in u2 do
        if not table.find(p18.ClaimedRewardIndices, v20) and p18.Experience >= v21.RequiredExperience then
            v19[v20] = v21
        end
    end
    return v19
end
return v4