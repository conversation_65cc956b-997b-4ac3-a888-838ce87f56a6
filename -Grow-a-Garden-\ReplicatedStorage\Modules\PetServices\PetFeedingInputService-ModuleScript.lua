-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\PetServices\PetFeedingInputService-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
local u2 = game:GetService("Players").LocalPlayer
local u3 = u2.Character or u2.CharacterAdded:Wait()
u2.CharacterAdded:Connect(function(p4) --[[Anonymous function at line 7]]
    --[[
    Upvalues:
        [1] = u3
    --]]
    u3 = p4
end)
local u5 = require(v1.Modules.PetServices.PetUtilities)
local u6 = require(v1.Modules.PetServices.ActivePetsService)
local u7 = require(v1.Data.PetRegistry).PetConfig.PET_FEEDING_CONFIG.MINIMUM_DISTANCE_FOR_FEEDING
local function u16() --[[Anonymous function at line 21]]
    --[[
    Upvalues:
        [1] = u6
        [2] = u2
        [3] = u3
        [4] = u7
    --]]
    local v8 = (1 / 0)
    local v9 = nil
    local v10 = nil
    for v11, v12 in u6:GetClientPetState(u2.Name) do
        local v13 = v12.Asset
        if not v13 then
            return
        end
        local v14 = v13:FindFirstChildWhichIsA("Model", true)
        local v15 = (v14:GetPivot().Position - u3:GetPivot().Position).Magnitude
        if v8 > v15 and u7 > v15 then
            v10 = v14
            v9 = v11
            v8 = v15
        end
    end
    return v9, v10
end
local u17 = nil
local u18 = Instance.new("Part")
u18.CanCollide = false
u18.CanQuery = false
u18.Anchored = true
u18.Transparency = 1
u18.Parent = workspace
local u19 = Instance.new("ProximityPrompt")
u19.Exclusivity = Enum.ProximityPromptExclusivity.AlwaysShow
u19.KeyboardKeyCode = Enum.KeyCode.F
u19.RequiresLineOfSight = false
u19.HoldDuration = 0.5
u19.MaxActivationDistance = (1 / 0)
u19.Triggered:Connect(function() --[[Anonymous function at line 58]]
    --[[
    Upvalues:
        [1] = u17
        [2] = u6
    --]]
    if u17 then
        u6:Feed(u17)
    end
end)
u19.Parent = u18
local u20 = require(v1.Data.DecimalNumberFormat)
task.spawn(function() --[[Anonymous function at line 71]]
    --[[
    Upvalues:
        [1] = u3
        [2] = u19
        [3] = u18
        [4] = u16
        [5] = u6
        [6] = u17
        [7] = u5
        [8] = u20
    --]]
    while true do
        task.wait(0.05)
        task.spawn(function() --[[Anonymous function at line 75]]
            --[[
            Upvalues:
                [1] = u3
                [2] = u19
                [3] = u18
                [4] = u16
                [5] = u6
                [6] = u17
                [7] = u5
                [8] = u20
            --]]
            local v21 = u3:FindFirstChildWhichIsA("Tool")
            if v21 then
                if v21:HasTag("FruitTool") then
                    local v22, v23 = u16()
                    if v23 then
                        local v24 = u6:GetPetDataFromPetObject(v23.Parent).PetType
                        u17 = v22
                        local v25 = u5:GetFruitWorthPercentageForAnimal(v24, v21) * 100
                        u18.CFrame = v23:GetPivot()
                        u19.ObjectText = v23.Name
                        u19.ActionText = ("Feed (%*%%) %*"):format(u20(v25), v21.Name)
                        u19.Enabled = true
                    else
                        u19.Enabled = false
                        u18.CFrame = CFrame.new(100, 5000, 100)
                    end
                else
                    u19.Enabled = false
                    u18.CFrame = CFrame.new(100, 5000, 100)
                    return
                end
            else
                u19.Enabled = false
                u18.CFrame = CFrame.new(100, 5000, 100)
                return
            end
        end)
    end
end)
return {}