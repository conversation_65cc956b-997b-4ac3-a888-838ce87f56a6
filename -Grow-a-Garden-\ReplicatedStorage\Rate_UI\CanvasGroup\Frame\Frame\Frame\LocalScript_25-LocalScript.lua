-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Rate_UI\CanvasGroup\Frame\Frame\Frame\LocalScript_25-LocalScript.lua
local u1 = {}
for _, v2 in pairs(script.Parent:GetChildren()) do
    if v2:IsA("ImageButton") then
        table.insert(u1, v2)
    end
end
for _, u3 in pairs(u1) do
    local u4 = game.SoundService.Click
    local u5 = game.SoundService.Hover
    u3.MouseButton1Down:Connect(function() --[[Anonymous function at line 10]]
        --[[
        Upvalues:
            [1] = u4
        --]]
        u4.PlaybackSpeed = 1 + math.random(-15, 15) / 100
        u4.TimePosition = 0
        u4.Playing = true
    end)
    local u6 = game:GetService("UserInputService")
    u3.MouseEnter:Connect(function() --[[Anonymous function at line 16]]
        --[[
        Upvalues:
            [1] = u6
            [2] = u5
        --]]
        if u6.TouchEnabled == false then
            u5.PlaybackSpeed = 1 + math.random(-15, 15) / 100
            u5.TimePosition = 0
            u5.Playing = true
        end
    end)
    u3.MouseButton1Click:Connect(function() --[[Anonymous function at line 23]]
        --[[
        Upvalues:
            [1] = u1
            [2] = u3
        --]]
        for _, v7 in pairs(u1) do
            if v7.LayoutOrder <= u3.LayoutOrder then
                v7.ImageColor3 = Color3.new(1, 1, 1)
                script.Parent.Parent.Parent.Parent.Parent.Rating_Val.Value = u3.LayoutOrder
            else
                v7.ImageColor3 = Color3.new(0, 0, 0)
            end
        end
    end)
end