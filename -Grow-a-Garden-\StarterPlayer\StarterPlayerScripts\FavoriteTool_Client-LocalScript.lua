-- Full Path: -Grow-a-Garden-\StarterPlayer\StarterPlayerScripts\FavoriteTool_Client-LocalScript.lua
local v1 = game:GetService("Players")
local u2 = game:GetService("UserInputService")
local u3 = game:GetService("CollectionService")
local u4 = game:GetService("TweenService")
local v5 = game:GetService("RunService")
local v6 = game:GetService("ReplicatedStorage")
local v7 = game:GetService("ProximityPromptService")
local u8 = v6:WaitForChild("GameEvents"):WaitForChild("FavoriteToolRemote")
local u9 = require(v6.Modules.GetFarm)
local u10 = require(v6.Modules.ProximityPromptController)
local u11 = require(v6.Modules.Notification)
local u12 = v1.LocalPlayer
local u13 = workspace.CurrentCamera
local u14 = script:WaitFor<PERSON>hild("Highlight")
local u15 = nil
local u16 = nil
local u17 = false
local u18 = false
local function u22(p19) --[[Anonymous function at line 39]]
    --[[
    Upvalues:
        [1] = u9
        [2] = u12
        [3] = u3
    --]]
    local v20 = p19:FindFirstAncestorOfClass("Model")
    if v20 then
        local v21 = u9(u12)
        if v21 then
            if u3:HasTag(v20, "Harvestable") then
                if v20:IsDescendantOf(v21) then
                    return v20
                else
                    return false
                end
            else
                return false
            end
        else
            return false
        end
    else
        return false
    end
end
local function u29() --[[Anonymous function at line 61]]
    --[[
    Upvalues:
        [1] = u2
        [2] = u13
        [3] = u3
        [4] = u12
        [5] = u22
    --]]
    local v23 = u2:GetMouseLocation()
    local v24 = u13:ViewportPointToRay(v23.X, v23.Y)
    local v25 = RaycastParams.new()
    local v26 = u3:GetTagged("ShovelIgnore")
    v25.FilterType = Enum.RaycastFilterType.Exclude
    v25.FilterDescendantsInstances = v26
    v25:AddToFilter(u12.Character)
    v25:AddToFilter(u13)
    local v27 = workspace:Raycast(v24.Origin, v24.Direction * 500, v25)
    if v27 and v27.Instance then
        local v28 = v27.Instance:FindFirstAncestorOfClass("Model")
        if v28 and u22(v27.Instance) then
            return v28
        end
    end
end
local function u38(_, p30) --[[Anonymous function at line 129]]
    --[[
    Upvalues:
        [1] = u17
        [2] = u29
        [3] = u11
        [4] = u15
        [5] = u8
        [6] = u16
    --]]
    if u17 and not p30 then
        local u31 = u29()
        if u31 then
            local v32 = u31:FindFirstChild("Grow")
            if v32 and v32:FindFirstChild("Age") then
                local v33 = u31:GetAttribute("MaxAge")
                if not v33 or v32.Age.Value < v33 then
                    u11:CreateNotification("You can only favorite fully grown fruit!")
                    return
                end
            end
            u15 = u31
            local u34 = not u31:GetAttribute("Favorited")
            local v35, v36 = pcall(function() --[[Anonymous function at line 151]]
                --[[
                Upvalues:
                    [1] = u8
                    [2] = u16
                    [3] = u31
                    [4] = u34
                --]]
                return u8:InvokeServer(u16, u31, u34)
            end)
            if v35 then
                if v36 == true then
                    if u34 then
                        u11:CreateNotification((("Favorited %*"):format(u31.Name)))
                    else
                        u11:CreateNotification((("Unfavorited %*"):format(u31.Name)))
                        for _, v37 in u31:GetDescendants() do
                            if v37:IsA("ProximityPrompt") then
                                v37.Enabled = true
                            end
                        end
                    end
                else
                    u11:CreateNotification("You have no more uses!")
                    return
                end
            else
                warn("Failed to toggle favorite state:", v36)
                return
            end
        else
            return
        end
    else
        return
    end
end
if u2.TouchEnabled then
    u2.TouchTapInWorld:Connect(u38)
else
    u12:GetMouse().Button1Down:Connect(function() --[[Anonymous function at line 181]]
        --[[
        Upvalues:
            [1] = u38
            [2] = u2
        --]]
        u38(u2:GetMouseLocation(), false)
    end)
end
v5.RenderStepped:Connect(function() --[[Anonymous function at line 187]]
    --[[
    Upvalues:
        [1] = u17
        [2] = u14
        [3] = u15
        [4] = u29
        [5] = u4
    --]]
    if u17 then
        local v39 = u29()
        if v39 and v39 ~= u15 then
            u14.Adornee = v39
            u14.FillTransparency = 1
            u4:Create(u14, TweenInfo.new(0.2), {
                ["FillTransparency"] = 0.5
            }):Play()
            u15 = v39
        elseif not v39 then
            u14.Adornee = nil
            u15 = nil
        end
    else
        u14.Adornee = nil
        u15 = nil
        return
    end
end)
v7.PromptShown:Connect(function(p40, _) --[[Anonymous function at line 208]]
    --[[
    Upvalues:
        [1] = u9
        [2] = u12
    --]]
    local v41 = p40:FindFirstAncestorOfClass("Model")
    if v41 then
        if v41:GetAttribute("Favorited") then
            local v42 = u9(u12)
            if v42 and v41:IsDescendantOf(v42) then
                p40.Enabled = false
            end
        end
    end
end)
v7.PromptTriggered:Connect(function(p43, p44) --[[Anonymous function at line 221]]
    --[[
    Upvalues:
        [1] = u12
        [2] = u9
    --]]
    if p44 == u12 then
        local v45 = p43:FindFirstAncestorOfClass("Model")
        if v45 then
            if v45:GetAttribute("Favorited") then
                local v46 = u9(u12)
                if v46 and v45:IsDescendantOf(v46) then
                    p43.Enabled = false
                    return
                end
            end
        end
    else
        return
    end
end)
task.spawn(function() --[[Anonymous function at line 237]]
    --[[
    Upvalues:
        [1] = u18
        [2] = u12
        [3] = u16
        [4] = u17
        [5] = u10
        [6] = u14
        [7] = u15
    --]]
    while not u18 do
        task.wait(0.1)
        local u47 = u12.Character
        if u47 then
            u47 = u12.Character:FindFirstChildOfClass("Tool")
        end
        if not (u47 and u47.Name:match("Favorite")) then
            u47 = nil
        end
        if u12.Character and u47 then
            u47.Equipped:Connect(function() --[[Anonymous function at line 243]]
                --[[
                Upvalues:
                    [1] = u16
                    [2] = u47
                    [3] = u17
                    [4] = u10
                --]]
                u16 = u47
                u17 = true
                u10:AddDisabler("Favorite Tool")
            end)
            u47.Unequipped:Connect(function() --[[Anonymous function at line 250]]
                --[[
                Upvalues:
                    [1] = u16
                    [2] = u17
                    [3] = u14
                    [4] = u15
                    [5] = u10
                --]]
                u16 = nil
                u17 = false
                u14.Adornee = nil
                u15 = nil
                u10:RemoveDisabler("Favorite Tool")
            end)
            u18 = true
        end
    end
end)