-- Full Path: -Grow-a-Garden-\StarterPlayer\StarterPlayerScripts\WeatherClient\Tornado-ModuleScript.lua
local v1 = {}
local u2 = false
local u3 = script.Sky
local u4 = require(game.ReplicatedStorage.Modules.SkyboxManager)
local u5 = game.Lighting.ColorCorrection:Clone()
u5.Name = script.Name
u5.Parent = game.Lighting
local function u36() --[[Anonymous function at line 18]]
    --[[
    Upvalues:
        [1] = u2
        [2] = u4
        [3] = u3
        [4] = u5
    --]]
    u2 = true
    u4.UpdateSkybox(u3, 2)
    game.TweenService:Create(game.Lighting, TweenInfo.new(3), {
        ["Ambient"] = Color3.fromRGB(162, 162, 162),
        ["ExposureCompensation"] = 0.425,
        ["Brightness"] = 0.7
    }):Play()
    game.TweenService:Create(u5, TweenInfo.new(3), {
        ["Brightness"] = 0.1,
        ["TintColor"] = Color3.fromRGB(217, 217, 217)
    }):Play()
    game.Workspace.Terrain.Clouds.Enabled = true
    game.TweenService:Create(game.Workspace.Terrain.Clouds, TweenInfo.new(3), {
        ["Cover"] = 0.905,
        ["Density"] = 0.083
    }):Play()
    local u6 = game.ReplicatedStorage.Assets.Tornado:Clone()
    u6.Parent = workspace
    u6:PivotTo(game.ReplicatedStorage.StrikeLightningHere.CFrame)
    local u7 = u6.PrimaryPart
    local u8 = Random.new()
    local u9 = {}
    local function u15() --[[Anonymous function at line 57]]
        --[[
        Upvalues:
            [1] = u6
            [2] = u9
            [3] = u8
        --]]
        local v10 = u6.Template:Clone()
        local v11 = u9
        local v12 = {
            ["CreationTime"] = tick(),
            ["Template"] = v10,
            ["Direction"] = Random.new():NextInteger(1, 2) == 2 and -1 or 1
        }
        table.insert(v11, v12)
        v10.Name = tick()
        v10.Parent = u6
        local v13 = u8:NextNumber(2.35, 4)
        for _, v14 in v10:GetDescendants() do
            if v14:IsA("Beam") then
                v14.Width0 = v13
                v14.Width1 = v13
            end
        end
        game.TweenService:Create(v10, TweenInfo.new(1.2), {
            ["Size"] = Vector3.new(60, 0.5, 60)
        }):Play()
    end
    local u16 = tick()
    if workspace.Visuals:FindFirstChild("TornadoSpot") then
        u6:PivotTo(u6:GetPivot():Lerp(workspace.Visuals.TornadoSpot.CFrame, 5))
    end
    local u17 = false
    local u18 = true
    task.spawn(function() --[[Anonymous function at line 103]]
        --[[
        Upvalues:
            [1] = u18
            [2] = u6
            [3] = u9
            [4] = u7
            [5] = u16
            [6] = u8
            [7] = u17
            [8] = u15
            [9] = u4
            [10] = u3
            [11] = u5
        --]]
        while u18 do
            local v19 = game:GetService("RunService").Heartbeat:Wait()
            if workspace.Visuals:FindFirstChild("TornadoSpot") then
                u6:PivotTo(u6:GetPivot():Lerp(workspace.Visuals.TornadoSpot.CFrame, v19 * 5))
            end
            for v20, v21 in u9 do
                local v22 = (tick() - v21.CreationTime) / 1.2
                local v23 = math.clamp(v22, 0, 1)
                local v24 = v23 * 180
                local v25 = math.rad(v24)
                local v26 = 1 - math.sin(v25)
                local v27 = v21.Template
                local v28 = u7.CFrame * CFrame.new(0, v23 * 90, 0)
                local v29 = CFrame.Angles
                local v30 = v23 * 720
                v27.CFrame = v28 * v29(0, math.rad(v30) * v21.Direction, 0) * CFrame.new(0, 0, v23 * 10)
                for _, v31 in v21.Template:GetChildren() do
                    v31:GetAttribute("Direction")
                    local v32 = v21.Template.Size.X
                    local v33 = v21.Template.Size.Z
                    v31.Position = Vector3.new(v32, 0, v33) / 2
                    v31.Position = v31.Position * v31:GetAttribute("Direction")
                end
                for _, v34 in v21.Template:GetDescendants() do
                    if v34:IsA("Beam") then
                        v34.Transparency = NumberSequence.new(v26)
                    end
                end
                if v23 == 1 then
                    v21.Template:Destroy()
                    table.remove(u9, v20)
                end
            end
            if tick() - u16 > u8:NextNumber(0.1, 0.2) and u17 == false then
                u15()
                u16 = tick()
            end
        end
        u4.UpdateSkybox(u3, 0)
        game.TweenService:Create(u5, TweenInfo.new(3), {
            ["Brightness"] = 0,
            ["TintColor"] = Color3.fromRGB(255, 255, 255)
        }):Play()
        game.TweenService:Create(game.Lighting, TweenInfo.new(3), {
            ["Ambient"] = game.Lighting:GetAttribute("DefaultAmbient"),
            ["ExposureCompensation"] = game.Lighting:GetAttribute("DefaultExposure"),
            ["Brightness"] = game.Lighting:GetAttribute("DefaultBrightness")
        }):Play()
        game.TweenService:Create(game.Workspace.Terrain.Clouds, TweenInfo.new(3), {
            ["Cover"] = 0,
            ["Density"] = 0
        }):Play()
        task.delay(3, function() --[[Anonymous function at line 168]]
            game.Workspace.Terrain.Clouds.Enabled = false
        end)
    end)
    repeat
        task.wait()
    until u2 == false
    u17 = true
    task.wait(1)
    u18 = false
    for _, v35 in u6:GetDescendants() do
        if v35:IsA("ParticleEmitter") then
            v35.Enabled = false
        end
    end
    task.delay(4, function() --[[Anonymous function at line 186]]
        --[[
        Upvalues:
            [1] = u6
        --]]
        u6:Destroy()
    end)
end
workspace:GetAttributeChangedSignal("TornadoEvent"):Connect(function() --[[Anonymous function at line 193]]
    --[[
    Upvalues:
        [1] = u36
        [2] = u2
    --]]
    if workspace:GetAttribute("TornadoEvent") then
        u36()
    else
        u2 = false
    end
end)
if workspace:GetAttribute("TornadoEvent") then
    task.defer(u36)
else
    u2 = false
end
return v1