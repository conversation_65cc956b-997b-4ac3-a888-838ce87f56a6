-- Full Path: -Grow-a-Garden-\ReplicatedStorage\CmdrClient\CmdrInterface\Window-ModuleScript.lua
local u1 = game:GetService("GuiService")
local u2 = game:GetService("UserInputService")
local u3 = game:GetService("TextChatService")
local u4 = game:GetService("Players").LocalPlayer
local u5 = { Enum.UserInputType.MouseButton1, Enum.UserInputType.MouseButton2, Enum.UserInputType.Touch }
local u6 = {
    ["Valid"] = true,
    ["AutoComplete"] = nil,
    ["ProcessEntry"] = nil,
    ["OnTextChanged"] = nil,
    ["Cmdr"] = nil,
    ["HistoryState"] = nil
}
if not u4 then
    return {}
end
local u7 = u4:WaitForChild("PlayerGui"):WaitForChild("Cmdr"):WaitForChild("Frame")
local u8 = u7:WaitForChild("Line")
local u9 = u7:WaitForChild("Entry")
u8.Parent = nil
function u6.UpdateLabel(p10) --[[Anonymous function at line 31]]
    --[[
    Upvalues:
        [1] = u9
        [2] = u4
    --]]
    u9.TextLabel.Text = u4.Name .. "@" .. p10.Cmdr.PlaceName .. "$"
end
function u6.GetLabel(_) --[[Anonymous function at line 36]]
    --[[
    Upvalues:
        [1] = u9
    --]]
    return u9.TextLabel.Text
end
function u6.UpdateWindowHeight(_) --[[Anonymous function at line 41]]
    --[[
    Upvalues:
        [1] = u7
    --]]
    local v11 = u7.UIListLayout.AbsoluteContentSize.Y + u7.UIPadding.PaddingTop.Offset + u7.UIPadding.PaddingBottom.Offset
    u7.Size = UDim2.new(u7.Size.X.Scale, u7.Size.X.Offset, 0, (math.clamp(v11, 0, 300)))
    u7.CanvasPosition = Vector2.new(0, v11)
end
function u6.AddLine(p12, p13, p14) --[[Anonymous function at line 50]]
    --[[
    Upvalues:
        [1] = u6
        [2] = u8
        [3] = u7
    --]]
    local v15 = p14 or {}
    local v16 = tostring(p13)
    local v17 = typeof(v15) == "Color3" and {
        ["Color"] = v15
    } or v15
    if #v16 == 0 then
        u6:UpdateWindowHeight()
    else
        local v18 = p12.Cmdr.Util.EmulateTabstops(v16 or "nil", 8)
        local v19 = u8:Clone()
        v19.Text = v18
        v19.TextColor3 = v17.Color or v19.TextColor3
        v19.RichText = v17.RichText or false
        v19.Parent = u7
    end
end
function u6.IsVisible(_) --[[Anonymous function at line 73]]
    --[[
    Upvalues:
        [1] = u7
    --]]
    return u7.Visible
end
function u6.SetVisible(p20, p21) --[[Anonymous function at line 78]]
    --[[
    Upvalues:
        [1] = u7
        [2] = u3
        [3] = u9
        [4] = u2
    --]]
    u7.Visible = p21
    if p21 then
        p20.PreviousChatWindowConfigurationEnabled = u3.ChatWindowConfiguration.Enabled
        p20.PreviousChatInputBarConfigurationEnabled = u3.ChatInputBarConfiguration.Enabled
        u3.ChatWindowConfiguration.Enabled = false
        u3.ChatInputBarConfiguration.Enabled = false
        u9.TextBox:CaptureFocus()
        p20:SetEntryText("")
        if p20.Cmdr.ActivationUnlocksMouse then
            p20.PreviousMouseBehavior = u2.MouseBehavior
            u2.MouseBehavior = Enum.MouseBehavior.Default
            return
        end
    else
        u3.ChatWindowConfiguration.Enabled = p20.PreviousChatWindowConfigurationEnabled == nil and true or p20.PreviousChatWindowConfigurationEnabled
        u3.ChatInputBarConfiguration.Enabled = p20.PreviousChatInputBarConfigurationEnabled == nil and true or p20.PreviousChatInputBarConfigurationEnabled
        u9.TextBox:ReleaseFocus()
        p20.AutoComplete:Hide()
        if p20.PreviousMouseBehavior then
            u2.MouseBehavior = p20.PreviousMouseBehavior
            p20.PreviousMouseBehavior = nil
        end
    end
end
function u6.Hide(p22) --[[Anonymous function at line 111]]
    return p22:SetVisible(false)
end
function u6.Show(p23) --[[Anonymous function at line 116]]
    return p23:SetVisible(true)
end
function u6.SetEntryText(p24, p25) --[[Anonymous function at line 121]]
    --[[
    Upvalues:
        [1] = u9
        [2] = u6
    --]]
    u9.TextBox.Text = p25
    if p24:IsVisible() then
        u9.TextBox:CaptureFocus()
        u9.TextBox.CursorPosition = #p25 + 1
        u6:UpdateWindowHeight()
    end
end
function u6.GetEntryText(_) --[[Anonymous function at line 132]]
    --[[
    Upvalues:
        [1] = u9
    --]]
    return u9.TextBox.Text:gsub("\t", "")
end
function u6.SetIsValidInput(p26, p27, p28) --[[Anonymous function at line 138]]
    --[[
    Upvalues:
        [1] = u9
    --]]
    u9.TextBox.TextColor3 = p27 and Color3.fromRGB(255, 255, 255) or Color3.fromRGB(255, 73, 73)
    p26.Valid = p27
    p26._errorText = p28
end
function u6.HideInvalidState(_) --[[Anonymous function at line 144]]
    --[[
    Upvalues:
        [1] = u9
    --]]
    u9.TextBox.TextColor3 = Color3.fromRGB(255, 255, 255)
end
function u6.LoseFocus(p29, p30) --[[Anonymous function at line 149]]
    --[[
    Upvalues:
        [1] = u9
        [2] = u7
        [3] = u1
    --]]
    local v31 = u9.TextBox.Text
    p29:ClearHistoryState()
    if u7.Visible and not u1.MenuIsOpen then
        u9.TextBox:CaptureFocus()
    elseif u1.MenuIsOpen and u7.Visible then
        p29:Hide()
    end
    if p30 and p29.Valid then
        wait()
        p29:SetEntryText("")
        p29.ProcessEntry(v31)
    elseif p30 then
        p29:AddLine(p29._errorText, Color3.fromRGB(255, 153, 153))
    end
end
function u6.TraverseHistory(p32, p33) --[[Anonymous function at line 170]]
    local v34 = p32.Cmdr.Dispatcher:GetHistory()
    if p32.HistoryState == nil then
        p32.HistoryState = {
            ["Position"] = #v34 + 1,
            ["InitialText"] = p32:GetEntryText()
        }
    end
    local v35 = p32.HistoryState
    local v36 = p32.HistoryState.Position + p33
    local v37 = #v34 + 1
    v35.Position = math.clamp(v36, 1, v37)
    p32:SetEntryText(p32.HistoryState.Position == #v34 + 1 and p32.HistoryState.InitialText or v34[p32.HistoryState.Position])
end
function u6.ClearHistoryState(p38) --[[Anonymous function at line 188]]
    p38.HistoryState = nil
end
function u6.SelectVertical(p39, p40) --[[Anonymous function at line 192]]
    if p39.AutoComplete:IsVisible() and not p39.HistoryState then
        p39.AutoComplete:Select(p40)
    else
        p39:TraverseHistory(p40)
    end
end
local u41 = 0
local u42 = 0
function u6.BeginInput(p43, p44, p45) --[[Anonymous function at line 203]]
    --[[
    Upvalues:
        [1] = u1
        [2] = u41
        [3] = u42
        [4] = u5
        [5] = u7
    --]]
    if u1.MenuIsOpen then
        p43:Hide()
    end
    if p45 and p43:IsVisible() == false then
        return
    elseif p43.Cmdr.ActivationKeys[p44.KeyCode] then
        if p43.Cmdr.MashToEnable and not p43.Cmdr.Enabled then
            if tick() - u41 < 1 then
                if u42 >= 5 then
                    return p43.Cmdr:SetEnabled(true)
                end
                u42 = u42 + 1
            else
                u42 = 1
            end
            u41 = tick()
        elseif p43.Cmdr.Enabled then
            p43:SetVisible(not p43:IsVisible())
            wait()
            p43:SetEntryText("")
            if u1.MenuIsOpen then
                p43:Hide()
            end
        end
    elseif p43.Cmdr.Enabled == false or not p43:IsVisible() then
        if p43:IsVisible() then
            p43:Hide()
        end
    elseif p43.Cmdr.HideOnLostFocus and table.find(u5, p44.UserInputType) then
        local v46 = p44.Position
        local v47 = u7.AbsolutePosition
        local v48 = u7.AbsoluteSize
        if v46.X < v47.X or (v46.X > v47.X + v48.X or (v46.Y < v47.Y or v46.Y > v47.Y + v48.Y)) then
            p43:Hide()
            return
        end
    else
        if p44.KeyCode == Enum.KeyCode.Down then
            p43:SelectVertical(1)
            return
        end
        if p44.KeyCode == Enum.KeyCode.Up then
            p43:SelectVertical(-1)
            return
        end
        if p44.KeyCode == Enum.KeyCode.Return then
            wait()
            p43:SetEntryText(p43:GetEntryText():gsub("\n", ""):gsub("\r", ""))
            return
        end
        if p44.KeyCode == Enum.KeyCode.Tab then
            local v49 = p43.AutoComplete:GetSelectedItem()
            local v50 = p43:GetEntryText()
            if v49 and not (v50:sub(#v50, #v50):match("%s") and p43.AutoComplete.LastItem) then
                local v51 = v49[2]
                local v52 = p43.AutoComplete.Command
                local v53, v54
                if v52 then
                    local v55 = p43.AutoComplete.Arg
                    v53 = v52.Alias
                    if p43.AutoComplete.NumArgs == #v52.ArgumentDefinitions then
                        v54 = false
                    else
                        v54 = p43.AutoComplete.IsPartial == false
                    end
                    local v56 = v52.Arguments
                    for v57 = 1, #v56 do
                        local v58 = v56[v57]
                        local v59 = v58.RawSegments
                        if v58 == v55 then
                            v59[#v59] = v51
                        end
                        local v60 = v58.Prefix .. table.concat(v59, ",")
                        if v60:find(" ") or v60 == "" then
                            v60 = ("%q"):format(v60)
                        end
                        v53 = ("%s %s"):format(v53, v60)
                        if v58 == v55 then
                            break
                        end
                    end
                else
                    v53 = v51
                    v54 = true
                end
                wait()
                p43:SetEntryText(v53 .. (v54 and " " or ""))
            else
                wait()
                p43:SetEntryText(p43:GetEntryText())
            end
        end
        p43:ClearHistoryState()
    end
end
u9.TextBox.FocusLost:Connect(function(p61) --[[Anonymous function at line 314]]
    --[[
    Upvalues:
        [1] = u6
    --]]
    return u6:LoseFocus(p61)
end)
u2.InputBegan:Connect(function(p62, p63) --[[Anonymous function at line 318]]
    --[[
    Upvalues:
        [1] = u6
    --]]
    return u6:BeginInput(p62, p63)
end)
u9.TextBox:GetPropertyChangedSignal("Text"):Connect(function() --[[Anonymous function at line 322]]
    --[[
    Upvalues:
        [1] = u7
        [2] = u9
        [3] = u6
    --]]
    u7.CanvasPosition = Vector2.new(0, u7.AbsoluteCanvasSize.Y)
    if u9.TextBox.Text:match("\t") then
        u9.TextBox.Text = u9.TextBox.Text:gsub("\t", "")
    elseif u6.OnTextChanged then
        return u6.OnTextChanged(u9.TextBox.Text)
    end
end)
u7.ChildAdded:Connect(function() --[[Anonymous function at line 334]]
    --[[
    Upvalues:
        [1] = u6
    --]]
    task.defer(u6.UpdateWindowHeight)
end)
return u6