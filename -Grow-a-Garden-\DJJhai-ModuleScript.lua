-- Full Path: -Grow-a-Garden-\\DJJhai-ModuleScript.lua
local v1 = {}
local _ = game.ReplicatedStorage.RainParticle
Random.new()
local u2 = workspace.CurrentCamera
require(game.ReplicatedStorage.Code.CameraShaker)
require(game.ReplicatedStorage.Code.LightningBolt)
local v3 = RaycastParams.new()
v3.FilterDescendantsInstances = { workspace.Terrain, workspace }
v3.FilterType = Enum.RaycastFilterType.Include
local u4 = false
local u5 = script.Sky
local u6 = require(game.ReplicatedStorage.Modules.SkyboxManager)
u6.AddSkybox(u5)
local u7 = game.Lighting.ColorCorrection:Clone()
u7.Name = script.Name
u7.Parent = game.Lighting
local function u16(p8) --[[Anonymous function at line 33]]
    local v9 = game.ReplicatedStorage.ShootingStar:Clone()
    v9.CFrame = p8
    v9.Parent = workspace
    local v10 = script.Fire:Clone()
    v10.SoundId = "rbxassetid://113919877495546"
    v10.Parent = workspace
    v10:Play()
    game.Debris:AddItem(v10, 5)
    local v11 = 0
    while v11 < 1 do
        v11 = v11 + game:GetService("RunService").Heartbeat:Wait()
        v9:PivotTo(p8:Lerp(p8 * CFrame.new(0, 200, 0), v11))
    end
    for _, v12 in v9:GetDescendants() do
        if v12:IsA("BillboardGui") then
            v12.Enabled = false
        end
    end
    game.Debris:AddItem(v9, 4)
    local v13 = game.ReplicatedStorage.FireworkBoom:Clone()
    v13.Parent = workspace
    v13.CFrame = v9.CFrame
    for _, v14 in v13:GetDescendants() do
        if v14:IsA("ParticleEmitter") then
            v14:Emit(v14:GetAttribute("EmitCount"))
        end
    end
    local v15 = script.Boom:Clone()
    v15.SoundId = "rbxassetid://4583102108"
    v15.Parent = workspace
    v15:Play()
    game.Debris:AddItem(v15, 5)
    game.Debris:AddItem(v13, 10)
end
local function u50() --[[Anonymous function at line 85]]
    --[[
    Upvalues:
        [1] = u4
        [2] = u7
        [3] = u6
        [4] = u5
        [5] = u2
        [6] = u16
    --]]
    u4 = true
    game.TweenService:Create(u7, TweenInfo.new(2), {
        ["Brightness"] = 0.1
    }):Play()
    game.TweenService:Create(game.Lighting, TweenInfo.new(2), {
        ["Ambient"] = Color3.fromRGB(60, 162, 230),
        ["Brightness"] = 1
    }):Play()
    game.TweenService:Create(game.Lighting.Atmosphere, TweenInfo.new(2), {
        ["Density"] = 0.436,
        ["Offset"] = 1,
        ["Color"] = Color3.fromRGB(215, 244, 255),
        ["Decay"] = Color3.fromRGB(190, 238, 255)
    }):Play()
    u6.UpdateSkybox(u5, 2)
    local u17 = workspace.Visuals:WaitForChild("DJJhai")
    local u18 = u17:GetPivot()
    local u19 = u18 * CFrame.new(0, 85, 0)
    local v20 = u17:WaitForChild("jhailatte"):WaitForChild("Humanoid"):WaitForChild("Animator")
    local u21 = v20:LoadAnimation(script.Animation)
    u21:Play()
    local u22 = game.ReplicatedStorage.Assets.DanceFloor:Clone()
    local u23 = u22:GetPivot()
    u22.Parent = workspace
    local function u25() --[[Anonymous function at line 127]]
        --[[
        Upvalues:
            [1] = u22
        --]]
        for _, v24 in u22.Colour:GetChildren() do
            v24.Color = Color3.fromHSV(Random.new():NextNumber(0, 1), Random.new():NextNumber(0, 1), 1)
        end
    end
    task.spawn(function() --[[Anonymous function at line 133]]
        --[[
        Upvalues:
            [1] = u25
        --]]
        while workspace:GetAttribute("DJJhai") do
            u25()
            task.wait(1)
        end
    end)
    local v26 = 0
    while v26 < 2 do
        v26 = v26 + game:GetService("RunService").Heartbeat:Wait()
        local v27 = game.TweenService:GetValue(v26, Enum.EasingStyle.Quad, Enum.EasingDirection.InOut)
        u17:PivotTo(u18:Lerp(u19, v27))
        u22:PivotTo(u23:Lerp(u23 * CFrame.new(0, 2, 0), v27))
    end
    local u28 = Instance.new("Sound")
    local v29 = {
        104445637963020,
        5410086218,
        118939739460633,
        99445078556609,
        121336636707861
    }
    u28.SoundId = "rbxassetid://" .. v29[Random.new():NextInteger(1, #v29)]
    u28.Volume = 0.6
    u28.Looped = true
    u28.Parent = workspace
    u28.SoundGroup = game.SoundService.Music
    u28:Play()
    local u30 = v20:LoadAnimation(script.Up)
    local u31 = 0
    local u32 = false
    task.spawn(function() --[[Anonymous function at line 181]]
        --[[
        Upvalues:
            [1] = u28
            [2] = u32
            [3] = u17
            [4] = u2
            [5] = u31
            [6] = u30
            [7] = u7
            [8] = u16
        --]]
        local v33 = nil
        while true do
            task.wait(0)
            local v34 = (u28.PlaybackLoudness - 200) / 700
            local v35 = math.clamp(v34, 0, 1)
            if v33 and (u28.PlaybackLoudness / 1000 - v33 > 0.2 and not u32) then
                for _, v36 in u17.jhailatte.HumanoidRootPart.Attachment:GetChildren() do
                    v36:Emit(v36:GetAttribute("EmitCount"))
                end
                local _, v37 = u2:WorldToScreenPoint(u17:GetPivot().p)
                if v37 then
                    game.TweenService:Create(workspace.CurrentCamera, TweenInfo.new(0.2), {
                        ["FieldOfView"] = 68
                    }):Play()
                    task.delay(0.2, function() --[[Anonymous function at line 204]]
                        game.TweenService:Create(workspace.CurrentCamera, TweenInfo.new(0.3), {
                            ["FieldOfView"] = 70
                        }):Play()
                    end)
                end
            end
            v33 = u28.PlaybackLoudness / 1000
            if v35 > 0.45 then
                local u38 = tick()
                u31 = u38
                if not u32 then
                    u32 = true
                    u30:Play()
                    game.TweenService:Create(u7, TweenInfo.new(0.3), {
                        ["TintColor"] = Color3.fromRGB(226, 192, 255),
                        ["Brightness"] = 0.5
                    }):Play()
                    game.TweenService:Create(game.Workspace.CurrentCamera, TweenInfo.new(0.5), {
                        ["FieldOfView"] = 90
                    }):Play()
                    for _, u39 in u17.Pyro:GetDescendants() do
                        if u39:IsA("ParticleEmitter") then
                            u39.Enabled = true
                            task.delay(1.5, function() --[[Anonymous function at line 233]]
                                --[[
                                Upvalues:
                                    [1] = u39
                                --]]
                                u39.Enabled = false
                            end)
                        end
                    end
                    for _, v40 in u17.Confetti:GetDescendants() do
                        v40:Emit(v40:GetAttribute("EmitCount"))
                    end
                    task.spawn(function() --[[Anonymous function at line 243]]
                        --[[
                        Upvalues:
                            [1] = u17
                            [2] = u16
                        --]]
                        for _, u41 in u17.SpawnPoints:GetChildren() do
                            task.spawn(function() --[[Anonymous function at line 245]]
                                --[[
                                Upvalues:
                                    [1] = u16
                                    [2] = u41
                                --]]
                                u16(u41.CFrame)
                            end)
                            task.wait(0.1)
                        end
                    end)
                    task.delay(3, function() --[[Anonymous function at line 252]]
                        --[[
                        Upvalues:
                            [1] = u7
                        --]]
                        game.TweenService:Create(u7, TweenInfo.new(3), {
                            ["TintColor"] = Color3.fromRGB(255, 255, 255),
                            ["Brightness"] = 0.1
                        }):Play()
                    end)
                    task.spawn(function() --[[Anonymous function at line 256]]
                        --[[
                        Upvalues:
                            [1] = u32
                            [2] = u17
                            [3] = u2
                        --]]
                        while u32 do
                            for _, v42 in u17.jhailatte.HumanoidRootPart.Attachment:GetChildren() do
                                v42:Emit(v42:GetAttribute("EmitCount"))
                            end
                            local _, v43 = u2:WorldToScreenPoint(u17:GetPivot().p)
                            if v43 then
                                local v44 = {
                                    ["FieldOfView"] = 68
                                }
                                game.TweenService:Create(workspace.CurrentCamera, TweenInfo.new(0.2), v44):Play()
                                task.delay(0.2, function() --[[Anonymous function at line 266]]
                                    game.TweenService:Create(workspace.CurrentCamera, TweenInfo.new(0.3), {
                                        ["FieldOfView"] = 70
                                    }):Play()
                                end)
                            end
                            task.wait(0.5)
                        end
                    end)
                end
                task.delay(1, function() --[[Anonymous function at line 276]]
                    --[[
                    Upvalues:
                        [1] = u31
                        [2] = u38
                        [3] = u32
                        [4] = u30
                    --]]
                    if u31 == u38 then
                        u32 = false
                        u30:Stop()
                    end
                end)
            end
            for _, v45 in u17.DJBooth.Speakers:GetChildren() do
                if v45:IsA("BasePart") then
                    local v46 = v45.Mesh
                    local v47 = v35 * -1.4
                    v46.Offset = Vector3.new(0, v47, 0)
                    v45.Mesh.Scale = Vector3.new(1, 1, 1) * (v35 * 0.4 + 1)
                end
            end
        end
    end)
    task.spawn(function() --[[Anonymous function at line 296]]
        --[[
        Upvalues:
            [1] = u28
            [2] = u17
            [3] = u19
            [4] = u18
            [5] = u22
            [6] = u23
            [7] = u30
            [8] = u21
            [9] = u6
            [10] = u5
            [11] = u7
        --]]
        game.TweenService:Create(game.SoundService.Music.Tunes, TweenInfo.new(2), {
            ["Volume"] = 0
        }):Play()
        repeat
            task.wait()
        until not workspace:GetAttribute("DJJhai")
        game.TweenService:Create(u28, TweenInfo.new(1), {
            ["Volume"] = 0
        }):Play()
        task.delay(2, function() --[[Anonymous function at line 303]]
            --[[
            Upvalues:
                [1] = u28
            --]]
            u28:Destroy()
        end)
        local v48 = 0
        while v48 < 2 do
            v48 = v48 + game:GetService("RunService").Heartbeat:Wait()
            local v49 = game.TweenService:GetValue(v48, Enum.EasingStyle.Quad, Enum.EasingDirection.InOut)
            u17:PivotTo(u19:Lerp(u18, v49))
            u22:PivotTo((u23 * CFrame.new(0, 2, 0)):Lerp(u23, v49))
        end
        game.TweenService:Create(game.SoundService.Music.Tunes, TweenInfo.new(2), {
            ["Volume"] = 1
        }):Play()
        u22:Destroy()
        u30:Destroy()
        u21:Destroy()
        u17:Destroy()
        u6.UpdateSkybox(u5, 0)
        game.TweenService:Create(u7, TweenInfo.new(3), {
            ["Brightness"] = 0,
            ["TintColor"] = Color3.fromRGB(255, 255, 255)
        }):Play()
        game.TweenService:Create(game.Lighting, TweenInfo.new(3), {
            ["Ambient"] = game.Lighting:GetAttribute("DefaultAmbient"),
            ["ExposureCompensation"] = game.Lighting:GetAttribute("DefaultExposure"),
            ["Brightness"] = game.Lighting:GetAttribute("DefaultBrightness")
        }):Play()
        game.TweenService:Create(game.Lighting.Atmosphere, TweenInfo.new(3), {
            ["Density"] = 0,
            ["Offset"] = 0,
            ["Color"] = Color3.fromRGB(215, 244, 255),
            ["Decay"] = Color3.fromRGB(190, 238, 255)
        }):Play()
    end)
end
workspace:GetAttributeChangedSignal("DJJhai"):Connect(function() --[[Anonymous function at line 350]]
    --[[
    Upvalues:
        [1] = u50
        [2] = u4
    --]]
    if workspace:GetAttribute("DJJhai") then
        u50()
    else
        u4 = false
    end
end)
if workspace:GetAttribute("DJJhai") then
    task.defer(u50)
else
    u4 = false
end
return v1