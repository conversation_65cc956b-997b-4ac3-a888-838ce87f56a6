-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\ReplicationClass\TableListener\Signal\Deferred-ModuleScript.lua
local u1 = {}
u1.__index = u1
local u2 = {}
u2.__index = u2
function u1.new() --[[Anonymous function at line 50]]
    --[[
    Upvalues:
        [1] = u1
    --]]
    local v3 = u1
    return setmetatable({
        ["_active"] = true,
        ["_head"] = nil
    }, v3)
end
function u1.Is(p4) --[[Anonymous function at line 71]]
    --[[
    Upvalues:
        [1] = u1
    --]]
    local v5
    if typeof(p4) == "table" then
        v5 = getmetatable(p4) == u1
    else
        v5 = false
    end
    return v5
end
function u1.IsActive(p6) --[[Anonymous function at line 87]]
    return p6._active == true
end
function u1.Connect(p7, p8) --[[Anonymous function at line 108]]
    --[[
    Upvalues:
        [1] = u2
    --]]
    local v9 = typeof(p8) == "function"
    assert(v9, "Must be function")
    if p7._active ~= true then
        local v10 = u2
        return setmetatable({
            ["Connected"] = false,
            ["_node"] = nil
        }, v10)
    end
    local v11 = p7._head
    local v12 = {
        ["_signal"] = p7,
        ["_connection"] = nil,
        ["_handler"] = p8,
        ["_next"] = v11,
        ["_prev"] = nil
    }
    if v11 ~= nil then
        v11._prev = v12
    end
    p7._head = v12
    local v13 = u2
    local v14 = setmetatable({
        ["Connected"] = true,
        ["_node"] = v12
    }, v13)
    v12._connection = v14
    return v14
end
function u1.Once(p15, u16) --[[Anonymous function at line 169]]
    local v17 = typeof(u16) == "function"
    assert(v17, "Must be function")
    local u18 = nil
    u18 = p15:Connect(function(...) --[[Anonymous function at line 179]]
        --[[
        Upvalues:
            [1] = u18
            [2] = u16
        --]]
        if u18 ~= nil then
            u18:Disconnect()
            u18 = nil
            u16(...)
        end
    end)
    return u18
end
u1.ConnectOnce = u1.Once
function u1.Wait(p19) --[[Anonymous function at line 211]]
    local u20 = coroutine.running()
    local u21 = nil
    u21 = p19:Connect(function(...) --[[Anonymous function at line 216]]
        --[[
        Upvalues:
            [1] = u21
            [2] = u20
        --]]
        if u21 ~= nil then
            u21:Disconnect()
            u21 = nil
            task.spawn(u20, ...)
        end
    end)
    return coroutine.yield()
end
function u1.Fire(p22, ...) --[[Anonymous function at line 246]]
    local v23 = p22._head
    while v23 ~= nil do
        task.defer(v23._handler, ...)
        v23 = v23._next
    end
end
function u1.DisconnectAll(p24) --[[Anonymous function at line 266]]
    local v25 = p24._head
    while v25 ~= nil do
        local v26 = v25._connection
        if v26 ~= nil then
            v26.Connected = false
            v26._node = nil
            v25._connection = nil
        end
        v25 = v25._next
    end
    p24._head = nil
end
function u1.Destroy(p27) --[[Anonymous function at line 293]]
    if p27._active == true then
        p27:DisconnectAll()
        p27._active = false
    end
end
function u2.Disconnect(p28) --[[Anonymous function at line 314]]
    if p28.Connected == true then
        p28.Connected = false
        local v29 = p28._node
        local v30 = v29._prev
        local v31 = v29._next
        if v31 ~= nil then
            v31._prev = v30
        end
        if v30 == nil then
            v29._signal._head = v31
        else
            v30._next = v31
        end
        v29._connection = nil
        p28._node = nil
    end
end
u2.Destroy = u2.Disconnect
return u1