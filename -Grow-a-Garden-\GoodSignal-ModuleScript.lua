-- Full Path: -Grow-a-Garden-\\GoodSignal-ModuleScript.lua
local u1 = nil
local function u4(p2, ...) --[[Anonymous function at line 34]]
    --[[
    Upvalues:
        [1] = u1
    --]]
    local v3 = u1
    u1 = nil
    p2(...)
    u1 = v3
end
local function u5() --[[Anonymous function at line 45]]
    --[[
    Upvalues:
        [1] = u4
    --]]
    while true do
        u4(coroutine.yield())
    end
end
local u6 = {}
u6.__index = u6
function u6.new(p7, p8) --[[Anonymous function at line 60]]
    --[[
    Upvalues:
        [1] = u6
    --]]
    local v9 = u6
    return setmetatable({
        ["_connected"] = true,
        ["_signal"] = p7,
        ["_fn"] = p8,
        ["_next"] = false
    }, v9)
end
function u6.Disconnect(p10) --[[Anonymous function at line 69]]
    p10._connected = false
    local v11 = p10._signal
    v11._connectionCount = v11._connectionCount - 1
    if p10._signal._handlerListHead == p10 then
        p10._signal._handlerListHead = p10._next
    else
        local v12 = p10._signal._handlerListHead
        while v12 and v12._next ~= p10 do
            v12 = v12._next
        end
        if v12 then
            v12._next = p10._next
        end
    end
end
setmetatable(u6, {
    ["__index"] = function(_, p13) --[[Function name: __index, line 92]]
        error(("Attempt to get Connection::%s (not a valid member)"):format((tostring(p13))), 2)
    end,
    ["__newindex"] = function(_, p14, _) --[[Function name: __newindex, line 95]]
        error(("Attempt to set Connection::%s (not a valid member)"):format((tostring(p14))), 2)
    end
})
local u15 = {}
u15.__index = u15
function u15.new() --[[Anonymous function at line 104]]
    --[[
    Upvalues:
        [1] = u15
    --]]
    local v16 = u15
    return setmetatable({
        ["_handlerListHead"] = false,
        ["_connectionCount"] = 0
    }, v16)
end
function u15.Connect(p17, p18) --[[Anonymous function at line 111]]
    --[[
    Upvalues:
        [1] = u6
    --]]
    p17._connectionCount = p17._connectionCount + 1
    local v19 = u6.new(p17, p18)
    if not p17._handlerListHead then
        p17._handlerListHead = v19
        return v19
    end
    v19._next = p17._handlerListHead
    p17._handlerListHead = v19
    return v19
end
function u15.DisconnectAll(p20) --[[Anonymous function at line 125]]
    p20._connectionCount = 0
    p20._handlerListHead = false
end
function u15.Fire(p21, ...) --[[Anonymous function at line 134]]
    --[[
    Upvalues:
        [1] = u1
        [2] = u5
    --]]
    local v22 = p21._handlerListHead
    while v22 do
        if v22._connected then
            if not u1 then
                u1 = coroutine.create(u5)
                coroutine.resume(u1)
            end
            task.spawn(u1, v22._fn, ...)
        end
        v22 = v22._next
    end
end
function u15.Wait(p23) --[[Anonymous function at line 151]]
    local u24 = coroutine.running()
    local u25 = nil
    u25 = p23:Connect(function(...) --[[Anonymous function at line 154]]
        --[[
        Upvalues:
            [1] = u25
            [2] = u24
        --]]
        u25:Disconnect()
        task.spawn(u24, ...)
    end)
    return coroutine.yield()
end
function u15.Once(p26, u27) --[[Anonymous function at line 163]]
    local u28 = nil
    u28 = p26:Connect(function(...) --[[Anonymous function at line 165]]
        --[[
        Upvalues:
            [1] = u28
            [2] = u27
        --]]
        if u28._connected then
            u28:Disconnect()
        end
        u27(...)
    end)
    return u28
end
setmetatable(u15, {
    ["__index"] = function(_, p29) --[[Function name: __index, line 176]]
        error(("Attempt to get Signal::%s (not a valid member)"):format((tostring(p29))), 2)
    end,
    ["__newindex"] = function(_, p30, _) --[[Function name: __newindex, line 179]]
        error(("Attempt to set Signal::%s (not a valid member)"):format((tostring(p30))), 2)
    end
})
return u15