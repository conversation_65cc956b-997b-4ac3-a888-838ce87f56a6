-- Full Path: -Grow-a-Garden-\\Tips-ModuleScript.lua
return {
    {
        ["Message"] = "For each friend that is in your server, you get a extra 10% sheckles bonus.",
        ["Color"] = Color3.fromRGB(23, 255, 120)
    },
    {
        ["Message"] = "The shop restocks every 5 minutes.",
        ["Color"] = Color3.fromRGB(14, 211, 255)
    },
    {
        ["Message"] = "Join our communication server to report bugs, talk with other farmers and view sneakpeaks on updates!",
        ["Color"] = Color3.fromRGB(155, 148, 255)
    },
    {
        ["Message"] = "Using watering cans on crops speeds up their growth!",
        ["Color"] = Color3.fromRGB(255, 240, 50)
    },
    {
        ["Message"] = "Mutated versions of crops sell for more than regular crops!",
        ["Color"] = Color3.fromRGB(255, 85, 180)
    },
    {
        ["Message"] = "Weather events give a chance to give your crops custom mutations.",
        ["Color"] = Color3.fromRGB(255, 145, 35)
    },
    {
        ["Message"] = "Gear to help your garden can be purchased from the gear stand.",
        ["Color"] = Color3.fromRGB(100, 255, 200)
    },
    {
        ["Message"] = "Pets grow offline when fed!",
        ["Color"] = Color3.fromRGB(170, 85, 255)
    },
    {
        ["Message"] = "Easily collect lots of crops by holding the collect button.",
        ["Color"] = Color3.fromRGB(185, 155, 255)
    },
    {
        ["Message"] = "Right click to favorite plants. Tap and hold to favorite on Mobile.",
        ["Color"] = Color3.fromRGB(255, 61, 64)
    },
    {
        ["Message"] = "Favouriting items prevents them from being sold when you sell all!",
        ["Color"] = Color3.fromRGB(23, 255, 120)
    },
    {
        ["Message"] = "Hand in pets to the Pet NPC to unlock extra pet equip slots!",
        ["Color"] = Color3.fromRGB(255, 185, 85)
    }
}