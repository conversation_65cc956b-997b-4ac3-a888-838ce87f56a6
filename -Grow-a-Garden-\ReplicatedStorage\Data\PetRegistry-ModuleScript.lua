-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Data\PetRegistry-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage"):WaitF<PERSON><PERSON>hild("Assets")
v1:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("Models")
v1:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("Animations"):<PERSON><PERSON><PERSON><PERSON>hil<PERSON>("PetAnimations")
local v2 = require(script:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("PetConfig"))
local v3 = require(script:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("DefaultPetNames"))
local v4 = require(script:WaitF<PERSON><PERSON>hild("PetStatesRegistry"))
local v5 = require(script:WaitF<PERSON><PERSON>hild("PetRarities"))
local v6 = require(script:<PERSON><PERSON><PERSON><PERSON>hild("DefaultPetActions"))
local v7 = require(script:WaitFor<PERSON>hild("DefaultPetStates"))
return {
    ["PetStatesRegistry"] = v4,
    ["PetList"] = require(script:<PERSON><PERSON><PERSON><PERSON>hil<PERSON>("PetList")),
    ["PetConfig"] = v2,
    ["PassiveRegistry"] = require(script:Wait<PERSON><PERSON><PERSON>hil<PERSON>("PassiveRegistry")),
    ["PetEggs"] = require(script:<PERSON><PERSON><PERSON><PERSON>hil<PERSON>("PetEggs")),
    ["DefaultPetActions"] = v6,
    ["DefaultPetStates"] = v7,
    ["DefaultPetNames"] = v3,
    ["PetRarities"] = v5
}