-- Full Path: -Grow-a-Garden-\\CollectController-ModuleScript.lua
local u1 = game:GetService("ProximityPromptService")
game:GetService("CollectionService")
local v2 = game:GetService("ReplicatedStorage")
local u3 = game:GetService("UserInputService")
local v4 = game:GetService("RunService")
game:GetService("GuiService")
local v5 = game:GetService("Players")
local v6 = require(v2.Modules.Trove)
local u7 = require(v2.Modules.Remotes)
require(v2.Modules.Notification)
local u8 = require(v2.Modules.GetFarm)
local u9 = require(v2.Modules.InventoryService)
local u10 = v4:IsStudio() and false
local u11 = v5.LocalPlayer
local u12 = u11.PlayerGui
local u13 = u12:WaitForChild("HoldToCollect")
local _ = u11.Backpack
local u14 = {}
local u15 = v6.new()
local u21 = {
    ["_lastCollected"] = 0,
    ["_holding"] = false,
    ["_updateButtonState"] = function(_) --[[Function name: _updateButtonState, line 53]]
        --[[
        Upvalues:
            [1] = u13
            [2] = u14
        --]]
        u13.Collect.Visible = next(u14) ~= nil
    end,
    ["Collect"] = function(p16) --[[Function name: Collect, line 59]]
        --[[
        Upvalues:
            [1] = u15
            [2] = u14
            [3] = u7
        --]]
        local v17 = os.clock()
        u15:Clean()
        local _ = v17 - p16._lastCollected
        p16._lastCollected = v17
        local v18 = {}
        for u19, _ in u14 do
            u19:SetAttribute("Collected", true)
            task.delay(1, function() --[[Anonymous function at line 86]]
                --[[
                Upvalues:
                    [1] = u19
                --]]
                u19:SetAttribute("Collected", nil)
            end)
            local v20 = u19.Parent
            if v20 then
                v20 = u19.Parent.Parent
            end
            if v20 then
                table.insert(v18, v20)
            end
        end
        if #v18 > 0 then
            p16._lastCollected = v17
            u7.Crops.Collect.send(v18)
        end
    end
}
local u22 = require(game.ReplicatedStorage.Code.Queue)
local u23 = workspace.Terrain:WaitForChild("PickupParticle")
function u21.Start(u24) --[[Anonymous function at line 108]]
    --[[
    Upvalues:
        [1] = u3
        [2] = u10
        [3] = u13
        [4] = u1
        [5] = u14
        [6] = u12
        [7] = u11
        [8] = u8
        [9] = u22
        [10] = u9
        [11] = u23
    --]]
    local function v26() --[[Anonymous function at line 110]]
        --[[
        Upvalues:
            [1] = u3
            [2] = u10
            [3] = u13
        --]]
        local v25 = u3.TouchEnabled
        if v25 then
            v25 = u3:GetLastInputType() == Enum.UserInputType.Touch
        end
        u13.Enabled = v25 or u10
    end
    u3.LastInputTypeChanged:Connect(v26)
    local v27 = u3.TouchEnabled
    if v27 then
        v27 = u3:GetLastInputType() == Enum.UserInputType.Touch
    end
    u13.Enabled = v27 or u10
    task.delay(3, function() --[[Anonymous function at line 120]]
        --[[
        Upvalues:
            [1] = u3
            [2] = u10
            [3] = u13
        --]]
        local v28 = u3.TouchEnabled
        if v28 then
            v28 = u3:GetLastInputType() == Enum.UserInputType.Touch
        end
        u13.Enabled = v28 or u10
    end)
    local u29 = nil
    local u30 = 0
    local u31 = nil
    u1.PromptShown:Connect(function(p32) --[[Anonymous function at line 130]]
        --[[
        Upvalues:
            [1] = u14
            [2] = u24
            [3] = u31
        --]]
        if p32:HasTag("CollectPrompt") then
            u14[p32] = true
            u24:_updateButtonState()
        end
        u31 = p32
    end)
    u1.PromptHidden:Connect(function(p33) --[[Anonymous function at line 139]]
        --[[
        Upvalues:
            [1] = u14
            [2] = u24
            [3] = u31
        --]]
        if p33:HasTag("CollectPrompt") then
            u14[p33] = nil
            u24:_updateButtonState()
        end
        if u31 == p33 then
            u31 = nil
        end
    end)
    local function u40(_) --[[Anonymous function at line 150]]
        --[[
        Upvalues:
            [1] = u24
            [2] = u30
            [3] = u12
            [4] = u11
            [5] = u8
            [6] = u22
            [7] = u31
            [8] = u1
            [9] = u14
            [10] = u9
            [11] = u23
        --]]
        if u24._holding then
            return
        end
        u24._holding = true
        u30 = 0
        u12.PickupTally.TextLabel.val:SetAttribute("LastUpdate", os.time() - 5)
        u12.PickupTally.TextLabel.val:SetAttribute("ActualValue", 0)
        u11.PlayerScripts.CollectionQOL.Enabled = false
        local v34 = u8(u11)
        local u35 = u22.new()
        if u31 then
            u35:enqueue(u31)
        end
        u1.PromptShown:Connect(function(p36) --[[Anonymous function at line 178]]
            --[[
            Upvalues:
                [1] = u35
            --]]
            if not table.find(u35._queue, p36) then
                u35:enqueue(p36)
            end
        end)
        while true do
            while true do
                if not u24._holding then
                    u11.PlayerScripts.CollectionQOL.Enabled = true
                    return
                end
                if u35:isEmpty() then
                    break
                end
                local u37 = u35:dequeue()
                os.time()
                if u37 then
                    if u37:IsDescendantOf(v34) and (u37:HasTag("CollectPrompt") and (u37:GetAttribute("Collected") ~= true and (u37:IsDescendantOf(v34) and (u37:IsDescendantOf(workspace) and u37.Enabled)))) then
                        u37.Enabled = false
                        task.delay(1, function() --[[Anonymous function at line 221]]
                            --[[
                            Upvalues:
                                [1] = u37
                            --]]
                            if u37 and u37:IsDescendantOf(workspace) then
                                u37.Enabled = true
                            end
                        end)
                        u14[u37] = true
                        u24:_updateButtonState()
                        u24:Collect()
                        local v38 = u37.Parent
                        if v38 then
                            v38 = u37.Parent.Parent
                        end
                        if v38:FindFirstChild("Variant") then
                            if u9:IsMaxInventory(u11) == false then
                                local v39
                                if v38.Variant.Value == "Normal" then
                                    v39 = game.SoundService.Collect.Name
                                else
                                    v39 = v38.Variant.Value
                                end
                                game.ReplicatedStorage.GameEvents.PickupEvent:Fire(v39)
                                u23.WorldPosition = u37.Parent.Position
                                u23.ParticleEmitter:Emit(Random.new():NextInteger(1, 3))
                                u23.Gradient:Emit(1)
                            end
                            goto l11
                        end
                    end
                else
                    ::l11::
                    task.wait(0.125)
                end
            end
            task.wait(0.125)
        end
    end
    u1.PromptTriggered:Connect(function(p41) --[[Anonymous function at line 343]]
        --[[
        Upvalues:
            [1] = u3
            [2] = u40
        --]]
        if p41:HasTag("CollectPrompt") then
            if u3:IsKeyDown(Enum.KeyCode.E) or u3:IsGamepadButtonDown(Enum.UserInputType.Gamepad1, Enum.KeyCode.ButtonX) then
                local u42 = false
                local u43 = nil
                u43 = u3.InputEnded:Connect(function(p44) --[[Anonymous function at line 354]]
                    --[[
                    Upvalues:
                        [1] = u43
                        [2] = u42
                    --]]
                    if p44.KeyCode == Enum.KeyCode.E or p44.KeyCode == Enum.KeyCode.ButtonX then
                        u43:Disconnect()
                        u42 = true
                    end
                end)
                task.wait(1)
                if not u42 then
                    u43:Disconnect()
                    u40(false)
                end
            end
        else
            return
        end
    end)
    u3.InputBegan:Connect(function(p45, p46) --[[Anonymous function at line 369]]
        --[[
        Upvalues:
            [1] = u29
            [2] = u3
            [3] = u40
        --]]
        if not p46 and (p45.KeyCode == Enum.KeyCode.E or p45.KeyCode == Enum.KeyCode.ButtonX) then
            u29 = p45
            local u47 = false
            local u48 = nil
            u48 = u3.InputEnded:Connect(function(p49) --[[Anonymous function at line 376]]
                --[[
                Upvalues:
                    [1] = u48
                    [2] = u47
                --]]
                if p49.KeyCode == Enum.KeyCode.E or p49.KeyCode == Enum.KeyCode.ButtonX then
                    u48:Disconnect()
                    u47 = true
                end
            end)
            task.wait(1)
            if not u47 and (game:GetService("UserInputService"):IsGamepadButtonDown(Enum.UserInputType.Gamepad1, Enum.KeyCode.ButtonX) or game:GetService("UserInputService"):IsKeyDown(Enum.KeyCode.E)) then
                u48:Disconnect()
                u40(false)
            end
        end
    end)
    u24:_updateButtonState()
    u13.Collect.MouseButton1Down:Connect(function(_) --[[Anonymous function at line 396]]
        --[[
        Upvalues:
            [1] = u40
        --]]
        u40(true)
    end)
    u3.InputEnded:Connect(function(p50, _) --[[Anonymous function at line 400]]
        --[[
        Upvalues:
            [1] = u24
        --]]
        if p50.KeyCode == Enum.KeyCode.E or p50.KeyCode == Enum.KeyCode.ButtonX then
            u24._holding = false
        end
        if p50.UserInputType == Enum.UserInputType.Touch or p50.UserInputType == Enum.UserInputType.MouseButton1 then
            u24._holding = false
        end
    end)
    u3.WindowFocusReleased:Connect(function() --[[Anonymous function at line 410]]
        --[[
        Upvalues:
            [1] = u24
        --]]
        u24._holding = false
    end)
    u12.PickupTally.Enabled = true
end
task.spawn(function() --[[Anonymous function at line 424]]
    --[[
    Upvalues:
        [1] = u21
    --]]
    while true do
        task.wait(2)
        u21:_updateButtonState()
    end
end)
task.spawn(u21.Start, u21)
return u21