-- Full Path: -Grow-a-Garden-\\PetActionUserInterfaceService-ModuleScript.lua
game:GetService("Selection")
local v1 = game:GetService("Players")
local v2 = game:GetService("ReplicatedStorage")
local u3 = game:GetService("TweenService")
local u4 = game:GetService("CollectionService")
local v5 = game:GetService("RunService")
local u6 = game:GetService("UserInputService")
local u7 = game:GetService("GuiService")
local u8 = workspace.CurrentCamera
workspace:GetPropertyChangedSignal("CurrentCamera"):Connect(function() --[[Anonymous function at line 12]]
    --[[
    Upvalues:
        [1] = u8
    --]]
    u8 = workspace.CurrentCamera
end)
local v9 = v2.Modules
local v10 = require(v9.WaitForDescendant)
local v11 = v9:WaitForChild("PetServices")
local u12 = require(v11.ActivePetsService)
local v13 = require(v2.Data.PetRegistry)
local u14 = v13.PetList
local u15 = v13.DefaultPetActions
local u16 = v13.PetStatesRegistry
local u17 = v1.LocalPlayer
local u18 = u17:WaitForChild("PlayerGui")
local u19 = u18:WaitForChild("PetUI"):WaitForChild("PetActionUI")
local u20 = u19:WaitForChild("UIScale")
local u21 = v10(u19, "CLOSE_BUTTON")
local u22 = v10(u19, "OPTION_HOLDER")
local u23 = script:WaitForChild("PetActionsHandlers")
local u24 = script:WaitForChild("PetActionTemplates")
local v25 = script:WaitForChild("ScriptModules")
local u26 = require(v2.Modules.SetupHoverAnimations)
local u27 = require(v2.Modules.SetupBrightnessAnimationImage)
local u28 = require(v25:WaitForChild("Radialize"))
local u29 = require(v2.Modules.PlayHoverSound)
local u30 = require(v2.Modules.PlayClickSound)
local u31 = {
    ["Active"] = false,
    ["Target"] = nil,
    ["LastStateBeforeInspection"] = nil
}
local u32 = nil
local function u37() --[[Anonymous function at line 58]]
    --[[
    Upvalues:
        [1] = u4
        [2] = u6
        [3] = u7
        [4] = u18
    --]]
    local v33 = u4:GetTagged("PetActionButton")
    local v34 = u6:GetMouseLocation() - u7:GetGuiInset()
    local v35 = u18:GetGuiObjectsAtPosition(v34.X, v34.Y)
    for _, v36 in v33 do
        if v36:IsDescendantOf(u18) and table.find(v35, v36) then
            return true
        end
    end
    return false
end
local u38 = require(game.ReplicatedStorage.Frame_Popup_Module)
local u39 = u18.ConfirmPetEggPurchase
local function u51(p40) --[[Anonymous function at line 81]]
    --[[
    Upvalues:
        [1] = u24
        [2] = u23
        [3] = u31
        [4] = u22
        [5] = u26
        [6] = u27
        [7] = u38
        [8] = u39
        [9] = u17
        [10] = u30
        [11] = u29
    --]]
    local v41 = u24:FindFirstChild(p40)
    local v42 = u23:FindFirstChild(p40)
    if v42 then
        local u43 = require(v42)
        local v44 = u43.Verifier
        if not v44 or v44(u31.Target) then
            local v45 = v41:Clone()
            v45.Parent = u22
            local v46 = v45:FindFirstChild("Inner")
            local v47 = u26(v46)
            u27(v46)
            v47.ZIndex = 1000
            local function u49() --[[Anonymous function at line 101]]
                --[[
                Upvalues:
                    [1] = u43
                    [2] = u31
                    [3] = u38
                    [4] = u39
                    [5] = u17
                    [6] = u30
                --]]
                local v48 = u43.Activate
                if v48 then
                    v48(u31.Target)
                end
                u38.Hide(u39)
                u31:Toggle(false)
                u17:SetAttribute("DontOpenAction", true)
                task.delay(0.3, function() --[[Anonymous function at line 113]]
                    --[[
                    Upvalues:
                        [1] = u17
                    --]]
                    u17:SetAttribute("DontOpenAction", false)
                end)
                u30()
            end
            v47.MouseButton1Down:Connect(u49)
            v47.InputBegan:Connect(function(p50) --[[Anonymous function at line 121]]
                --[[
                Upvalues:
                    [1] = u49
                --]]
                if p50.UserInputState == Enum.UserInputState.Begin and p50.KeyCode == Enum.KeyCode.ButtonR2 then
                    u49()
                end
            end)
            v47.MouseEnter:Connect(function() --[[Anonymous function at line 126]]
                --[[
                Upvalues:
                    [1] = u29
                --]]
                u29()
            end)
        end
    else
        return warn("No handler for", p40)
    end
end
function u31.SetTarget(_, p52) --[[Anonymous function at line 131]]
    --[[
    Upvalues:
        [1] = u32
        [2] = u31
        [3] = u22
        [4] = u12
        [5] = u14
        [6] = u51
        [7] = u15
        [8] = u28
    --]]
    u32 = nil
    if u31.Target == p52 then
        return
    else
        u31.Target = p52
        if p52 then
            for _, v53 in u22:GetChildren() do
                if v53:HasTag("PetActionButton") then
                    v53:Destroy()
                end
            end
            local v54 = u12:GetPetDataFromPetObject(p52)
            if v54 then
                for _, v55 in u14[v54.PetType].Actions do
                    u51(v55)
                end
                for _, v56 in u15 do
                    u51(v56)
                end
                u28(u22)
            end
        else
            return
        end
    end
end
local u57 = nil
function u31.Toggle(_, p58) --[[Anonymous function at line 167]]
    --[[
    Upvalues:
        [1] = u31
        [2] = u3
        [3] = u20
        [4] = u12
        [5] = u16
        [6] = u57
        [7] = u21
        [8] = u6
    --]]
    if u31.Active ~= p58 then
        u31.Active = p58
        u3:Create(u20, TweenInfo.new(0.1), {
            ["Scale"] = p58 and 1 or 0
        }):Play()
        local u59 = u31.Target
        if u59 then
            local v60 = u59:GetAttribute("UUID")
            local v61 = u59:GetAttribute("OWNER")
            if p58 then
                u31.LastStateBeforeInspection = u16[u12:GetServerState(v61, v60).CurrentState]
                u57 = tick()
                game.Workspace:SetAttribute("LastMenu", u57)
                game:GetService("GamepadService"):EnableGamepadCursor(u21)
                if u6.GamepadEnabled and u6:GetLastInputType() == Enum.UserInputType.Gamepad1 then
                    game:GetService("RunService"):BindToRenderStep("LockOn", Enum.RenderPriority.Camera.Value + 1, function(p62) --[[Anonymous function at line 194]]
                        --[[
                        Upvalues:
                            [1] = u59
                        --]]
                        workspace.CurrentCamera.CFrame = workspace.CurrentCamera.CFrame:Lerp(CFrame.new(workspace.CurrentCamera.CFrame.Position, u59:GetPivot().Position), p62 * 2)
                    end)
                    return
                end
            elseif not p58 then
                u31.Target = nil
                if u31.LastStateBeforeInspection then
                    if game.Workspace:GetAttribute("LastMenu") == u57 then
                        game:GetService("GamepadService"):DisableGamepadCursor()
                    end
                    task.spawn(function() --[[Anonymous function at line 213]]
                        game:GetService("RunService"):UnbindFromRenderStep("LockOn")
                    end)
                end
            end
        end
    end
end
v5.RenderStepped:Connect(function() --[[Anonymous function at line 228]]
    --[[
    Upvalues:
        [1] = u31
        [2] = u8
        [3] = u19
    --]]
    if u31.Active then
        if u31.Target then
            local v63 = u31.Target.Position
            local v64 = 2.5 + u31.Target.Size.Y / 2
            local v65 = v63 + Vector3.new(0, v64, 0)
            local v66, v67
            if v65 then
                local v68
                v68, v66 = u8:WorldToScreenPoint(v65)
                v67 = UDim2.fromOffset(v68.X, v68.Y)
            else
                v66 = nil
                v67 = nil
            end
            u19.Position = v66 and v67 and v67 or UDim2.fromScale(2, 2)
        else
            u31:Toggle(false)
        end
    else
        return
    end
end)
u26(u21).MouseButton1Down:Connect(function() --[[Anonymous function at line 238]]
    --[[
    Upvalues:
        [1] = u31
        [2] = u17
    --]]
    u31:Toggle(false)
    u17:SetAttribute("DontOpenAction", true)
    task.delay(0.3, function() --[[Anonymous function at line 244]]
        --[[
        Upvalues:
            [1] = u17
        --]]
        u17:SetAttribute("DontOpenAction", false)
    end)
end)
local function u69() --[[Anonymous function at line 249]]
    --[[
    Upvalues:
        [1] = u37
        [2] = u31
        [3] = u17
    --]]
    if not u37() then
        u31:Toggle(false)
        u17:SetAttribute("DontOpenAction", true)
        task.delay(0.3, function() --[[Anonymous function at line 255]]
            --[[
            Upvalues:
                [1] = u17
            --]]
            u17:SetAttribute("DontOpenAction", false)
        end)
    end
end
local u70 = {
    [Enum.UserInputType.MouseButton1] = {
        ["Activate"] = function() --[[Anonymous function at line 262]] end
    }
}
local u71 = {
    [Enum.UserInputType.MouseButton1] = {
        ["Deactivate"] = function() --[[Anonymous function at line 270]]
            --[[
            Upvalues:
                [1] = u69
            --]]
            u69()
        end
    }
}
u6.InputBegan:Connect(function(p72, p73) --[[Anonymous function at line 276]]
    --[[
    Upvalues:
        [1] = u70
    --]]
    if p73 then
        return
    else
        local v74 = u70[p72.KeyCode] or u70[p72.UserInputType]
        if v74 then
            for _, v75 in v74 do
                task.spawn(v75)
            end
        end
    end
end)
u6.InputEnded:Connect(function(p76, p77) --[[Anonymous function at line 287]]
    --[[
    Upvalues:
        [1] = u71
    --]]
    if p77 then
        return
    else
        local v78 = u71[p76.KeyCode] or u71[p76.UserInputType]
        if v78 then
            for _, v79 in v78 do
                task.spawn(v79)
            end
        end
    end
end)
u6.TouchTap:Connect(function() --[[Anonymous function at line 297]]
    --[[
    Upvalues:
        [1] = u69
    --]]
    u69()
end)
return u31