-- Full Path: -Grow-a-Garden-\\BloodMoon-ModuleScript.lua
local v1 = {}
local _ = workspace.CurrentCamera
local u2 = require(game.ReplicatedStorage.Modules.SkyboxManager)
local u3 = script.Sky
u2.AddSkybox(u3)
local u4 = script.Ambience:Clone()
local u5 = false
u3:GetPropertyChangedSignal("Parent"):Connect(function() --[[Anonymous function at line 20]]
    print("Test")
end)
local _ = game.SoundService.Blood_Moon_SFX
local function u7(p6) --[[Anonymous function at line 26]]
    --[[
    Upvalues:
        [1] = u5
        [2] = u2
        [3] = u3
        [4] = u4
    --]]
    if u5 == true then
        return
    else
        u5 = true
        if p6 then
            game.Lighting.ClockTime = 16
            game.TweenService:Create(game.Lighting, TweenInfo.new(0.1), {
                ["Ambient"] = Color3.fromRGB(138, 107, 107),
                ["ExposureCompensation"] = 1,
                ["Brightness"] = 0.6,
                ["OutdoorAmbient"] = Color3.fromRGB(62, 16, 16)
            }):Play()
            game.TweenService:Create(game.Lighting.NightColor, TweenInfo.new(0.1), {
                ["Brightness"] = 0.05,
                ["TintColor"] = Color3.fromRGB(255, 226, 226),
                ["Contrast"] = 0.1
            }):Play()
            game.TweenService:Create(game.Lighting.Atmosphere, TweenInfo.new(0.1), {
                ["Density"] = 0.357,
                ["Offset"] = 0.721
            }):Play()
            game.TweenService:Create(game.Workspace.Terrain.Clouds, TweenInfo.new(0.1), {
                ["Cover"] = 0.657,
                ["Density"] = 1,
                ["Color"] = Color3.fromRGB(45, 1, 1)
            }):Play()
            game.Lighting.SunRays.Intensity = 0.028
            u2.UpdateSkybox(u3, 4)
            u4.Parent = workspace
            u4:Play()
            u4.Volume = 0.1
        else
            game.TweenService:Create(game.Lighting, TweenInfo.new(5), {
                ["ClockTime"] = 21
            }):Play()
            task.wait(5)
            u2.UpdateSkybox(u3, 4)
            game.Lighting.ClockTime = 3
            game.TweenService:Create(game.Lighting, TweenInfo.new(5), {
                ["Ambient"] = Color3.fromRGB(138, 107, 107),
                ["ExposureCompensation"] = 0.6,
                ["Brightness"] = 0.6,
                ["ClockTime"] = 16,
                ["OutdoorAmbient"] = Color3.fromRGB(62, 16, 16)
            }):Play()
            game.Lighting:SetAttribute("DefaultAmbient", Color3.fromRGB(138, 107, 107))
            game.Lighting:SetAttribute("DefaultExposure", 1)
            game.Lighting:SetAttribute("DefaultBrightness", 0.6)
            game.TweenService:Create(game.Lighting.Atmosphere, TweenInfo.new(5), {
                ["Density"] = 0.357,
                ["Offset"] = 0.721
            }):Play()
            game.TweenService:Create(game.Lighting.NightColor, TweenInfo.new(5), {
                ["Brightness"] = 0.05,
                ["TintColor"] = Color3.fromRGB(255, 203, 158),
                ["Contrast"] = 0.1
            }):Play()
            game.Lighting.SunRays.Enabled = true
            u4.Parent = workspace
            u4:Play()
            game.TweenService:Create(u4, TweenInfo.new(1), {
                ["Volume"] = 0.1
            }):Play()
            game.TweenService:Create(game.Workspace.Terrain.Clouds, TweenInfo.new(5), {
                ["Cover"] = 0.657,
                ["Density"] = 1,
                ["Color"] = Color3.fromRGB(45, 1, 1)
            }):Play()
            task.delay(5, function() --[[Anonymous function at line 112]]
                game.TweenService:Create(game.Lighting.SunRays, TweenInfo.new(2), {
                    ["Intensity"] = 0.01
                }):Play()
            end)
        end
    end
end
local function u9(p8) --[[Anonymous function at line 122]]
    --[[
    Upvalues:
        [1] = u5
        [2] = u4
        [3] = u2
        [4] = u3
    --]]
    if u5 == false then
        return
    else
        u5 = false
        if not p8 then
            game.TweenService:Create(game.Lighting, TweenInfo.new(5), {
                ["ClockTime"] = 21
            }):Play()
            game.TweenService:Create(game.Lighting.NightColor, TweenInfo.new(5), {
                ["Brightness"] = 0,
                ["Contrast"] = 0,
                ["TintColor"] = Color3.fromRGB(255, 255, 255)
            }):Play()
            game.TweenService:Create(game.Lighting.SunRays, TweenInfo.new(2), {
                ["Intensity"] = 0
            }):Play()
            game.TweenService:Create(game.Lighting.Atmosphere, TweenInfo.new(5), {
                ["Density"] = 0,
                ["Offset"] = 0
            }):Play()
            game.TweenService:Create(game.Workspace.Terrain.Clouds, TweenInfo.new(5), {
                ["Cover"] = 0,
                ["Density"] = 0,
                ["Color"] = Color3.fromRGB(22, 40, 70)
            }):Play()
            task.wait(5)
            game.TweenService:Create(u4, TweenInfo.new(1), {
                ["Volume"] = 0
            }):Play()
            task.delay(1, function() --[[Anonymous function at line 161]]
                --[[
                Upvalues:
                    [1] = u4
                --]]
                u4.Parent = script
                u4:Stop()
            end)
            u2.UpdateSkybox(u3, 0)
            game.Lighting.ClockTime = 3
            game.Lighting:SetAttribute("DefaultAmbient", Color3.fromRGB(138, 138, 138))
            game.Lighting:SetAttribute("DefaultExposure", 0.2)
            game.Lighting:SetAttribute("DefaultBrightness", 2)
            game.TweenService:Create(game.Lighting, TweenInfo.new(5), {
                ["Ambient"] = Color3.fromRGB(138, 138, 138),
                ["ExposureCompensation"] = 0.2,
                ["Brightness"] = 2,
                ["ClockTime"] = 14,
                ["OutdoorAmbient"] = Color3.fromRGB(128, 128, 128)
            }):Play()
            task.delay(6, function() --[[Anonymous function at line 183]]
                game.Lighting.SunRays.Enabled = false
            end)
        end
    end
end
workspace:GetAttributeChangedSignal("BloodMoonEvent"):Connect(function() --[[Anonymous function at line 191]]
    --[[
    Upvalues:
        [1] = u7
        [2] = u9
    --]]
    if workspace:GetAttribute("BloodMoonEvent") then
        u7(workspace:GetAttribute("Instant"))
    else
        u9()
    end
end)
if workspace:GetAttribute("BloodMoonEvent") then
    task.defer(function() --[[Anonymous function at line 203]]
        --[[
        Upvalues:
            [1] = u7
        --]]
        u7(true)
    end)
else
    u9(true)
end
return v1