-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\CosmeticServices\UserInterface\CosmeticMovementUserInterfaceService-ModuleScript.lua
local v1 = game:GetService("Players")
local v2 = game:GetService("RunService")
local u3 = game:GetService("UserInputService")
local u4 = game:GetService("GuiService")
local v5 = game:GetService("ReplicatedStorage")
local v6 = v1.LocalPlayer
local u7 = require(v5.Modules.Fingers)
local u8 = v6:WaitForChild("PlayerGui"):WaitForChild("CosmeticUI"):WaitForChild("MovementIcon")
u8.Visible = false
local u9 = {
    ["Active"] = false
}
local u10 = nil
function u9.Toggle(_, p11) --[[Anonymous function at line 31]]
    --[[
    Upvalues:
        [1] = u10
        [2] = u7
        [3] = u9
        [4] = u3
        [5] = u4
        [6] = u8
    --]]
    u10 = u7:GetOne()
    u9.Active = p11
    local v12 = u3:GetMouseLocation()
    local v13 = u4:GetGuiInset()
    local _ = (FingerPosition or v12).X
    u8.Position = UDim2.fromOffset(v12.X + v13.X, v12.Y - v13.Y)
    u8.Visible = p11
end
v2.RenderStepped:Connect(function() --[[Anonymous function at line 38]]
    --[[
    Upvalues:
        [1] = u9
        [2] = u3
        [3] = u4
        [4] = u8
    --]]
    if u9.Active then
        local v14 = u3:GetMouseLocation()
        local v15 = u4:GetGuiInset()
        local _ = (FingerPosition or v14).X
        u8.Position = UDim2.fromOffset(v14.X + v15.X, v14.Y - v15.Y)
    end
end)
return u9