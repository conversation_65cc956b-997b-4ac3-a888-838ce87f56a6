-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\PetServices\PetUtilities-ModuleScript.lua
local u1 = game:GetService("ReplicatedStorage")
local u2 = game:GetService("Debris")
local u3 = game:GetService("RunService")
local u4 = game:GetService("ServerScriptService")
local v5 = u1.Assets.VFX
local v6 = require(u1.Data.PetRegistry)
local u7 = require(u1.Modules.CalculatePlantValue)
local u8 = require(u1.Modules.TimeHelper)
local u9 = v5.SpottedEffect
local v10 = v6.PetConfig
local u11 = v6.PetList
local v12 = v6.PetEggs
local u13 = v6.PassiveRegistry
local v14 = v10.XP_CONFIG
local _ = v14.XP_EXPONENTIAL
local u15 = v10.PET_FEEDING_CONFIG.HUNGER_PER_WEIGHT
local u16 = v10.WEIGHT_CONFIG.SCALE_PERCENTAGE_OF_BASE_WEIGHT_PER_LEVEL
local v17 = require(u1.Modules.ExponentialScaler)
local u18 = require(u1.Data.DecimalNumberFormat)
local u19 = require(u1.Modules.ReplicationClass.DeepClone)
local u20 = v17.new({
    ["BaseCost"] = v14.XP_BASE_COST,
    ["ScalingFactor"] = v14.XP_EXPONENTIAL
})
local u21 = 0
for v22 = 1, v14.MAX_LEVEL do
    u21 = u21 + u20:GetCost(v22)
end
local u34 = {
    ["GetCurrentLevelXPCost"] = function(_, p23) --[[Function name: GetCurrentLevelXPCost, line 47]]
        --[[
        Upvalues:
            [1] = u20
        --]]
        return u20:GetCost(p23)
    end,
    ["GetCurrentLevelState"] = function(_, p24, p25) --[[Function name: GetCurrentLevelState, line 58]]
        --[[
        Upvalues:
            [1] = u13
        --]]
        local v26 = u13[p25]
        if not v26 then
            warn("Could not find passive", p25)
            return {}
        end
        local v27 = {}
        for v28, v29 in v26.States do
            local v30 = v29.Min
            local v31 = v29.Max
            local v32 = v29.Scale * p24
            local v33 = v29.Base + v32
            if v30 and v31 then
                v33 = math.clamp(v33, v30, v31)
            elseif v30 then
                v33 = math.max(v33, v30)
            elseif v31 then
                v33 = math.min(v33, v31)
            end
            if v28 == "Cooldown" then
                v33 = v33 * 1 or v33
            end
            v27[v28] = v33
        end
        return v27
    end
}
local u36 = {
    ["ColonTime"] = function(p35) --[[Anonymous function at line 88]]
        --[[
        Upvalues:
            [1] = u8
        --]]
        return u8:GenerateColonFormatFromTime(p35)
    end
}
function u34.GetPassiveString(_, p37, p38) --[[Anonymous function at line 94]]
    --[[
    Upvalues:
        [1] = u34
        [2] = u13
        [3] = u36
        [4] = u18
    --]]
    local v39 = u34:GetCurrentLevelState(p37, p38)
    local v40 = u13[p38]
    local v41 = v40.Description
    local v42 = v40.States
    for v43, v44 in v39 do
        local v45 = u36[v42[v43].Formatter]
        local v46 = v45 and v45(v44)
        if not v46 then
            local v47 = u18
            v46 = tostring(v47(v44))
        end
        v41 = v41:gsub(("<%*>"):format(v43), v46)
    end
    return v41
end
function u34.GetFruitWorthForAnimal(_, p48, p49) --[[Anonymous function at line 114]]
    --[[
    Upvalues:
        [1] = u11
        [2] = u7
        [3] = u15
    --]]
    local v50 = u11[p48].HungerFruitMultipliers[p49:GetAttribute("ItemName")] or 1
    return u7(p49) * u15 * v50
end
function u34.GetFruitWorthPercentageForAnimal(_, p51, p52) --[[Anonymous function at line 126]]
    --[[
    Upvalues:
        [1] = u34
        [2] = u11
    --]]
    return u34:GetFruitWorthForAnimal(p51, p52) / u11[p51].DefaultHunger
end
function u34.NormalizeOdds(_, p53) --[[Anonymous function at line 135]]
    local v54 = p53.Items
    local v55 = 0
    for _, v56 in v54 do
        v55 = v55 + v56.ItemOdd
    end
    for v57, v58 in v54 do
        v58.NormalizedOdd = v58.ItemOdd / v55 * 100
        v58.Name = v57
    end
    p53.TotalOdds = v55
    return p53
end
function u34.CalculateWeight(_, p59, p60) --[[Anonymous function at line 154]]
    --[[
    Upvalues:
        [1] = u16
    --]]
    return p59 + p59 * u16 * p60
end
function u34.GetLevelProgress(_, p61) --[[Anonymous function at line 163]]
    --[[
    Upvalues:
        [1] = u20
        [2] = u21
    --]]
    if not p61 then
        warn("PetUtilities:GetLevelProgress | No Level!")
        return 0
    end
    local v62 = 0
    for v63 = 1, p61 do
        v62 = v62 + u20:GetCost(v63)
    end
    return v62 / u21
end
for _, v64 in v12 do
    v64.Color = v64.Color or Color3.fromRGB(255, 252, 252)
    v64.HatchTime = v64.HatchTime or 6
    v64.RarityData = v64.RarityData or {
        ["Items"] = {}
    }
    u34:NormalizeOdds(v64.RarityData)
end
function u34.GetRandomPet(_, p65) --[[Anonymous function at line 186]]
    if not (p65 and (p65.TotalOdds and p65.Items)) then
        return warn((("GetRandomPet | %* is nil or invalid!"):format(p65)))
    end
    local v66 = p65.TotalOdds
    local v67 = p65.Items
    local v68 = math.random() * v66
    local v69 = 0
    for _, v70 in v67 do
        v69 = v69 + v70.ItemOdd
        if v69 >= v68 then
            return v70
        end
    end
end
function u34.GetLookAt(_, p71, p72) --[[Anonymous function at line 205]]
    local v73 = CFrame.new
    local v74 = p72.X
    local v75 = p71.Y
    local v76 = p72.Z
    return v73(p71, (Vector3.new(v74, v75, v76)))
end
function u34.GetGeneratedEclipsePositions(_, p77) --[[Anonymous function at line 209]]
    local v78 = p77.TargetCFrame
    if not v78 then
        return warn("GetGeneratedEclipsePositions: There is no TargetCFrame in Config:", p77, debug.traceback())
    end
    local v79 = p77.TotalPositions
    if not v79 then
        return warn("GetGeneratedEclipsePositions: There is no TotalPositions in Config:", p77, debug.traceback())
    end
    local v80 = p77.StartingRowAmount or 5
    local v81 = p77.RadiusIncrementX or 3
    local v82 = p77.RadiusIncrementZ or 3
    local v83 = p77.Angle or 0.39269908169872414
    local v84 = {}
    local v85 = 1
    while #v84 < v79 do
        local v86 = v79 - #v84
        local v87 = math.min(v80, v86)
        local v88 = v87 == 1 and 0 or v83 * (v87 - 1) / 2
        for v89 = 1, v87 do
            local v90 = v83 * (v89 - 1) - v88
            local v91 = math.sin(v90) * v85 * v81
            local v92 = math.cos(v90) * v85 * v82
            local v93 = (v78 * CFrame.new(v91, 0, v92)).Position
            table.insert(v84, v93)
        end
        v85 = v85 + 1
        v80 = v80 + 1
    end
    return v84
end
function u34.GetPetsSortedByAge(_, p94, p95, p96, p97) --[[Anonymous function at line 250]]
    --[[
    Upvalues:
        [1] = u3
        [2] = u4
        [3] = u19
        [4] = u1
    --]]
    local v98 = p96 or false
    local v99 = p97 or false
    local v100 = p95 or 0
    if not u3:IsServer() then
        if not u3:IsClient() then
            return
        end
        local v101 = require(u1.Modules.PetServices.ActivePetsService):GetPlayerDatastorePetData(p94.Name)
        local v102 = v101.PetInventory.Data
        local v103 = v101.EquippedPets
        local v104 = {}
        for v105, v106 in v102 do
            if v106.PetData.Level >= v100 then
                if v99 then
                    if table.find(v103, v105) then
                        ::l22::
                        local v107 = u19(v106)
                        v107.UUID = v105
                        table.insert(v104, v107)
                    end
                elseif not (v98 and table.find(v103, v105)) then
                    goto l22
                end
            end
        end
        table.sort(v104, function(p108, p109) --[[Anonymous function at line 314]]
            return p108.PetData.Level > p109.PetData.Level
        end)
        return v104
    end
    local v110 = require(u4.Modules.DataService):GetPlayerDataAsync(p94)
    if not v110 then
        return
    end
    local v111 = v110.PetsData.PetInventory.Data
    local v112 = v110.PetsData.EquippedPets
    local v113 = {}
    for v114, v115 in v111 do
        if v115.PetData.Level >= v100 then
            if v99 then
                if table.find(v112, v114) then
                    ::l11::
                    local v116 = u19(v115)
                    v116.UUID = v114
                    table.insert(v113, v116)
                end
            elseif not (v98 and table.find(v112, v114)) then
                goto l11
            end
        end
    end
    table.sort(v113, function(p117, p118) --[[Anonymous function at line 282]]
        return p117.PetData.Level > p118.PetData.Level
    end)
    return v113
end
function u34.AbilityFX(_, p119, p120) --[[Anonymous function at line 323]]
    --[[
    Upvalues:
        [1] = u3
        [2] = u9
        [3] = u2
    --]]
    local v121
    if u3:IsServer() then
        v121 = require(game.ServerScriptService.Modules.PetsServices.ActivePetsService)
    else
        v121 = require(game.ReplicatedStorage.Modules.PetServices.ActivePetsService)
    end
    local v122 = u9:Clone()
    v122.CFrame = CFrame.new(v121:GetRealPosition(p119, p120)) * CFrame.new(0, 3, 0)
    v122.Parent = workspace
    v122.SpottedFX.SpotParticle:Emit(1)
    u2:AddItem(v122, 3)
end
return u34