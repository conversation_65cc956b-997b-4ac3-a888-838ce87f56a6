-- Full Path: -Grow-a-Garden-\\GuiController-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
local u2 = game:GetService("GamepadService")
game:GetService("SoundService")
local v3 = game:GetService("Players")
local u4 = require(v1.Modules.Signal)
require(v1.Modules.FastTween)
local u5 = require(v1.Frame_Popup_Module)
local _ = v3.LocalPlayer.PlayerGui
local u6 = {}
local u7 = {}
local function u17(p8) --[[Anonymous function at line 27]]
    --[[
    Upvalues:
        [1] = u4
    --]]
    local u9 = u4.new()
    local u16 = {
        ["_value"] = p8,
        ["_signal"] = u9,
        ["Get"] = function(_) --[[Function name: Get, line 34]]
            --[[
            Upvalues:
                [1] = u16
            --]]
            return u16._value
        end,
        ["Set"] = function(_, p10) --[[Function name: Set, line 38]]
            --[[
            Upvalues:
                [1] = u16
                [2] = u9
            --]]
            local v11 = u16._value
            u16._value = p10
            u9:Fire(p10, v11)
        end,
        ["Listen"] = function(_, p12) --[[Function name: Listen, line 44]]
            --[[
            Upvalues:
                [1] = u9
            --]]
            return u9:Connect(p12)
        end,
        ["Observe"] = function(p13, p14) --[[Function name: Observe, line 48]]
            --[[
            Upvalues:
                [1] = u16
            --]]
            local u15 = u16:Listen(p14)
            task.spawn(p14, p13:Get(), nil)
            return function() --[[Anonymous function at line 51]]
                --[[
                Upvalues:
                    [1] = u15
                --]]
                u15:Disconnect()
            end
        end
    }
    return u16
end
local u35 = {
    ["GetStateForGui"] = function(_, p18) --[[Function name: GetStateForGui, line 70]]
        --[[
        Upvalues:
            [1] = u6
            [2] = u17
        --]]
        local v19 = u6[p18]
        if not v19 then
            v19 = {
                ["Visible"] = u17(false),
                ["ChangeEnabled"] = u17(true)
            }
            u6[p18] = v19
        end
        return v19
    end,
    ["GetStateForGroup"] = function(_, p20) --[[Function name: GetStateForGroup, line 83]]
        --[[
        Upvalues:
            [1] = u7
            [2] = u17
        --]]
        local v21 = u7[p20]
        if not v21 then
            v21 = {
                ["Gui"] = u17(nil)
            }
            u7[p20] = v21
        end
        return v21
    end,
    ["GetGroupForGui"] = function(_, p22) --[[Function name: GetGroupForGui, line 95]]
        return p22:GetAttribute("Group") or "Menu"
    end,
    ["Close"] = function(_, p23) --[[Function name: Close, line 99]]
        --[[
        Upvalues:
            [1] = u35
            [2] = u2
        --]]
        local v24 = u35:GetStateForGroup((u35:GetGroupForGui(p23)))
        if v24.Gui:Get() == p23 then
            local v25 = u35:GetStateForGui(p23)
            v25.Visible:Set(false)
            v24.Gui:Set(nil)
            if v25.ChangeEnabled:Get() then
                p23.Enabled = false
                u2:DisableGamepadCursor()
            end
        end
    end,
    ["Open"] = function(_, p26) --[[Function name: Open, line 116]]
        --[[
        Upvalues:
            [1] = u35
            [2] = u2
        --]]
        local v27 = u35:GetStateForGroup((u35:GetGroupForGui(p26)))
        local v28 = v27.Gui:Get()
        if v28 ~= p26 then
            if v28 then
                u35:Close(v28)
            end
            local v29 = u35:GetStateForGui(p26)
            v29.Visible:Set(true)
            v27.Gui:Set(p26)
            if v29.ChangeEnabled:Get() then
                p26.Enabled = true
                u2:EnableGamepadCursor(p26:FindFirstChildWhichIsA("GuiObject", true))
            end
        end
    end,
    ["Toggle"] = function(_, p30) --[[Function name: Toggle, line 137]]
        --[[
        Upvalues:
            [1] = u35
        --]]
        local v31 = u35:GetStateForGui(p30)
        if v31.Visible:Get() then
            u35:Close(p30)
        else
            u35:Open(p30)
        end
        return v31.Visible
    end,
    ["UsePopupAnims"] = function(_, u32) --[[Function name: UsePopupAnims, line 147]]
        --[[
        Upvalues:
            [1] = u35
            [2] = u5
        --]]
        if u32:FindFirstChild("Main") or u32:FindFirstChild("Frame") then
            u32.Enabled = false
            local v33 = u35:GetStateForGui(u32)
            v33.ChangeEnabled:Set(true)
            v33.Visible:Observe(function(p34) --[[Anonymous function at line 158]]
                --[[
                Upvalues:
                    [1] = u5
                    [2] = u32
                --]]
                if p34 then
                    u5.Show(u32)
                else
                    u5.Hide(u32)
                end
            end)
        else
            error((("No root frame found for %*"):format((u32:GetFullName()))))
        end
    end
}
return u35