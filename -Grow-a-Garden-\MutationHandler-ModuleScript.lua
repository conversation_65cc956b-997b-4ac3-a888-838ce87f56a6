-- Full Path: -Grow-a-Garden-\\MutationHandler-ModuleScript.lua
local v1 = game:GetService("CollectionService")
local u2 = game:GetService("RunService")
local u3 = game:GetService("Players")
local u4 = require(game.ReplicatedStorage.Modules.TableUtils)
local u5 = require(game.ReplicatedStorage.Calculate_Weight)
local u6 = {}
local u7 = {}
function u6.CanBeMutated(_, p8) --[[Anonymous function at line 40]]
    if p8 then
        return p8:HasTag("Harvestable") or p8:HasTag("FruitTool")
    end
    warn("MutationHandler.CanBeMutated | No plant given!")
    return false
end
local function u14(p9) --[[Anonymous function at line 52]]
    --[[
    Upvalues:
        [1] = u2
        [2] = u3
    --]]
    if not p9 then
        warn("MutationHandler.GetFXPart | No plant given!")
        return nil
    end
    if not p9:IsDescendantOf(game) then
        return nil
    end
    local v10 = {}
    if #v10 == 1 then
        return v10[1]
    end
    if #v10 > 1 then
        return v10
    end
    local v11 = p9:IsA("Tool") and p9:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("Handle", 0.3)
    if v11 then
        return v11
    end
    if p9.PrimaryPart then
        return p9.PrimaryPart
    end
    local v12
    if u2:IsStudio() then
        for _, v13 in u3:GetPlayers() do
            if v13.Name == "JexSavron" then
                v12 = true
                goto l16
            end
        end
        v12 = false
        goto l16
    else
        v12 = false
        ::l16::
        if v12 then
            warn((("MutationHandler.GetFXPart | No valid FX part found for %* of type %*"):format(p9:GetFullName(), p9.ClassName)))
        end
        return nil
    end
end
local u218 = {
    ["Shocked"] = {
        ["Name"] = "Shocked",
        ["ValueMulti"] = 100,
        ["Color"] = Color3.fromRGB(255, 255, 100),
        ["_AddFX"] = function(p15, p16, p17) --[[Function name: _AddFX, line 157]]
            p15:_RemoveFX(p16, p17)
            if p17 then
                for _, v18 in game.ReplicatedStorage.Mutation_FX.Shocked:GetChildren() do
                    local v19 = v18:Clone()
                    v19.Parent = p17
                    v19:AddTag("Cleanup_Shock")
                end
            end
            for _, v20 in p16:GetDescendants() do
                if (v20:IsA("BasePart") or v20:IsA("UnionOperation")) and not v20:HasTag("Effect") then
                    v20.Material = Enum.Material.Neon
                end
            end
        end,
        ["_RemoveFX"] = function(_, p21, p22) --[[Function name: _RemoveFX, line 180]]
            if p22 then
                for _, v23 in p22:GetChildren() do
                    if v23:HasTag("Cleanup_Shock") then
                        v23:Destroy()
                    end
                end
            end
            for _, v24 in p21:GetDescendants() do
                if (v24:IsA("BasePart") or v24:IsA("UnionOperation")) and (v24:GetAttribute("Original_Material") and not v24:HasTag("Effect")) then
                    v24.Material = v24:GetAttribute("Original_Material")
                end
            end
        end
    },
    ["Twisted"] = {
        ["Name"] = "Twisted",
        ["ValueMulti"] = 5,
        ["Color"] = Color3.fromRGB(191, 191, 191),
        ["_AddFX"] = function(p25, p26, p27) --[[Function name: _AddFX, line 207]]
            p25:_RemoveFX(p26, p27)
            if p27 then
                for _, v28 in game.ReplicatedStorage.Mutation_FX.Twisted:GetChildren() do
                    local v29 = v28:Clone()
                    v29.Parent = p27
                    v29:AddTag("Cleanup_Twisted")
                end
            end
        end,
        ["_RemoveFX"] = function(_, _, p30) --[[Function name: _RemoveFX, line 222]]
            if p30 then
                for _, v31 in p30:GetChildren() do
                    if v31:HasTag("Twisted") then
                        v31:Destroy()
                    end
                end
            end
        end
    },
    ["Wet"] = {
        ["Name"] = "Wet",
        ["ValueMulti"] = 2,
        ["Color"] = Color3.fromRGB(64, 164, 223),
        ["_AddFX"] = function(p32, p33, p34) --[[Function name: _AddFX, line 241]]
            p32:_RemoveFX(p33, p34)
            if p34 then
                for _, v35 in game.ReplicatedStorage.Mutation_FX.Wet:GetChildren() do
                    local v36 = v35:Clone()
                    v36.Parent = p34
                    v36.Enabled = true
                    v36:AddTag("Cleanup_Wet")
                end
            end
        end,
        ["_RemoveFX"] = function(_, _, p37) --[[Function name: _RemoveFX, line 255]]
            if p37 then
                for _, v38 in p37:GetChildren() do
                    if v38:HasTag("Cleanup_Wet") then
                        v38:Destroy()
                    end
                end
            end
        end
    },
    ["Chilled"] = {
        ["Name"] = "Chilled",
        ["ValueMulti"] = 2,
        ["Color"] = Color3.fromRGB(135, 206, 250),
        ["_AddFX"] = function(p39, p40, p41) --[[Function name: _AddFX, line 272]]
            p39:_RemoveFX(p40, p41)
            if p41 then
                for _, v42 in game.ReplicatedStorage.Mutation_FX.Chilled:GetChildren() do
                    local v43 = v42:Clone()
                    v43.Parent = p41
                    v43.Enabled = true
                    v43:AddTag("Cleanup_Chilled")
                end
            end
            for _, v44 in p40:GetChildren() do
                if v44:IsA("BasePart") or v44:IsA("UnionOperation") then
                    v44.Reflectance = 0.1
                end
            end
        end,
        ["_RemoveFX"] = function(_, p45, p46) --[[Function name: _RemoveFX, line 292]]
            if p46 then
                for _, v47 in p46:GetChildren() do
                    if v47:HasTag("Cleanup_Chilled") then
                        v47:Destroy()
                    end
                end
            end
            for _, v48 in p45:GetChildren() do
                if v48:IsA("BasePart") or v48:IsA("UnionOperation") then
                    v48.Reflectance = 0
                end
            end
        end
    },
    ["Frozen"] = {
        ["Name"] = "Frozen",
        ["ValueMulti"] = 10,
        ["Color"] = Color3.fromRGB(108, 184, 255),
        ["_AddFX"] = function(p49, p50, p51) --[[Function name: _AddFX, line 315]]
            p49:_RemoveFX(p50, p51)
            local v52 = {}
            for _, v53 in p50:GetDescendants() do
                if v53:IsA("BasePart") or (v53:IsA("UnionOperation") or v53:IsA("MeshPart") and v53.Transparency < 1) then
                    table.insert(v52, v53)
                end
            end
            if #v52 ~= 0 then
                local v54 = Vector3.new(inf, inf, inf)
                local v55 = Vector3.new(-inf, -inf, -inf)
                for _, v56 in v52 do
                    local v57 = v56.CFrame
                    local v58 = v56.Size
                    local v59 = {}
                    local v60 = -v58.X / 2
                    local v61 = -v58.Y / 2
                    local v62 = -v58.Z / 2
                    local v63 = v57 * Vector3.new(v60, v61, v62)
                    local v64 = v58.X / 2
                    local v65 = -v58.Y / 2
                    local v66 = -v58.Z / 2
                    local v67 = v57 * Vector3.new(v64, v65, v66)
                    local v68 = -v58.X / 2
                    local v69 = v58.Y / 2
                    local v70 = -v58.Z / 2
                    local v71 = v57 * Vector3.new(v68, v69, v70)
                    local v72 = v58.X / 2
                    local v73 = v58.Y / 2
                    local v74 = -v58.Z / 2
                    local v75 = v57 * Vector3.new(v72, v73, v74)
                    local v76 = -v58.X / 2
                    local v77 = -v58.Y / 2
                    local v78 = v58.Z / 2
                    local v79 = v57 * Vector3.new(v76, v77, v78)
                    local v80 = v58.X / 2
                    local v81 = -v58.Y / 2
                    local v82 = v58.Z / 2
                    local v83 = v57 * Vector3.new(v80, v81, v82)
                    local v84 = -v58.X / 2
                    local v85 = v58.Y / 2
                    local v86 = v58.Z / 2
                    local v87 = v57 * Vector3.new(v84, v85, v86)
                    local v88 = v58.X / 2
                    local v89 = v58.Y / 2
                    local v90 = v58.Z / 2
                    __set_list(v59, 1, {v63, v67, v71, v75, v79, v83, v87, v57 * Vector3.new(v88, v89, v90)})
                    for _, v91 in ipairs(v59) do
                        local v92 = v54.X
                        local v93 = v91.X
                        local v94 = math.min(v92, v93)
                        local v95 = v54.Y
                        local v96 = v91.Y
                        local v97 = math.min(v95, v96)
                        local v98 = v54.Z
                        local v99 = v91.Z
                        local v100 = math.min(v98, v99)
                        v54 = Vector3.new(v94, v97, v100)
                        local v101 = v55.X
                        local v102 = v91.X
                        local v103 = math.max(v101, v102)
                        local v104 = v55.Y
                        local v105 = v91.Y
                        local v106 = math.max(v104, v105)
                        local v107 = v55.Z
                        local v108 = v91.Z
                        local v109 = math.max(v107, v108)
                        v55 = Vector3.new(v103, v106, v109)
                    end
                end
                local v110 = (v54 + v55) / 2
                local v111 = v55 - v54 + Vector3.new(0.1, 0.1, 0.1)
                local v112 = Instance.new("Part")
                v112.Name = "FrozenShell"
                v112.Size = v111
                v112.CFrame = CFrame.new(v110)
                v112.Anchored = false
                v112.CanCollide = false
                v112.CanQuery = false
                v112.Massless = true
                v112.Transparency = 0.5
                v112.Color = Color3.fromRGB(108, 184, 255)
                v112.Material = Enum.Material.Glass
                v112:AddTag("Cleanup_Frozen")
                v112:AddTag("Effect")
                local v113 = p50.PrimaryPart or (p50:FindFirstChild("Handle") or p50:FindFirstChildOfClass("BasePart"))
                if v113 then
                    local v114 = Instance.new("WeldConstraint")
                    v114.Part0 = v112
                    v114.Part1 = v113
                    v114.Parent = v112
                    v112.Parent = v113
                else
                    v112:Destroy()
                end
                for _, v115 in game.ReplicatedStorage.Mutation_FX.Frozen:GetChildren() do
                    local v116 = v115:Clone()
                    v116.Parent = p51
                    v116.Enabled = true
                    v116:AddTag("Cleanup_Frozen")
                end
                for _, v117 in p50:GetDescendants() do
                    if v117:IsA("BasePart") or v117:IsA("UnionOperation") then
                        v117.Reflectance = 0.3
                    end
                end
            end
        end,
        ["_RemoveFX"] = function(_, p118, p119) --[[Function name: _RemoveFX, line 403]]
            local function v122(p120) --[[Anonymous function at line 405]]
                for _, v121 in p120:GetDescendants() do
                    if v121:HasTag("Cleanup_Frozen") then
                        v121:Destroy()
                    end
                end
            end
            if p119 then
                v122(p119)
            end
            if p118 then
                v122(p118)
            end
            for _, v123 in p118:GetDescendants() do
                if v123:IsA("BasePart") or v123:IsA("UnionOperation") then
                    v123.Reflectance = 0
                end
            end
        end
    },
    ["Disco"] = {
        ["Name"] = "Disco",
        ["ValueMulti"] = 125,
        ["Color"] = Color3.fromRGB(255, 105, 180),
        ["_AddFX"] = function(p124, p125, p126) --[[Function name: _AddFX, line 430]]
            p124:_RemoveFX(p125, p126)
            local function u129(p127) --[[Anonymous function at line 433]]
                --[[
                Upvalues:
                    [1] = u129
                --]]
                for _, v128 in p127:GetChildren() do
                    if v128:IsA("BasePart") or v128:IsA("UnionOperation") then
                        v128.Material = Enum.Material.Neon
                        v128:AddTag("Discofied")
                    elseif v128:IsA("Model") then
                        u129(v128)
                    end
                end
            end
            u129(p125)
        end,
        ["_RemoveFX"] = function(_, p130, _) --[[Function name: _RemoveFX, line 447]]
            for _, v131 in p130:GetDescendants() do
                if (v131:IsA("BasePart") or v131:IsA("UnionOperation")) and v131:HasTag("Discofied") then
                    v131.Reflectance = 0
                    v131:RemoveTag("Discofied")
                    local v132 = v131:GetAttribute("Original_Material")
                    if v132 and typeof(v132) == "string" then
                        local v133 = Enum.Material[v132]
                        if v133 then
                            v131.Material = v133
                        end
                    end
                end
            end
        end
    },
    ["Choc"] = {
        ["Name"] = "Choc",
        ["ValueMulti"] = 2,
        ["Color"] = Color3.fromRGB(92, 64, 51),
        ["_AddFX"] = function(p134, p135, p136) --[[Function name: _AddFX, line 472]]
            --[[
            Upvalues:
                [1] = u7
            --]]
            p134:_RemoveFX(p135, p136)
            local function u143(p137) --[[Anonymous function at line 479]]
                --[[
                Upvalues:
                    [1] = u7
                    [2] = u143
                --]]
                for _, u138 in p137:GetChildren() do
                    if u138:IsA("BasePart") or u138:IsA("UnionOperation") then
                        local u139 = {}
                        for _, v140 in game.ReplicatedStorage.Mutation_Textures.Choc:GetChildren() do
                            local v141 = v140:Clone()
                            v141.Parent = u138
                            v141.Transparency = u138.Transparency
                            table.insert(u139, v141)
                            v141:AddTag("Cleanup_Choc")
                        end
                        u7[u138] = u138.Changed:Connect(function() --[[Anonymous function at line 492]]
                            --[[
                            Upvalues:
                                [1] = u138
                                [2] = u7
                                [3] = u139
                            --]]
                            if u138.Transparency == 0 then
                                u7[u138]:Disconnect()
                                for _, v142 in u139 do
                                    v142.Transparency = 0
                                end
                            end
                        end)
                    elseif u138:IsA("Model") then
                        u143(u138)
                    end
                end
            end
            u143(p135)
        end,
        ["_RemoveFX"] = function(_, p144, _) --[[Function name: _RemoveFX, line 510]]
            --[[
            Upvalues:
                [1] = u7
            --]]
            for _, v145 in p144:GetDescendants() do
                if v145:HasTag("Cleanup_Choc") then
                    v145:Destroy()
                end
                if u7[v145] then
                    u7[v145]:Disconnect()
                end
            end
        end
    },
    ["Plasma"] = {
        ["Name"] = "Plasma",
        ["ValueMulti"] = 5,
        ["Color"] = Color3.fromRGB(208, 43, 137),
        ["_AddFX"] = function(p146, p147, p148) --[[Function name: _AddFX, line 528]]
            p146:_RemoveFX(p147, p148)
            if p148 then
                for _, v149 in game.ReplicatedStorage.Mutation_FX.Plasma:GetChildren() do
                    local v150 = v149:Clone()
                    v150.Parent = p148
                    v150.Enabled = true
                    v150:AddTag("Cleanup_Plasma")
                end
            end
            for _, v151 in p147:GetDescendants() do
                if (v151:IsA("BasePart") or v151:IsA("UnionOperation")) and not v151:HasTag("Effect") then
                    v151.Color = Color3.fromRGB(189, 67, 142)
                    v151.Material = Enum.Material.Neon
                    v151.Reflectance = 0.05
                    v151:AddTag("PlasmaVisual")
                end
            end
        end,
        ["_RemoveFX"] = function(_, p152, p153) --[[Function name: _RemoveFX, line 552]]
            local function v156(p154) --[[Anonymous function at line 553]]
                for _, v155 in p154:GetDescendants() do
                    if v155:HasTag("Cleanup_Plasma") then
                        v155:Destroy()
                    end
                end
            end
            if p153 then
                v156(p153)
            end
            if p152 then
                v156(p152)
            end
            for _, v157 in p152:GetDescendants() do
                if (v157:IsA("BasePart") or v157:IsA("UnionOperation")) and v157:HasTag("PlasmaVisual") then
                    v157.Reflectance = 0
                    v157.Color = Color3.fromRGB(255, 255, 255)
                    v157:RemoveTag("PlasmaVisual")
                    if v157:GetAttribute("Original_Material") then
                        v157.Material = v157:GetAttribute("Original_Material")
                    end
                end
            end
        end
    },
    ["Burnt"] = {
        ["Name"] = "Burnt",
        ["ValueMulti"] = 5,
        ["Color"] = Color3.fromRGB(40, 40, 40),
        ["_AddFX"] = function(p158, p159, p160) --[[Function name: _AddFX, line 585]]
            p158:_RemoveFX(p159, p160)
            if p160 then
                for _, v161 in game.ReplicatedStorage.Mutation_FX.Burnt:GetChildren() do
                    local v162 = v161:Clone()
                    v162.Parent = p160
                    v162.Enabled = true
                    v162:AddTag("Cleanup_Burnt")
                end
            end
            for _, v163 in p159:GetDescendants() do
                if (v163:IsA("BasePart") or v163:IsA("UnionOperation")) and not v163:HasTag("Effect") then
                    v163.Color = Color3.fromRGB(25, 25, 25)
                    v163.Material = Enum.Material.Slate
                    v163.Reflectance = 0.05
                    v163:AddTag("BurntVisual")
                end
            end
        end,
        ["_RemoveFX"] = function(_, p164, p165) --[[Function name: _RemoveFX, line 609]]
            local function v168(p166) --[[Anonymous function at line 610]]
                for _, v167 in p166:GetDescendants() do
                    if v167:HasTag("Cleanup_Burnt") then
                        v167:Destroy()
                    end
                end
            end
            if p165 then
                v168(p165)
            end
            if p164 then
                v168(p164)
            end
            for _, v169 in p164:GetDescendants() do
                if (v169:IsA("BasePart") or v169:IsA("UnionOperation")) and v169:HasTag("BurntVisual") then
                    v169.Reflectance = 0
                    v169.Color = Color3.fromRGB(255, 255, 255)
                    v169:RemoveTag("BurntVisual")
                    if v169:GetAttribute("Original_Material") then
                        v169.Material = v169:GetAttribute("Original_Material")
                    end
                end
            end
        end
    },
    ["Moonlit"] = {
        ["Name"] = "Moonlit",
        ["ValueMulti"] = 2,
        ["Color"] = Color3.fromRGB(153, 141, 255),
        ["_AddFX"] = function(p170, p171, p172) --[[Function name: _AddFX, line 642]]
            p170:_RemoveFX(p171, p172)
            if p172 then
                for _, v173 in game.ReplicatedStorage.Mutation_FX.Moonlit:GetChildren() do
                    local v174 = v173:Clone()
                    v174.Parent = p172
                    v174.Enabled = true
                    v174:AddTag("Cleanup_Moonlit")
                    v174:AddTag("Effect")
                end
            end
            for _, v175 in p171:GetDescendants() do
                if (v175:IsA("BasePart") or v175:IsA("UnionOperation")) and not v175:HasTag("Effect") then
                    v175.Color = Color3.fromRGB(62, 56, 86)
                    v175.Reflectance = 0.3
                    v175:AddTag("MoonlitVisual")
                end
            end
        end,
        ["_RemoveFX"] = function(_, p176, p177) --[[Function name: _RemoveFX, line 666]]
            if p177 then
                for _, v178 in p177:GetChildren() do
                    if v178:HasTag("Cleanup_Moonlit") then
                        v178:Destroy()
                    end
                end
            end
            for _, v179 in p176:GetDescendants() do
                if (v179:IsA("BasePart") or v179:IsA("UnionOperation")) and v179:HasTag("MoonlitVisual") then
                    v179.Reflectance = 0
                    v179:RemoveTag("MoonlitVisual")
                    local v180 = v179:GetAttribute("Original_Material")
                    if v180 and typeof(v180) == "string" then
                        local v181 = Enum.Material[v180]
                        if v181 then
                            v179.Material = v181
                        end
                    end
                end
            end
        end
    },
    ["Bloodlit"] = {
        ["Name"] = "Bloodlit",
        ["ValueMulti"] = 4,
        ["Color"] = Color3.fromRGB(200, 0, 0),
        ["_AddFX"] = function(p182, p183, p184) --[[Function name: _AddFX, line 701]]
            p182:_RemoveFX(p183, p184)
            if p184 then
                for _, v185 in game.ReplicatedStorage.Mutation_FX.Bloodlit:GetChildren() do
                    local v186 = v185:Clone()
                    v186.Parent = p184
                    v186.Enabled = true
                    v186:AddTag("Cleanup_Bloodlit")
                    v186:AddTag("Effect")
                end
            end
            for _, v187 in p183:GetDescendants() do
                if (v187:IsA("BasePart") or v187:IsA("UnionOperation")) and not v187:HasTag("Effect") then
                    v187.Color = Color3.fromRGB(143, 1, 3)
                    v187.Reflectance = 0.3
                    v187:AddTag("BloodlitVisual")
                end
            end
        end,
        ["_RemoveFX"] = function(_, p188, p189) --[[Function name: _RemoveFX, line 725]]
            if p189 then
                for _, v190 in p189:GetChildren() do
                    if v190:HasTag("Cleanup_Bloodlit") then
                        v190:Destroy()
                    end
                end
            end
            for _, v191 in p188:GetDescendants() do
                if (v191:IsA("BasePart") or v191:IsA("UnionOperation")) and v191:HasTag("BloodlitVisual") then
                    v191.Reflectance = 0
                    v191:RemoveTag("BloodlitVisual")
                    local v192 = v191:GetAttribute("Original_Material")
                    if v192 and typeof(v192) == "string" then
                        local v193 = Enum.Material[v192]
                        if v193 then
                            v191.Material = v193
                        end
                    end
                end
            end
        end
    },
    ["Zombified"] = {
        ["Name"] = "Zombified",
        ["ValueMulti"] = 25,
        ["Color"] = Color3.fromRGB(128, 199, 127),
        ["_AddFX"] = function(p194, p195, p196) --[[Function name: _AddFX, line 760]]
            p194:_RemoveFX(p195, p196)
            if p196 then
                for _, v197 in game.ReplicatedStorage.Mutation_FX.Zombified:GetChildren() do
                    local v198 = v197:Clone()
                    v198.Parent = p196
                    v198.Enabled = true
                    v198:AddTag("Cleanup_Zombified")
                    v198:AddTag("Effect")
                end
            end
            for _, v199 in p195:GetDescendants() do
                if (v199:IsA("BasePart") or v199:IsA("UnionOperation")) and not v199:HasTag("Effect") then
                    v199:AddTag("ZombifiedVisual")
                end
            end
        end,
        ["_RemoveFX"] = function(_, p200, p201) --[[Function name: _RemoveFX, line 782]]
            if p201 then
                for _, v202 in p201:GetChildren() do
                    if v202:HasTag("Cleanup_Zombified") then
                        v202:Destroy()
                    end
                end
            end
            for _, v203 in p200:GetDescendants() do
                if (v203:IsA("BasePart") or v203:IsA("UnionOperation")) and v203:HasTag("ZombifiedVisual") then
                    v203:RemoveTag("ZombifiedVisual")
                    local v204 = v203:GetAttribute("Original_Material")
                    if v204 and typeof(v204) == "string" then
                        local v205 = Enum.Material[v204]
                        if v205 then
                            v203.Material = v205
                        end
                    end
                end
            end
        end
    },
    ["Celestial"] = {
        ["Name"] = "Celestial",
        ["ValueMulti"] = 120,
        ["Color"] = Color3.fromRGB(255, 0, 255),
        ["_AddFX"] = function(p206, p207, p208) --[[Function name: _AddFX, line 814]]
            p206:_RemoveFX(p207, p208)
            if p208 then
                for _, v209 in game.ReplicatedStorage.Mutation_FX.Celestial:GetChildren() do
                    local v210 = v209:Clone()
                    v210.Parent = p208
                    v210.Enabled = true
                    v210:AddTag("Cleanup_Celestial")
                    v210:AddTag("Effect")
                end
            end
            for _, v211 in p207:GetDescendants() do
                if (v211:IsA("BasePart") or v211:IsA("UnionOperation")) and not v211:HasTag("Effect") then
                    v211.Reflectance = 0.5
                    v211:AddTag("CelestialVisual")
                end
            end
        end,
        ["_RemoveFX"] = function(_, p212, p213) --[[Function name: _RemoveFX, line 837]]
            if p213 then
                for _, v214 in p213:GetChildren() do
                    if v214:HasTag("Cleanup_Celestial") then
                        v214:Destroy()
                    end
                end
            end
            for _, v215 in p212:GetDescendants() do
                if (v215:IsA("BasePart") or v215:IsA("UnionOperation")) and v215:HasTag("CelestialVisual") then
                    v215.Reflectance = 0
                    v215:RemoveTag("CelestialVisual")
                    local v216 = v215:GetAttribute("Original_Material")
                    if v216 and typeof(v216) == "string" then
                        local v217 = Enum.Material[v216]
                        if v217 then
                            v215.Material = v217
                        end
                    end
                end
            end
        end
    }
}
function u6.GetMutations(_) --[[Anonymous function at line 868]]
    --[[
    Upvalues:
        [1] = u4
        [2] = u218
    --]]
    return u4:DeepCopy(u218)
end
function u6.AddMutation(_, p219, p220) --[[Anonymous function at line 874]]
    if p219 then
        p219:SetAttribute(p220.Name, true)
    else
        warn("MutationHandler:AddMutation | No plant")
    end
end
function u6.RemoveMutation(_, p221, p222) --[[Anonymous function at line 885]]
    if p221 then
        p221:SetAttribute(p222.Name, false)
    else
        warn("MutationHandler:RemoveMutation | No plant")
    end
end
function u6.GetPlantMutations(_, p223) --[[Anonymous function at line 896]]
    --[[
    Upvalues:
        [1] = u6
    --]]
    local v224 = {}
    for _, v225 in u6:GetMutations() do
        if p223:GetAttribute(v225.Name) then
            table.insert(v224, v225)
        end
    end
    return v224
end
function u6.GetMutationsAsString(_, p226, p227) --[[Anonymous function at line 908]]
    --[[
    Upvalues:
        [1] = u218
    --]]
    local v228 = ""
    local v229 = p227 or false
    for _, v230 in u218 do
        local v231 = v230.Name
        if p226:GetAttribute(v231) then
            if v228 ~= "" then
                v228 = v228 .. ", "
            end
            v228 = v228 .. v231
        end
    end
    if v228 ~= "" and v229 then
        v228 = "[" .. v228 .. "]"
    end
    return v228
end
function u6.ExtractMutationsFromString(_, p232) --[[Anonymous function at line 929]]
    --[[
    Upvalues:
        [1] = u6
    --]]
    local v233 = {}
    if not p232 or p232 == "" then
        return v233
    end
    local v234 = p232:gsub("%[", ""):gsub("%]", ""):gsub("%s+", "")
    for v235 in string.gmatch(v234, "([^,]+)") do
        local v236 = false
        for _, v237 in u6:GetMutations() do
            if v237.Name == v235 then
                table.insert(v233, v237)
                v236 = true
            end
        end
        if not v236 then
            warn((("MutationHandler:ExtractMutationsFromString | Mutation name: %* does not exist"):format(v235)))
        end
    end
    return v233
end
function u6.TransferMutations(_, p238, p239) --[[Anonymous function at line 960]]
    --[[
    Upvalues:
        [1] = u6
    --]]
    for _, v240 in u6:GetMutations() do
        if p238:GetAttribute(v240.Name) then
            u6:AddMutation(p239, v240)
            u6:RemoveMutation(p238, v240)
        end
    end
    p239:SetAttribute("WeightMulti", p238:GetAttribute("WeightMulti") or 1)
end
function u6.CalcValueMulti(_, p241) --[[Anonymous function at line 972]]
    --[[
    Upvalues:
        [1] = u6
    --]]
    local v242 = 1
    for _, v243 in u6:GetMutations() do
        if p241:GetAttribute(v243.Name) then
            v242 = v242 + (v243.ValueMulti - 1)
        end
    end
    return math.max(1, v242)
end
function u6.SetToolName(_, u244) --[[Anonymous function at line 989]]
    --[[
    Upvalues:
        [1] = u5
        [2] = u6
    --]]
    if u244:IsA("Tool") then
        task.spawn(function() --[[Anonymous function at line 992]]
            --[[
            Upvalues:
                [1] = u244
                [2] = u5
                [3] = u6
            --]]
            local v245 = u244:WaitForChild("Variant", 0.3)
            if v245 then
                local v246 = u244:WaitForChild("Item_String", 0.3)
                if v246 then
                    if u244:WaitForChild("Weight", 0.3) then
                        local v247, _ = u5.Calculate_Weight(u244.Item_Seed.Value, v246.Value)
                        local v248 = v247 * (u244:GetAttribute("WeightMulti") or 1)
                        local v249 = u6:GetMutationsAsString(u244)
                        local v250 = v245.Value
                        if v250 and (v250 ~= "" and v250 ~= "Normal") then
                            if v249 ~= "" then
                                v250 = v249 .. ", " .. v250
                            end
                        else
                            v250 = v249
                        end
                        u244.Name = (v250 == "" and "" or (("[%*] "):format(v250) or "")) .. v246.Value .. " [" .. string.format("%.2f", v248) .. "kg]"
                    end
                else
                    return
                end
            else
                return
            end
        end)
    end
end
local u251 = {}
local function u267(u252) --[[Anonymous function at line 112]]
    --[[
    Upvalues:
        [1] = u6
        [2] = u14
    --]]
    local u253 = {}
    local u254 = {}
    for _, v255 in u6:GetMutations() do
        if not u252:GetAttribute(v255.Name) then
            u254[v255] = v255._RemoveFX
        end
        if u252:GetAttribute(v255.Name) then
            u253[v255] = v255._AddFX
        end
    end
    task.spawn(function() --[[Anonymous function at line 126]]
        --[[
        Upvalues:
            [1] = u14
            [2] = u252
            [3] = u254
            [4] = u253
        --]]
        local v256 = u14(u252)
        local function v262(p257, p258, p259, p260) --[[Anonymous function at line 130]]
            if typeof(p260) == "table" then
                for _, v261 in ipairs(p260) do
                    p257(p258, p259, v261)
                end
            elseif p260 then
                p257(p258, p259, p260)
            end
        end
        for v263, v264 in u254 do
            v262(v264, v263, u252, v256)
        end
        for v265, v266 in u253 do
            v262(v266, v265, u252, v256)
        end
    end)
end
local u268 = {}
local function u270(p269) --[[Anonymous function at line 98]]
    if p269:GetAttribute("Frozen") or p269:GetAttribute("Wet") and p269:GetAttribute("Chilled") then
        p269:SetAttribute("Wet", false)
        p269:SetAttribute("Chilled", false)
        p269:SetAttribute("Frozen", true)
    end
end
for _, v271 in u218 do
    u251[v271.Name] = true
end
local function v276(u272) --[[Anonymous function at line 1033]]
    --[[
    Upvalues:
        [1] = u267
        [2] = u6
        [3] = u251
        [4] = u268
        [5] = u270
    --]]
    if u272 then
        for _, v273 in u272:GetDescendants() do
            if v273:IsA("Part") or (v273:IsA("MeshPart") or v273:IsA("UnionOperation")) then
                v273:SetAttribute("Original_Material", v273.Material.Name)
            end
        end
        task.defer(function() --[[Anonymous function at line 1047]]
            --[[
            Upvalues:
                [1] = u272
                [2] = u267
            --]]
            local v274 = 0
            while not u272:GetAttribute("Frozen") and v274 < 2 do
                task.wait(0.05)
                v274 = v274 + 0.05
            end
            u267(u272)
        end)
        u6:SetToolName(u272)
        u272.AttributeChanged:Connect(function(p275) --[[Anonymous function at line 1060]]
            --[[
            Upvalues:
                [1] = u251
                [2] = u268
                [3] = u272
                [4] = u270
                [5] = u267
                [6] = u6
            --]]
            if u251[p275] then
                if not u268[u272] then
                    u268[u272] = true
                    task.defer(function() --[[Anonymous function at line 1065]]
                        --[[
                        Upvalues:
                            [1] = u270
                            [2] = u272
                            [3] = u267
                            [4] = u6
                            [5] = u268
                        --]]
                        u270(u272)
                        u267(u272)
                        u6:SetToolName(u272)
                        u268[u272] = nil
                    end)
                end
            else
                return
            end
        end)
    else
        warn("MutationHandler.MutatableAdded | No plant")
    end
end
if u2:IsServer() then
    v1:GetInstanceAddedSignal("Harvestable"):Connect(v276)
    v1:GetInstanceAddedSignal("FruitTool"):Connect(v276)
    v1:GetInstanceAddedSignal("PlantScaled"):Connect(function(p277) --[[Anonymous function at line 1095]]
        --[[
        Upvalues:
            [1] = u6
            [2] = u267
        --]]
        if u6:CanBeMutated(p277) then
            if p277:IsA("Model") or p277:IsA("Tool") then
                u267(p277)
                u6:SetToolName(p277)
            end
        else
            return
        end
    end)
end
return u6