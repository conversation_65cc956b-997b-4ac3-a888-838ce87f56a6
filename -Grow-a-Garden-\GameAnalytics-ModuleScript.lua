-- Full Path: -Grow-a-Garden-\\GameAnalytics-ModuleScript.lua
local u1 = {
    ["EGAResourceFlowType"] = require(script.GAResourceFlowType),
    ["EGAProgressionStatus"] = require(script.GAProgressionStatus),
    ["EGAErrorSeverity"] = require(script.GAErrorSeverity)
}
local u2 = require(script.Logger)
local u3 = require(script.Threading)
local u4 = require(script.State)
local u5 = require(script.Validation)
local u6 = require(script.Store)
local u7 = require(script.Events)
local u8 = require(script.Utilities)
local u9 = game:GetService("Players")
local u10 = game:GetService("MarketplaceService")
local u11 = game:GetService("RunService")
local u12 = game:GetService("ReplicatedStorage")
local u13 = game:GetService("LocalizationService")
local v14 = game:GetService("ScriptContext")
local u15 = require(script.Postie)
local u16 = nil
local u17 = {}
local u18 = {}
local u19 = {}
local u20 = {}
local u21 = {}
local u22 = {}
local function u25(p23, ...) --[[Anonymous function at line 36]]
    --[[
    Upvalues:
        [1] = u21
        [2] = u2
    --]]
    if u21 == nil then
        u2:w("Initialization queue already cleared.")
    else
        local v24 = u21
        table.insert(v24, {
            ["Func"] = p23,
            ["Args"] = { ... }
        })
        u2:i("Added event to initialization queue")
    end
end
local function u29(p26, p27, ...) --[[Anonymous function at line 50]]
    --[[
    Upvalues:
        [1] = u1
        [2] = u22
        [3] = u2
    --]]
    if u1:isPlayerReady(p26) then
        u2:w("Player initialization queue already cleared.")
    else
        if u22[p26] == nil then
            u22[p26] = {}
        end
        local v28 = u22[p26]
        table.insert(v28, {
            ["Func"] = p27,
            ["Args"] = { ... }
        })
        u2:i("Added event to player initialization queue")
    end
end
local function u35(p30) --[[Anonymous function at line 70]]
    --[[
    Upvalues:
        [1] = u4
        [2] = u2
    --]]
    local v31 = p30.playerId or nil
    local v32 = p30.needsInitialized or true
    local v33 = p30.shouldWarn or false
    local v34 = p30.message or ""
    if v32 and not u4.Initialized then
        if v33 then
            u2:w(v34 .. " SDK is not initialized")
        end
        return false
    end
    if v32 and (v31 and not u4:isEnabled(v31)) then
        if v33 then
            u2:w(v34 .. " SDK is disabled")
        end
        return false
    end
    if not v32 or (not v31 or u4:sessionIsStarted(v31)) then
        return true
    end
    if v33 then
        u2:w(v34 .. " Session has not started yet")
    end
    return false
end
function u1.configureAvailableCustomDimensions01(_, p36) --[[Anonymous function at line 106]]
    --[[
    Upvalues:
        [1] = u35
        [2] = u2
        [3] = u4
    --]]
    if u35({
        ["needsInitialized"] = true,
        ["shouldWarn"] = false
    }) then
        u2:w("Available custom dimensions must be set before SDK is initialized")
    else
        u4:setAvailableCustomDimensions01(p36)
    end
end
function u1.configureAvailableCustomDimensions02(_, p37) --[[Anonymous function at line 115]]
    --[[
    Upvalues:
        [1] = u35
        [2] = u2
        [3] = u4
    --]]
    if u35({
        ["needsInitialized"] = true,
        ["shouldWarn"] = false
    }) then
        u2:w("Available custom dimensions must be set before SDK is initialized")
    else
        u4:setAvailableCustomDimensions02(p37)
    end
end
function u1.configureAvailableCustomDimensions03(_, p38) --[[Anonymous function at line 124]]
    --[[
    Upvalues:
        [1] = u35
        [2] = u2
        [3] = u4
    --]]
    if u35({
        ["needsInitialized"] = true,
        ["shouldWarn"] = false
    }) then
        u2:w("Available custom dimensions must be set before SDK is initialized")
    else
        u4:setAvailableCustomDimensions03(p38)
    end
end
function u1.configureAvailableResourceCurrencies(_, p39) --[[Anonymous function at line 133]]
    --[[
    Upvalues:
        [1] = u35
        [2] = u2
        [3] = u7
    --]]
    if u35({
        ["needsInitialized"] = true,
        ["shouldWarn"] = false
    }) then
        u2:w("Available resource currencies must be set before SDK is initialized")
    else
        u7:setAvailableResourceCurrencies(p39)
    end
end
function u1.configureAvailableResourceItemTypes(_, p40) --[[Anonymous function at line 142]]
    --[[
    Upvalues:
        [1] = u35
        [2] = u2
        [3] = u7
    --]]
    if u35({
        ["needsInitialized"] = true,
        ["shouldWarn"] = false
    }) then
        u2:w("Available resource item types must be set before SDK is initialized")
    else
        u7:setAvailableResourceItemTypes(p40)
    end
end
function u1.configureBuild(_, p41) --[[Anonymous function at line 151]]
    --[[
    Upvalues:
        [1] = u35
        [2] = u2
        [3] = u7
    --]]
    if u35({
        ["needsInitialized"] = true,
        ["shouldWarn"] = false
    }) then
        u2:w("Build version must be set before SDK is initialized.")
    else
        u7:setBuild(p41)
    end
end
function u1.configureAvailableGamepasses(_, p42) --[[Anonymous function at line 160]]
    --[[
    Upvalues:
        [1] = u35
        [2] = u2
        [3] = u4
    --]]
    if u35({
        ["needsInitialized"] = true,
        ["shouldWarn"] = false
    }) then
        u2:w("Available gamepasses must be set before SDK is initialized.")
    else
        u4:setAvailableGamepasses(p42)
    end
end
function u1.startNewSession(_, u43, u44) --[[Anonymous function at line 169]]
    --[[
    Upvalues:
        [1] = u3
        [2] = u4
        [3] = u2
    --]]
    u3:performTaskOnGAThread(function() --[[Anonymous function at line 170]]
        --[[
        Upvalues:
            [1] = u4
            [2] = u2
            [3] = u43
            [4] = u44
        --]]
        if u4:isEventSubmissionEnabled() then
            if u4.Initialized then
                u4:startNewSession(u43, u44)
            else
                u2:w("Cannot start new session. SDK is not initialized yet.")
            end
        else
            return
        end
    end)
end
function u1.endSession(_, u45) --[[Anonymous function at line 184]]
    --[[
    Upvalues:
        [1] = u3
        [2] = u4
    --]]
    u3:performTaskOnGAThread(function() --[[Anonymous function at line 185]]
        --[[
        Upvalues:
            [1] = u4
            [2] = u45
        --]]
        if u4:isEventSubmissionEnabled() then
            u4:endSession(u45)
        end
    end)
end
function u1.filterForBusinessEvent(_, p46) --[[Anonymous function at line 193]]
    return string.gsub(p46, "[^A-Za-z0-9%s%-_%.%(%)!%?]", "")
end
function u1.addBusinessEvent(_, u47, u48) --[[Anonymous function at line 197]]
    --[[
    Upvalues:
        [1] = u3
        [2] = u4
        [3] = u35
        [4] = u29
        [5] = u1
        [6] = u25
        [7] = u7
        [8] = u9
        [9] = u6
    --]]
    u3:performTaskOnGAThread(function() --[[Anonymous function at line 198]]
        --[[
        Upvalues:
            [1] = u4
            [2] = u35
            [3] = u47
            [4] = u29
            [5] = u1
            [6] = u48
            [7] = u25
            [8] = u7
            [9] = u9
            [10] = u6
        --]]
        if u4:isEventSubmissionEnabled() then
            if u35({
                ["playerId"] = u47,
                ["needsInitialized"] = true,
                ["shouldWarn"] = false,
                ["message"] = "Could not add business event"
            }) then
                local v49 = u48.amount or 0
                local v50 = u48.itemType or ""
                local v51 = u48.itemId or ""
                local v52 = u48.cartType or ""
                local v53 = v49 * 0.7 * 0.35
                local v54 = math.floor(v53)
                local v55 = u48.gamepassId or nil
                u7:addBusinessEvent(u47, "USD", v54, v50, v51, v52)
                if v50 == "Gamepass" and v52 ~= "Website" then
                    local v56 = u9:GetPlayerByUserId(u47)
                    local v57 = u6:GetPlayerDataFromCache(u47)
                    if not v57.OwnedGamepasses then
                        v57.OwnedGamepasses = {}
                    end
                    local v58 = v57.OwnedGamepasses
                    table.insert(v58, v55)
                    u6.PlayerCache[u47] = v57
                    u6:SavePlayerData(v56)
                end
                return
            elseif u47 then
                u29(u47, u1.addBusinessEvent, u1, u47, u48)
            else
                u25(u1.addBusinessEvent, u1, u47, u48)
            end
        else
            return
        end
    end)
end
function u1.addResourceEvent(_, u59, u60) --[[Anonymous function at line 234]]
    --[[
    Upvalues:
        [1] = u3
        [2] = u4
        [3] = u35
        [4] = u29
        [5] = u1
        [6] = u25
        [7] = u7
    --]]
    u3:performTaskOnGAThread(function() --[[Anonymous function at line 235]]
        --[[
        Upvalues:
            [1] = u4
            [2] = u35
            [3] = u59
            [4] = u29
            [5] = u1
            [6] = u60
            [7] = u25
            [8] = u7
        --]]
        if u4:isEventSubmissionEnabled() then
            if u35({
                ["playerId"] = u59,
                ["needsInitialized"] = true,
                ["shouldWarn"] = false,
                ["message"] = "Could not add resource event"
            }) then
                u7:addResourceEvent(u59, u60.flowType or 0, u60.currency or "", u60.amount or 0, u60.itemType or "", u60.itemId or "")
                return
            elseif u59 then
                u29(u59, u1.addResourceEvent, u1, u59, u60)
            else
                u25(u1.addResourceEvent, u1, u59, u60)
            end
        else
            return
        end
    end)
end
function u1.addProgressionEvent(_, u61, u62) --[[Anonymous function at line 259]]
    --[[
    Upvalues:
        [1] = u3
        [2] = u4
        [3] = u35
        [4] = u29
        [5] = u1
        [6] = u25
        [7] = u7
    --]]
    u3:performTaskOnGAThread(function() --[[Anonymous function at line 260]]
        --[[
        Upvalues:
            [1] = u4
            [2] = u35
            [3] = u61
            [4] = u29
            [5] = u1
            [6] = u62
            [7] = u25
            [8] = u7
        --]]
        if u4:isEventSubmissionEnabled() then
            if u35({
                ["playerId"] = u61,
                ["needsInitialized"] = true,
                ["shouldWarn"] = false,
                ["message"] = "Could not add progression event"
            }) then
                u7:addProgressionEvent(u61, u62.progressionStatus or 0, u62.progression01 or "", u62.progression02 or nil, u62.progression03 or nil, u62.score or nil)
                return
            elseif u61 then
                u29(u61, u1.addProgressionEvent, u1, u61, u62)
            else
                u25(u1.addProgressionEvent, u1, u61, u62)
            end
        else
            return
        end
    end)
end
function u1.addDesignEvent(_, u63, u64) --[[Anonymous function at line 284]]
    --[[
    Upvalues:
        [1] = u3
        [2] = u4
        [3] = u35
        [4] = u29
        [5] = u1
        [6] = u25
        [7] = u7
    --]]
    u3:performTaskOnGAThread(function() --[[Anonymous function at line 285]]
        --[[
        Upvalues:
            [1] = u4
            [2] = u35
            [3] = u63
            [4] = u29
            [5] = u1
            [6] = u64
            [7] = u25
            [8] = u7
        --]]
        if u4:isEventSubmissionEnabled() then
            if u35({
                ["playerId"] = u63,
                ["needsInitialized"] = true,
                ["shouldWarn"] = false,
                ["message"] = "Could not add design event"
            }) then
                u7:addDesignEvent(u63, u64.eventId or "", u64.value or nil)
                return
            elseif u63 then
                u29(u63, u1.addDesignEvent, u1, u63, u64)
            else
                u25(u1.addDesignEvent, u1, u63, u64)
            end
        else
            return
        end
    end)
end
function u1.addErrorEvent(_, u65, u66) --[[Anonymous function at line 306]]
    --[[
    Upvalues:
        [1] = u3
        [2] = u4
        [3] = u35
        [4] = u29
        [5] = u1
        [6] = u25
        [7] = u7
    --]]
    u3:performTaskOnGAThread(function() --[[Anonymous function at line 307]]
        --[[
        Upvalues:
            [1] = u4
            [2] = u35
            [3] = u65
            [4] = u29
            [5] = u1
            [6] = u66
            [7] = u25
            [8] = u7
        --]]
        if u4:isEventSubmissionEnabled() then
            if u35({
                ["playerId"] = u65,
                ["needsInitialized"] = true,
                ["shouldWarn"] = false,
                ["message"] = "Could not add error event"
            }) then
                u7:addErrorEvent(u65, u66.severity or 0, u66.message or "")
                return
            elseif u65 then
                u29(u65, u1.addErrorEvent, u1, u65, u66)
            else
                u25(u1.addErrorEvent, u1, u65, u66)
            end
        else
            return
        end
    end)
end
function u1.setEnabledDebugLog(_, p67) --[[Anonymous function at line 328]]
    --[[
    Upvalues:
        [1] = u11
        [2] = u2
    --]]
    if u11:IsStudio() then
        if p67 then
            u2:setDebugLog(p67)
            u2:i("Debug logging enabled")
        else
            u2:i("Debug logging disabled")
            u2:setDebugLog(p67)
        end
    else
        u2:i("setEnabledDebugLog can only be used in studio")
        return
    end
end
function u1.setEnabledInfoLog(_, p68) --[[Anonymous function at line 342]]
    --[[
    Upvalues:
        [1] = u2
    --]]
    if p68 then
        u2:setInfoLog(p68)
        u2:i("Info logging enabled")
    else
        u2:i("Info logging disabled")
        u2:setInfoLog(p68)
    end
end
function u1.setEnabledVerboseLog(_, p69) --[[Anonymous function at line 352]]
    --[[
    Upvalues:
        [1] = u2
    --]]
    if p69 then
        u2:setVerboseLog(p69)
        u2:ii("Verbose logging enabled")
    else
        u2:ii("Verbose logging disabled")
        u2:setVerboseLog(p69)
    end
end
function u1.setEnabledEventSubmission(_, u70) --[[Anonymous function at line 363]]
    --[[
    Upvalues:
        [1] = u3
        [2] = u4
        [3] = u2
    --]]
    u3:performTaskOnGAThread(function() --[[Anonymous function at line 364]]
        --[[
        Upvalues:
            [1] = u70
            [2] = u4
            [3] = u2
        --]]
        if u70 then
            u4:setEventSubmission(u70)
            u2:i("Event submission enabled")
        else
            u2:i("Event submission disabled")
            u4:setEventSubmission(u70)
        end
    end)
end
function u1.setCustomDimension01(_, u71, u72) --[[Anonymous function at line 375]]
    --[[
    Upvalues:
        [1] = u3
        [2] = u5
        [3] = u4
        [4] = u2
        [5] = u35
    --]]
    u3:performTaskOnGAThread(function() --[[Anonymous function at line 376]]
        --[[
        Upvalues:
            [1] = u5
            [2] = u4
            [3] = u72
            [4] = u2
            [5] = u35
            [6] = u71
        --]]
        if u5:validateDimension(u4._availableCustomDimensions01, u72) then
            if u35({
                ["playerId"] = u71,
                ["needsInitialized"] = true,
                ["shouldWarn"] = true,
                ["message"] = "Could not set custom01 dimension"
            }) then
                u4:setCustomDimension01(u71, u72)
            end
        else
            u2:w("Could not set custom01 dimension value to \'" .. u72 .. "\'. Value not found in available custom01 dimension values")
            return
        end
    end)
end
function u1.setCustomDimension02(_, u73, u74) --[[Anonymous function at line 390]]
    --[[
    Upvalues:
        [1] = u3
        [2] = u5
        [3] = u4
        [4] = u2
        [5] = u35
    --]]
    u3:performTaskOnGAThread(function() --[[Anonymous function at line 391]]
        --[[
        Upvalues:
            [1] = u5
            [2] = u4
            [3] = u74
            [4] = u2
            [5] = u35
            [6] = u73
        --]]
        if u5:validateDimension(u4._availableCustomDimensions02, u74) then
            if u35({
                ["playerId"] = u73,
                ["needsInitialized"] = true,
                ["shouldWarn"] = true,
                ["message"] = "Could not set custom02 dimension"
            }) then
                u4:setCustomDimension02(u73, u74)
            end
        else
            u2:w("Could not set custom02 dimension value to \'" .. u74 .. "\'. Value not found in available custom02 dimension values")
            return
        end
    end)
end
function u1.setCustomDimension03(_, u75, u76) --[[Anonymous function at line 405]]
    --[[
    Upvalues:
        [1] = u3
        [2] = u5
        [3] = u4
        [4] = u2
        [5] = u35
    --]]
    u3:performTaskOnGAThread(function() --[[Anonymous function at line 406]]
        --[[
        Upvalues:
            [1] = u5
            [2] = u4
            [3] = u76
            [4] = u2
            [5] = u35
            [6] = u75
        --]]
        if u5:validateDimension(u4._availableCustomDimensions03, u76) then
            if u35({
                ["playerId"] = u75,
                ["needsInitialized"] = true,
                ["shouldWarn"] = true,
                ["message"] = "Could not set custom03 dimension"
            }) then
                u4:setCustomDimension03(u75, u76)
            end
        else
            u2:w("Could not set custom03 dimension value to \'" .. u76 .. "\'. Value not found in available custom03 dimension values")
            return
        end
    end)
end
function u1.setEnabledReportErrors(_, u77) --[[Anonymous function at line 420]]
    --[[
    Upvalues:
        [1] = u3
        [2] = u4
    --]]
    u3:performTaskOnGAThread(function() --[[Anonymous function at line 421]]
        --[[
        Upvalues:
            [1] = u4
            [2] = u77
        --]]
        u4.ReportErrors = u77
    end)
end
function u1.setEnabledCustomUserId(_, u78) --[[Anonymous function at line 426]]
    --[[
    Upvalues:
        [1] = u3
        [2] = u4
    --]]
    u3:performTaskOnGAThread(function() --[[Anonymous function at line 427]]
        --[[
        Upvalues:
            [1] = u4
            [2] = u78
        --]]
        u4.UseCustomUserId = u78
    end)
end
function u1.setEnabledAutomaticSendBusinessEvents(_, u79) --[[Anonymous function at line 432]]
    --[[
    Upvalues:
        [1] = u3
        [2] = u4
    --]]
    u3:performTaskOnGAThread(function() --[[Anonymous function at line 433]]
        --[[
        Upvalues:
            [1] = u4
            [2] = u79
        --]]
        u4.AutomaticSendBusinessEvents = u79
    end)
end
function u1.addGameAnalyticsTeleportData(_, p80, p81) --[[Anonymous function at line 438]]
    --[[
    Upvalues:
        [1] = u6
    --]]
    local v82 = {}
    for _, v83 in ipairs(p80) do
        local v84 = u6:GetPlayerDataFromCache(v83)
        v84.PlayerTeleporting = true
        local v85 = {
            ["SessionID"] = v84.SessionID,
            ["Sessions"] = v84.Sessions,
            ["SessionStart"] = v84.SessionStart
        }
        v82[tostring(v83)] = v85
    end
    p81.gameanalyticsData = v82
    return p81
end
function u1.getRemoteConfigsValueAsString(_, p86, p87) --[[Anonymous function at line 457]]
    --[[
    Upvalues:
        [1] = u4
    --]]
    return u4:getRemoteConfigsStringValue(p86, p87.key or "", p87.defaultValue or nil)
end
function u1.isRemoteConfigsReady(_, p88) --[[Anonymous function at line 463]]
    --[[
    Upvalues:
        [1] = u4
    --]]
    return u4:isRemoteConfigsReady(p88)
end
function u1.getRemoteConfigsContentAsString(_, p89) --[[Anonymous function at line 467]]
    --[[
    Upvalues:
        [1] = u4
    --]]
    return u4:getRemoteConfigsContentAsString(p89)
end
function u1.PlayerJoined(_, u90) --[[Anonymous function at line 471]]
    --[[
    Upvalues:
        [1] = u6
        [2] = u15
        [3] = u8
        [4] = u13
        [5] = u7
        [6] = u4
        [7] = u2
        [8] = u1
        [9] = u16
        [10] = u12
        [11] = u10
        [12] = u17
        [13] = u22
    --]]
    local v91 = u90:GetJoinData().TeleportData
    local v92 = u6:GetPlayerData(u90)
    local v93
    if v91 then
        v93 = v91.gameanalyticsData
        if v93 then
            local v94 = v91.gameanalyticsData
            local v95 = u90.UserId
            v93 = v94[tostring(v95)]
        end
    else
        v93 = nil
    end
    local v96 = u6:GetPlayerDataFromCache(u90.UserId)
    if v96 then
        if v93 then
            v96.SessionID = v93.SessionID
            v96.SessionStart = v93.SessionStart
        end
        v96.PlayerTeleporting = false
    else
        local v97, v98 = u15.invokeClient("getPlatform", u90, 5)
        local v99 = not v97 and "unknown" or v98
        for v100, v101 in pairs(u6.BasePlayerData) do
            if not v92[v100] then
                if typeof(v101) == "table" then
                    v92[v100] = u8:copyTable(v101)
                else
                    v92[v100] = v101
                end
            end
        end
        local v102, v103 = pcall(function() --[[Anonymous function at line 512]]
            --[[
            Upvalues:
                [1] = u13
                [2] = u90
            --]]
            return u13:GetCountryRegionForPlayerAsync(u90)
        end)
        if v102 then
            v92.CountryCode = v103
        end
        u6.PlayerCache[u90.UserId] = v92
        local v104
        if v99 == "Console" then
            v104 = "uwp_console"
        elseif v99 == "Mobile" then
            v104 = "uwp_mobile"
        else
            local _ = v99 == "Desktop"
            v104 = "uwp_desktop"
        end
        v92.Platform = v104
        v92.OS = v92.Platform .. " 0.0.0"
        if not v102 then
            u7:addSdkErrorEvent(u90.UserId, "event_validation", "player_joined", "string_empty_or_null", "country_code", "")
        end
        local v105 = ""
        local v106
        if u4.UseCustomUserId then
            local v107
            v107, v106 = u15.invokeClient("getCustomUserId", u90, 5)
            if not v107 then
                v106 = v105
            end
        else
            v106 = v105
        end
        if not u8:isStringNullOrEmpty(v106) then
            u2:i("Using custom id: " .. v106)
            v92.CustomUserId = v106
        end
        u1:startNewSession(u90, v93)
        u16 = u16 or u12:WaitForChild("OnPlayerReadyEvent")
        u16:Fire(u90)
        if u4.AutomaticSendBusinessEvents then
            if v92.OwnedGamepasses == nil then
                v92.OwnedGamepasses = {}
                for _, v108 in ipairs(u4._availableGamepasses) do
                    if u10:UserOwnsGamePassAsync(u90.UserId, v108) then
                        local v109 = v92.OwnedGamepasses
                        table.insert(v109, v108)
                    end
                end
                u6.PlayerCache[u90.UserId] = v92
                u6:SavePlayerData(u90)
            else
                local v110 = {}
                for _, v111 in ipairs(u4._availableGamepasses) do
                    if u10:UserOwnsGamePassAsync(u90.UserId, v111) then
                        table.insert(v110, v111)
                    end
                end
                local v112 = {}
                for _, v113 in ipairs(v92.OwnedGamepasses) do
                    v112[v113] = true
                end
                for _, v114 in ipairs(v110) do
                    if not v112[v114] then
                        local v115 = v92.OwnedGamepasses
                        table.insert(v115, v114)
                        local v116 = u17[v114]
                        if not v116 then
                            v116 = u10:GetProductInfo(v114, Enum.InfoType.GamePass)
                            u17[v114] = v116
                        end
                        u1:addBusinessEvent(u90.UserId, {
                            ["amount"] = v116.PriceInRobux,
                            ["itemType"] = "Gamepass",
                            ["itemId"] = u1:filterForBusinessEvent(v116.Name),
                            ["cartType"] = "Website"
                        })
                    end
                end
                u6.PlayerCache[u90.UserId] = v92
                u6:SavePlayerData(u90)
            end
        end
        local v117 = u22[u90.UserId]
        if v117 then
            u22[u90.UserId] = nil
            for _, v118 in ipairs(v117) do
                local v119 = v118.Func
                local v120 = v118.Args
                v119(unpack(v120))
            end
            u2:i("Player initialization queue called #" .. #v117 .. " events")
        end
    end
end
function u1.PlayerRemoved(_, p121) --[[Anonymous function at line 615]]
    --[[
    Upvalues:
        [1] = u6
        [2] = u1
    --]]
    u6:SavePlayerData(p121)
    local v122 = u6:GetPlayerDataFromCache(p121.UserId)
    if v122 then
        if not v122.PlayerTeleporting then
            u1:endSession(p121.UserId)
            return
        end
        u6.PlayerCache[p121.UserId] = nil
        u6.DataStoreQueue.RemoveKey(p121.UserId)
    end
end
function u1.isPlayerReady(_, p123) --[[Anonymous function at line 630]]
    --[[
    Upvalues:
        [1] = u6
    --]]
    return u6:GetPlayerDataFromCache(p123) and true or false
end
function u1.ProcessReceiptCallback(_, u124) --[[Anonymous function at line 638]]
    --[[
    Upvalues:
        [1] = u17
        [2] = u10
        [3] = u1
    --]]
    local u125 = u17[u124.ProductId]
    if not u125 then
        pcall(function() --[[Anonymous function at line 645]]
            --[[
            Upvalues:
                [1] = u125
                [2] = u10
                [3] = u124
                [4] = u17
            --]]
            u125 = u10:GetProductInfo(u124.ProductId, Enum.InfoType.Product)
            u17[u124.ProductId] = u125
        end)
    end
    if u125 then
        u1:addBusinessEvent(u124.PlayerId, {
            ["amount"] = u124.CurrencySpent,
            ["itemType"] = "DeveloperProduct",
            ["itemId"] = u1:filterForBusinessEvent(u125.Name)
        })
    end
end
function u1.GamepassPurchased(_, p126, p127, p128) --[[Anonymous function at line 661]]
    --[[
    Upvalues:
        [1] = u17
        [2] = u10
        [3] = u1
    --]]
    local v129 = u17[p127]
    if not v129 then
        v129 = u10:GetProductInfo(p127, Enum.InfoType.GamePass)
        u17[p127] = v129
    end
    local v130 = 0
    local v131 = "GamePass"
    if p128 then
        v130 = p128.PriceInRobux
        v131 = p128.Name
    elseif v129 then
        v130 = v129.PriceInRobux
        v131 = v129.Name
    end
    u1:addBusinessEvent(p126.UserId, {
        ["amount"] = v130 or 0,
        ["itemType"] = "Gamepass",
        ["itemId"] = u1:filterForBusinessEvent(v131),
        ["gamepassId"] = p127
    })
end
local u132 = { "gameKey", "secretKey" }
function u1.initServer(_, p133, p134) --[[Anonymous function at line 692]]
    --[[
    Upvalues:
        [1] = u1
    --]]
    u1:initialize({
        ["gameKey"] = p133,
        ["secretKey"] = p134
    })
end
function u1.initialize(_, u135) --[[Anonymous function at line 699]]
    --[[
    Upvalues:
        [1] = u3
        [2] = u132
        [3] = u2
        [4] = u1
        [5] = u35
        [6] = u5
        [7] = u7
        [8] = u4
        [9] = u9
        [10] = u21
    --]]
    u3:performTaskOnGAThread(function() --[[Anonymous function at line 700]]
        --[[
        Upvalues:
            [1] = u132
            [2] = u135
            [3] = u2
            [4] = u1
            [5] = u35
            [6] = u5
            [7] = u7
            [8] = u4
            [9] = u9
            [10] = u21
        --]]
        for _, v136 in ipairs(u132) do
            if u135[v136] == nil then
                u2:e("Initialize \'" .. v136 .. "\' option missing")
                return
            end
        end
        if u135.enableInfoLog ~= nil and u135.enableInfoLog then
            u1:setEnabledInfoLog(u135.enableInfoLog)
        end
        if u135.enableVerboseLog ~= nil and u135.enableVerboseLog then
            u1:setEnabledVerboseLog(u135.enableVerboseLog)
        end
        if u135.availableCustomDimensions01 ~= nil and #u135.availableCustomDimensions01 > 0 then
            u1:configureAvailableCustomDimensions01(u135.availableCustomDimensions01)
        end
        if u135.availableCustomDimensions02 ~= nil and #u135.availableCustomDimensions02 > 0 then
            u1:configureAvailableCustomDimensions02(u135.availableCustomDimensions02)
        end
        if u135.availableCustomDimensions03 ~= nil and #u135.availableCustomDimensions03 > 0 then
            u1:configureAvailableCustomDimensions03(u135.availableCustomDimensions03)
        end
        if u135.availableResourceCurrencies ~= nil and #u135.availableResourceCurrencies > 0 then
            u1:configureAvailableResourceCurrencies(u135.availableResourceCurrencies)
        end
        if u135.availableResourceItemTypes ~= nil and #u135.availableResourceItemTypes > 0 then
            u1:configureAvailableResourceItemTypes(u135.availableResourceItemTypes)
        end
        if u135.build ~= nil and #u135.build > 0 then
            u1:configureBuild(u135.build)
        end
        if u135.availableGamepasses ~= nil and #u135.availableGamepasses > 0 then
            u1:configureAvailableGamepasses(u135.availableGamepasses)
        end
        if u135.enableDebugLog ~= nil then
            u1:setEnabledDebugLog(u135.enableDebugLog)
        end
        if u135.automaticSendBusinessEvents ~= nil then
            u1:setEnabledAutomaticSendBusinessEvents(u135.automaticSendBusinessEvents)
        end
        if u135.reportErrors ~= nil then
            u1:setEnabledReportErrors(u135.reportErrors)
        end
        if u135.useCustomUserId ~= nil then
            u1:setEnabledCustomUserId(u135.useCustomUserId)
        end
        if u35({
            ["needsInitialized"] = true,
            ["shouldWarn"] = false
        }) then
            u2:w("SDK already initialized. Can only be called once.")
            return
        else
            local v137 = u135.gameKey
            local v138 = u135.secretKey
            if u5:validateKeys(v137, v138) then
                u7.GameKey = v137
                u7.SecretKey = v138
                u4.Initialized = true
                u9.PlayerAdded:Connect(function(p139) --[[Anonymous function at line 768]]
                    --[[
                    Upvalues:
                        [1] = u1
                    --]]
                    u1:PlayerJoined(p139)
                end)
                u9.PlayerRemoving:Connect(function(p140) --[[Anonymous function at line 773]]
                    --[[
                    Upvalues:
                        [1] = u1
                    --]]
                    u1:PlayerRemoved(p140)
                end)
                for _, v141 in ipairs(u9:GetPlayers()) do
                    coroutine.wrap(u1.PlayerJoined)(u1, v141)
                end
                for _, v142 in ipairs(u21) do
                    local v143 = task.spawn
                    local v144 = v142.Func
                    local v145 = v142.Args
                    v143(v144, unpack(v145))
                end
                u2:i("Server initialization queue called #" .. #u21 .. " events")
                u21 = nil
                u7:processEventQueue()
            else
                u2:w("SDK failed initialize. Game key or secret key is invalid. Can only contain characters A-z 0-9, gameKey is 32 length, secretKey is 40 length. Failed keys - gameKey: " .. v137 .. ", secretKey: " .. v138)
            end
        end
    end)
end
if not u12:FindFirstChild("GameAnalyticsRemoteConfigs") then
    local v146 = Instance.new("RemoteEvent")
    v146.Name = "GameAnalyticsRemoteConfigs"
    v146.Parent = u12
end
if not u12:FindFirstChild("OnPlayerReadyEvent") then
    local v147 = Instance.new("BindableEvent")
    v147.Name = "OnPlayerReadyEvent"
    v147.Parent = u12
end
task.spawn(function() --[[Anonymous function at line 808]]
    --[[
    Upvalues:
        [1] = u18
        [2] = u6
        [3] = u19
        [4] = u20
    --]]
    local v148 = os.time() / 3600
    u18 = u6:GetErrorDataStore((math.floor(v148)))
    while task.wait(3600) do
        local v149 = os.time() / 3600
        u18 = u6:GetErrorDataStore((math.floor(v149)))
        u19 = {}
        u20 = {}
    end
end)
task.spawn(function() --[[Anonymous function at line 820]]
    --[[
    Upvalues:
        [1] = u6
        [2] = u20
        [3] = u19
        [4] = u18
    --]]
    while task.wait(u6.AutoSaveData) do
        for _, v150 in pairs(u20) do
            local v151 = u19[v150]
            local v152 = v151.currentCount - v151.countInDS
            u19[v150].countInDS = u6:IncrementErrorCount(u18, v150, v152)
            u19[v150].currentCount = u19[v150].countInDS
        end
    end
end)
local function u160(p153, p154, p155, p156) --[[Anonymous function at line 831]]
    --[[
    Upvalues:
        [1] = u19
        [2] = u20
        [3] = u1
    --]]
    local v157 = (p155 == nil and "(null)" or p155) .. ": message=" .. (p153 == nil and "(null)" or p153) .. ", trace=" .. (p154 == nil and "(null)" or p154)
    if #v157 > 8192 then
        v157 = string.sub(v157, 1, 8192)
    end
    local v158
    if p156 then
        v158 = p156.UserId
        v157 = v157:gsub(p156.Name, "[LocalPlayer]")
    else
        v158 = nil
    end
    local v159
    if #v157 > 50 then
        v159 = string.sub(v157, 1, 50)
    else
        v159 = v157
    end
    if u19[v159] == nil then
        u20[#u20 + 1] = v159
        u19[v159] = {}
        u19[v159].countInDS = 0
        u19[v159].currentCount = 0
    end
    if u19[v159].currentCount <= 10 then
        u1:addErrorEvent(v158, {
            ["severity"] = u1.EGAErrorSeverity.error,
            ["message"] = v157
        })
        u19[v159].currentCount = u19[v159].currentCount + 1
    end
end
v14.Error:Connect(function(p161, p162, u163) --[[Function name: ErrorHandlerFromServer, line 882]]
    --[[
    Upvalues:
        [1] = u4
        [2] = u160
    --]]
    if u4.ReportErrors then
        if u163 then
            local u164 = nil
            local v165, _ = pcall(function() --[[Anonymous function at line 893]]
                --[[
                Upvalues:
                    [1] = u164
                    [2] = u163
                --]]
                u164 = u163:GetFullName()
            end)
            if v165 then
                return u160(p161, p162, u164)
            end
        end
    else
        return
    end
end)
if not u12:FindFirstChild("GameAnalyticsError") then
    local v166 = Instance.new("RemoteEvent")
    v166.Name = "GameAnalyticsError"
    v166.Parent = u12
end
u12.GameAnalyticsError.OnServerEvent:Connect(function(p167, p168, p169, p170) --[[Anonymous function at line 921]]
    --[[
    Upvalues:
        [1] = u4
        [2] = u160
    --]]
    if u4.ReportErrors then
        u160(p168, p169, p170, p167)
    end
end)
u10.PromptGamePassPurchaseFinished:Connect(function(p171, p172, p173) --[[Anonymous function at line 927]]
    --[[
    Upvalues:
        [1] = u4
        [2] = u1
    --]]
    if u4.AutomaticSendBusinessEvents and p173 then
        u1:GamepassPurchased(p171, p172)
    end
end)
return u1