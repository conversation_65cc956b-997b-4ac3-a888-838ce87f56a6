-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\ByteNet\process\client-ModuleScript.lua
local u1 = game:GetService("ReplicatedStorage")
local u2 = game:GetService("RunService")
require(script.Parent.Parent.types)
local u3 = require(script.Parent.read)
local u4 = require(script.Parent.bufferWriter)
local u5 = u4.alloc
local u6 = u4.u8
local u7 = u4.load
local function u10(p8, p9) --[[Anonymous function at line 12]]
    --[[
    Upvalues:
        [1] = u3
    --]]
    u3(p8, p9)
end
local function v11() --[[Anonymous function at line 17]]
    return {
        ["cursor"] = 0,
        ["size"] = 256,
        ["references"] = {},
        ["buff"] = buffer.create(256)
    }
end
local u12 = v11()
local u13 = v11()
return {
    ["sendReliable"] = function(p14, p15, p16) --[[Function name: sendReliable, line 41]]
        --[[
        Upvalues:
            [1] = u12
            [2] = u7
            [3] = u5
            [4] = u6
            [5] = u4
        --]]
        u12 = u7(u12)
        u5(1)
        u6(p14)
        p15(p16)
        u12 = u4.export()
    end,
    ["sendUnreliable"] = function(p17, p18, p19) --[[Function name: sendUnreliable, line 51]]
        --[[
        Upvalues:
            [1] = u13
            [2] = u7
            [3] = u5
            [4] = u6
            [5] = u4
        --]]
        u13 = u7(u13)
        u5(1)
        u6(p17)
        p18(p19)
        u13 = u4.export()
    end,
    ["start"] = function() --[[Function name: start, line 61]]
        --[[
        Upvalues:
            [1] = u1
            [2] = u10
            [3] = u2
            [4] = u12
            [5] = u13
        --]]
        local u20 = u1:WaitForChild("ByteNetReliable")
        u20.OnClientEvent:Connect(u10)
        local u21 = u1:WaitForChild("ByteNetUnreliable")
        u21.OnClientEvent:Connect(u10)
        u2.Heartbeat:Connect(function() --[[Anonymous function at line 68]]
            --[[
            Upvalues:
                [1] = u12
                [2] = u20
                [3] = u13
                [4] = u21
            --]]
            if u12.cursor > 0 then
                local v22 = u12
                local v23 = v22.cursor
                local v24 = buffer.create(v23)
                buffer.copy(v24, 0, v22.buff, 0, v23)
                local v25
                if #v22.references > 0 then
                    v25 = v22.references
                else
                    v25 = nil
                end
                u20:FireServer(v24, v25)
                u12.cursor = 0
                table.clear(u12.references)
            end
            if u13.cursor > 0 then
                local v26 = u13
                local v27 = v26.cursor
                local v28 = buffer.create(v27)
                buffer.copy(v28, 0, v26.buff, 0, v27)
                local v29
                if #v26.references > 0 then
                    v29 = v26.references
                else
                    v29 = nil
                end
                u21:FireServer(v28, v29)
                u13.cursor = 0
                table.clear(u13.references)
            end
        end)
    end
}