-- Full Path: -Grow-a-Garden-\ReplicatedStorage\CullThrottle-ModuleScript.lua
local u1 = game:GetService("RunService")
local u2 = game:GetService("CollectionService")
if u1:IsServer() and not u1:IsEdit() then
    error("CullThrottle is a client side effect and cannot be used on the server")
end
require(script.types)
local u3 = require(script.PriorityQueue)
local u4 = require(script.Config)
local u5 = require(script.Utilities.GoodSignal)
local u6 = require(script.Utilities.ObjectDimensions)
local u7 = require(script.Utilities.CameraCache)
local u8 = {}
u8.__index = u8
function u8._resizeTables(p9) --[[Anonymous function at line 73]]
    local v10 = table.create(#p9._visibleVoxelKeys)
    table.move(p9._visibleVoxelKeys, 1, #p9._visibleVoxelKeys, 1, v10)
    p9._visibleVoxelKeys = v10
    local v11 = table.create(#p9._volumesToCheck)
    table.move(p9._volumesToCheck, 1, #p9._volumesToCheck, 1, v11)
    p9._volumesToCheck = v11
end
function u8._addVoxelKeyToVisible(p12, p13) --[[Anonymous function at line 85]]
    --[[
    Upvalues:
        [1] = u7
    --]]
    local v14 = p12._config.voxelSize
    local v15 = u7.Position // v14
    local v16 = p13 - v15
    local v17 = v16.X
    local v18 = math.abs(v17)
    local v19 = v16.Y
    local v20 = v18 + math.abs(v19)
    local v21 = v16.Z
    local v22 = v20 + math.abs(v21)
    local v23 = p12._visibleVoxelKeys
    local v24 = #v23
    local v25 = 1
    while v25 <= v24 do
        local v26 = (v25 + v24) / 2
        local v27 = math.floor(v26)
        local v28 = v23[v27]
        if v28 == p13 then
            return
        end
        local v29 = v28 - v15
        local v30 = v29.X
        local v31 = math.abs(v30)
        local v32 = v29.Y
        local v33 = v31 + math.abs(v32)
        local v34 = v29.Z
        if v33 + math.abs(v34) < v22 then
            v25 = v27 + 1
        else
            v24 = v27 - 1
        end
    end
    table.insert(v23, v25, p13)
end
function u8._updateDesiredVoxelKeys(p35, p36, p37) --[[Anonymous function at line 118]]
    --[[
    Upvalues:
        [1] = u7
    --]]
    local v38 = p35._config.voxelSize
    local v39 = p35._config._radiusThresholdForCorners
    local v40 = {}
    local v41 = p37.cframe
    local v42 = p37.halfBoundingBox
    local v43 = v41.Position
    v40[v43 // v38] = true
    if v39 < p37.radius then
        for _, v44 in {
            (v41 * CFrame.new(v42.X, v42.Y, v42.Z)).Position,
            (v41 * CFrame.new(-v42.X, -v42.Y, -v42.Z)).Position,
            (v41 * CFrame.new(-v42.X, v42.Y, v42.Z)).Position,
            (v41 * CFrame.new(-v42.X, -v42.Y, v42.Z)).Position,
            (v41 * CFrame.new(-v42.X, v42.Y, -v42.Z)).Position,
            (v41 * CFrame.new(v42.X, v42.Y, -v42.Z)).Position,
            (v41 * CFrame.new(v42.X, -v42.Y, -v42.Z)).Position,
            (v41 * CFrame.new(v42.X, -v42.Y, v42.Z)).Position
        } do
            v40[v44 // v38] = true
        end
    end
    for v45 in p37.voxelKeys do
        if v40[v45] then
            v40[v45] = nil
        else
            v40[v45] = false
        end
    end
    p37.desiredVoxelKeys = v40
    if next(v40) and not p35._updateObjectVoxelsQueue:contains(p36) then
        local v46 = v43 - u7.Position
        local v47 = v46.X
        local v48 = math.abs(v47)
        local v49 = v46.Y
        local v50 = v48 + math.abs(v49)
        local v51 = v46.Z
        local v52 = v50 + math.abs(v51)
        p35._updateObjectVoxelsQueue:addToIncomingBatch(p36, v52)
    end
    return v40
end
function u8._insertToVoxel(p53, p54, p55) --[[Anonymous function at line 176]]
    local v56 = p53._voxels[p54]
    if v56 then
        table.insert(v56, p55)
    else
        p53._voxels[p54] = { p55 }
    end
end
function u8._removeFromVoxel(p57, p58, p59) --[[Anonymous function at line 187]]
    local v60 = p57._voxels[p58]
    if v60 then
        local v61 = table.find(v60, p59)
        if v61 then
            local v62 = #v60
            if v62 == 1 then
                p57._voxels[p58] = nil
                return
            elseif v62 == v61 then
                v60[v61] = nil
            else
                local v63 = v60[v62]
                v60[v62] = nil
                v60[v61] = v63
            end
        else
            return
        end
    else
        return
    end
end
function u8._updateObjectVoxels(p64, p65, p66) --[[Anonymous function at line 216]]
    debug.profilebegin("updateObjectVoxels")
    p64._updateObjectVoxelsQueue:enqueueIncomingBatch()
    p64._updateObjectVoxelsQueue:clearIncomingBatch()
    while not p64._updateObjectVoxelsQueue:isEmpty() and os.clock() - p66 < p65 do
        local v67 = p64._updateObjectVoxelsQueue:dequeue()
        local v68 = p64._objects[v67]
        if v68 and next(v68.desiredVoxelKeys) then
            for v69, v70 in v68.desiredVoxelKeys do
                if v70 then
                    p64:_insertToVoxel(v69, v67)
                    v68.voxelKeys[v69] = true
                    v68.desiredVoxelKeys[v69] = nil
                else
                    p64:_removeFromVoxel(v69, v67)
                    v68.voxelKeys[v69] = nil
                    v68.desiredVoxelKeys[v69] = nil
                end
            end
        end
    end
    debug.profileend()
end
function u8._pollPhysicsObjects(p71, p72, p73) --[[Anonymous function at line 245]]
    --[[
    Upvalues:
        [1] = u7
        [2] = u6
    --]]
    debug.profilebegin("pollPhysicsObjects")
    local v74 = p71._physicsObjectIterIndex
    if #p71._physicsObjects == 0 then
        debug.profileend()
        return
    end
    local v75 = u7.Position
    while os.clock() - p73 < p72 do
        local v76 = p71._physicsObjects[p71._physicsObjectIterIndex]
        p71._physicsObjectIterIndex = p71._physicsObjectIterIndex % #p71._physicsObjects + 1
        if v76 then
            local v77 = p71._objects[v76]
            if v77 then
                local v78 = u6.getObjectCFrame(v77)
                if v78 then
                    v77.cframe = v78
                    v77.distance = (v78.Position - v75).Magnitude
                    p71:_updateDesiredVoxelKeys(v76, v77)
                end
                if v74 == p71._physicsObjectIterIndex then
                    break
                end
            else
                warn("Physics object", v76, "is missing objectData, this shouldn\'t happen!")
            end
        end
    end
    debug.profileend()
end
function u8._isBoxInFrustum(p79, p80, p81, p82, p83, p84, p85, p86, p87) --[[Anonymous function at line 285]]
    debug.profilebegin("isBoxInFrustum")
    local v88 = p79._config.voxelSize
    local v89 = p82 * v88
    local v90 = p85 * v88
    local v91 = p83 * v88
    local v92 = p86 * v88
    local v93 = p84 * v88
    local v94 = p87 * v88
    local v95 = math.min(v89, v90)
    local v96 = math.max(v89, v90)
    local v97 = math.min(v91, v92)
    local v98 = math.max(v91, v92)
    local v99 = math.min(v93, v94)
    local v100 = math.max(v93, v94)
    local v101 = (v95 + v96) / 2
    local v102 = (v97 + v98) / 2
    local v103 = (v99 + v100) / 2
    local v104 = Vector3.new(v101, v102, v103)
    local v105 = (v96 - v95) / 2
    local v106 = (v98 - v97) / 2
    local v107 = (v100 - v99) / 2
    local v108 = Vector3.new(v105, v106, v107)
    local v109 = true
    for v110 = 1, #p81, 2 do
        local v111 = p81[v110]
        local v112 = p81[v110 + 1]
        local v113 = (v104 - v111):Dot(v112)
        local v114 = v108.X * v112.X
        local v115 = math.abs(v114)
        local v116 = v108.Y * v112.Y
        local v117 = v115 + math.abs(v116)
        local v118 = v108.Z * v112.Z
        local v119 = v117 + math.abs(v118)
        if v119 + 0.0001 < v113 then
            debug.profileend()
            return false, false
        end
        if p80 and v113 + v119 > 0.0001 then
            v109 = false
        end
    end
    debug.profileend()
    return true, v109
end
function u8._ingestVoxel(p120, p121, p122) --[[Anonymous function at line 342]]
    --[[
    Upvalues:
        [1] = u7
        [2] = u6
    --]]
    debug.profilebegin("ingestVoxel")
    local v123 = u7.Position
    local v124 = p120._config._renderDistance
    local v125 = p120._config.bestRefreshRate
    local v126 = p120._config.worstRefreshRate
    local v127 = p120._config._refreshRateRange
    local v128 = p120._objectVisibilityTimestamps
    for _, v129 in p122 do
        local v130 = p120._objects[v129]
        if v130 and v130.lastCheckClock ~= p121 then
            v130.lastCheckClock = p121
            local v131 = (v130.cframe.Position - v123).Magnitude
            v130.distance = v131
            if v124 >= v131 then
                local v132 = u6.getScreenSize(v131, v130.radius)
                local v133 = p121 - v130.lastUpdateClock + v130.jitterOffset
                local v134
                if v133 <= v125 then
                    v134 = (1 - v132) * 1000000
                elseif v126 <= v133 then
                    v134 = 0.9 - v132
                elseif v131 < 30 then
                    v134 = v131 / 30
                else
                    local v135 = 85 * (1 - v132)
                    local v136 = 13 * (1 - (v133 - v125) / v127)
                    local v137 = 2 * (v131 / v124)
                    v134 = v135 + v136 + v137
                end
                p120._visibleObjectsQueue:addToIncomingBatch(v129, v134)
                if v128[v129] == nil then
                    p120.ObjectEnteredView:Fire(v129)
                end
                v128[v129] = p121
            end
        end
    end
    debug.profileend()
end
function u8._fastApproxIngestRemainingVoxels(p138, p139, p140) --[[Anonymous function at line 410]]
    local v141 = p138._voxels
    local v142 = p138._visibleVoxelKeys
    local v143 = p138._objectVisibilityTimestamps
    for v144 = p140, #v142 do
        local v145 = v141[v142[v144]]
        if v145 then
            p138._visibleObjectsQueue:addMultipleToIncomingBatch(v145, table.create(#v145, v144 * 10000))
            for _, v146 in v145 do
                if v143[v146] == nil then
                    p138.ObjectEnteredView:Fire(v146)
                end
                v143[v146] = p139
            end
        end
    end
    p138._perfMetrics._skippedIngest[1] = #v142 + 1 - p140
end
function u8._searchVolume(p147, p148, p149, p150, p151, p152, p153, p154, p155) --[[Anonymous function at line 437]]
    local v156
    if p153 - p150 == 1 and p154 - p151 == 1 then
        v156 = p155 - p152 == 1
    else
        v156 = false
    end
    local v157 = p147._voxels
    local v158 = p147._lastVoxelVisibility
    local v159 = 0.2 * (1 + (math.random() * 0.1 - 0.05))
    if v156 then
        local v160 = Vector3.new(p150, p151, p152)
        if v157[v160] then
            if p148 - (v158[v160] or 0) < v159 then
                p147:_addVoxelKeyToVisible(v160)
                return
            elseif p147:_isBoxInFrustum(false, p149, p150, p151, p152, p153, p154, p155) then
                v158[v160] = p148
                p147:_addVoxelKeyToVisible(v160)
            else
                v158[v160] = nil
            end
        else
            return
        end
    end
    debug.profilebegin("checkBoxVisibilityCache")
    local v161 = true
    local v162 = false
    for v163 = p150, p153 - 1 do
        for v164 = p151, p154 - 1 do
            for v165 = p152, p155 - 1 do
                local v166 = Vector3.new(v163, v164, v165)
                if v157[v166] then
                    v162 = true
                    if v159 <= p148 - (v158[v166] or 0) then
                        v161 = false
                        break
                    end
                end
            end
            if v161 == false then
                break
            end
        end
        if v161 == false then
            break
        end
    end
    debug.profileend()
    if v162 then
        if v161 then
            debug.profilebegin("allVoxelsVisible")
            for v167 = p150, p153 - 1 do
                for v168 = p151, p154 - 1 do
                    for v169 = p152, p155 - 1 do
                        local v170 = Vector3.new(v167, v168, v169)
                        if v157[v170] then
                            p147:_addVoxelKeyToVisible(v170)
                        end
                    end
                end
            end
            debug.profileend()
            return
        else
            local v171, v172 = p147:_isBoxInFrustum(true, p149, p150, p151, p152, p153, p154, p155)
            if v171 then
                if v172 then
                    debug.profilebegin("isCompletelyInside")
                    for v173 = p150, p153 - 1 do
                        for v174 = p151, p154 - 1 do
                            for v175 = p152, p155 - 1 do
                                local v176 = Vector3.new(v173, v174, v175)
                                if v157[v176] then
                                    v158[v176] = p148
                                    p147:_addVoxelKeyToVisible(v176)
                                end
                            end
                        end
                    end
                    debug.profileend()
                    return
                else
                    local v177 = p153 - p150
                    local v178 = p154 - p151
                    local v179 = p155 - p152
                    local v180, v181
                    if v178 <= v177 and v179 <= v177 then
                        local v182 = (p150 + p153) // 2
                        v180 = {
                            v182,
                            p151,
                            p152,
                            p153,
                            p154,
                            p155
                        }
                        v181 = {
                            p150,
                            p151,
                            p152,
                            v182,
                            p154,
                            p155
                        }
                    elseif v177 <= v178 and v179 <= v178 then
                        local v183 = (p151 + p154) // 2
                        v180 = {
                            p150,
                            v183,
                            p152,
                            p153,
                            p154,
                            p155
                        }
                        v181 = {
                            p150,
                            p151,
                            p152,
                            p153,
                            v183,
                            p155
                        }
                    else
                        local v184 = (p152 + p155) // 2
                        v180 = {
                            p150,
                            p151,
                            v184,
                            p153,
                            p154,
                            p155
                        }
                        v181 = {
                            p150,
                            p151,
                            p152,
                            p153,
                            p154,
                            v184
                        }
                    end
                    if math.random(1, 2) == 1 then
                        local v185 = p147._volumesToCheck
                        table.insert(v185, v181)
                        local v186 = p147._volumesToCheck
                        table.insert(v186, v180)
                    else
                        local v187 = p147._volumesToCheck
                        table.insert(v187, v180)
                        local v188 = p147._volumesToCheck
                        table.insert(v188, v181)
                    end
                end
            else
                debug.profilebegin("clearBoxVisibilityCache")
                for v189 = p150, p153 - 1 do
                    for v190 = p151, p154 - 1 do
                        for v191 = p152, p155 - 1 do
                            local v192 = Vector3.new(v189, v190, v191)
                            if v158[v192] then
                                v158[v192] = nil
                            end
                        end
                    end
                end
                debug.profileend()
                return
            end
        end
    else
        return
    end
end
function u8._reuseCachedVisibilityForRemainingVolumes(p193) --[[Anonymous function at line 612]]
    local v194 = p193._voxels
    local v195 = p193._lastVoxelVisibility
    local v196 = 0
    for _, v197 in p193._volumesToCheck do
        local v198, v199, v200, v201, v202, v203 = table.unpack(v197)
        if v201 - v198 == 1 and (v202 - v199 == 1 and v203 - v200 == 1) then
            local v204 = Vector3.new(v198, v199, v200)
            if v194[v204] then
                v196 = v196 + 1
                if v195[v204] then
                    p193:_addVoxelKeyToVisible(v204)
                end
            end
        else
            for v205 = v198, v201 - 1 do
                for v206 = v199, v202 - 1 do
                    for v207 = v200, v203 - 1 do
                        local v208 = Vector3.new(v205, v206, v207)
                        if v194[v208] then
                            v196 = v196 + 1
                            if v195[v208] then
                                p193:_addVoxelKeyToVisible(v208)
                            end
                        end
                    end
                end
            end
        end
    end
    p193._perfMetrics._skippedSearch[1] = v196
end
function u8._clearVisibleObjects(p209) --[[Anonymous function at line 653]]
    table.clear(p209._visibleVoxelKeys)
    p209._visibleObjectsQueue:clearIncomingBatch()
    p209._visibleObjectsQueue:clear()
end
function u8._fillVisibleObjectsQueue(p210) --[[Anonymous function at line 659]]
    debug.profilebegin("fillVisibleObjectsQueue")
    p210._visibleObjectsQueue:enqueueIncomingBatch()
    debug.profileend()
end
function u8._signalVisibilityChanges(p211, p212) --[[Anonymous function at line 665]]
    debug.profilebegin("signalVisibilityChanges")
    local v213 = p211._objectVisibilityTimestamps
    for v214, v215 in v213 do
        if v215 ~= p212 then
            v213[v214] = nil
            p211.ObjectExitedView:Fire(v214)
        end
    end
    debug.profileend()
end
function u8._findVisibleObjects(p216, p217) --[[Anonymous function at line 680]]
    --[[
    Upvalues:
        [1] = u7
    --]]
    debug.profilebegin("findVisibleObjects")
    p216:_clearVisibleObjects()
    p216._perfMetrics._skippedIngest[4] = nil
    p216._perfMetrics._skippedSearch[4] = nil
    local v218 = p216._perfMetrics._skippedIngest
    table.insert(v218, 1, 0)
    local v219 = p216._perfMetrics._skippedSearch
    table.insert(v219, 1, 0)
    local v220 = p216._config.searchTimeBudget
    local v221 = p216._config.ingestTimeBudget
    local v222 = p216._config.voxelSize
    local v223 = p216._config._renderDistance
    local v224, v225, v226 = u7.getPlanesAndBounds(v223, v222)
    local v227 = v225.X
    local v228 = v225.Y
    local v229 = v225.Z
    local v230 = v226.X + 1
    local v231 = v226.Y + 1
    local v232 = v226.Z + 1
    local v233 = p216._volumesToCheck
    table.clear(v233)
    if v227 == v230 or (v228 == v231 or v229 == v232) then
        p216._volumesToCheck[1] = {
            v227,
            v228,
            v229,
            v230,
            v231,
            v232
        }
    else
        local v234 = (v225 + v226) // 2
        local v235 = v234.X
        local v236 = v234.Y
        local v237 = v234.Z
        v233[1] = {
            v227,
            v228,
            v229,
            v235,
            v236,
            v237
        }
        v233[2] = {
            v235,
            v228,
            v229,
            v230,
            v236,
            v237
        }
        v233[3] = {
            v227,
            v228,
            v237,
            v235,
            v236,
            v232
        }
        v233[4] = {
            v235,
            v228,
            v237,
            v230,
            v236,
            v232
        }
        v233[5] = {
            v227,
            v236,
            v229,
            v235,
            v231,
            v237
        }
        v233[6] = {
            v235,
            v236,
            v229,
            v230,
            v231,
            v237
        }
        v233[7] = {
            v227,
            v236,
            v237,
            v235,
            v231,
            v232
        }
        v233[8] = {
            v235,
            v236,
            v237,
            v230,
            v231,
            v232
        }
        for v238 = 8, 2, -1 do
            local v239 = math.random(1, v238)
            local v240 = v233[v239]
            local v241 = v233[v238]
            v233[v238] = v240
            v233[v239] = v241
        end
    end
    local v242 = p216._voxels
    debug.profilebegin("searchVolumes")
    while #p216._volumesToCheck > 0 do
        local v243 = #p216._volumesToCheck
        local v244 = p216._volumesToCheck[v243]
        p216._volumesToCheck[v243] = nil
        p216:_searchVolume(p217, v224, v244[1], v244[2], v244[3], v244[4], v244[5], v244[6])
        if v220 <= os.clock() - p217 then
            p216:_reuseCachedVisibilityForRemainingVolumes()
            break
        end
    end
    p216._perfMetrics._searchDuration = os.clock() - p217
    debug.profileend()
    debug.profilebegin("ingestVoxels")
    local v245 = p216._visibleVoxelKeys
    local v246 = os.clock()
    for v247, v248 in v245 do
        local v249 = v242[v248]
        if v249 then
            p216:_ingestVoxel(p217, v249)
            if v221 <= os.clock() - v246 then
                p216:_fastApproxIngestRemainingVoxels(p217, v247 + 1)
                break
            end
        end
    end
    p216._perfMetrics._ingestDuration = os.clock() - v246
    debug.profileend()
    debug.profileend()
end
function u8._adjustDynamicRenderDistance(p250) --[[Anonymous function at line 782]]
    if p250._config.dynamicRenderDistance then
        if p250._perfMetrics._getAverage(p250._perfMetrics._skippedIngest) == 0 and (p250._perfMetrics._getAverage(p250._perfMetrics._skippedSearch) == 0 and (p250._perfMetrics._averageObjectDeltaTime < p250._config._refreshRateMidpoint and (p250._perfMetrics._searchDuration / p250._config.searchTimeBudget <= 1 and p250._perfMetrics._ingestDuration / p250._config.ingestTimeBudget <= 1))) then
            if p250._perfMetrics._averageObjectDeltaTime <= p250._config.bestRefreshRate then
                local v251 = p250._config
                local v252 = p250._config._renderDistance + p250._config.renderDistanceTarget * 0.015
                local v253 = p250._config._maxRenderDistance
                v251._renderDistance = math.min(v252, v253)
            end
        else
            local v254 = p250._config
            local v255 = p250._config._renderDistance - p250._config.renderDistanceTarget * 0.03
            local v256 = p250._config._minRenderDistance
            v254._renderDistance = math.max(v255, v256)
        end
    else
        return
    end
end
function u8._processObjectVisibility(p257, p258) --[[Anonymous function at line 807]]
    if not p257._processedVisibilityThisFrame then
        debug.profilebegin("CullThrottle.processObjectVisibility")
        p257:_pollPhysicsObjects(0.00005, p258)
        p257:_updateObjectVoxels(0.00005, os.clock())
        p257:_findVisibleObjects(p258)
        p257:_signalVisibilityChanges(p258)
        p257:_adjustDynamicRenderDistance()
        p257._processedVisibilityThisFrame = true
        debug.profileend()
    end
end
function u8.new() --[[Anonymous function at line 836]]
    --[[
    Upvalues:
        [1] = u8
        [2] = u4
        [3] = u5
        [4] = u3
        [5] = u1
    --]]
    local v259 = u8
    local u260 = setmetatable({}, v259)
    u260._config = u4.new()
    u260.ObjectEnteredView = u5.new()
    u260.ObjectExitedView = u5.new()
    u260._perfMetrics = {
        ["_skippedSearch"] = table.create(4, 0),
        ["_skippedIngest"] = table.create(4, 0),
        ["_searchDuration"] = 0,
        ["_ingestDuration"] = 0,
        ["_averageObjectDeltaTime"] = u260._config.bestRefreshRate,
        ["_getAverage"] = function(p261) --[[Function name: _getAverage, line 850]]
            local v262 = 0
            for _, v263 in p261 do
                v262 = v262 + v263
            end
            return v262 / 4
        end
    }
    u260._voxels = {}
    u260._objects = {}
    u260._physicsObjects = {}
    u260._physicsObjectIterIndex = 1
    u260._updateObjectVoxelsQueue = u3.new()
    u260._visibleObjectsQueue = u3.new()
    u260._visibleVoxelKeys = {}
    u260._objectVisibilityTimestamps = {}
    u260._lastVoxelVisibility = {}
    u260._lastTableResize = os.clock()
    u260._processedVisibilityThisFrame = false
    u260._volumesToCheck = table.create(8)
    u260._tagConnections = {}
    u260._renderConnection = u1.PreRender:Connect(function() --[[Anonymous function at line 872]]
        --[[
        Upvalues:
            [1] = u260
        --]]
        u260._processedVisibilityThisFrame = false
        local v264 = os.clock()
        if v264 - u260._lastTableResize > 5 then
            u260._lastTableResize = v264
            u260:_resizeTables()
        end
        if u260._config.computeVisibilityOnlyOnDemand == false or (u260.ObjectEnteredView._connectionCount > 0 or u260.ObjectExitedView._connectionCount > 0) then
            u260:_processObjectVisibility(v264)
        end
    end)
    return u260
end
function u8.AddObject(u265, u266) --[[Anonymous function at line 895]]
    --[[
    Upvalues:
        [1] = u6
        [2] = u7
    --]]
    local v267, v268 = u6.getObjectCFrameSource(u266)
    local v269, v270 = u6.getObjectBoundingBoxSource(u266)
    if not (v267 and v268) then
        error("Cannot add " .. u266:GetFullName() .. " to CullThrottle, cframe is unknown")
    end
    if not (v269 and v270) then
        error("Cannot add " .. u266:GetFullName() .. " to CullThrottle, bounding box is unknown")
    end
    local u271 = {
        ["cframe"] = CFrame.new(),
        ["distance"] = 1,
        ["halfBoundingBox"] = Vector3.new(1, 1, 1),
        ["radius"] = 0.5,
        ["voxelKeys"] = {},
        ["desiredVoxelKeys"] = {},
        ["lastCheckClock"] = 0,
        ["lastUpdateClock"] = 0,
        ["jitterOffset"] = math.random(-1000, 1000) / 500000,
        ["changeConnections"] = {},
        ["cframeSource"] = v267,
        ["cframeType"] = v268,
        ["boundingBoxSource"] = v269,
        ["boundingBoxType"] = v270
    }
    local v272 = u6.getObjectCFrame(u271)
    if not v272 then
        error("Cannot add " .. u266:GetFullName() .. " to CullThrottle, cframe is unknown")
    end
    u271.cframe = v272
    u271.distance = (u7.Position - v272.Position).Magnitude
    local v273 = u6.getObjectBoundingBox(u271)
    if not v273 then
        error("Cannot add " .. u266:GetFullName() .. " to CullThrottle, bounding box is unknown")
    end
    u271.halfBoundingBox = v273 / 2
    local v274 = v273.X
    local v275 = v273.Y
    local v276 = v273.Z
    u271.radius = math.max(v274, v275, v276) / 2
    u6.subscribeToDimensionChanges(u271, function(p277) --[[Anonymous function at line 940]]
        --[[
        Upvalues:
            [1] = u271
            [2] = u7
            [3] = u265
            [4] = u266
        --]]
        u271.cframe = p277
        u271.distance = (p277.Position - u7.Position).Magnitude
        u265:_updateDesiredVoxelKeys(u266, u271)
    end, function(p278) --[[Anonymous function at line 945]]
        --[[
        Upvalues:
            [1] = u271
            [2] = u265
            [3] = u266
        --]]
        u271.halfBoundingBox = p278 / 2
        local v279 = u271
        local v280 = p278.X
        local v281 = p278.Y
        local v282 = p278.Z
        v279.radius = math.max(v280, v281, v282) / 2
        u265:_updateDesiredVoxelKeys(u266, u271)
    end)
    u265:_updateDesiredVoxelKeys(u266, u271)
    u265._objects[u266] = u271
    for v283, v284 in u271.desiredVoxelKeys do
        if v284 then
            u265:_insertToVoxel(v283, u266)
            u271.voxelKeys[v283] = true
            u271.desiredVoxelKeys[v283] = nil
        end
    end
    return u271
end
function u8.AddPhysicsObject(p285, p286) --[[Anonymous function at line 967]]
    p285:AddObject(p286)
    local v287 = p285._physicsObjects
    table.insert(v287, p286)
end
function u8.RemoveObject(p288, p289) --[[Anonymous function at line 975]]
    local v290 = p288._objects[p289]
    if v290 then
        p288._objects[p289] = nil
        local v291 = table.find(p288._physicsObjects, p289)
        if v291 then
            local v292 = #p288._physicsObjects
            if v291 ~= v292 then
                p288._physicsObjects[v291] = p288._physicsObjects[v292]
            end
            p288._physicsObjects[v292] = nil
        end
        for _, v293 in v290.changeConnections do
            v293:Disconnect()
        end
        for v294 in v290.voxelKeys do
            p288:_removeFromVoxel(v294, p289)
        end
    end
end
function u8.CaptureTag(u295, p296) --[[Anonymous function at line 1001]]
    --[[
    Upvalues:
        [1] = u2
    --]]
    u295._tagConnections[p296] = { u2:GetInstanceAddedSignal(p296):Connect(function(p297) --[[Anonymous function at line 1003]]
            --[[
            Upvalues:
                [1] = u295
            --]]
            if p297:IsA("BasePart") and p297.Anchored == false then
                u295:AddPhysicsObject(p297)
            else
                u295:AddObject(p297)
            end
        end), u2:GetInstanceRemovedSignal(p296):Connect(function(p298) --[[Anonymous function at line 1010]]
            --[[
            Upvalues:
                [1] = u295
            --]]
            u295:RemoveObject(p298)
        end) }
    for _, v299 in u2:GetTagged(p296) do
        if v299:IsA("BasePart") and v299.Anchored == false then
            u295:AddPhysicsObject(v299)
        else
            u295:AddObject(v299)
        end
    end
end
function u8.ReleaseTag(p300, p301) --[[Anonymous function at line 1024]]
    local v302 = p300._tagConnections[p301]
    if v302 then
        for _, v303 in v302 do
            v303:Disconnect()
        end
    end
end
function u8.RemoveObjectsWithTag(p304, p305) --[[Anonymous function at line 1035]]
    for v306 in p304._objects do
        if v306:HasTag(p305) then
            p304:RemoveObject(v306)
        end
    end
end
function u8.GetVisibleObjects(p307) --[[Anonymous function at line 1043]]
    p307:_processObjectVisibility(os.clock())
    return table.clone(p307._visibleObjectsQueue._incomingBatch.items)
end
function u8.IterateObjectsToUpdate(u308) --[[Anonymous function at line 1050]]
    debug.profilebegin("CullThrottle.iterObjects")
    u308:_processObjectVisibility(os.clock())
    u308:_fillVisibleObjectsQueue()
    local u309 = os.clock()
    local u310 = u308._visibleObjectsQueue
    local u311 = u308._config.updateTimeBudget
    local u312 = u308._config.strictlyEnforceWorstRefreshRate and 1 or u311 * 1.15
    local u313 = 0
    local u314 = 0
    return function() --[[Anonymous function at line 1070]]
        --[[
        Upvalues:
            [1] = u310
            [2] = u308
            [3] = u314
            [4] = u313
            [5] = u312
            [6] = u311
            [7] = u309
        --]]
        if u310:isEmpty() then
            debug.profileend()
            u308._perfMetrics._averageObjectDeltaTime = u314 / u313
            return
        else
            local v315
            if u310:peekPriority() < 0.9 then
                v315 = u312
            else
                v315 = u311
            end
            if v315 <= os.clock() - u309 then
                u308._visibleObjectsQueue:clear()
                debug.profileend()
                u308._perfMetrics._averageObjectDeltaTime = u314 / u313
                return
            else
                local v316 = u310:dequeue()
                if v316 then
                    local v317 = u308._objects[v316]
                    if v317 then
                        local v318 = u309 - v317.lastUpdateClock
                        v317.lastUpdateClock = u309
                        if v318 < 1 then
                            u314 = u314 + v318
                            u313 = u313 + 1
                        end
                        return v316, v318, v317.distance
                    end
                    debug.profileend()
                    u308._perfMetrics._averageObjectDeltaTime = u314 / u313
                else
                    debug.profileend()
                    u308._perfMetrics._averageObjectDeltaTime = u314 / u313
                end
            end
        end
    end
end
function u8.SetVoxelSize(p319, p320) --[[Anonymous function at line 1124]]
    p319._config.voxelSize = p320
    p319._config._halfVoxelSizeVec = Vector3.new(1, 1, 1) * (p320 / 2)
    p319._config._radiusThresholdForCorners = p320 * 0.125
    p319._voxels = {}
    for v321, v322 in p319._objects do
        p319:_updateDesiredVoxelKeys(v321, v322)
    end
    p319:_updateObjectVoxels(5, os.clock())
end
function u8.SetRenderDistanceTarget(p323, p324) --[[Anonymous function at line 1139]]
    p323._config.renderDistanceTarget = p324
    p323._config._renderDistance = p324
    p323._config._minRenderDistance = p324 / 3
    p323._config._maxRenderDistance = p324 * 5
end
function u8.SetTimeBudgets(p325, p326, p327, p328) --[[Anonymous function at line 1146]]
    p325._config.searchTimeBudget = p326
    p325._config.ingestTimeBudget = p327
    p325._config.updateTimeBudget = p328
end
function u8.SetRefreshRates(p329, p330, p331) --[[Anonymous function at line 1157]]
    if p330 > 2 then
        p330 = 1 / p330
    end
    if p331 > 2 then
        p331 = 1 / p331
    end
    p329._config.bestRefreshRate = p330
    p329._config.worstRefreshRate = p331
    p329._config._refreshRateRange = p331 - p330
    p329._config._refreshRateMidpoint = (p331 + p330) / 2
end
function u8.SetComputeVisibilityOnlyOnDemand(p332, p333) --[[Anonymous function at line 1171]]
    p332._config.computeVisibilityOnlyOnDemand = p333
end
function u8.SetStrictlyEnforcedWorstRefreshRate(p334, p335) --[[Anonymous function at line 1175]]
    p334._config.strictlyEnforceWorstRefreshRate = p335
end
function u8.SetDynamicRenderDistance(p336, p337) --[[Anonymous function at line 1179]]
    p336._config.dynamicRenderDistance = p337
end
return u8