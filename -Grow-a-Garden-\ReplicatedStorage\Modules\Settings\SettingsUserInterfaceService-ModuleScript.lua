-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\Settings\SettingsUserInterfaceService-ModuleScript.lua
local u1 = {}
local v2 = game:GetService("Players")
local v3 = game:GetService("ReplicatedStorage")
game:GetService("TweenService")
local u4 = v2.LocalPlayer.PlayerGui:WaitFor<PERSON>hild("SettingsUI")
local v5 = require(v3.Modules.WaitForDescendant)
local v6 = require(v3.Modules.SetupSounds)
local v7 = require(v3.Modules.SetupBrightnessAnimationFrame)
local v8 = require(v3.Modules.SetupHoverAnimations)
require(v3.Modules.FindDescendantsWithTag)
local u9 = require(v3.Modules.DataService)
local u10 = require(v3.Data.SettingsRegistry)
local u11 = require(script.Classes.BooleanSetting)
local u12 = require(script.Classes.MultipleSetting)
local u13 = require(v3.Modules.Settings.SettingsService)
local u14 = require(v3.Modules.ClaimableCodeService)
local v15 = require(v3.Modules.Icon)
local v16 = game:GetService("GuiService")
local u17 = require(game.ReplicatedStorage.Frame_Popup_Module)
local u18 = {}
local u23 = {
    ["Boolean"] = function(p19) --[[Anonymous function at line 32]]
        --[[
        Upvalues:
            [1] = u11
            [2] = u18
        --]]
        local v20 = u11.new():SetTitle(p19.Title):SetDescription(p19.Description):SetBackgroundImage(p19.BackgroundTexture):SetId(p19.Name):SetLayoutOrder(p19.LayoutOrder or 1):Complete()
        u18[p19.Name] = v20
    end,
    ["Multiple"] = function(p21) --[[Anonymous function at line 43]]
        --[[
        Upvalues:
            [1] = u12
            [2] = u18
        --]]
        local v22 = u12.new():SetTitle(p21.Title):SetDescription(p21.Description):SetBackgroundImage(p21.BackgroundTexture):SetId(p21.Name):SetLayoutOrder(p21.LayoutOrder or 1):Complete()
        u18[p21.Name] = v22
    end
}
function u1.Toggle(_, p24) --[[Anonymous function at line 63]]
    --[[
    Upvalues:
        [1] = u4
        [2] = u17
    --]]
    u4.Enabled = p24
    if p24 == true then
        u17.Show(u4.SettingsFrame)
    else
        u17.Hide(u4.SettingsFrame)
    end
end
function u1.Synchronize(_) --[[Anonymous function at line 72]]
    --[[
    Upvalues:
        [1] = u9
        [2] = u10
        [3] = u18
        [4] = u23
        [5] = u13
    --]]
    local v25 = u9:GetData()
    if v25 then
        local _ = v25.Settings
        for v26, v27 in u10 do
            if not (v27.Disabled or u18[v26]) then
                local v28 = u23[v27.SettingsData.Type]
                if v28 then
                    v28(v27)
                end
            end
        end
        for v29, v30 in u10 do
            if not v30.Disabled then
                local v31 = u18[v29]
                if v31 then
                    v31:Update((u13:GetSetting(v29)))
                end
            end
        end
    end
end
local v32 = v5(u4, "EXIT_BUTTON")
v6(v32)
v7(v32)
local v33 = v32:WaitForChild("SENSOR")
u1:Synchronize()
u9:GetPathSignal("Settings/@"):Connect(function() --[[Anonymous function at line 104]]
    --[[
    Upvalues:
        [1] = u1
    --]]
    u1:Synchronize()
end)
local u34 = v15.new()
u34:setImage("rbxassetid://132848201849699")
u34:setName("Settings")
u34:setImageScale(0.8)
u34:setOrder(-6)
u34:setCaption("Toggle Settings")
u34.deselectWhenOtherIconSelected = false
v33.MouseButton1Click:Connect(function() --[[Anonymous function at line 116]]
    --[[
    Upvalues:
        [1] = u34
    --]]
    u34:deselect()
end)
u34.selected:Connect(function() --[[Anonymous function at line 120]]
    --[[
    Upvalues:
        [1] = u1
    --]]
    u1:Toggle(true)
end)
u34.deselected:Connect(function() --[[Anonymous function at line 123]]
    --[[
    Upvalues:
        [1] = u1
    --]]
    u1:Toggle(false)
end)
local u35 = v5(u4, "CODE_INPUT_BOX")
local v36 = v5(u4, "CODE_CLAIM_BUTTON")
v6(v36)
v8(v36).MouseButton1Click:Connect(function() --[[Anonymous function at line 132]]
    --[[
    Upvalues:
        [1] = u14
        [2] = u35
    --]]
    u14:ClaimCode(u35.Text)
end)
local u37 = game.Players.LocalPlayer.PlayerGui.TopbarStandard.Holders.Left.Settings
v16.MenuOpened:Connect(function() --[[Anonymous function at line 137]]
    --[[
    Upvalues:
        [1] = u34
        [2] = u37
    --]]
    u34:deselect()
    u37.Visible = false
end)
v16.MenuClosed:Connect(function() --[[Anonymous function at line 141]]
    --[[
    Upvalues:
        [1] = u37
    --]]
    u37.Visible = true
end)
return u1