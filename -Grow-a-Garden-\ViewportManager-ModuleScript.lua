-- Full Path: -Grow-a-Garden-\\ViewportManager-ModuleScript.lua
local u1 = game:GetService("RunService")
local u2 = game:GetService("HttpService")
local u3 = game:GetService("TweenService")
local u4 = game:GetService("Players")
local u5 = os.clock
local u6 = {
    "Accessory",
    "Shirt",
    "Pants",
    "BodyColors",
    "Humanoid"
}
local u7 = {
    ["InvalidInstance"] = "Incorrect type of instance given (%s). Can only track BaseParts, Models or Characters",
    ["RootNotFound"] = "WorldModel \'Root\' not found under viewportframe: %s",
    ["NotDescendantOfWorkspace"] = "Object: %s is not a descendant of workspace."
}
local u8 = {}
u8.__index = u8
function u8.new(p9) --[[Anonymous function at line 70]]
    --[[
    Upvalues:
        [1] = u7
        [2] = u8
    --]]
    if p9 and p9.Object then
        local v10 = u8
        local u11 = setmetatable(p9, v10)
        u11.RenderPriority = u11.RenderPriority or Enum.RenderPriority.Camera.Value + 1
        u11.RenderedProperties = u11.RenderedProperties or { "CFrame" }
        local v12 = u11.RenderPriority
        if typeof(v12) == "EnumItem" then
            u11.RenderPriority = u11.RenderPriority.Value
        end
        u11.Object.Destroying:Once(function() --[[Anonymous function at line 84]]
            --[[
            Upvalues:
                [1] = u11
            --]]
            u11:Destroy()
        end)
        return u11
    end
    error(u7.InvalidInstance:format(p9 and p9.Object or "nil"))
end
function u8.Update(p13, p14, u15) --[[Anonymous function at line 96]]
    --[[
    Upvalues:
        [1] = u7
    --]]
    if p13._renderframe and p13._renderframe:FindFirstChild("Root") then
        for v16 = 1, #p13.RenderedProperties do
            local u17 = p13.RenderedProperties[v16]
            if pcall(function() --[[Anonymous function at line 106]]
                --[[
                Upvalues:
                    [1] = u15
                    [2] = u17
                --]]
                return u15[u17] ~= nil
            end) then
                p14[u17] = u15[u17]
            end
        end
    else
        local v18 = error
        local v19 = u7.RootNotFound
        local v20 = p13._renderframe
        v18(v19:format((tostring(v20))))
    end
end
function u8.Start(u21, u22) --[[Anonymous function at line 121]]
    --[[
    Upvalues:
        [1] = u7
        [2] = u4
        [3] = u2
        [4] = u6
    --]]
    if u21._active then
        return
    elseif u21.Object:IsDescendantOf(workspace) then
        local v23 = u4.LocalPlayer
        local v24 = u21.Parent
        u21._active = true
        u21._connection_id = u2:GenerateGUID(false)
        if not u21.Parent then
            v24 = v23.PlayerGui:FindFirstChild("ViewportManagerScreenGUI") or Instance.new("ScreenGui")
            v24.Name = "ViewportManagerScreenGUI"
            v24.IgnoreGuiInset = true
            v24.Parent = v23.PlayerGui
            v24.Destroying:Once(function() --[[Anonymous function at line 144]]
                --[[
                Upvalues:
                    [1] = u21
                --]]
                u21:Destroy()
            end)
        end
        u21._renderframe = Instance.new("ViewportFrame")
        u21._renderframe.Size = UDim2.fromScale(1, 1)
        u21._renderframe.BackgroundTransparency = 1
        u21._renderframe.LightDirection = Vector3.new(1, -1, -1)
        u21._renderframe.Ambient = Color3.fromRGB(138, 138, 138)
        u21._renderframe.LightColor = Color3.new(1, 1, 1)
        u21._renderframe.CurrentCamera = workspace.CurrentCamera
        u21._renderframe.Name = ("VFM%*"):format(u21._connection_id)
        u21._renderframe.Parent = v24
        u21._renderframe.ZIndex = u21.ZIndex or u21._renderframe.ZIndex
        if u21._renderframe:FindFirstChildOfClass("WorldModel") then
            u21._renderframe:FindFirstChildOfClass("WorldModel").Name = "Root"
        else
            local v25 = Instance.new("WorldModel")
            v25.Parent = u21._renderframe
            v25.Name = "Root"
        end
        u21._connections = {}
        u21._viewportobject = Instance.new("Model")
        u21._viewportobject.Parent = u21._renderframe.Root
        u21._viewportobject.Name = ""
        if u21.Object:IsA("Model") then
            local v26 = u21.Object:FindFirstChild("Humanoid")
            local v27 = u21.Object:GetChildren()
            local function u35(p28, p29) --[[Anonymous function at line 184]]
                --[[
                Upvalues:
                    [1] = u21
                    [2] = u35
                    [3] = u22
                --]]
                for _, v30 in p28 do
                    if u21.GetModelDescendants and v30:IsA("Model") then
                        local v31 = Instance.new("Model")
                        local v32 = v30.Name
                        local v33 = u21._viewportobject
                        v31.Name = v32
                        v31.Parent = v33
                        u35(v30:GetChildren(), v31)
                    end
                    if v30:IsA("BasePart") then
                        local v34 = v30:Clone()
                        v34.CFrame = v30.CFrame
                        v34.Anchored = true
                        v34.Parent = p29 or u21._viewportobject
                        u21:AddConnection(v34, v30, u22)
                    end
                end
            end
            u35(v27)
            if v26 then
                v26.Died:Once(function() --[[Anonymous function at line 206]]
                    --[[
                    Upvalues:
                        [1] = u21
                    --]]
                    u21:Destroy()
                end)
                for _, v36 in v27 do
                    if table.find(u6, v36.ClassName) then
                        local v37 = v36:Clone()
                        v37.Parent = u21._viewportobject
                        if v36:IsA("Accessory") and v36:FindFirstChild("Handle") then
                            local v38 = v36.Handle
                            u21:AddConnection(v37.Handle, v38, u22)
                        end
                    end
                end
            end
        elseif u21.Object:IsA("BasePart") then
            local v39 = u21.Object:Clone()
            v39.CFrame = u21.Object.CFrame
            v39.Anchored = true
            v39.Parent = u21._viewportobject
            u21:AddConnection(v39, u21.Object, u22)
        end
    else
        local v40 = error
        local v41 = u7.NotDescendantOfWorkspace
        local v42 = u21.Object
        v40(v41:format((tostring(v42))))
        return
    end
end
function u8.AddConnection(u43, u44, u45, p46) --[[Anonymous function at line 242]]
    --[[
    Upvalues:
        [1] = u5
        [2] = u2
        [3] = u1
    --]]
    if p46 then
        u43:Update(u44, u45)
    else
        local u47 = u5()
        local u48 = ("%*%*"):format(u2:GenerateGUID(false):sub(1, 7), u43._connection_id)
        u1:BindToRenderStep(u48, u43.RenderPriority, function(_) --[[Anonymous function at line 251]]
            --[[
            Upvalues:
                [1] = u44
                [2] = u45
                [3] = u43
                [4] = u48
                [5] = u1
                [6] = u5
                [7] = u47
            --]]
            if u44 and u45 then
                if not u43.Framerate or u5() - u47 >= 1 / u43.Framerate then
                    u47 = u5()
                    u43:Update(u44, u45)
                end
            else
                local v49 = table.find(u43._connections, u48)
                if v49 then
                    u1:UnbindFromRenderStep(u48)
                    table.remove(u43._connections, v49)
                end
                return
            end
        end)
        local v50 = u43._connections
        table.insert(v50, u48)
    end
end
function u8.PlayOnce(p51, p52) --[[Anonymous function at line 275]]
    if p52 then
        p51:Start(true)
        task.spawn(p51.PlayFade, p51, p52)
    end
end
function u8.PlayFade(p53, p54) --[[Anonymous function at line 287]]
    --[[
    Upvalues:
        [1] = u3
    --]]
    if p54 and p53._renderframe then
        if p54.StopTracking then
            p53:Stop()
        end
        local v55 = p54.Time or 0.5
        local v56 = p54.Ease or "Linear"
        local v57 = p53._renderframe
        if p54.StopTracking then
            p53._renderframe = nil
        end
        local v58 = Enum.EasingStyle[v56]
        local v59 = u3:Create(v57, TweenInfo.new(v55, v58), {
            ["ImageTransparency"] = 1
        })
        v59:Play()
        v59.Completed:Wait()
        v59:Destroy()
        v57:Destroy()
    end
end
function u8.Destroy(p60, p61) --[[Anonymous function at line 316]]
    if p60._active then
        p60:PlayFade(p61)
        p60:Stop()
        if p60._renderframe then
            p60._renderframe:Destroy()
        end
        for v62 in p60 do
            p60[v62] = nil
        end
    end
end
function u8.Stop(p63) --[[Anonymous function at line 337]]
    --[[
    Upvalues:
        [1] = u1
    --]]
    p63._active = false
    if p63._connections then
        for _, v64 in p63._connections do
            u1:UnbindFromRenderStep(v64)
        end
        p63._connections = nil
    end
end
return u1:IsServer() and {} or u8