-- Full Path: -Grow-a-Garden-\\CollectionQOL-LocalScript.lua
local v1 = game:GetService("UserInputService")
local v2 = game:GetService("ProximityPromptService")
local u3 = false
local u4 = false
local u5 = true
local u6 = nil
local _ = game.Players.LocalPlayer
v2.PromptShown:Connect(function(p7) --[[Anonymous function at line 12]]
    --[[
    Upvalues:
        [1] = u6
        [2] = u4
        [3] = u5
    --]]
    if p7:HasTag("CollectPrompt") then
        u6 = p7
        if u4 and u5 then
            u5 = false
            task.delay(0.25, function() --[[Anonymous function at line 18]]
                --[[
                Upvalues:
                    [1] = u5
                --]]
                u5 = true
            end)
            p7:InputHoldBegin()
        else
            task.wait(0.28)
            if u4 and u6 == p7 then
                p7:InputHoldBegin()
            end
        end
    else
        return
    end
end)
v1.InputBegan:Connect(function(p8, _) --[[Anonymous function at line 34]]
    --[[
    Upvalues:
        [1] = u3
        [2] = u4
    --]]
    if (p8.UserInputType == Enum.UserInputType.Keyboard or p8.UserInputType == Enum.UserInputType.Gamepad1) and (p8.KeyCode == Enum.KeyCode.E or p8.KeyCode == Enum.KeyCode.ButtonX) then
        u3 = true
        task.wait(0.1)
        if u3 then
            u4 = true
        end
    end
end)
v1.InputEnded:Connect(function(p9, _) --[[Anonymous function at line 50]]
    --[[
    Upvalues:
        [1] = u3
        [2] = u4
    --]]
    if (p9.UserInputType == Enum.UserInputType.Keyboard or p9.UserInputType == Enum.UserInputType.Gamepad1) and (p9.KeyCode == Enum.KeyCode.E or p9.KeyCode == Enum.KeyCode.ButtonX) then
        u3 = false
        u4 = false
    end
end)