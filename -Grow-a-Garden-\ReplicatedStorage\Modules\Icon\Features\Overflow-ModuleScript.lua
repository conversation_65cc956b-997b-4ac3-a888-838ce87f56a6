-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\Icon\Features\Overflow-ModuleScript.lua
local u1 = {}
local u2 = {}
local u3 = {}
local u4 = nil
local u5 = workspace.CurrentCamera
local u6 = {}
local u7 = {}
local u8 = require(script.Parent.Parent.Utility)
local u9 = nil
function u1.start(p10) --[[Anonymous function at line 23]]
    --[[
    Upvalues:
        [1] = u9
        [2] = u4
        [3] = u2
        [4] = u8
        [5] = u1
        [6] = u5
    --]]
    u9 = p10
    u4 = u9.iconsDictionary
    local v11 = nil
    for _, v12 in pairs(u9.container) do
        if v11 == nil then
            if v12.ScreenInsets == Enum.ScreenInsets.TopbarSafeInsets then
                v11 = v12
            end
        end
        for _, v13 in pairs(v12.Holders:GetChildren()) do
            if v13:GetAttribute("IsAHolder") then
                u2[v13.Name] = v13
            end
        end
    end
    local u14 = false
    local u16 = u8.createStagger(0.1, function(p15) --[[Anonymous function at line 41]]
        --[[
        Upvalues:
            [1] = u14
            [2] = u1
        --]]
        if u14 then
            if not p15 then
                u1.updateAvailableIcons("Center")
            end
            u1.updateBoundary("Left")
            u1.updateBoundary("Right")
        end
    end)
    task.delay(1, function() --[[Anonymous function at line 51]]
        --[[
        Upvalues:
            [1] = u14
            [2] = u16
        --]]
        u14 = true
        u16()
    end)
    u9.iconAdded:Connect(u16)
    u9.iconRemoved:Connect(u16)
    u9.iconChanged:Connect(u16)
    u5:GetPropertyChangedSignal("ViewportSize"):Connect(function() --[[Anonymous function at line 61]]
        --[[
        Upvalues:
            [1] = u16
        --]]
        u16(true)
    end)
    v11:GetPropertyChangedSignal("AbsoluteSize"):Connect(function() --[[Anonymous function at line 64]]
        --[[
        Upvalues:
            [1] = u16
        --]]
        u16(true)
    end)
end
function u1.getWidth(p17, _) --[[Anonymous function at line 69]]
    local v18 = p17.widget
    return v18:GetAttribute("TargetWidth") or v18.AbsoluteSize.X
end
function u1.getAvailableIcons(p19) --[[Anonymous function at line 74]]
    --[[
    Upvalues:
        [1] = u3
        [2] = u1
    --]]
    return u3[p19] or u1.updateAvailableIcons(p19)
end
function u1.updateAvailableIcons(p20) --[[Anonymous function at line 82]]
    --[[
    Upvalues:
        [1] = u2
        [2] = u4
        [3] = u7
        [4] = u3
    --]]
    local _ = u2[p20].UIListLayout
    local v21 = {}
    local v22 = 0
    for _, v23 in pairs(u4) do
        local v24 = v23.parentIconUID
        local v25 = not v24 or u7[v24]
        local v26 = u7[v23.UID]
        if v25 and (v23.alignment == p20 and not v26) then
            table.insert(v21, v23)
            v22 = v22 + 1
        end
    end
    if v22 <= 0 then
        return {}
    end
    table.sort(v21, function(p27, p28) --[[Anonymous function at line 106]]
        local v29 = p27.widget.LayoutOrder
        local v30 = p28.widget.LayoutOrder
        local v31 = p27.parentIconUID
        local v32 = p28.parentIconUID
        if v31 == v32 then
            if v29 < v30 then
                return true
            elseif v30 < v29 then
                return false
            else
                return p27.widget.AbsolutePosition.X < p28.widget.AbsolutePosition.X
            end
        else
            if v32 then
                return false
            end
            if v31 then
                return true
            end
            return
        end
    end)
    u3[p20] = v21
    return v21
end
function u1.getRealXPositions(p33, p34) --[[Anonymous function at line 132]]
    --[[
    Upvalues:
        [1] = u2
        [2] = u8
        [3] = u1
    --]]
    local v35 = p33 == "Left"
    local v36 = u2[p33]
    local v37 = v36.AbsolutePosition.X
    local v38 = v36.AbsoluteSize.X
    local v39 = v36.UIListLayout.Padding.Offset
    local v40 = v35 and v37 and v37 or v37 + v38
    local v41 = {}
    if v35 then
        u8.reverseTable(p34)
    end
    for v42 = #p34, 1, -1 do
        local v43 = p34[v42]
        local v44 = u1.getWidth(v43)
        if not v35 then
            v40 = v40 - v44
        end
        v41[v43.UID] = v40
        if v35 then
            v40 = v40 + v44
        end
        v40 = v40 + (v35 and v39 and v39 or -v39)
    end
    return v41
end
function u1.updateBoundary(p45) --[[Anonymous function at line 162]]
    --[[
    Upvalues:
        [1] = u2
        [2] = u1
        [3] = u6
        [4] = u9
        [5] = u7
        [6] = u8
    --]]
    local v46 = u2[p45]
    local v47 = v46.UIListLayout
    local v48 = v46.AbsolutePosition.X
    local v49 = v46.AbsoluteSize.X
    local v50 = v47.Padding.Offset
    local v51 = v47.Padding.Offset
    local v52 = u1.updateAvailableIcons(p45)
    local v53 = 0
    local v54 = 0
    for _, v55 in pairs(v52) do
        v53 = v53 + (u1.getWidth(v55) + v51)
        v54 = v54 + 1
    end
    if v54 > 0 then
        local v56 = p45 == "Central"
        local v57 = p45 == "Left"
        local v58 = not v57
        local v59 = u6[p45]
        if not v59 and (not v56 and #v52 > 0) then
            v59 = u9.new()
            v59:setImage(6069276526, "Deselected")
            v59:setName("Overflow" .. p45)
            v59:setOrder(v57 and -9999999 or 9999999)
            v59:setAlignment(p45)
            v59:autoDeselect(false)
            v59.isAnOverflow = true
            v59:select("OverflowStart", v59)
            v59:setEnabled(false)
            u6[p45] = v59
            u7[v59.UID] = true
        end
        local v60 = p45 == "Left" and "Right" or "Left"
        local v61 = u1.updateAvailableIcons(v60)
        local v62 = v57 and v61[1]
        if not v62 then
            if v58 then
                v62 = v61[#v61]
            else
                v62 = v58
            end
        end
        local v63 = u6[v60]
        local v64
        if v57 then
            v64 = v48 + v49 or v48
        else
            v64 = v48
        end
        if v62 then
            local _ = v62.widget
            local v65 = u1.getRealXPositions(v60, v61)[v62.UID]
            local v66 = u1.getWidth(v62)
            v64 = v57 and v65 - v50 or v65 + v66 + v50
        end
        local v67 = u1.getAvailableIcons("Center")
        local v68 = v67[v57 and 1 or #v67]
        if v68 and not v68.hasRelocatedInOverflow then
            local v69 = v57 and v52[#v52]
            if not v69 then
                if v58 then
                    v69 = v52[1]
                else
                    v69 = v58
                end
            end
            local v70 = v68.widget.AbsolutePosition.X
            local v71 = v69.widget.AbsolutePosition.X
            local v72 = u1.getWidth(v69)
            local v73 = v57 and v70 - v50 or v70 + u1.getWidth(v68) + v50
            if v57 then
                v71 = v71 + v72 or v71
            end
            if v57 then
                if v73 < v71 then
                    v68:align("Left")
                    v68.hasRelocatedInOverflow = true
                end
            elseif v58 and v71 < v73 then
                v68:align("Right")
                v68.hasRelocatedInOverflow = true
            end
        end
        if v59 then
            local v74 = v59:getInstance("Menu")
            local v75 = v48 + v49
            if v74 and v63 then
                local v76 = v63.widget.AbsolutePosition.X
                local v77 = u1.getWidth(v63)
                local v78 = v57 and v76 - v50 or v76 + v77 + v50
                local v79 = v63:getInstance("Menu")
                local v80 = v74.AbsoluteCanvasSize.X >= v79.AbsoluteCanvasSize.X
                local v81 = v48 + v49 / 2
                local v82 = v57 and v81 - v50 / 2 or v81 + v50 / 2
                if v80 then
                    v82 = v78
                end
                v49 = v57 and v82 - v48 or v75 - v82
            end
            local v83
            if v74 then
                v83 = v74:GetAttribute("MaxWidth")
            else
                v83 = v74
            end
            local v84 = u8.round(v49)
            if v74 and v83 ~= v84 then
                v74:SetAttribute("MaxWidth", v84)
            end
        end
        local v85 = u1.getRealXPositions(p45, v52)
        local v86 = false
        for v87 = #v52, 1, -1 do
            local v88 = v52[v87]
            local v89 = u1.getWidth(v88)
            local v90 = v85[v88.UID]
            if v57 and v64 <= v90 + v89 or v58 and v90 <= v64 then
                v86 = true
            end
        end
        for v91 = #v52, 1, -1 do
            local v92 = v52[v91]
            if not u7[v92.UID] then
                if v86 and not v92.parentIconUID then
                    v92:joinMenu(v59)
                elseif not v86 and v92.parentIconUID then
                    v92:leave()
                end
            end
        end
        if v59.isEnabled ~= v86 then
            v59:setEnabled(v86)
        end
        if v59.isEnabled and not v59.overflowAlreadyOpened then
            v59.overflowAlreadyOpened = true
            v59:select()
        end
    end
end
return u1