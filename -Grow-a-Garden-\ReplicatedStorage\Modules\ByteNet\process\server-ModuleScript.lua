-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\ByteNet\process\server-ModuleScript.lua
local u1 = game:GetService("Players")
local u2 = game:GetService("ReplicatedStorage")
local u3 = game:GetService("RunService")
require(script.Parent.Parent.types)
local u4 = require(script.Parent.read)
local u5 = require(script.Parent.bufferWriter)
local u6 = u5.alloc
local u7 = u5.u8
local u8 = u5.load
local u9 = {}
local u10 = {}
local function u11() --[[Anonymous function at line 20]]
    return {
        ["cursor"] = 0,
        ["size"] = 256,
        ["references"] = {},
        ["buff"] = buffer.create(256)
    }
end
local u12 = u11()
local u13 = u11()
local function u17(p14, p15, p16) --[[Anonymous function at line 43]]
    --[[
    Upvalues:
        [1] = u4
    --]]
    if typeof(p15) == "buffer" then
        if buffer.len(p15) <= 9000 then
            u4(p15, p16, p14)
        end
    else
        return
    end
end
local function u19(p18) --[[Anonymous function at line 56]]
    --[[
    Upvalues:
        [1] = u9
        [2] = u11
        [3] = u10
    --]]
    if not u9[p18] then
        u9[p18] = u11()
    end
    if not u10[p18] then
        u10[p18] = u11()
    end
end
return {
    ["sendAllReliable"] = function(p20, p21, p22) --[[Function name: sendAllReliable, line 68]]
        --[[
        Upvalues:
            [1] = u8
            [2] = u12
            [3] = u6
            [4] = u7
            [5] = u5
        --]]
        u8(u12)
        u6(1)
        u7(p20)
        p21(p22)
        u12 = u5.export()
    end,
    ["sendAllUnreliable"] = function(p23, p24, p25) --[[Function name: sendAllUnreliable, line 78]]
        --[[
        Upvalues:
            [1] = u8
            [2] = u13
            [3] = u11
            [4] = u6
            [5] = u7
            [6] = u5
        --]]
        u8(u13 or u11())
        u6(1)
        u7(p23)
        p24(p25)
        u13 = u5.export()
    end,
    ["sendPlayerReliable"] = function(p26, p27, p28, p29) --[[Function name: sendPlayerReliable, line 88]]
        --[[
        Upvalues:
            [1] = u8
            [2] = u9
            [3] = u11
            [4] = u6
            [5] = u7
            [6] = u5
        --]]
        u8(u9[p26] or u11())
        u6(1)
        u7(p27)
        p28(p29)
        u9[p26] = u5.export()
    end,
    ["sendPlayerUnreliable"] = function(p30, p31, p32, p33) --[[Function name: sendPlayerUnreliable, line 103]]
        --[[
        Upvalues:
            [1] = u8
            [2] = u10
            [3] = u6
            [4] = u7
            [5] = u5
        --]]
        u8(u10[p30])
        u6(1)
        u7(p31)
        p32(p33)
        u10[p30] = u5.export()
    end,
    ["start"] = function() --[[Function name: start, line 118]]
        --[[
        Upvalues:
            [1] = u17
            [2] = u2
            [3] = u1
            [4] = u9
            [5] = u11
            [6] = u10
            [7] = u19
            [8] = u3
            [9] = u12
            [10] = u13
        --]]
        local u34 = Instance.new("RemoteEvent")
        u34.Name = "ByteNetReliable"
        u34.OnServerEvent:Connect(u17)
        u34.Parent = u2
        local u35 = Instance.new("UnreliableRemoteEvent")
        u35.Name = "ByteNetUnreliable"
        u35.OnServerEvent:Connect(u17)
        u35.Parent = u2
        for _, v36 in u1:GetPlayers() do
            if not u9[v36] then
                u9[v36] = u11()
            end
            if not u10[v36] then
                u10[v36] = u11()
            end
        end
        u1.PlayerAdded:Connect(u19)
        u3.Heartbeat:Connect(function() --[[Anonymous function at line 135]]
            --[[
            Upvalues:
                [1] = u12
                [2] = u34
                [3] = u13
                [4] = u35
                [5] = u1
                [6] = u9
                [7] = u10
            --]]
            if u12.cursor > 0 then
                local v37 = u12
                local v38 = v37.cursor
                local v39 = buffer.create(v38)
                buffer.copy(v39, 0, v37.buff, 0, v38)
                local v40
                if #v37.references > 0 then
                    v40 = v37.references
                else
                    v40 = nil
                end
                u34:FireAllClients(v39, v40)
                u12.cursor = 0
                table.clear(u12.references)
            end
            if u13.cursor > 0 then
                local v41 = u13
                local v42 = v41.cursor
                local v43 = buffer.create(v42)
                buffer.copy(v43, 0, v41.buff, 0, v42)
                local v44
                if #v41.references > 0 then
                    v44 = v41.references
                else
                    v44 = nil
                end
                u35:FireAllClients(v43, v44)
                u13.cursor = 0
                table.clear(u13.references)
            end
            for _, v45 in u1:GetPlayers() do
                if u9[v45].cursor > 0 then
                    local v46 = u9[v45]
                    local v47 = v46.cursor
                    local v48 = buffer.create(v47)
                    buffer.copy(v48, 0, v46.buff, 0, v47)
                    local v49
                    if #v46.references > 0 then
                        v49 = v46.references
                    else
                        v49 = nil
                    end
                    u34:FireClient(v45, v48, v49)
                    u9[v45].cursor = 0
                    table.clear(u9[v45].references)
                end
                if u10[v45].cursor > 0 then
                    local v50 = u10[v45]
                    local v51 = v50.cursor
                    local v52 = buffer.create(v51)
                    buffer.copy(v52, 0, v50.buff, 0, v51)
                    local v53
                    if #v50.references > 0 then
                        v53 = v50.references
                    else
                        v53 = nil
                    end
                    u35:FireClient(v45, v52, v53)
                    u10[v45].cursor = 0
                    table.clear(u10[v45].references)
                end
            end
        end)
    end
}