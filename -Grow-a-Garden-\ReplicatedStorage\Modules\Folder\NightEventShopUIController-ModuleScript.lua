-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\Folder\NightEventShopUIController-ModuleScript.lua
local u1 = game:GetService("ReplicatedStorage")
game:GetService("TweenService")
game:GetService("RunService")
local u2 = game:GetService("ContentProvider")
local u3 = game:GetService("UserInputService")
local v4 = u1:WaitForChild("GameEvents")
local u5 = game.Players.LocalPlayer.PlayerGui:WaitForChild("NightEventShop_UI")
local u6 = require(u1.Modules.DataService)
local v7 = require(u1.Modules.Signal)
local u8 = require(u1.Modules.FastTween)
require(u1.Modules.DumpTable)
local u9 = require(u1.Modules.MarketController)
local u10 = require(u1.Modules.GiftController)
local u11 = require(u1.Modules.GuiController)
local u12 = require(u1.Modules.WaitForDescendant)
local u13 = require(u1.Comma_Module)
local u14 = require(u1.Item_Module)
require(u1.Modules.NumberUtil)
require(u1.Data.NightEventShopConfigData)
local u15 = require(u1.Data.NightEventShopData)
local u16 = v4.BuyNightEventShopStock
local u17 = nil
local u18 = v7.new()
local u19 = u5:WaitForChild("Frame"):WaitForChild("ScrollingFrame")
u5:WaitForChild("Frame"):WaitForChild("Frame"):WaitForChild("Timer")
local u20 = u19:WaitForChild("ItemFrame")
u20.Parent = script
local u21 = u19:WaitForChild("ItemPadding")
u21.Parent = u19
local u22 = {}
local v23 = {}
local u24 = game.SoundService.Click
local function u25() --[[Anonymous function at line 63]] end
local function u60() --[[Anonymous function at line 123]]
    --[[
    Upvalues:
        [1] = u25
        [2] = u3
        [3] = u15
        [4] = u2
        [5] = u20
        [6] = u22
        [7] = u14
        [8] = u13
        [9] = u19
        [10] = u21
        [11] = u16
        [12] = u9
        [13] = u24
        [14] = u10
        [15] = u8
        [16] = u17
        [17] = u18
        [18] = u6
    --]]
    task.spawn(u25)
    if not u3.TouchEnabled then
        local v26 = {}
        for _, v27 in u15 do
            if v27.DisplayInShop or v27.ItemType ~= "Seed" then
                local v28 = Instance.new("ImageLabel")
                v28.Image = v27.FruitIcon
                table.insert(v26, v28)
            end
        end
        u2:PreloadAsync(v26)
    end
    local u29 = {}
    for u30, u31 in u15 do
        if u31.DisplayInShop then
            local u32 = u20:Clone()
            u22[u30] = u32
            u32.Name = u30
            u32.LayoutOrder = u31.LayoutOrder * 10
            table.insert(u29, u32)
            local u33 = u32.Main_Frame
            local v34 = u14.Return_Rarity_Data(u31.SeedRarity)
            if v34 then
                u33.Rarity_Text.Text = v34[1]
                local v35 = u33.Rarity_Text.UIStroke
                local v36, v37, v38 = v34[2]:ToHSV()
                v35.Color = Color3.fromHSV(v36, v37, v38 / 2)
                u33.Rarity_BG.ImageColor3 = v34[2]
            end
            u33.Seed_Text.Text = u31.SeedName
            u33.Seed_Text_Shadow.Text = u31.SeedName
            u33.Cost_Text.Text = u13.Comma(u31.Price) .. "\194\162"
            u33.Description_Text.Text = u31.Description
            local v39 = u33:FindFirstChild("ShopItem_Image", true)
            v39.Visible = true
            v39.Image = u31.Asset
            u32.Parent = u19
            local u40 = u21:Clone()
            u40.LayoutOrder = u31.LayoutOrder * 10 + 1
            u40.Name = ("%*_Padding"):format(u30)
            u40.Parent = u19
            local u41 = u32.Frame
            u41.Sheckles_Buy.In_Stock.Cost_Text.Text = u13.Comma(u31.Price) .. "\194\162"
            u41.Sheckles_Buy.Activated:Connect(function() --[[Anonymous function at line 184]]
                --[[
                Upvalues:
                    [1] = u16
                    [2] = u30
                --]]
                u16:FireServer(u30)
            end)
            u41.Robux_Buy.Activated:Connect(function() --[[Anonymous function at line 188]]
                --[[
                Upvalues:
                    [1] = u9
                    [2] = u31
                --]]
                u9:PromptPurchase(u31.PurchaseID, Enum.InfoType.Product)
            end)
            u41.Gift.Visible = u31.GiftPurchaseID ~= nil
            if u31.GiftPurchaseID then
                u41.Gift.Activated:Connect(function() --[[Anonymous function at line 194]]
                    --[[
                    Upvalues:
                        [1] = u24
                        [2] = u10
                        [3] = u31
                    --]]
                    u24.PlaybackSpeed = 1 + math.random(-15, 15) / 100
                    u24.TimePosition = 0
                    u24.Playing = true
                    u10:PromptGiftFromGiftId(u31.GiftPurchaseID)
                end)
            end
            if u31.ItemType == "Seed" then
                local u42 = nil
                local u43 = nil
                u33.MouseEnter:Connect(function() --[[Anonymous function at line 207]]
                    --[[
                    Upvalues:
                        [1] = u42
                        [2] = u8
                        [3] = u32
                        [4] = u43
                        [5] = u31
                    --]]
                    u42 = u8(u32.Main_Frame.CanvasGroup.ShopItem_Image, TweenInfo.new(0.1, Enum.EasingStyle.Linear, Enum.EasingDirection.Out, -1, true, 0), {
                        ["Rotation"] = u32.Main_Frame.CanvasGroup.ShopItem_Image.Rotation + 10
                    })
                    u43 = u8(u32.Main_Frame.CanvasGroup.ShopItem_Image.UIScale, TweenInfo.new(0.5, Enum.EasingStyle.Linear, Enum.EasingDirection.Out, 0, false, 0), {
                        ["Scale"] = 1.5
                    })
                    u43.Completed:Connect(function(p44) --[[Anonymous function at line 223]]
                        --[[
                        Upvalues:
                            [1] = u32
                            [2] = u31
                        --]]
                        if p44 == Enum.PlaybackState.Completed then
                            u32.Main_Frame.CanvasGroup.ShopItem_Image.Image = u31.FruitIcon
                        end
                    end)
                end)
                u33.MouseLeave:Connect(function() --[[Anonymous function at line 230]]
                    --[[
                    Upvalues:
                        [1] = u42
                        [2] = u32
                        [3] = u31
                        [4] = u8
                    --]]
                    if u42 then
                        u42:Cancel()
                    end
                    u32.Main_Frame.CanvasGroup.ShopItem_Image.Rotation = 0
                    u32.Main_Frame.CanvasGroup.ShopItem_Image.Image = u31.Asset
                    u8(u32.Main_Frame.CanvasGroup.ShopItem_Image.UIScale, TweenInfo.new(0.25, Enum.EasingStyle.Linear, Enum.EasingDirection.Out, 0, false, 0), {
                        ["Scale"] = 1
                    })
                end)
            end
            local function v45() --[[Anonymous function at line 251]]
                --[[
                Upvalues:
                    [1] = u17
                    [2] = u30
                    [3] = u41
                    [4] = u8
                    [5] = u40
                --]]
                if u17 == u30 then
                    u41.Visible = true
                    u8(u41, TweenInfo.new(0.25, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
                        ["Position"] = UDim2.fromScale(0.5, 1.3)
                    })
                    u8(u40, TweenInfo.new(0.25, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
                        ["Size"] = UDim2.fromScale(0.13, 0.25)
                    })
                else
                    task.delay(0.25, function() --[[Anonymous function at line 263]]
                        --[[
                        Upvalues:
                            [1] = u41
                        --]]
                        if u41.Position == UDim2.fromScale(0.5, 0.5) then
                            u41.Visible = false
                        end
                    end)
                    u8(u41, TweenInfo.new(0.25, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
                        ["Position"] = UDim2.fromScale(0.5, 0.5)
                    })
                    u8(u40, TweenInfo.new(0.25, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
                        ["Size"] = UDim2.fromScale(0.13, 0.02)
                    })
                end
            end
            u18:Connect(v45)
            task.spawn(v45)
            local u46 = false
            u33.Activated:Connect(function() --[[Anonymous function at line 282]]
                --[[
                Upvalues:
                    [1] = u8
                    [2] = u19
                    [3] = u32
                    [4] = u29
                    [5] = u17
                    [6] = u30
                    [7] = u18
                    [8] = u46
                    [9] = u9
                    [10] = u41
                    [11] = u31
                --]]
                u8(u19, TweenInfo.new(0.35), {
                    ["CanvasPosition"] = Vector2.new(0, u32.AbsoluteSize.Y * (table.find(u29, u32) - 1))
                })
                local v47
                if u17 == u30 then
                    v47 = nil
                else
                    v47 = u30
                end
                u17 = v47
                u18:Fire()
                if not u46 then
                    u9:SetPriceLabel(u41.Robux_Buy.Price, u31.PurchaseID, ":robux::value:")
                    u46 = true
                end
            end)
            local function u53() --[[Anonymous function at line 297]]
                --[[
                Upvalues:
                    [1] = u6
                    [2] = u30
                    [3] = u33
                    [4] = u41
                    [5] = u13
                    [6] = u31
                --]]
                local v48 = u6:GetData()
                if v48 then
                    v48 = v48.NightEventShopStock.Stocks[u30]
                end
                local v49 = v48 and v48.Stock or 0
                local v50 = ("X%* Stock"):format(v49)
                u33.Stock_Text.Text = v50
                u41.Sheckles_Buy.In_Stock.Visible = v49 > 0
                u41.Sheckles_Buy.No_Stock.Visible = v49 <= 0
                u41.Sheckles_Buy.HoverImage = v49 > 0 and "rbxassetid://71551639169723" or "rbxassetid://138411009141674"
                u41.Sheckles_Buy.Image = v49 > 0 and "rbxassetid://96160773850314" or "rbxassetid://104713419928195"
                u33.Cost_Text.Text = v49 <= 0 and "NO STOCK" or u13.Comma(u31.Price) .. "\194\162"
                local v51 = u33.Cost_Text
                local v52
                if v49 <= 0 then
                    v52 = Color3.fromRGB(255, 0, 0)
                else
                    v52 = Color3.fromRGB(0, 255, 0)
                end
                v51.TextColor3 = v52
            end
            task.spawn(u53)
            task.spawn(function() --[[Anonymous function at line 317]]
                --[[
                Upvalues:
                    [1] = u6
                    [2] = u53
                --]]
                local v54 = u6:GetPathSignal("NightEventShopStock/@")
                if v54 then
                    v54:Connect(u53)
                end
                local v55 = u6:GetPathSignal("NightEventShopStock")
                if v55 then
                    v55:Connect(u53)
                end
            end)
        end
    end
    table.sort(u29, function(p56, p57) --[[Anonymous function at line 335]]
        local v58 = p56.LayoutOrder
        local v59 = p57.LayoutOrder
        if v58 == v59 then
            return p56.Name < p57.Name
        else
            return v58 < v59
        end
    end)
end
function v23.Start(_) --[[Anonymous function at line 345]]
    --[[
    Upvalues:
        [1] = u11
        [2] = u5
        [3] = u60
        [4] = u1
        [5] = u12
    --]]
    u11:UsePopupAnims(u5)
    u5.Frame.Frame.ExitButton.Activated:Connect(function() --[[Anonymous function at line 348]]
        --[[
        Upvalues:
            [1] = u11
            [2] = u5
        --]]
        u11:Close(u5)
    end)
    u5.Frame.Frame.Restock.Activated:Connect(function() --[[Anonymous function at line 352]] end)
    u60()
    local u61 = nil
    local u62 = nil
    local u63 = nil
    local function u67() --[[Anonymous function at line 363]]
        --[[
        Upvalues:
            [1] = u61
            [2] = u1
            [3] = u62
            [4] = u12
            [5] = u63
        --]]
        local v64 = workspace:GetAttribute("NightEvent") or false
        if not u61 then
            u61 = workspace:WaitForChild("NightEvent"):FindFirstChild("NightEventShrine", true) or u1.Modules.UpdateService:FindFirstChild("NightEventShrine", true)
        end
        if not u62 then
            u62 = u12(workspace, "NightEventProximityPrompt")
        end
        if not u63 then
            u63 = u12(workspace, "NightEventArrow")
        end
        for _, v65 in u61:GetDescendants() do
            if v65:IsA("ParticleEmitter") then
                v65.Enabled = v64
            end
        end
        for _, v66 in u63:GetDescendants() do
            if v66:IsA("BasePart") then
                v66.Transparency = v64 and 0 or 1
            end
        end
        u62.Enabled = v64
    end
    u67()
    workspace:GetAttributeChangedSignal("NightEvent"):Connect(function() --[[Anonymous function at line 388]]
        --[[
        Upvalues:
            [1] = u67
        --]]
        u67()
    end)
end
task.spawn(v23.Start, v23)
return v23