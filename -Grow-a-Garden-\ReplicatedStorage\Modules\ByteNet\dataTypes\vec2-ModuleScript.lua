-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\ByteNet\dataTypes\vec2-ModuleScript.lua
local v1 = require(script.Parent.Parent.process.bufferWriter)
require(script.Parent.Parent.types)
local u2 = v1.f32NoAlloc
local u3 = v1.alloc
local u10 = {
    ["read"] = function(p4, p5) --[[Function name: read, line 12]]
        local v6 = Vector2.new
        local v7 = buffer.readf32(p4, p5)
        local v8 = p5 + 4
        return v6(v7, (buffer.readf32(p4, v8))), 8
    end,
    ["write"] = function(p9) --[[Function name: write, line 16]]
        --[[
        Upvalues:
            [1] = u3
            [2] = u2
        --]]
        u3(8)
        u2(p9.X)
        u2(p9.Y)
    end
}
return function() --[[Anonymous function at line 23]]
    --[[
    Upvalues:
        [1] = u10
    --]]
    return u10
end