-- Full Path: -Grow-a-Garden-\StarterPlayer\StarterPlayerScripts\PlayerModule\CameraModule\MouseLockController-ModuleScript.lua
local v1 = script.Parent.Parent:WaitForChild("CommonUtils")
require(v1:WaitFor<PERSON>hild("FlagUtil"))
local u2 = Enum.ContextActionPriority.Medium.Value
local u3 = game:GetService("Players")
local u4 = game:GetService("ContextActionService")
local u5 = UserSettings().GameSettings
local u6 = require(script.Parent:WaitForChild("CameraUtils"))
local u7 = {}
u7.__index = u7
function u7.new() --[[Anonymous function at line 31]]
    --[[
    Upvalues:
        [1] = u7
        [2] = u5
        [3] = u3
    --]]
    local v8 = u7
    local u9 = setmetatable({}, v8)
    u9.isMouseLocked = false
    u9.savedMouseCursor = nil
    u9.boundKeys = { Enum.KeyCode.LeftShift, Enum.KeyCode.RightShift }
    u9.mouseLockToggledEvent = Instance.new("BindableEvent")
    local v10 = script:FindFirstChild("BoundKeys")
    if not (v10 and v10:IsA("StringValue")) then
        if v10 then
            v10:Destroy()
        end
        v10 = Instance.new("StringValue")
        assert(v10, "")
        v10.Name = "BoundKeys"
        v10.Value = "LeftShift,RightShift"
        v10.Parent = script
    end
    if v10 then
        v10.Changed:Connect(function(p11) --[[Anonymous function at line 56]]
            --[[
            Upvalues:
                [1] = u9
            --]]
            u9:OnBoundKeysObjectChanged(p11)
        end)
        u9:OnBoundKeysObjectChanged(v10.Value)
    end
    u5.Changed:Connect(function(p12) --[[Anonymous function at line 63]]
        --[[
        Upvalues:
            [1] = u9
        --]]
        if p12 == "ControlMode" or p12 == "ComputerMovementMode" then
            u9:UpdateMouseLockAvailability()
        end
    end)
    u3.LocalPlayer:GetPropertyChangedSignal("DevEnableMouseLock"):Connect(function() --[[Anonymous function at line 70]]
        --[[
        Upvalues:
            [1] = u9
        --]]
        u9:UpdateMouseLockAvailability()
    end)
    u3.LocalPlayer:GetPropertyChangedSignal("DevComputerMovementMode"):Connect(function() --[[Anonymous function at line 75]]
        --[[
        Upvalues:
            [1] = u9
        --]]
        u9:UpdateMouseLockAvailability()
    end)
    u9:UpdateMouseLockAvailability()
    return u9
end
function u7.GetIsMouseLocked(p13) --[[Anonymous function at line 84]]
    return p13.isMouseLocked
end
function u7.GetBindableToggleEvent(p14) --[[Anonymous function at line 88]]
    return p14.mouseLockToggledEvent.Event
end
function u7.GetMouseLockOffset(_) --[[Anonymous function at line 92]]
    return Vector3.new(1.75, 0, 0)
end
function u7.UpdateMouseLockAvailability(p15) --[[Anonymous function at line 96]]
    --[[
    Upvalues:
        [1] = u3
        [2] = u5
    --]]
    local v16 = u3.LocalPlayer.DevEnableMouseLock
    local v17 = u3.LocalPlayer.DevComputerMovementMode == Enum.DevComputerMovementMode.Scriptable
    local v18 = v16 and (u5.ControlMode == Enum.ControlMode.MouseLockSwitch and u5.ComputerMovementMode ~= Enum.ComputerMovementMode.ClickToMove)
    if v18 then
        v18 = not v17
    end
    if v18 ~= p15.enabled then
        p15:EnableMouseLock(v18)
    end
end
function u7.OnBoundKeysObjectChanged(p19, p20) --[[Anonymous function at line 108]]
    p19.boundKeys = {}
    for v21 in string.gmatch(p20, "[^%s,]+") do
        for _, v22 in pairs(Enum.KeyCode:GetEnumItems()) do
            if v21 == v22.Name then
                p19.boundKeys[#p19.boundKeys + 1] = v22
                break
            end
        end
    end
    p19:UnbindContextActions()
    p19:BindContextActions()
end
function u7.OnMouseLockToggled(p23) --[[Anonymous function at line 123]]
    --[[
    Upvalues:
        [1] = u6
    --]]
    p23.isMouseLocked = not p23.isMouseLocked
    if p23.isMouseLocked then
        local v24 = script:FindFirstChild("CursorImage")
        if v24 and (v24:IsA("StringValue") and v24.Value) then
            u6.setMouseIconOverride(v24.Value)
        else
            if v24 then
                v24:Destroy()
            end
            local v25 = Instance.new("StringValue")
            assert(v25, "")
            v25.Name = "CursorImage"
            v25.Value = "rbxasset://textures/MouseLockedCursor.png"
            v25.Parent = script
            u6.setMouseIconOverride("rbxasset://textures/MouseLockedCursor.png")
        end
    else
        u6.restoreMouseIcon()
    end
    p23.mouseLockToggledEvent:Fire()
end
function u7.DoMouseLockSwitch(p26, _, p27, _) --[[Anonymous function at line 148]]
    if p27 ~= Enum.UserInputState.Begin then
        return Enum.ContextActionResult.Pass
    end
    p26:OnMouseLockToggled()
    return Enum.ContextActionResult.Sink
end
function u7.BindContextActions(u28) --[[Anonymous function at line 156]]
    --[[
    Upvalues:
        [1] = u4
        [2] = u2
    --]]
    local v29 = u4
    local v30 = u2
    local v31 = u28.boundKeys
    v29:BindActionAtPriority("MouseLockSwitchAction", function(p32, p33, p34) --[[Anonymous function at line 157]]
        --[[
        Upvalues:
            [1] = u28
        --]]
        return u28:DoMouseLockSwitch(p32, p33, p34)
    end, false, v30, unpack(v31))
end
function u7.UnbindContextActions(_) --[[Anonymous function at line 162]]
    --[[
    Upvalues:
        [1] = u4
    --]]
    u4:UnbindAction("MouseLockSwitchAction")
end
function u7.IsMouseLocked(p35) --[[Anonymous function at line 166]]
    local v36 = p35.enabled
    if v36 then
        v36 = p35.isMouseLocked
    end
    return v36
end
function u7.EnableMouseLock(p37, p38) --[[Anonymous function at line 170]]
    --[[
    Upvalues:
        [1] = u6
    --]]
    if p38 ~= p37.enabled then
        p37.enabled = p38
        if p37.enabled then
            p37:BindContextActions()
            return
        end
        u6.restoreMouseIcon()
        p37:UnbindContextActions()
        if p37.isMouseLocked then
            p37.mouseLockToggledEvent:Fire()
        end
        p37.isMouseLocked = false
    end
end
return u7