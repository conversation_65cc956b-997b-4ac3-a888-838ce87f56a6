-- Full Path: -Grow-a-Garden-\StarterGui\Trowel_Client\Frame\RotateFrame\Rotate\SFX-LocalScript.lua
local u1 = game.SoundService.Hover
local u2 = game.SoundService.Click
local v3 = game:GetService("UserInputService")
local v4 = game:GetService("TweenService")
local v5 = TweenInfo.new(0.1, Enum.EasingStyle.Sine, Enum.EasingDirection.Out, 0, false, 0)
local u6 = v4:Create(script.Parent.TextLabel, v5, {
    ["Size"] = UDim2.new(1, 0, 1, 0)
})
local u7 = v4:Create(script.Parent.TextLabel, v5, {
    ["Size"] = UDim2.new(0.9, 0, 0.9, 0)
})
script.Parent.TextLabel.Size = UDim2.new(0.9, 0, 0.9, 0)
if v3.TouchEnabled == false then
    script.Parent.MouseEnter:Connect(function() --[[Anonymous function at line 10]]
        --[[
        Upvalues:
            [1] = u1
            [2] = u6
        --]]
        u1.PlaybackSpeed = 1 + math.random(-15, 15) / 100
        u1.TimePosition = 0
        u1.Playing = true
        u6:Play()
    end)
    script.Parent.MouseLeave:Connect(function() --[[Anonymous function at line 16]]
        --[[
        Upvalues:
            [1] = u7
        --]]
        u7:Play()
    end)
end
script.Parent.MouseButton1Click:Connect(function() --[[Anonymous function at line 20]]
    --[[
    Upvalues:
        [1] = u2
    --]]
    u2.PlaybackSpeed = 1 + math.random(-15, 15) / 100
    u2.TimePosition = 0
    u2.Playing = true
end)