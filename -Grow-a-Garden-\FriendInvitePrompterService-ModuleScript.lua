-- Full Path: -Grow-a-Garden-\\FriendInvitePrompterService-ModuleScript.lua
local u1 = game:GetService("SocialService")
local v2 = game:GetService("Players")
game:GetService("ReplicatedStorage")
local u3 = game:GetService("TweenService")
local v4 = game:GetService("ReplicatedStorage")
local v5 = v4:WaitFor<PERSON>hild("GameEvents"):WaitForChild("FriendInvitePrompterService")
local u6 = v2.LocalPlayer
local u7 = u6.PlayerGui:WaitForChild("FriendInvites")
local u8 = require(v4.Modules.WaitForDescendant)
local u9 = require(v4.Modules.SetupHoverAnimations)
local u10 = require(v4.Modules.SetupSounds)
local u26 = {
    ["CreatePrompt"] = function(_) --[[Function name: CreatePrompt, line 28]]
        --[[
        Upvalues:
            [1] = u6
            [2] = u1
            [3] = u7
            [4] = u8
            [5] = u9
            [6] = u10
            [7] = u3
        --]]
        print("Called!")
        local u11 = u6
        local v12, v13 = pcall(function() --[[Anonymous function at line 22]]
            --[[
            Upvalues:
                [1] = u1
                [2] = u11
            --]]
            return u1:CanSendGameInviteAsync(u11)
        end)
        if v12 and v13 then
            local v14 = u7:FindFirstChild("InsertionPoint")
            if v14 then
                local u15 = v14.FriendInviteTemplate:Clone()
                local u16 = u15.UIScale
                u16.Scale = 0
                local v17 = u8(u15, "ACCEPT_BUTTON")
                local v18 = u8(u15, "DECLINE_BUTTON")
                u9(v17)
                u9(v18)
                local v19 = u10(v17)
                local v20 = u10(v18)
                local function u22() --[[Anonymous function at line 47]]
                    --[[
                    Upvalues:
                        [1] = u3
                        [2] = u16
                        [3] = u15
                    --]]
                    local v21 = u3:Create(u16, TweenInfo.new(0.2), {
                        ["Scale"] = 0
                    })
                    v21:Play()
                    v21.Completed:Once(function() --[[Anonymous function at line 53]]
                        --[[
                        Upvalues:
                            [1] = u15
                        --]]
                        u15:Destroy()
                    end)
                end
                v19.MouseButton1Click:Connect(function() --[[Anonymous function at line 58]]
                    --[[
                    Upvalues:
                        [1] = u22
                        [2] = u6
                        [3] = u1
                    --]]
                    u22()
                    local u23 = u6
                    local v24, v25 = pcall(function() --[[Anonymous function at line 22]]
                        --[[
                        Upvalues:
                            [1] = u1
                            [2] = u23
                        --]]
                        return u1:CanSendGameInviteAsync(u23)
                    end)
                    if v24 and v25 then
                        u1:PromptGameInvite(u6)
                    end
                end)
                v20.MouseButton1Click:Connect(function() --[[Anonymous function at line 65]]
                    --[[
                    Upvalues:
                        [1] = u22
                    --]]
                    u22()
                end)
                u15.Visible = true
                u15.Parent = v14
                u3:Create(u16, TweenInfo.new(0.5), {
                    ["Scale"] = 1
                }):Play()
            end
        else
            return warn("Cannot send game invite!")
        end
    end
}
local u27 = { "CreatePrompt" }
v5.OnClientEvent:Connect(function(p28, ...) --[[Anonymous function at line 81]]
    --[[
    Upvalues:
        [1] = u27
        [2] = u26
    --]]
    print(Player, p28)
    if table.find(u27, p28) then
        local v29 = u26[p28]
        if v29 then
            v29(u26, Player, ...)
        end
    else
        return
    end
end)
return u26