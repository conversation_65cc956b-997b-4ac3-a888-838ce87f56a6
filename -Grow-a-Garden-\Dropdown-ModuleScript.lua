-- Full Path: -Grow-a-Garden-\\Dropdown-ModuleScript.lua
return function(u1) --[[Anonymous function at line 1]]
    local u2 = Instance.new("Frame")
    u2.Name = "Dropdown"
    u2.AutomaticSize = Enum.AutomaticSize.XY
    u2.BackgroundTransparency = 1
    u2.BorderSizePixel = 0
    u2.AnchorPoint = Vector2.new(0.5, 0)
    u2.Position = UDim2.new(0.5, 0, 1, 10)
    u2.ZIndex = -2
    u2.ClipsDescendants = true
    u2.Parent = u1.widget
    local v3 = Instance.new("UICorner")
    v3.Name = "DropdownCorner"
    v3.CornerRadius = UDim.new(0, 10)
    v3.Parent = u2
    local u4 = Instance.new("ScrollingFrame")
    u4.Name = "DropdownScroller"
    u4.AutomaticSize = Enum.AutomaticSize.X
    u4.BackgroundTransparency = 1
    u4.BorderSizePixel = 0
    u4.AnchorPoint = Vector2.new(0, 0)
    u4.Position = UDim2.new(0, 0, 0, 0)
    u4.ZIndex = -1
    u4.ClipsDescendants = true
    u4.Visible = true
    u4.VerticalScrollBarInset = Enum.ScrollBarInset.ScrollBar
    u4.VerticalScrollBarPosition = Enum.VerticalScrollBarPosition.Right
    u4.Active = false
    u4.ScrollingEnabled = true
    u4.AutomaticCanvasSize = Enum.AutomaticSize.Y
    u4.ScrollBarThickness = 5
    u4.ScrollBarImageColor3 = Color3.fromRGB(255, 255, 255)
    u4.ScrollBarImageTransparency = 0.8
    u4.CanvasSize = UDim2.new(0, 0, 0, 0)
    u4.Selectable = false
    u4.Active = true
    u4.Parent = u2
    local u5 = Instance.new("UIPadding")
    u5.Name = "DropdownPadding"
    u5.PaddingTop = UDim.new(0, 8)
    u5.PaddingBottom = UDim.new(0, 8)
    u5.Parent = u4
    local v6 = Instance.new("UIListLayout")
    v6.Name = "DropdownList"
    v6.FillDirection = Enum.FillDirection.Vertical
    v6.SortOrder = Enum.SortOrder.LayoutOrder
    v6.HorizontalAlignment = Enum.HorizontalAlignment.Center
    v6.HorizontalFlex = Enum.UIFlexAlignment.SpaceEvenly
    v6.Parent = u4
    local v7 = u1.dropdownJanitor
    local u8 = require(u1.iconModule)
    u1.dropdownChildAdded:Connect(function(u9) --[[Anonymous function at line 58]]
        local _, u10 = u9:modifyTheme({
            { "Widget", "BorderSize", 0 },
            { "IconCorners", "CornerRadius", UDim.new(0, 4) },
            { "Widget", "MinimumWidth", 190 },
            { "Widget", "MinimumHeight", 56 },
            { "IconLabel", "TextSize", 19 },
            { "PaddingLeft", "Size", UDim2.fromOffset(25, 0) },
            { "Notice", "Position", UDim2.new(1, -24, 0, 5) },
            { "ContentsList", "HorizontalAlignment", Enum.HorizontalAlignment.Left },
            { "Selection", "Size", UDim2.new(1, -8, 1, -8) },
            { "Selection", "Position", UDim2.new(0, 4, 0, 4) }
        })
        task.defer(function() --[[Anonymous function at line 72]]
            --[[
            Upvalues:
                [1] = u9
                [2] = u10
            --]]
            u9.joinJanitor:add(function() --[[Anonymous function at line 73]]
                --[[
                Upvalues:
                    [1] = u9
                    [2] = u10
                --]]
                u9:removeModification(u10)
            end)
        end)
    end)
    u1.dropdownSet:Connect(function(p11) --[[Anonymous function at line 78]]
        --[[
        Upvalues:
            [1] = u1
            [2] = u8
        --]]
        for _, v12 in pairs(u1.dropdownIcons) do
            u8.getIconByUID(v12):destroy()
        end
        local _ = #p11
        if type(p11) == "table" then
            for _, v13 in pairs(p11) do
                v13:joinDropdown(u1)
            end
        end
    end)
    local u14 = require(script.Parent.Parent.Utility)
    v7:add(u1.toggled:Connect(function() --[[Function name: updateVisibility, line 95]]
        --[[
        Upvalues:
            [1] = u14
            [2] = u2
            [3] = u1
        --]]
        u14.setVisible(u2, u1.isSelected, "InternalDropdown")
    end))
    u14.setVisible(u2, u1.isSelected, "InternalDropdown")
    local u15 = 0
    local u16 = false
    local function u32() --[[Anonymous function at line 107]]
        --[[
        Upvalues:
            [1] = u15
            [2] = u16
            [3] = u32
            [4] = u2
            [5] = u4
            [6] = u8
            [7] = u1
            [8] = u5
        --]]
        u15 = u15 + 1
        if u16 then
            return
        end
        local u17 = u15
        u16 = true
        task.defer(function() --[[Anonymous function at line 116]]
            --[[
            Upvalues:
                [1] = u16
                [2] = u15
                [3] = u17
                [4] = u32
            --]]
            u16 = false
            if u15 ~= u17 then
                u32()
            end
        end)
        local v18 = u2:GetAttribute("MaxIcons")
        if not v18 then
            return
        end
        local v19 = {}
        for _, v20 in pairs(u4:GetChildren()) do
            if v20:IsA("GuiObject") then
                local v21 = { v20, v20.AbsolutePosition.Y }
                table.insert(v19, v21)
            end
        end
        table.sort(v19, function(p22, p23) --[[Anonymous function at line 133]]
            return p22[2] < p23[2]
        end)
        local v24 = 0
        local v25 = false
        for v26 = 1, v18 do
            local v27 = v19[v26]
            if not v27 then
                break
            end
            local v28 = v27[1]
            v24 = v24 + v28.AbsoluteSize.Y
            local v29 = v28:GetAttribute("WidgetUID")
            if v29 then
                v29 = u8.getIconByUID(v29)
            end
            if v29 then
                local v30
                if v25 then
                    v30 = nil
                else
                    v30 = u1:getInstance("ClickRegion")
                    v25 = true
                end
                v29:getInstance("ClickRegion").NextSelectionUp = v30
            end
        end
        local v31 = v24 + u5.PaddingTop.Offset + u5.PaddingBottom.Offset
        u4.Size = UDim2.fromOffset(0, v31)
    end
    v7:add(u4:GetPropertyChangedSignal("AbsoluteCanvasSize"):Connect(u32))
    v7:add(u4.ChildAdded:Connect(u32))
    v7:add(u4.ChildRemoved:Connect(u32))
    v7:add(u2:GetAttributeChangedSignal("MaxIcons"):Connect(u32))
    v7:add(u1.childThemeModified:Connect(u32))
    u32()
    return u2
end