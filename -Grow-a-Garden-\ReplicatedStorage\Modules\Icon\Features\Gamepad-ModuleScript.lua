-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\Icon\Features\Gamepad-ModuleScript.lua
local u1 = game:GetService("GamepadService")
local u2 = game:GetService("UserInputService")
local u3 = game:GetService("GuiService")
local u4 = {}
local u5 = nil
function u4.start(p6) --[[Anonymous function at line 24]]
    --[[
    Upvalues:
        [1] = u5
        [2] = u3
        [3] = u2
        [4] = u4
        [5] = u1
    --]]
    u5 = p6
    u5.highlightKey = Enum.KeyCode.DPadUp
    u5.highlightIcon = false
    task.delay(1, function() --[[Anonymous function at line 33]]
        --[[
        Upvalues:
            [1] = u5
            [2] = u3
            [3] = u2
            [4] = u4
            [5] = u1
        --]]
        local u7 = u5.iconsDictionary
        local u8 = nil
        local u9 = false
        local u10 = false
        require(script.Parent.Parent.Utility)
        local u11 = require(script.Parent.Parent.Elements.Selection)
        local function u18() --[[Anonymous function at line 50]]
            --[[
            Upvalues:
                [1] = u3
                [2] = u7
                [3] = u2
                [4] = u11
                [5] = u5
                [6] = u8
                [7] = u10
                [8] = u9
                [9] = u4
            --]]
            local v12 = u3.SelectedObject
            if v12 then
                v12 = v12:GetAttribute("CorrespondingIconUID")
            end
            if v12 then
                v12 = u7[v12]
            end
            local v13 = u2.GamepadEnabled
            if v12 then
                if v13 then
                    local v14 = v12:getInstance("ClickRegion")
                    local v15 = v12.selection
                    if not v15 then
                        v15 = v12.janitor:add(u11(u5))
                        v15:SetAttribute("IgnoreVisibilityUpdater", true)
                        v15.Parent = v12.widget
                        v12.selection = v15
                        v12:refreshAppearance(v15)
                    end
                    v14.SelectionImageObject = v15.Selection
                end
                if u8 and u8 ~= v12 then
                    u8:setIndicator()
                end
                local v16
                if v13 and not (u10 or v12.parentIconUID) then
                    v16 = Enum.KeyCode.ButtonA
                else
                    v16 = nil
                end
                u8 = v12
                u5.lastHighlightedIcon = v12
                v12:setIndicator(v16)
            else
                local v17
                if v13 and not u9 then
                    v17 = u5.highlightKey
                else
                    v17 = nil
                end
                if not u8 then
                    u8 = u4.getIconToHighlight()
                end
                if v17 == u5.highlightKey then
                    u9 = true
                end
                if u8 then
                    u8:setIndicator(v17)
                end
            end
        end
        u3:GetPropertyChangedSignal("SelectedObject"):Connect(u18)
        local function v19() --[[Anonymous function at line 93]]
            --[[
            Upvalues:
                [1] = u2
                [2] = u9
                [3] = u10
                [4] = u18
            --]]
            if not u2.GamepadEnabled then
                u9 = false
                u10 = false
            end
            u18()
        end
        u2:GetPropertyChangedSignal("GamepadEnabled"):Connect(v19)
        if not u2.GamepadEnabled then
            u9 = false
            u10 = false
        end
        u18()
        u2.InputBegan:Connect(function(p20, _) --[[Anonymous function at line 107]]
            --[[
            Upvalues:
                [1] = u3
                [2] = u7
                [3] = u5
                [4] = u4
                [5] = u1
            --]]
            if p20.UserInputType == Enum.UserInputType.MouseButton1 then
                local v21 = u3.SelectedObject
                if v21 then
                    v21 = v21:GetAttribute("CorrespondingIconUID")
                end
                if v21 then
                    v21 = u7[v21]
                end
                if v21 then
                    u3.SelectedObject = nil
                end
                return
            elseif p20.KeyCode == u5.highlightKey then
                local v22 = u4.getIconToHighlight()
                if v22 then
                    if u1.GamepadCursorEnabled then
                        task.wait(0.2)
                        u1:DisableGamepadCursor()
                    end
                    u3.SelectedObject = v22:getInstance("ClickRegion")
                end
            end
        end)
    end)
end
function u4.getIconToHighlight() --[[Anonymous function at line 134]]
    --[[
    Upvalues:
        [1] = u5
    --]]
    local v23 = u5.iconsDictionary
    local v24 = u5.highlightIcon or u5.lastHighlightedIcon
    if not v24 then
        local v25 = nil
        for _, v26 in pairs(v23) do
            if not v26.parentIconUID and (not v25 or v26.widget.AbsolutePosition.X < v25) then
                v25 = v26.widget.AbsolutePosition.X
                v24 = v26
            end
        end
    end
    return v24
end
function u4.registerButton(u27) --[[Anonymous function at line 156]]
    --[[
    Upvalues:
        [1] = u2
        [2] = u1
        [3] = u3
    --]]
    local u28 = false
    u27.InputBegan:Connect(function(_) --[[Anonymous function at line 162]]
        --[[
        Upvalues:
            [1] = u28
        --]]
        u28 = true
        task.wait()
        task.wait()
        u28 = false
    end)
    local u32 = u2.InputBegan:Connect(function(p29) --[[Anonymous function at line 171]]
        --[[
        Upvalues:
            [1] = u28
            [2] = u1
            [3] = u3
            [4] = u27
        --]]
        task.wait()
        if p29.KeyCode == Enum.KeyCode.ButtonA and u28 then
            task.wait(0.2)
            u1:DisableGamepadCursor()
            u3.SelectedObject = u27
        else
            local v30 = u3.SelectedObject == u27
            local v31 = p29.KeyCode.Name
            if table.find({ "ButtonB", "ButtonSelect" }, v31) and (v30 and (v31 ~= "ButtonSelect" or u1.GamepadCursorEnabled)) then
                u3.SelectedObject = nil
            end
        end
    end)
    u27.Destroying:Once(function() --[[Anonymous function at line 192]]
        --[[
        Upvalues:
            [1] = u32
        --]]
        u32:Disconnect()
    end)
end
return u4