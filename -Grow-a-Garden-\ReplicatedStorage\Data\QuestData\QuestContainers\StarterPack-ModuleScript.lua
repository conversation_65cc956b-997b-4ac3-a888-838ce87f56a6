-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Data\QuestData\QuestContainers\StarterPack-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
require(v1.Data.QuestData.Types)
local u2 = require(v1.Data.QuestData.Quests)
require(v1.Data.QuestData.QuestRewards)
return {
    ["Type"] = "StarterPack",
    ["Display"] = function(_) --[[Function name: Display, line 13]]
        return "Stater Pack Plant Logic"
    end,
    ["Generate"] = function(_) --[[Function name: Generate, line 17]]
        --[[
        Upvalues:
            [1] = u2
        --]]
        local v3 = {
            ["Quests"] = { u2.Plant:Use({
                    ["Target"] = 2,
                    ["Arguments"] = { "Tomato" }
                }) },
            ["Rewards"] = {}
        }
        return v3
    end
}