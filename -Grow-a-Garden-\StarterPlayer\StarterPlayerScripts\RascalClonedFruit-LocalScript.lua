-- Full Path: -Grow-a-Garden-\StarterPlayer\StarterPlayerScripts\RascalClonedFruit-LocalScript.lua
local u1 = game:GetService("ReplicatedStorage")
local u2 = require(u1.Modules.PetServices.ActivePetsService)
local v3 = u1.GameEvents:WaitForChild("RascalVisualFruit_RE")
local v4 = u1.GameEvents:WaitFor<PERSON>hild("RascalDestroyFruit_RE")
local function u9(p5) --[[Anonymous function at line 7]]
    if p5:IsA("Model") then
        local v6 = p5.PrimaryPart
        if v6 then
            for _, v7 in ipairs(p5:GetDescendants()) do
                if v7:IsA("BasePart") then
                    v7.Anchored = false
                    if v7 ~= v6 then
                        local v8 = Instance.new("WeldConstraint")
                        v8.Part0 = v6
                        v8.Part1 = v7
                        v8.Parent = v6
                    end
                end
            end
        else
            warn("Model must have a PrimaryPart set")
        end
    else
        warn("Provided instance is not a Model")
        return
    end
end
local function u14(p10, p11) --[[Anonymous function at line 32]]
    local v12 = p10:FindFirstChild("FruitHold", true)
    if v12 and p11.PrimaryPart then
        p11:SetPrimaryPartCFrame(v12.CFrame * CFrame.new(0, 0, 0))
        p11.Parent = p10
        local v13 = Instance.new("Motor6D")
        v13.Part0 = v12
        v13.Part1 = p11.PrimaryPart
        v13.C0 = CFrame.new(0, 0, 0)
        v13.Parent = v12
    end
end
v3.OnClientEvent:Connect(function(u15, u16, u17, u18) --[[Anonymous function at line 49]]
    --[[
    Upvalues:
        [1] = u2
        [2] = u1
        [3] = u9
        [4] = u14
    --]]
    task.defer(function() --[[Anonymous function at line 50]]
        --[[
        Upvalues:
            [1] = u2
            [2] = u15
            [3] = u16
            [4] = u1
            [5] = u17
            [6] = u18
            [7] = u9
            [8] = u14
        --]]
        local v19 = u2:GetClientPetState(u15.Name)
        if v19 then
            v19 = v19[u16]
        end
        if v19 then
            v19 = v19.Asset
        end
        if v19 then
            v19 = v19:FindFirstChildWhichIsA("Model", true)
        end
        if v19 then
            local v20 = u1:WaitForChild("RascalFruitCache"):FindFirstChild(u17)
            if v20 then
                local v21 = v20:Clone()
                if not v21.PrimaryPart then
                    local v22 = v21:FindFirstChildWhichIsA("BasePart")
                    if not v22 then
                        warn("No valid PrimaryPart found for ClonedFruit:", v21.Name)
                        return
                    end
                    v21.PrimaryPart = v22
                end
                local v23 = math.random(0, 360)
                local v24 = math.rad(v23)
                v21:SetPrimaryPartCFrame(u18 * CFrame.Angles(0, v24, 0))
                u9(v21)
                u14(v19, v21)
            end
        else
            return
        end
    end)
end)
v4.OnClientEvent:Connect(function(p25) --[[Anonymous function at line 86]]
    for _, v26 in ipairs(workspace:GetDescendants()) do
        if v26:IsA("Model") and v26:GetAttribute("UUID") == p25 then
            v26:Destroy()
        end
    end
end)