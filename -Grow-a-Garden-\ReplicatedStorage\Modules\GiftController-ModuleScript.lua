-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\GiftController-ModuleScript.lua
local u1 = game:GetService("MarketplaceService")
local v2 = game:GetService("ReplicatedStorage")
local u3 = game:GetService("Players")
local u4 = u3.LocalPlayer
local u5 = u4.PlayerGui:WaitForChild("GiftPlayerList")
local u6 = require(v2.Modules.MarketController)
local u7 = require(v2.Modules.GuiController)
local u8 = require(v2.Data.GiftData)
local u9 = require(v2.Modules.Remotes)
local u20 = {
    ["CurrentProduct"] = nil,
    ["PromptGift"] = function(_, p10) --[[Function name: PromptGift, line 19]]
        --[[
        Upvalues:
            [1] = u8
            [2] = u20
            [3] = u7
            [4] = u5
        --]]
        local v11 = u8[p10]
        if v11 then
            u20.CurrentProduct = v11.NormalId
            u7:Open(u5)
        else
            warn((("Gift not found for \"%*\""):format(p10)))
        end
    end,
    ["PromptGiftFromGiftId"] = function(_, p12) --[[Function name: PromptGiftFromGiftId, line 30]]
        --[[
        Upvalues:
            [1] = u8
            [2] = u20
        --]]
        local v13 = nil
        for v14, v15 in u8 do
            if p12 == v15.GiftId then
                v13 = v14
                break
            end
        end
        if v13 then
            u20:PromptGift(v13)
        else
            warn((("Gift not found for \"%*\""):format(p12)))
        end
    end,
    ["PromptGiftFromNormalId"] = function(_, p16) --[[Function name: PromptGiftFromNormalId, line 47]]
        --[[
        Upvalues:
            [1] = u8
            [2] = u20
        --]]
        local v17 = nil
        for v18, v19 in u8 do
            if p16 == v19.NormalId then
                v17 = v18
                break
            end
        end
        if v17 then
            u20:PromptGift(v17)
        else
            warn((("Gift not found for \"%*\""):format(p16)))
        end
    end
}
local u21 = game.SoundService.Click
function u20.Start(_) --[[Anonymous function at line 66]]
    --[[
    Upvalues:
        [1] = u7
        [2] = u5
        [3] = u21
        [4] = u6
        [5] = u20
        [6] = u1
        [7] = u4
        [8] = u9
        [9] = u3
    --]]
    u7:UsePopupAnims(u5)
    local u22 = false
    local u23 = nil
    u5.Frame.Close.Activated:Connect(function() --[[Anonymous function at line 72]]
        --[[
        Upvalues:
            [1] = u21
            [2] = u7
            [3] = u5
        --]]
        u21.PlaybackSpeed = 1 + math.random(-15, 15) / 100
        u21.TimePosition = 0
        u21.Playing = true
        u7:Close(u5)
    end)
    u6.PromptProductPurchaseInitiated:Connect(function(_, p24) --[[Anonymous function at line 79]]
        --[[
        Upvalues:
            [1] = u20
            [2] = u23
            [3] = u5
        --]]
        if p24 == u20.CurrentProduct then
            u23 = true
            u5.Black.ZIndex = 1000
        end
    end)
    u1.PromptProductPurchaseFinished:Connect(function() --[[Anonymous function at line 87]]
        --[[
        Upvalues:
            [1] = u23
            [2] = u5
            [3] = u7
        --]]
        if u23 then
            u23 = false
            u5.Black.ZIndex = -1
            u7:Close(u5)
        end
    end)
    local function v28(u25) --[[Anonymous function at line 98]]
        --[[
        Upvalues:
            [1] = u4
            [2] = u5
            [3] = u20
            [4] = u22
            [5] = u23
            [6] = u9
        --]]
        if u25 ~= u4 then
            local v26 = u5.Frame.ScrollingFrame.UIListLayout.Template:Clone()
            local v27 = u25.UserId
            v26.Name = tostring(v27)
            v26.Button.Headshot.Image = ("rbxthumb://type=AvatarHeadShot&id=%*&w=180&h=180"):format(u25.UserId)
            v26.Button.Username.Text = ("@%*"):format(u25.Name)
            v26.Parent = u5.Frame.ScrollingFrame
            v26.Button.Activated:Connect(function() --[[Anonymous function at line 109]]
                --[[
                Upvalues:
                    [1] = u20
                    [2] = u22
                    [3] = u23
                    [4] = u9
                    [5] = u25
                --]]
                if u20.CurrentProduct then
                    if not (u22 or u23) then
                        u22 = true
                        u9.Gift.SendGiftTo.send({
                            ["productId"] = u20.CurrentProduct,
                            ["targetUserId"] = u25.UserId
                        })
                        task.wait(3)
                        u22 = false
                    end
                else
                    return
                end
            end)
        end
    end
    u3.PlayerAdded:Connect(v28)
    for _, v29 in u3:GetPlayers() do
        task.spawn(v28, v29)
    end
    u5.Frame.ScrollingFrame.NoPlayers.Visible = #u3:GetPlayers() <= 1
    u3.PlayerRemoving:Connect(function(p30) --[[Anonymous function at line 136]]
        --[[
        Upvalues:
            [1] = u5
            [2] = u3
        --]]
        local v31 = u5.Frame.ScrollingFrame
        local v32 = p30.UserId
        local v33 = v31:FindFirstChild((tostring(v32)))
        if v33 then
            v33:Destroy()
        end
        u5.Frame.ScrollingFrame.NoPlayers.Visible = #u3:GetPlayers() <= 1
    end)
end
task.spawn(u20.Start, u20)
return u20