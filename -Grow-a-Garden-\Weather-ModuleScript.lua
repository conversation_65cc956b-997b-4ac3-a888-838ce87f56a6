-- Full Path: -Grow-a-Garden-\\Weather-ModuleScript.lua
local u1 = require(script.Parent.Parent.Shared.Util)
game:GetService("ReplicatedStorage")
local u2 = game:GetService("HttpService")
local u3 = u2:J<PERSON>NDecode(workspace:<PERSON><PERSON><PERSON><PERSON>bute("AllWeather") or "[]")
workspace:GetAttributeChangedSignal("AllWeather"):Connect(function() --[[Anonymous function at line 8]]
    --[[
    Upvalues:
        [1] = u3
        [2] = u2
    --]]
    u3 = u2:JSONDecode(workspace:GetAttribute("AllWeather"))
end)
local u8 = {
    ["Transform"] = function(p4) --[[Function name: Transform, line 13]]
        --[[
        Upvalues:
            [1] = u1
            [2] = u3
        --]]
        return u1.MakeFuzzyFinder(u3)(p4)
    end,
    ["Validate"] = function(p5) --[[Function name: Validate, line 19]]
        return #p5 > 0, "No seeds could be found."
    end,
    ["Autocomplete"] = function(p6) --[[Function name: Autocomplete, line 23]]
        --[[
        Upvalues:
            [1] = u1
        --]]
        return u1.GetNames(p6)
    end,
    ["Parse"] = function(p7) --[[Function name: Parse, line 27]]
        return p7[1]
    end,
    ["Default"] = function(_) --[[Function name: Default, line 31]]
        return "Rain"
    end,
    ["ArgumentOperatorAliases"] = {
        ["me"] = ".",
        ["all"] = "*",
        ["others"] = "**",
        ["random"] = "?"
    }
}
return function(p9) --[[Anonymous function at line 43]]
    --[[
    Upvalues:
        [1] = u8
    --]]
    p9:RegisterType("weather", u8)
end