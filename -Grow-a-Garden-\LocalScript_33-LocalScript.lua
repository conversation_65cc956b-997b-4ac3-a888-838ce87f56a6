-- Full Path: -Grow-a-Garden-\\LocalScript_33-LocalScript.lua
local v1 = game:GetService("Players")
local u2 = game:GetService("TweenService")
local u3 = v1.LocalPlayer
local u4 = script.Parent
local function u6() --[[Anonymous function at line 8]]
    --[[
    Upvalues:
        [1] = u3
        [2] = u2
        [3] = u4
    --]]
    local v5 = {
        ["GroupTransparency"] = u3:GetAttribute("DataFullyLoaded") and 1 or 0
    }
    u2:Create(u4, TweenInfo.new(1.5), v5):Play()
end
task.spawn(function() --[[Anonymous function at line 16]]
    --[[
    Upvalues:
        [1] = u3
        [2] = u4
        [3] = u2
    --]]
    while true do
        repeat
            task.wait()
        until not u3:GetAttribute("DataFullyLoaded")
        local v7 = u4.ImageLabel.ImageLabel
        local v8 = u2:Create(v7, TweenInfo.new(0.25, Enum.EasingStyle.Linear), {
            ["Rotation"] = v7.Rotation + 180
        })
        v8:Play()
        v8.Completed:Wait()
        local v9 = u2:Create(v7, TweenInfo.new(0.25, Enum.EasingStyle.Linear), {
            ["Rotation"] = v7.Rotation + 180
        })
        v9:Play()
        v9.Completed:Wait()
        task.wait(1)
    end
end)
u3:GetAttributeChangedSignal("DataFullyLoaded"):Connect(function() --[[Anonymous function at line 44]]
    --[[
    Upvalues:
        [1] = u6
    --]]
    u6()
end)
u6()