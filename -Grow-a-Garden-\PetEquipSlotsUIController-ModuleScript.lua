-- Full Path: -Grow-a-Garden-\\PetEquipSlotsUIController-ModuleScript.lua
local u1 = game:GetService("Players").LocalPlayer
local v2 = game:GetService("ReplicatedStorage")
local u3 = game:GetService("TweenService")
local u4 = u1:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("PlayerGui"):<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("PetEquipSlots_UI")
local u5 = u4:Wait<PERSON><PERSON><PERSON><PERSON><PERSON>("Main")
local u6 = v2:Wait<PERSON>or<PERSON>hild("GameEvents")
local u7 = require(v2.Modules.PetServices.PetUtilities)
local u8 = require(v2.Modules.DataService)
local u9 = require(v2.Data.PetRegistry).PetList
local u10 = require(v2.Modules.GuiController)
local u11 = u5.Main.Holder.TradeInButton.TradeIn
local u12 = u5:WaitForChild("Details")
local v13 = u5.Main.Holder.TradeInButton
local u14 = v13.Confirm
local u15 = v13.Cancel
local u16 = v13.TradeIn
local u17 = u5.Main.Holder.TextHolder.REQUIRED_AGE
local u18 = {}
local u19 = {
    ["Pet"] = "Pet",
    ["Egg"] = "Egg",
    ["EggInfo"] = "EggInfo"
}
u18.CurrentMode = u19.Pet
local u20 = nil
local u21 = {
    [0] = UDim2.new(1, 0, 0, 0),
    [1] = UDim2.new(1, 0, 0.175, 0),
    [2] = UDim2.new(1, 0, 0.375, 0),
    [3] = UDim2.new(1, 0, 0.575, 0),
    [4] = UDim2.new(1, 0, 0.775, 0),
    [5] = UDim2.new(1, 0, 1, 0)
}
local function u27(p22) --[[Anonymous function at line 45]]
    --[[
    Upvalues:
        [1] = u3
        [2] = u12
    --]]
    local v23 = {
        ["Position"] = p22 and UDim2.new(0.8, 0, 0.5, 0) or UDim2.new(0.6, 0, 0.5, 0)
    }
    local v24 = {
        ["Scale"] = p22 and 1 or 0.1
    }
    local v25 = TweenInfo.new(0.25, Enum.EasingStyle.Quad, Enum.EasingDirection.Out)
    u3:Create(u12, v25, v23):Play()
    local v26 = u12:FindFirstChildWhichIsA("UIScale")
    if v26 then
        u3:Create(v26, v25, v24):Play()
    end
end
function u18.SetMode(p28, p29) --[[Anonymous function at line 78]]
    --[[
    Upvalues:
        [1] = u20
        [2] = u27
        [3] = u11
        [4] = u16
        [5] = u14
        [6] = u15
        [7] = u19
        [8] = u5
    --]]
    p28.CurrentMode = p29
    u20 = nil
    u27(false)
    u11.BackgroundTransparency = 0.5
    u11.UIStroke.Transparency = 0.5
    u11.TextLabel.TextTransparency = 0.5
    u16.Visible = true
    u14.Visible = false
    u15.Visible = false
    local v30 = p29 == u19.Pet and true or p29 == u19.Egg
    u5.Main.Holder.ScrollingFrame.Visible = v30
    u5.Main.Holder.TradeInButton.Visible = v30
    u5.Main.Holder.TextHolder.Visible = v30
    u5.Main.BAR.Visible = v30
    u5.Main.Holder.EggInfo.Visible = not v30
    if v30 then
        p28:RefreshUI()
    else
        u5.Main.Header.Title.Text = "Egg Rarity Info"
        u5.Main.Header.BackgroundColor3 = Color3.fromRGB(35, 150, 238)
        u5.Main.Header.Title.UIStroke.Color = Color3.fromRGB(17, 74, 117)
    end
end
local function u37(p31) --[[Anonymous function at line 102]]
    --[[
    Upvalues:
        [1] = u9
        [2] = u7
        [3] = u12
    --]]
    local v32 = u9[p31.PetType]
    local v33 = p31.PetData
    local v34 = v33.Level
    local v35 = u7:CalculateWeight(v33.BaseWeight, v34) * 100
    local v36 = math.round(v35) / 100
    u12.Holder.Header.PET_TEXT.Text = p31.PetType or "Unknown"
    u12.Holder.Display.Display.PET_NAME_CONFIG.Text = v33.Name or "Unnamed"
    u12.Holder.Display.Display.PET_IMAGE.Image = v32.Icon or ""
    u12.Holder.Description.Display.PET_DESCRIPTION.Text = v32.Description or "No description available."
    u12.Holder.Stats.Holder.Weight.PET_WEIGHT.Text = ("Weight: %* KG"):format(v36)
    u12.Holder.Stats.Holder.Age.PET_AGE.Text = ("Age: %*"):format(v34)
end
function u18.Show(p38) --[[Anonymous function at line 116]]
    --[[
    Upvalues:
        [1] = u4
    --]]
    u4.Enabled = true
    p38:SetMode(p38.CurrentMode)
    p38:RefreshUI()
end
function u18.Hide(_) --[[Anonymous function at line 122]]
    --[[
    Upvalues:
        [1] = u4
    --]]
    u4.Enabled = false
end
function u18.RefreshUI(_) --[[Anonymous function at line 126]]
    --[[
    Upvalues:
        [1] = u20
        [2] = u27
        [3] = u11
        [4] = u16
        [5] = u14
        [6] = u15
        [7] = u8
        [8] = u18
        [9] = u5
        [10] = u21
        [11] = u7
        [12] = u1
        [13] = u9
        [14] = u37
    --]]
    u20 = nil
    u27(false)
    u11.BackgroundTransparency = 0.5
    u11.UIStroke.Transparency = 0.5
    u11.TextLabel.TextTransparency = 0.5
    u16.Visible = true
    u14.Visible = false
    u15.Visible = false
    local v39 = u8:GetData()
    local _ = v39.PetsData.MutableStats
    local v40 = u18.CurrentMode == "Pet"
    local v41
    if v40 then
        v41 = v39.PetsData.PurchasedEquipSlots or 0
    else
        v41 = v39.PetsData.PurchasedEggSlots or 0
    end
    local v42 = ({
        20,
        30,
        45,
        60,
        75
    })[v41 + 1] or 999
    if u18.CurrentMode == "Pet" then
        u5.Main.Header.Title.Text = "Trade in Pets for Extra Pet Equip Slots!"
        u5.Main.Header.BackgroundColor3 = Color3.fromRGB(255, 175, 21)
        u5.Main.Header.Title.UIStroke.Color = Color3.fromRGB(134, 87, 11)
    elseif u18.CurrentMode == "Egg" then
        u5.Main.Header.Title.Text = "Trade in Pets for Extra Egg Equip Slots!"
        u5.Main.Header.BackgroundColor3 = Color3.fromRGB(83, 171, 52)
        u5.Main.Header.Title.UIStroke.Color = Color3.fromRGB(57, 100, 47)
    end
    if v41 >= 5 then
        u5.Main.Holder.TextHolder.REQUIRED_AGE.Text = "You have reached the maximum amount of pet equip slots!"
        u5.Main.Holder.ScrollingFrame.Visible = false
        u5.Main.Holder.TradeInButton.Visible = false
    elseif v40 or u18.CurrentMode == "Egg" then
        u5.Main.Holder.TextHolder.REQUIRED_AGE.Text = ("Required age for next pet equip slot unlock: <font color=\"#ffbe19\">%*</font>"):format(v42)
        u5.Main.Holder.ScrollingFrame.Visible = true
        u5.Main.Holder.TradeInButton.Visible = true
    end
    u5.Main.BAR.ACTUAL_BAR.Size = u21[math.clamp(v41, 0, 5)] or u21[0]
    local v43 = u5.Main.BAR.IncrementalListing
    local v44 = {
        "One",
        "Two",
        "Three",
        "Four",
        "Five"
    }
    for v45 = 1, 5 do
        local v46 = v43:FindFirstChild(v44[v45])
        if v46 then
            local v47 = v45 <= v41
            local v48 = v46:FindFirstChildWhichIsA("UIGradient")
            if v48 then
                v48.Enabled = v47
            end
        end
    end
    local v49 = v43:FindFirstChild("Title")
    if v49 and v49:FindFirstChild("UIGradient") then
        v49.UIGradient.Enabled = v41 >= 5
    end
    local u50 = u5.Main.Holder.ScrollingFrame.Content
    local v51 = u50:FindFirstChild("TEMPLATE")
    for _, v52 in u50:GetChildren() do
        if v52:IsA("ImageButton") and v52.Name ~= "TEMPLATE" then
            v52:Destroy()
        end
    end
    v51.Visible = false
    local v53 = u7:GetPetsSortedByAge(u1, 0, true)
    if v41 >= 5 then
        u11.BackgroundTransparency = 0.5
        u11.UIStroke.Transparency = 0.5
        u11.TextLabel.TextTransparency = 0.5
    else
        for _, u54 in v53 do
            if u54.PetData.Level >= v42 then
                local v55 = u9[u54.PetType]
                local v56 = v51:Clone()
                v56.Visible = true
                v56.Name = u54.UUID
                v56.Display.PET_NAME.Text = u54.PetType or "Unknown"
                v56.Display.PET_AGE.Text = u54.PetData.Level or "1"
                v56.Display.DetailHolder.PET_IMAGE.Image = v55.Icon or ""
                local function u59(p57, p58) --[[Anonymous function at line 216]]
                    if p58 then
                        p57.Display.BackgroundColor3 = Color3.fromRGB(30, 100, 14)
                        p57.Display.UIStroke.Color = Color3.fromRGB(37, 238, 38)
                        p57.Display.SELECTED_VIGNETTE.Visible = true
                    else
                        p57.Display.BackgroundColor3 = Color3.fromRGB(94, 44, 18)
                        p57.Display.UIStroke.Color = Color3.fromRGB(103, 31, 0)
                        p57.Display.SELECTED_VIGNETTE.Visible = false
                    end
                end
                u59(v56, false)
                v56.MouseButton1Click:Connect(function() --[[Anonymous function at line 230]]
                    --[[
                    Upvalues:
                        [1] = u20
                        [2] = u54
                        [3] = u50
                        [4] = u59
                        [5] = u11
                        [6] = u37
                        [7] = u27
                    --]]
                    if u20 == u54.UUID then
                        u20 = nil
                    else
                        u20 = u54.UUID
                    end
                    for _, v60 in u50:GetChildren() do
                        if v60:IsA("ImageButton") and v60:FindFirstChild("Display") then
                            u59(v60, v60.Name == u20)
                        end
                    end
                    local v61 = u20 ~= nil
                    u11.BackgroundTransparency = v61 and 0 or 0.5
                    u11.UIStroke.Transparency = v61 and 0 or 0.5
                    u11.TextLabel.TextTransparency = v61 and 0 or 0.5
                    if u20 then
                        u37(u54)
                        u27(true)
                    else
                        u27(false)
                    end
                end)
                v56.Parent = u50
                u11.BackgroundTransparency = 0.5
                u11.UIStroke.Transparency = 0.5
                u11.TextLabel.TextTransparency = 0.5
            end
        end
    end
end
function u18.OpenEggInfoExternally(p62) --[[Anonymous function at line 260]]
    --[[
    Upvalues:
        [1] = u19
    --]]
    p62:SetMode(u19.EggInfo)
end
local u63 = {}
function u18.Start(_) --[[Anonymous function at line 274]]
    --[[
    Upvalues:
        [1] = u10
        [2] = u4
        [3] = u16
        [4] = u63
        [5] = u20
        [6] = u17
        [7] = u14
        [8] = u15
        [9] = u6
        [10] = u18
        [11] = u27
        [12] = u11
        [13] = u5
        [14] = u12
        [15] = u19
    --]]
    u10:UsePopupAnims(u4)
    u16.SENSOR.MouseButton1Click:Connect(function() --[[Anonymous function at line 277]]
        --[[
        Upvalues:
            [1] = u63
            [2] = u20
            [3] = u17
            [4] = u16
            [5] = u14
            [6] = u15
        --]]
        local v64
        if u63.TradeInStart and (nil or 0.5) > tick() - u63.TradeInStart then
            v64 = true
        else
            u63.TradeInStart = tick()
            v64 = false
        end
        if v64 then
            return
        elseif u20 then
            u17.Text = "Are you sure you want to trade in this pet?"
            u16.Visible = false
            u14.Visible = true
            u15.Visible = true
        end
    end)
    u14.SENSOR.MouseButton1Click:Connect(function() --[[Anonymous function at line 285]]
        --[[
        Upvalues:
            [1] = u63
            [2] = u20
            [3] = u6
            [4] = u18
            [5] = u27
            [6] = u11
            [7] = u16
            [8] = u14
            [9] = u15
        --]]
        local v65
        if u63.TradeConfirm and (nil or 0.5) > tick() - u63.TradeConfirm then
            v65 = true
        else
            u63.TradeConfirm = tick()
            v65 = false
        end
        if v65 then
            return
        elseif u20 then
            u6.UnlockSlotFromPet:FireServer(u20, u18.CurrentMode)
            task.wait(1)
            u18:RefreshUI()
            u20 = nil
            u27(false)
            u11.BackgroundTransparency = 0.5
            u11.UIStroke.Transparency = 0.5
            u11.TextLabel.TextTransparency = 0.5
            u16.Visible = true
            u14.Visible = false
            u15.Visible = false
        end
    end)
    u15.SENSOR.MouseButton1Click:Connect(function() --[[Anonymous function at line 299]]
        --[[
        Upvalues:
            [1] = u63
            [2] = u16
            [3] = u14
            [4] = u15
            [5] = u18
        --]]
        local v66
        if u63.TradeCancel and (nil or 0.5) > tick() - u63.TradeCancel then
            v66 = true
        else
            u63.TradeCancel = tick()
            v66 = false
        end
        if not v66 then
            u16.Visible = true
            u14.Visible = false
            u15.Visible = false
            u18:RefreshUI()
        end
    end)
    u5.Main.Header.Exit.SENSOR.MouseButton1Click:Connect(function() --[[Anonymous function at line 305]]
        --[[
        Upvalues:
            [1] = u63
            [2] = u10
            [3] = u4
        --]]
        local v67
        if u63.ExitHeader and (nil or 0.5) > tick() - u63.ExitHeader then
            v67 = true
        else
            u63.ExitHeader = tick()
            v67 = false
        end
        if not v67 then
            u10:Close(u4)
        end
    end)
    u12.Holder.Header.EXIT_BUTTON.SENSOR.MouseButton1Click:Connect(function() --[[Anonymous function at line 310]]
        --[[
        Upvalues:
            [1] = u63
            [2] = u20
            [3] = u27
            [4] = u11
            [5] = u18
            [6] = u19
            [7] = u5
        --]]
        local v68
        if u63.ExitDetails and (nil or 0.5) > tick() - u63.ExitDetails then
            v68 = true
        else
            u63.ExitDetails = tick()
            v68 = false
        end
        if not v68 then
            u20 = nil
            u27(false)
            u11.BackgroundTransparency = 0.5
            u11.UIStroke.Transparency = 0.5
            u11.TextLabel.TextTransparency = 0.5
            u18:SetMode(u19.Pet)
            for _, v69 in u5.Main.Holder.ScrollingFrame.Content:GetChildren() do
                if v69:IsA("ImageButton") and v69:FindFirstChild("Display") then
                    v69.Display.BackgroundColor3 = Color3.fromRGB(94, 44, 18)
                    v69.Display.UIStroke.Color = Color3.fromRGB(103, 31, 0)
                    v69.Display.SELECTED_VIGNETTE.Visible = false
                end
            end
        end
    end)
    u5.Main.SideBar.ButtonList.PetEquipSlots.SENSOR.MouseButton1Click:Connect(function() --[[Anonymous function at line 327]]
        --[[
        Upvalues:
            [1] = u63
            [2] = u18
            [3] = u19
        --]]
        local v70
        if u63.PetButton and (nil or 0.5) > tick() - u63.PetButton then
            v70 = true
        else
            u63.PetButton = tick()
            v70 = false
        end
        if not v70 then
            u18:SetMode(u19.Pet)
        end
    end)
    u5.Main.SideBar.ButtonList.EggEquipSlots.SENSOR.MouseButton1Click:Connect(function() --[[Anonymous function at line 332]]
        --[[
        Upvalues:
            [1] = u63
            [2] = u18
            [3] = u19
        --]]
        local v71
        if u63.EggButton and (nil or 0.5) > tick() - u63.EggButton then
            v71 = true
        else
            u63.EggButton = tick()
            v71 = false
        end
        if not v71 then
            u18:SetMode(u19.Egg)
        end
    end)
    u5.Main.SideBar.ButtonList.EggInfo.SENSOR.MouseButton1Click:Connect(function() --[[Anonymous function at line 337]]
        --[[
        Upvalues:
            [1] = u63
            [2] = u18
            [3] = u19
        --]]
        local v72
        if u63.EggInfoButton and (nil or 0.5) > tick() - u63.EggInfoButton then
            v72 = true
        else
            u63.EggInfoButton = tick()
            v72 = false
        end
        if not v72 then
            u18:SetMode(u19.EggInfo)
        end
    end)
end
task.spawn(u18.Start, u18)
return u18