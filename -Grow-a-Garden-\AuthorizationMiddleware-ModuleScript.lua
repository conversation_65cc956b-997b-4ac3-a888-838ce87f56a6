-- Full Path: -Grow-a-Garden-\\AuthorizationMiddleware-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
local u2 = require(v1.Data.AuthorizedUsers)
return function(p3) --[[Anonymous function at line 12]]
    --[[
    Upvalues:
        [1] = u2
    --]]
    p3:RegisterHook("BeforeRun", function(p4) --[[Anonymous function at line 13]]
        --[[
        Upvalues:
            [1] = u2
        --]]
        local v5 = p4.Executor
        if not table.find(u2, v5.UserId) then
            return "You don\'t have permission to run this command please verify that your UserId is in \"ReplicatedStorage.Data.AuthorizedUsers\""
        end
    end)
end