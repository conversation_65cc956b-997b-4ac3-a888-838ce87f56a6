-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\Observers\observeProperty-ModuleScript.lua
return function(u1, u2, u3) --[[Function name: observeProperty, line 19]]
    local u4 = nil
    local u5 = nil
    local u6 = 0
    local function v10() --[[Anonymous function at line 25]]
        --[[
        Upvalues:
            [1] = u4
            [2] = u6
            [3] = u1
            [4] = u2
            [5] = u3
            [6] = u5
        --]]
        if u4 ~= nil then
            task.spawn(u4)
            u4 = nil
        end
        u6 = u6 + 1
        local u7 = u6
        local u8 = u1[u2]
        task.spawn(function() --[[Anonymous function at line 36]]
            --[[
            Upvalues:
                [1] = u3
                [2] = u8
                [3] = u7
                [4] = u6
                [5] = u5
                [6] = u4
            --]]
            local v9 = u3(u8)
            if u7 == u6 and u5.Connected then
                u4 = v9
            else
                task.spawn(v9)
            end
        end)
    end
    u5 = u1:GetPropertyChangedSignal(u2):Connect(v10)
    task.defer(function() --[[Anonymous function at line 50]]
        --[[
        Upvalues:
            [1] = u5
            [2] = u4
            [3] = u6
            [4] = u1
            [5] = u2
            [6] = u3
        --]]
        if u5.Connected then
            if u4 ~= nil then
                task.spawn(u4)
                u4 = nil
            end
            u6 = u6 + 1
            local u11 = u6
            local u12 = u1[u2]
            task.spawn(function() --[[Anonymous function at line 36]]
                --[[
                Upvalues:
                    [1] = u3
                    [2] = u12
                    [3] = u11
                    [4] = u6
                    [5] = u5
                    [6] = u4
                --]]
                local v13 = u3(u12)
                if u11 == u6 and u5.Connected then
                    u4 = v13
                else
                    task.spawn(v13)
                end
            end)
        end
    end)
    return function() --[[Anonymous function at line 58]]
        --[[
        Upvalues:
            [1] = u5
            [2] = u4
        --]]
        u5:Disconnect()
        if u4 ~= nil then
            task.spawn(u4)
            u4 = nil
        end
    end
end