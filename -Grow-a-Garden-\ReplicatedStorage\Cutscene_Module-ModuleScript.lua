-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Cutscene_Module-ModuleScript.lua
local v1 = {}
local u2 = game:GetService("TweenService")
function v1.Start_Cutscene(p3) --[[Anonymous function at line 5]]
    local v4 = game.Workspace.CurrentCamera
    local v5 = nil
    for _, v6 in pairs(game.Workspace.Farm:GetChildren()) do
        if v6.Important.Data.Owner == p3.Name then
            v5 = v6
        end
    end
    if v5 then
        v4.CameraSubject = v5.Cutscene_Model.Orbit_Camera
        v4.CameraType = Enum.CameraType.Fixed
    end
end
function v1.End_Cutscene(p7) --[[Anonymous function at line 19]]
    local v8 = game.Workspace.CurrentCamera
    v8.CameraSubject = p7.Character.Humanoid
    v8.CameraType = Enum.CameraType.Fixed
end
local u9 = require(game.ReplicatedStorage.Field_Of_View_Module)
function v1.Flash(p10, p11) --[[Anonymous function at line 25]]
    --[[
    Upvalues:
        [1] = u2
        [2] = u9
    --]]
    local u12 = TweenInfo.new(p11, Enum.EasingStyle.Cubic, Enum.EasingDirection.Out, 0, false, 0)
    local u13 = TweenInfo.new(p11 * 4, Enum.EasingStyle.Quad, Enum.EasingDirection.Out, 0, false, 0)
    local u14 = p10.PlayerGui.Rejoin_UI.Frame
    u2:Create(u14, u12, {
        ["BackgroundTransparency"] = 0
    }):Play()
    u9.Change_FOV(90, u12.Time)
    task.spawn(function() --[[Anonymous function at line 32]]
        --[[
        Upvalues:
            [1] = u12
            [2] = u9
            [3] = u13
            [4] = u2
            [5] = u14
        --]]
        task.wait(u12.Time + 1.5)
        u9.Change_FOV(70, u13.Time / 2)
        u2:Create(u14, u13, {
            ["BackgroundTransparency"] = 1
        }):Play()
    end)
end
return v1