-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\GetWorldToPosition-ModuleScript.lua
local u1 = workspace.CurrentCamera
workspace:GetPropertyChangedSignal("CurrentCamera"):Connect(function() --[[Anonymous function at line 3]]
    --[[
    Upvalues:
        [1] = u1
    --]]
    u1 = workspace.CurrentCamera
end)
return function(p2) --[[Function name: GetWorldToPosition, line 7]]
    --[[
    Upvalues:
        [1] = u1
    --]]
    if p2 then
        local v3, v4 = u1:WorldToScreenPoint(p2)
        return UDim2.fromOffset(v3.X, v3.Y), v4
    end
end