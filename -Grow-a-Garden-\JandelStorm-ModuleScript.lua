-- Full Path: -Grow-a-Garden-\\JandelStorm-ModuleScript.lua
local v1 = {}
local _ = game.ReplicatedStorage.RainParticle
Random.new()
local _ = workspace.CurrentCamera
local u2 = require(game.ReplicatedStorage.Code.CameraShaker)
local u3 = require(game.ReplicatedStorage.Code.LightningBolt)
local v4 = RaycastParams.new()
v4.FilterDescendantsInstances = { workspace.Terrain, workspace }
v4.FilterType = Enum.RaycastFilterType.Include
local u5 = false
local u7 = u2.new(Enum.RenderPriority.Camera.Value, function(p6) --[[Anonymous function at line 22]]
    workspace.CurrentCamera.CFrame = workspace.CurrentCamera.CFrame * p6
end)
u7:Start()
local u8 = script.Sky
local u9 = require(game.ReplicatedStorage.Modules.SkyboxManager)
u9.AddSkybox(u8)
local u10 = game.Lighting.ColorCorrection:Clone()
u10.Name = script.Name
u10.Parent = game.Lighting
local function u11() --[[Anonymous function at line 78]]
    --[[
    Upvalues:
        [1] = u5
        [2] = u7
        [3] = u2
        [4] = u10
        [5] = u9
        [6] = u8
    --]]
    u5 = true
    u7:Shake(u2.Presets.Explosion)
    game.TweenService:Create(u10, TweenInfo.new(0.3), {
        ["TintColor"] = Color3.fromRGB(190, 236, 255),
        ["Brightness"] = 0.5
    }):Play()
    game.TweenService:Create(game.Workspace.CurrentCamera, TweenInfo.new(0.5), {
        ["FieldOfView"] = 90
    }):Play()
    task.wait(0.3)
    game.TweenService:Create(game.Workspace.CurrentCamera, TweenInfo.new(3), {
        ["FieldOfView"] = 70
    }):Play()
    u9.UpdateSkybox(u8, 2)
    game.TweenService:Create(game.Lighting, TweenInfo.new(3), {}):Play()
    game.TweenService:Create(u10, TweenInfo.new(2), {
        ["Brightness"] = 0.1,
        ["TintColor"] = Color3.fromRGB(255, 238, 203)
    }):Play()
    game.Workspace.Terrain.Clouds.Enabled = true
    game.Workspace.Terrain.Clouds.Color = Color3.fromRGB(255, 241, 207)
    game.TweenService:Create(game.Workspace.Terrain.Clouds, TweenInfo.new(3), {
        ["Cover"] = 0.701,
        ["Density"] = 0
    }):Play()
    task.spawn(function() --[[Anonymous function at line 111]]
        --[[
        Upvalues:
            [1] = u9
            [2] = u8
            [3] = u10
        --]]
        repeat
            task.wait()
        until not workspace:GetAttribute("JanzenStorm")
        u9.UpdateSkybox(u8, 0)
        game.TweenService:Create(u10, TweenInfo.new(3), {
            ["Brightness"] = 0,
            ["TintColor"] = Color3.fromRGB(255, 255, 255)
        }):Play()
        game.TweenService:Create(game.Lighting, TweenInfo.new(3), {
            ["Ambient"] = game.Lighting:GetAttribute("DefaultAmbient"),
            ["ExposureCompensation"] = game.Lighting:GetAttribute("DefaultExposure"),
            ["Brightness"] = game.Lighting:GetAttribute("DefaultBrightness")
        }):Play()
        game.TweenService:Create(game.Workspace.Terrain.Clouds, TweenInfo.new(3), {
            ["Cover"] = 0,
            ["Density"] = 0
        }):Play()
        task.delay(3, function() --[[Anonymous function at line 132]]
            game.Workspace.Terrain.Clouds.Enabled = false
        end)
    end)
end
workspace:GetAttributeChangedSignal("JanzenStorm"):Connect(function() --[[Anonymous function at line 139]]
    --[[
    Upvalues:
        [1] = u11
        [2] = u5
    --]]
    if workspace:GetAttribute("JanzenStorm") then
        u11()
    else
        u5 = false
    end
end)
if workspace:GetAttribute("JanzenStorm") then
    task.defer(u11)
else
    u5 = false
end
local function u18(p12, p13) --[[Anonymous function at line 158]]
    --[[
    Upvalues:
        [1] = u3
    --]]
    local v14 = u3.new(p12, p13, 30)
    v14.Thickness = 1.5
    v14.Color = Color3.fromRGB(139, 205, 255)
    v14.Frequency = 15
    v14.MinThicknessMultiplier = 0.1
    v14.MaxThicknessMultiplier = 1
    task.wait(0.65)
    local v15 = game.ReplicatedStorage.LightningHit:Clone()
    v15.CFrame = CFrame.new(p13.WorldPosition)
    local v16 = workspace:Raycast(v15.CFrame.p, Vector3.new(-0, -50, -0))
    if v16 and v16.Position then
        v15.CFrame = CFrame.new(v16.Position)
    end
    v15.Parent = workspace.WeatherVisuals
    for _, v17 in pairs(v15:GetDescendants()) do
        if v17:IsA("ParticleEmitter") then
            v17:Emit(v17:GetAttribute("EmitCount"))
        elseif v17:IsA("Sound") then
            v17:Play()
        elseif v17:IsA("PointLight") then
            game.TweenService:Create(v17, TweenInfo.new(3), {
                ["Range"] = 0
            }):Play()
        end
    end
    v14:DestroyDissipate(0.7)
    game.Debris:AddItem(v15, 3)
end
local v19 = game:GetService("ReplicatedStorage"):WaitForChild("GameEvents")
v19.LightningBoltWithNoFlash.OnClientEvent:Connect(function(p20) --[[Anonymous function at line 213]]
    --[[
    Upvalues:
        [1] = u18
    --]]
    local u21 = Instance.new("Attachment")
    local u22 = Instance.new("Attachment")
    u21.Parent = workspace.Terrain
    u22.Parent = workspace.Terrain
    u22.WorldPosition = p20 + Vector3.new(0, 200, 0)
    u21.WorldPosition = p20
    u18(u22, u21)
    task.delay(5, function() --[[Anonymous function at line 225]]
        --[[
        Upvalues:
            [1] = u22
            [2] = u21
        --]]
        u22:Destroy()
        u21:Destroy()
    end)
end)
v19.JandelCharge.OnClientEvent:Connect(function(p23) --[[Anonymous function at line 235]]
    --[[
    Upvalues:
        [1] = u3
    --]]
    local v24 = p23["Right Arm"].Lightning
    local v25 = p23["Left Arm"].Lightning
    task.wait(0.5)
    local v26 = u3.new(v25, v24, 30)
    v26.Thickness = 30
    v26.Color = Color3.fromRGB(167, 229, 255)
    v26.Frequency = 15
    v26.MinThicknessMultiplier = 0.1
    v26.MaxThicknessMultiplier = 1
    task.wait(0.5)
    v26:DestroyDissipate(0.3)
end)
return v1