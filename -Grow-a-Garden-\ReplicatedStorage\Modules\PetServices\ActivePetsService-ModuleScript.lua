-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\PetServices\ActivePetsService-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
game:GetService("RunService")
local u2 = game:GetService("Players")
local v3 = game:GetService("UserInputService")
v1:WaitForChild("Assets"):WaitForChild("Models")
local u4 = workspace:WaitForChild("PetsPhysical")
local u5 = require(v1.Modules.TwoPointCast)
require(v1.Modules.ExponentialScaler)
local v6 = require(v1.Modules.ReplicationClass)
local v7 = require(v1.Data.PetRegistry)
local v8 = v1.Modules.PetServices.AnimationEvents
local u9 = require(v1.Modules.PetServices.PetUtilities)
local u10 = require(v1.Modules.PetServices.PetSoundHandler)
local u11 = RaycastParams.new()
u11.FilterDescendantsInstances = { workspace.Farm }
u11.FilterType = Enum.RaycastFilterType.Include
local u12 = require(v1.Modules.PetServices.PetVFXHandler)
local u13 = require(v1.Modules.SkinService)
require(v1.Modules.DumpTable)
require(v1.Modules.PlatformService)
local u14 = v3.TouchEnabled and "Mobile" or "PC"
local u15 = {}
for _, v16 in v8:GetDescendants() do
    if v16:IsA("ModuleScript") then
        u15[v16.Name] = require(v16)
    end
end
local u17 = v7.PetList
local u18 = v1:WaitForChild("GameEvents"):WaitForChild("ActivePetService")
local u19 = v6.new("ActivePetsService_Replicator")
u19:YieldUntilData()
local u20 = require(v1.Modules.GetFarm)
local u21 = {}
local u22 = {}
u21.ClientPetState = u22
local function u45(p23, p24, p25) --[[Anonymous function at line 107]]
    --[[
    Upvalues:
        [1] = u22
        [2] = u19
        [3] = u17
        [4] = u13
        [5] = u5
        [6] = u11
        [7] = u14
        [8] = u10
        [9] = u12
    --]]
    local v26 = u22[p23]
    if not v26 then
        v26 = {}
        u22[p23] = v26
    end
    local v27 = v26[p24]
    local v28 = u19:YieldUntilData().Table.PlayerPetData[p23].PetInventory.Data[p24].PetType
    local v29 = u17[v28]
    local v30
    if v29 then
        v30 = v29.Model
    else
        v30 = v29
    end
    local v31 = v27.Asset
    local v32 = v27.Model
    if v32 then
        v32:Destroy()
    end
    if v30 then
        v30 = v30:Clone()
    end
    local v33 = v29.Variant
    if v33 then
        u13:SetSkin(v30, v33)
    end
    local v34 = p25 * 1
    v30:ScaleTo((math.max(v34, 0.1)))
    local v35 = u5(v31.Position + Vector3.new(0, 2, 0), v31.Position - Vector3.new(0, 2, 0), u11)
    local v36 = v35 and v35.Position.Y or 0
    v30:PivotTo(v31.CFrame * CFrame.new(0, v36 + v30:GetExtentsSize().Y / 2, 0))
    v30:AddTag("PetModel")
    local v37
    if u14 == "PC" then
        v37 = u10.new(v28, v30, p24)
    else
        v37 = false
    end
    v27.PetSoundHandler = v37
    u12.new(v28, v30, p24)
    v27.PetVFXHandler = u12.new(v28, v30, p24)
    local v38 = v30.PrimaryPart
    local v39 = Instance.new("Weld")
    v39.Part0 = v38
    v39.Part1 = v31
    v39.Name = "Weld"
    v39.Parent = v31
    v39.Name = ("%*_%*_WELD"):format(v38.Name, v31.Name)
    v39.C1 = v39.C1 * v29.WeldOffset
    v30.Parent = v31
    v27.Model = v30
    local u40 = v30:FindFirstChild("AnimationController")
    if u40 then
        u40 = u40:FindFirstChild("Animator")
    end
    if u40 then
        local u41 = {}
        v27.LoadedAnimations = u41
        for u42, u43 in v29.Animations do
            task.spawn(function() --[[Anonymous function at line 160]]
                --[[
                Upvalues:
                    [1] = u40
                    [2] = u43
                    [3] = u41
                    [4] = u42
                --]]
                local v44 = u40:LoadAnimation(u43)
                v44.Looped = true
                u41[u42] = v44
                if u42 == "Idle" then
                    v44:Play()
                end
            end)
        end
    end
    return v30
end
local function u68(p46, p47) --[[Anonymous function at line 180]]
    --[[
    Upvalues:
        [1] = u22
        [2] = u2
        [3] = u20
        [4] = u19
        [5] = u17
        [6] = u5
        [7] = u11
        [8] = u4
        [9] = u45
    --]]
    local v48 = u22[p46]
    if not v48 then
        v48 = {}
        u22[p46] = v48
    end
    local v49 = v48[p47]
    local v50 = u2:FindFirstChild(p46)
    if v50 then
        local v51 = u20(v50)
        if not v49 then
            local v52 = Instance.new("Part")
            v52.Size = Vector3.new(2, 2, 2)
            v52.Transparency = 1
            v52.Material = Enum.Material.Neon
            v52.Color = Color3.fromRGB(15, 119, 255)
            v52.Anchored = true
            v52.CanQuery = false
            v52:AddTag("PetTargetable")
            v52:SetAttribute("UUID", p47)
            v52:SetAttribute("OWNER", (tostring(p46)))
            v52.Name = "PetMover"
            v52.CanCollide = false
            local v53 = u19:YieldUntilData().Table
            local v54 = v53.PlayerPetData
            local v55 = v53.ActivePetStates
            local v56 = v54[p46]
            if not v56 then
                return
            end
            local v57 = v56.PetInventory
            if not v57 then
                return
            end
            local v58 = v57.Data[p47]
            if not v58 then
                return
            end
            local v59 = v58.PetType
            local v60 = u17[v59]
            if v60 then
                local _ = v60.Model
            end
            local v61 = v58.PetData.BaseWeight
            if not v60 then
                warn(v59, "does not have foundpettypedata")
                return
            end
            local v62 = v61 + v60.ModelScalePerLevel * v58.PetData.Level
            local v63 = v51:FindFirstChild("PetArea")
            local v64 = v55[p46][p47]
            local v65 = u5(v52.Position + Vector3.new(0, 2, 0), v52.Position - Vector3.new(0, 2, 0), u11)
            if v65 then
                v65 = v65.Position.Y
            end
            local v66 = v65 or 0
            v52:PivotTo(v60.MovementType == "Grounded" and CFrame.new(v64.CurrentCFrame.X, v66, v64.CurrentCFrame.Z) or (v64.CurrentCFrame or v63.CFrame))
            v52.Parent = u4
            v48[p47] = {
                ["Asset"] = v52,
                ["LoadedAnimations"] = {}
            }
            local v67 = u45(p46, p47, v62)
            v67:PivotTo(v67:GetPivot() * CFrame.new(0, v66 + v67:GetExtentsSize().Y, 0))
        end
        return v49
    end
end
local function u75(p69, p70) --[[Anonymous function at line 255]]
    --[[
    Upvalues:
        [1] = u22
        [2] = u15
    --]]
    local v71 = u22[p69]
    if not v71 then
        v71 = {}
        u22[p69] = v71
    end
    local v72 = v71[p70]
    if v72 then
        if v72.PetSoundHandler then
            v72.PetSoundHandler:destroy()
        end
        v72.Asset:Destroy()
    end
    local v73 = v72.CurrentAnimation
    if v73 then
        local v74 = u15[v73.Name]
        if v74 then
            v74 = v74.AnimationEnded
        end
        if v74 then
            v74(p70)
        end
        v73:Stop()
    end
    v71[p70] = nil
end
local function u86() --[[Anonymous function at line 295]]
    --[[
    Upvalues:
        [1] = u19
        [2] = u22
        [3] = u75
        [4] = u68
    --]]
    local v76 = u19:YieldUntilData().Table.ActivePetStates
    for v77, v78 in u22 do
        local v79 = v76[v77]
        if v79 then
            for v80 in v78 do
                if not v79[v80] then
                    u75(v77, v80)
                end
            end
        else
            local v81 = u22[v77]
            if not v81 then
                v81 = {}
                u22[v77] = v81
            end
            if v81 then
                for v82 in v81 do
                    u75(v77, v82)
                end
            end
            u22[v77] = nil
        end
    end
    for v83, v84 in v76 do
        for v85 in v84 do
            u68(v83, v85)
        end
    end
end
local _ = workspace.Baseplate
local u87 = {}
local u88 = {}
local u89 = 0
local function u139(p90) --[[Anonymous function at line 338]]
    --[[
    Upvalues:
        [1] = u89
        [2] = u88
        [3] = u87
        [4] = u19
        [5] = u22
        [6] = u17
        [7] = u9
        [8] = u5
        [9] = u11
        [10] = u14
        [11] = u15
    --]]
    u89 = 0
    table.clear(u88)
    table.clear(u87)
    local v91 = u19:YieldUntilData().Table.ActivePetStates
    for v92, v93 in u22 do
        local v94 = v91[v92]
        if v94 then
            for v95, v96 in v93 do
                local v97 = v94[v95]
                if v97 then
                    local v98 = v96.Asset
                    local v99 = v97.CurrentCFrame
                    local v100 = (v99.Position - v98.Position).Magnitude
                    local v101 = v96.IsNotFirstTime
                    if v100 >= 3 or v101 then
                        v96.IsNotFirstTime = true
                        local v102 = v98:FindFirstChildWhichIsA("Model", true)
                        local v103 = u19:YieldUntilData().Table.PlayerPetData[v92]
                        if v103 then
                            local v104 = v103.PetInventory.Data[v95]
                            local v105
                            if v102 then
                                v105 = v102.PrimaryPart
                            else
                                v105 = v102
                            end
                            local v106
                            if v105 then
                                v106 = v105.Size
                            else
                                v106 = v105
                            end
                            if not v104 then
                                return
                            end
                            local v107 = u17[v104.PetType]
                            local v108 = v97.MovementSpeed
                            local v109 = v107.MovementType
                            local v110 = v104.PetData.BaseWeight
                            local v111 = v108 * p90 / v100
                            local v112 = math.clamp(v111, 0, 1)
                            local v113 = v98.Position:Lerp(v99.Position, v112)
                            local v114 = v96.LastScale
                            local v115 = u9:CalculateWeight(v104.PetData.BaseWeight or 1, v104.PetData.Level)
                            local v116 = v110 + v107.ModelScalePerLevel * v115
                            if v114 ~= v116 then
                                local v117 = v116 * 1
                                v102:ScaleTo((math.max(0.01, v117)))
                                v96.LastScale = v116
                            end
                            local v118 = u5(v98.Position + Vector3.new(0, 2, 0), v98.Position - Vector3.new(0, 2, 0), u11)
                            if v118 then
                                v118 = v118.Position.Y
                            end
                            local v119 = v118 or 0
                            local v120 = v96.LastYHeight or v119
                            local v121 = 5 * p90
                            local v122 = math.lerp(v120, v119, v121)
                            if not v96 then
                                return
                            end
                            v96.LastYHeight = v122
                            local v123 = (v107.YHeightScaler or 0) * v116
                            local v124
                            if v109 == "Grounded" then
                                v124 = CFrame.new(v113.X, (v122 or 0) + v123 + v106.Y / 2, v113.Z)
                            else
                                v124 = CFrame.new(v113)
                            end
                            local v125 = v105.Position
                            local v126 = v99.Position
                            local v127
                            if v109 == "Grounded" then
                                v127 = (Vector2.new(v125.X, v125.Z) - Vector2.new(v126.X, v126.Z)).Magnitude <= 2.5
                            else
                                v127 = false
                            end
                            local v128
                            if v109 == "Flight" then
                                v128 = (v125 - v126).Magnitude <= 1
                            else
                                v128 = false
                            end
                            local v129 = v97.CustomAnimationState or ((v128 or v127) and "Idle" or "Walk")
                            if not v96.LastAnimationState or v96.LastAnimationState ~= v129 then
                                if u14 == "PC" and v96.PetSoundHandler then
                                    v96.PetSoundHandler:playSound(v129)
                                end
                                v96.PetVFXHandler:playVFX(v129)
                                v96.LastAnimationState = v129
                            end
                            local v130 = v96.LoadedAnimations[v129]
                            local v131 = v96.CurrentAnimation
                            if v131 ~= v130 and v130 then
                                if v131 then
                                    local v132 = u15[v131.Name]
                                    if v132 then
                                        v132 = v132.AnimationEnded
                                    end
                                    if v132 then
                                        v132(v95)
                                    end
                                    v131:Stop()
                                end
                                v96.CurrentAnimation = v130
                                if v130 then
                                    local v133 = u15[v130.Name]
                                    if v133 then
                                        v133 = v133.AnimationStarted
                                    end
                                    if v133 then
                                        v133(v95)
                                    end
                                    v130:Play()
                                end
                            end
                            if v96.LastTargetCFrame == v99 and v96.LastAngles then
                                u89 = u89 + 1
                                u87[u89] = v98
                                u88[u89] = v124 * v96.LastAngles
                            else
                                local v134, v135, v136 = (v109 == "Grounded" and u9:GetLookAt(v98.Position, v99.Position) or CFrame.new(v98.Position, v99.Position)):ToEulerAnglesXYZ()
                                if v134 ~= v134 or (v135 ~= v135 or v136 ~= v136) then
                                    v134 = 0
                                    v135 = 0
                                    v136 = 0
                                end
                                local v137 = CFrame.Angles(v134, v135, v136)
                                local v138 = (v96.LastAngles or v137):Lerp(v137, 6 * p90)
                                u89 = u89 + 1
                                u87[u89] = v98
                                u88[u89] = v124 * v138
                                v96.LastAngles = v138
                            end
                        end
                    end
                end
            end
        end
    end
    workspace:BulkMoveTo(u87, u88, Enum.BulkMoveMode.FireCFrameChanged)
end
task.spawn(function() --[[Anonymous function at line 510]]
    --[[
    Upvalues:
        [1] = u14
        [2] = u139
    --]]
    while true do
        local u140 = task.wait(u14 == "Mobile" and 0.025 or 0.015)
        task.spawn(function() --[[Anonymous function at line 515]]
            --[[
            Upvalues:
                [1] = u139
                [2] = u140
            --]]
            u139(u140)
        end)
    end
end)
function u21.GetServerState(_, p141, p142) --[[Anonymous function at line 523]]
    --[[
    Upvalues:
        [1] = u19
    --]]
    local v143 = u19:YieldUntilData().Table.ActivePetStates[p141]
    if v143 then
        return v143[p142]
    end
end
function u21.GetPlayerDatastorePetData(_, p144) --[[Anonymous function at line 533]]
    --[[
    Upvalues:
        [1] = u19
    --]]
    local v145 = u19:YieldUntilData().Table.PlayerPetData[p144]
    if v145 then
        return v145
    end
end
function u21.GetPetData(_, p146, p147) --[[Anonymous function at line 541]]
    --[[
    Upvalues:
        [1] = u19
    --]]
    local v148 = u19:YieldUntilData().Table.PlayerPetData[p146]
    if v148 then
        return v148.PetInventory.Data[p147]
    end
end
function u21.GetPetDataFromPetObject(_, p149) --[[Anonymous function at line 553]]
    --[[
    Upvalues:
        [1] = u21
    --]]
    if p149 then
        return u21:GetPetData(p149:GetAttribute("OWNER"), (p149:GetAttribute("UUID")))
    end
end
function u21.SetPetState(_, p150, p151, p152) --[[Anonymous function at line 560]]
    --[[
    Upvalues:
        [1] = u18
    --]]
    u18:FireServer("SetPetState", p150, p151, p152)
end
function u21.GetClientPetState(_, p153) --[[Anonymous function at line 564]]
    --[[
    Upvalues:
        [1] = u22
    --]]
    local v154 = u22[p153]
    if not v154 then
        v154 = {}
        u22[p153] = v154
    end
    return v154
end
function u21.GetClientPetStateUUID(_, p155, p156) --[[Anonymous function at line 568]]
    --[[
    Upvalues:
        [1] = u21
    --]]
    local v157 = u21:GetClientPetState(p155)
    if Player then
        return v157[p156]
    end
end
function u21.Feed(_, p158) --[[Anonymous function at line 573]]
    --[[
    Upvalues:
        [1] = u18
    --]]
    u18:FireServer("Feed", p158)
end
task.delay(2, function() --[[Anonymous function at line 577]]
    --[[
    Upvalues:
        [1] = u86
        [2] = u19
    --]]
    u86()
    u19:GetPathSignal("ActivePetStates/@"):Connect(function(...) --[[Anonymous function at line 579]]
        --[[
        Upvalues:
            [1] = u86
        --]]
        u86()
    end)
end)
u86()
task.spawn(function() --[[Anonymous function at line 586]]
    --[[
    Upvalues:
        [1] = u86
    --]]
    while true do
        task.wait(2)
        u86()
    end
end)
return u21