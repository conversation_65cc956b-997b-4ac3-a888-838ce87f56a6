-- Full Path: -Grow-a-Garden-\\DisplayFavoriteIcon-LocalScript.lua
game:GetService("RunService")
local u1 = game:GetService("CollectionService")
local v2 = game:GetService("ReplicatedStorage")
local v3 = game:GetService("Players")
local u4 = require(v2.Modules.GetFarm)
local u5 = v3.LocalPlayer
local u6 = u5.Character or u5.CharacterAdded:Wait()
local u7 = script:WaitFor<PERSON>hild("LockBillboardGui")
local u8 = {}
local u9 = {}
local function u13(p10) --[[Anonymous function at line 28]]
    --[[
    Upvalues:
        [1] = u6
    --]]
    local v11 = u6:FindFirstChild("HumanoidRootPart")
    if v11 and p10:IsA("Model") then
        local v12 = p10.PrimaryPart or p10:FindFirstChildWhichIsA("BasePart")
        if v12 then
            return (v11.Position - v12.Position).Magnitude <= 30
        else
            return false
        end
    else
        return false
    end
end
local function u18(u14) --[[Anonymous function at line 38]]
    --[[
    Upvalues:
        [1] = u8
        [2] = u7
        [3] = u9
    --]]
    if u8[u14] then
        return
    else
        local v15 = u14.PrimaryPart or u14:FindFirstChildWhichIsA("BasePart")
        if v15 then
            local v16 = u7:Clone()
            v16.Adornee = v15
            v16.Parent = u14
            v16.Enabled = false
            u8[u14] = v16
            u9[u14] = false
            u14.AncestryChanged:Connect(function(_, p17) --[[Anonymous function at line 52]]
                --[[
                Upvalues:
                    [1] = u8
                    [2] = u14
                    [3] = u9
                --]]
                if not p17 and u8[u14] then
                    u8[u14]:Destroy()
                    u8[u14] = nil
                    u9[u14] = nil
                end
            end)
        end
    end
end
task.spawn(function() --[[Anonymous function at line 72]]
    --[[
    Upvalues:
        [1] = u5
        [2] = u4
        [3] = u1
        [4] = u8
        [5] = u9
        [6] = u18
        [7] = u13
    --]]
    while true do
        task.wait(0.5)
        local v19 = u5.Character
        if v19 then
            v19 = u5.Character:FindFirstChildOfClass("Tool")
        end
        if not (v19 and v19.Name:match("Favorite")) then
            v19 = nil
        end
        local v20 = u4(u5)
        if v20 then
            for _, v21 in v20:GetDescendants() do
                if v21:IsA("Model") and u1:HasTag(v21, "Harvestable") then
                    if v21:GetAttribute("Favorited") then
                        u18(v21)
                        local v22 = v19 or u13(v21)
                        local v23 = u8[v21]
                        if v23 and u9[v21] ~= v22 then
                            v23.Enabled = v22
                            u9[v21] = v22
                        end
                    else
                        local v24 = u8[v21]
                        if v24 and u9[v21] ~= false then
                            v24.Enabled = false
                            u9[v21] = false
                        end
                    end
                end
            end
        end
    end
end)