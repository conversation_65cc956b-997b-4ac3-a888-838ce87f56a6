-- Full Path: -Grow-a-Garden-\ReplicatedStorage\CmdrClient\Commands\edit-ModuleScript.lua
local u1 = game:GetService("Players")
local u2 = {
    ["AnchorPoint"] = Vector2.new(0.5, 0.5),
    ["BackgroundColor3"] = Color3.fromRGB(17, 17, 17),
    ["BackgroundTransparency"] = 0.05,
    ["BorderColor3"] = Color3.fromRGB(17, 17, 17),
    ["BorderSizePixel"] = 20,
    ["ClearTextOnFocus"] = false,
    ["MultiLine"] = true,
    ["Position"] = UDim2.new(0.5, 0, 0.5, 0),
    ["Size"] = UDim2.new(0.5, 0, 0.4, 0),
    ["Font"] = Enum.Font.Code,
    ["TextColor3"] = Color3.fromRGB(241, 241, 241),
    ["TextWrapped"] = true,
    ["TextSize"] = 18,
    ["TextXAlignment"] = "Left",
    ["TextYAlignment"] = "Top",
    ["AutoLocalize"] = false,
    ["PlaceholderText"] = "Right click to exit"
}
local u3 = nil
local v14 = {
    ["Name"] = "edit",
    ["Aliases"] = {},
    ["Description"] = "Edit text in a TextBox",
    ["Group"] = "DefaultUtil",
    ["Args"] = {
        {
            ["Type"] = "string",
            ["Name"] = "Input text",
            ["Description"] = "The text you wish to edit",
            ["Default"] = ""
        },
        {
            ["Type"] = "string",
            ["Name"] = "Delimiter",
            ["Description"] = "The character that separates each line",
            ["Default"] = ","
        }
    },
    ["ClientRun"] = function(p4, p5, u6) --[[Function name: ClientRun, line 45]]
        --[[
        Upvalues:
            [1] = u3
            [2] = u2
            [3] = u1
        --]]
        u3 = u3 or p4.Cmdr.Util.Mutex()
        local u7 = u3()
        p4:Reply("Right-click on the text area to exit.", Color3.fromRGB(158, 158, 158))
        local u8 = Instance.new("ScreenGui")
        u8.Name = "CmdrEditBox"
        u8.ResetOnSpawn = false
        local u9 = Instance.new("TextBox")
        for v10, v11 in pairs(u2) do
            u9[v10] = v11
        end
        u9.Text = p5:gsub(u6, "\n")
        u9.Parent = u8
        u8.Parent = u1.LocalPlayer:WaitForChild("PlayerGui")
        local u12 = coroutine.running()
        u9.InputBegan:Connect(function(p13) --[[Anonymous function at line 69]]
            --[[
            Upvalues:
                [1] = u12
                [2] = u9
                [3] = u6
                [4] = u8
                [5] = u7
            --]]
            if p13.UserInputType == Enum.UserInputType.MouseButton2 then
                coroutine.resume(u12, u9.Text:gsub("\n", u6))
                u8:Destroy()
                u7()
            end
        end)
        return coroutine.yield()
    end
}
return v14