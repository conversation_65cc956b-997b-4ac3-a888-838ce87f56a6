-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\EffectController-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
local u2 = script.Effects
local u3 = script.Libraries
local u4 = v1.GameEvents.EffectRequest
require(script.Types)
local u5 = require(v1.Modules.Trove)
local u6 = {}
return {
    ["Init"] = function(u7, _) --[[Function name: Init, line 25]]
        --[[
        Upvalues:
            [1] = u2
            [2] = u3
            [3] = u4
        --]]
        u7.Components = {}
        u7.Libraries = {}
        for _, u8 in u2:GetDescendants() do
            if u8.ClassName == "ModuleScript" then
                task.spawn(function() --[[Anonymous function at line 34]]
                    --[[
                    Upvalues:
                        [1] = u8
                        [2] = u7
                    --]]
                    local v9, v10 = pcall(require, u8)
                    if v9 then
                        u7.Components[u8.Name] = v10
                    end
                end)
            end
        end
        for _, u11 in u3:GetChildren() do
            if u11.ClassName == "ModuleScript" then
                task.spawn(function() --[[Anonymous function at line 48]]
                    --[[
                    Upvalues:
                        [1] = u11
                        [2] = u7
                    --]]
                    local v12, v13 = pcall(require, u11)
                    if v12 then
                        u7.Libraries[u11.Name] = v13
                    end
                end)
            end
        end
        u4.OnClientEvent:Connect(function(p14) --[[Anonymous function at line 57]]
            --[[
            Upvalues:
                [1] = u7
            --]]
            u7:Play(p14)
        end)
    end,
    ["SplitName"] = function(_, p15) --[[Function name: SplitName, line 62]]
        local v16 = p15:split("/")
        if v16 then
            return v16[1], v16[2]
        end
    end,
    ["FetchComponent"] = function(p17, p18) --[[Function name: FetchComponent, line 72]]
        if p17.Components[p18] then
            return p17.Components[p18]
        end
        warn((("Invalid Effect Name: %*, Component not Found."):format(p18)))
        return nil
    end,
    ["FetchCache"] = function(_, p19, p20) --[[Function name: FetchCache, line 82]]
        --[[
        Upvalues:
            [1] = u6
            [2] = u5
        --]]
        if not u6[p19] then
            u6[p19] = {}
        end
        if not u6[p19][p20] then
            u6[p19][p20] = {
                ["Cache"] = {},
                ["Container"] = u5.new()
            }
        end
        return u6[p19][p20]
    end,
    ["Play"] = function(p21, p22) --[[Function name: Play, line 97]]
        if p22.caster then
            local v23, v24 = p21:SplitName(p22.name_State)
            local v25 = p21:FetchComponent(v23)
            local v26
            if v25 then
                v26 = v25[v24]
            else
                v26 = v25
            end
            local v27
            if v25 then
                v27 = p21:FetchCache(p22.caster, v25)
            else
                v27 = v25
            end
            if v25 and (v26 and v27) then
                task.spawn(v26, {
                    ["Caster"] = p22.caster,
                    ["Parameters"] = p22.parameters,
                    ["Libraries"] = p21.Libraries,
                    ["Default"] = p21.Libraries.Default,
                    ["Cache"] = v27.Cache,
                    ["Container"] = v27.Container
                })
                return true
            else
                warn((("Component Found: %*, Callback Found: %*, Cache Found: %*."):format(v25, v26, v27)))
                return false
            end
        else
            warn(("No caster found in data table %*."):format(p22), p22)
            return
        end
    end,
    ["Request"] = function(p28, p29) --[[Function name: Request, line 131]]
        --[[
        Upvalues:
            [1] = u4
        --]]
        if p29.caster then
            if p28:Play(p29) then
                u4:FireServer(p29)
            end
        end
    end
}