-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\ByteNet\dataTypes\vec3-ModuleScript.lua
local v1 = require(script.Parent.Parent.process.bufferWriter)
require(script.Parent.Parent.types)
local u2 = v1.f32NoAlloc
local u3 = v1.alloc
local u12 = {
    ["read"] = function(p4, p5) --[[Function name: read, line 11]]
        local v6 = buffer.readf32(p4, p5)
        local v7 = p5 + 4
        local v8 = buffer.readf32(p4, v7)
        local v9 = p5 + 8
        local v10 = buffer.readf32(p4, v9)
        return Vector3.new(v6, v8, v10), 12
    end,
    ["write"] = function(p11) --[[Function name: write, line 15]]
        --[[
        Upvalues:
            [1] = u3
            [2] = u2
        --]]
        u3(12)
        u2(p11.X)
        u2(p11.Y)
        u2(p11.Z)
    end
}
return function() --[[Anonymous function at line 23]]
    --[[
    Upvalues:
        [1] = u12
    --]]
    return u12
end