-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Data\QuestData\QuestRewards\Seed Pack-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
game:GetService("ServerStorage")
require(v1.Data.QuestData.Types)
return {
    ["Type"] = "Seed Pack",
    ["Display"] = function(_, p2) --[[Function name: Display, line 13]]
        local v3 = p2.Data.Amount
        return ("+%* Seed Pack%*"):format(v3, v3 > 1 and "s" or "")
    end,
    ["Give"] = function(_, p4, p5) --[[Function name: Give, line 18]]
        local v6 = game:GetService("ServerScriptService")
        require(v6.Modules.SeedPackService):Give(p4, "Normal", p5.Data.Amount)
        return true
    end,
    ["Use"] = function(p7, p8) --[[Function name: Use, line 25]]
        return {
            ["Type"] = p7.Type,
            ["Data"] = p8
        }
    end
}