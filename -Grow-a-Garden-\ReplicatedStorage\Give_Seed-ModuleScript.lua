-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Give_Seed-ModuleScript.lua
local u1 = game:GetService("ServerScriptService")
local u2 = game:GetService("ReplicatedStorage")
local u3 = game:GetService("RunService"):IsServer()
local u4 = { "Normal", "Gold", "Rainbow" }
return {
    ["Give_Seed"] = function(p5, u6, p7, p8) --[[Function name: Give_Seed, line 11]]
        --[[
        Upvalues:
            [1] = u3
            [2] = u1
            [3] = u4
            [4] = u2
        --]]
        local v9 = u3
        assert(v9)
        local v10 = require(u1.Modules.InventoryService)
        local u11 = p8 == nil and "Normal" or p8
        if table.find(u4, u11) then
            if p7 == nil or type(p7) == "number" and (p7 > 0 and p7 == p7) then
                if u2.Seed_Models:FindFirstChild(u6) then
                    local v12 = p7 or 1
                    local v15 = v10:Find(p5, "Seed", function(p13) --[[Anonymous function at line 36]]
                        --[[
                        Upvalues:
                            [1] = u6
                            [2] = u11
                        --]]
                        local v14
                        if p13.ItemName == u6 then
                            v14 = p13.Variant == u11
                        else
                            v14 = false
                        end
                        return v14
                    end)
                    local _, v16 = next(v15)
                    if v16 then
                        local v17 = v16.ItemData
                        v17.Quantity = v17.Quantity + v12
                    else
                        v10:CreateItem(p5, "Seed", {
                            ["ItemName"] = u6,
                            ["Quantity"] = v12,
                            ["Variant"] = u11
                        })
                    end
                else
                    warn((("Seed \"%*\" does not exists\n%*"):format(u6, (debug.traceback()))))
                end
            else
                return
            end
        else
            warn((("Invalid variation given in Give_Seed, got \"%*\"\n%*"):format(u11, (debug.traceback()))))
            return
        end
    end
}