-- Full Path: -Grow-a-Garden-\\len-ModuleScript.lua
local v2 = {
    ["Name"] = "len",
    ["Aliases"] = {},
    ["Description"] = "Returns the length of a comma-separated list",
    ["Group"] = "DefaultUtil",
    ["Args"] = {
        {
            ["Type"] = "string",
            ["Name"] = "CSV",
            ["Description"] = "The comma-separated list"
        }
    },
    ["Run"] = function(_, p1) --[[Function name: Run, line 14]]
        return #p1:split(",")
    end
}
return v2