-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\ByteNet\replicated\replicatedValue-ModuleScript.lua
local u1 = game:GetService("HttpService")
local u2 = game:GetService("RunService"):IsServer() and "server" or "client"
local v3 = {}
local u4 = {
    ["__index"] = v3
}
function v3.write(p5, p6) --[[Anonymous function at line 16]]
    --[[
    Upvalues:
        [1] = u2
        [2] = u1
    --]]
    local v7 = u2 == "server"
    assert(v7, "cannot write to replicatdvalue on client")
    p5._luauData = p6
    p5._value.Value = u1:JSONEncode(p6)
end
function v3.read(p8) --[[Anonymous function at line 25]]
    return p8._luauData
end
return function(p9) --[[Anonymous function at line 29]]
    --[[
    Upvalues:
        [1] = u4
        [2] = u2
        [3] = u1
    --]]
    local v10 = u4
    local u11 = setmetatable({}, v10)
    u11._luauData = {}
    u11._value = p9
    if u2 == "client" then
        u11._luauData = table.freeze(u1:JSONDecode(p9.Value))
        p9.Changed:Connect(function(p12) --[[Anonymous function at line 40]]
            --[[
            Upvalues:
                [1] = u11
                [2] = u1
            --]]
            if p12 then
                u11._luauData = table.freeze(u1:JSONDecode(p12))
            end
        end)
    end
    return u11
end