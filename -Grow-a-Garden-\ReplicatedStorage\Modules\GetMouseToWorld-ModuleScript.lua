-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\GetMouseToWorld-ModuleScript.lua
local u1 = game:GetService("UserInputService")
local u2 = game:GetService("GuiService")
local u3 = workspace.CurrentCamera
workspace:GetPropertyChangedSignal("CurrentCamera"):Connect(function() --[[Anonymous function at line 5]]
    --[[
    Upvalues:
        [1] = u3
    --]]
    u3 = workspace.CurrentCamera
end)
return function(p4, p5) --[[Function name: GetMouseToWorld, line 9]]
    --[[
    Upvalues:
        [1] = u1
        [2] = u2
        [3] = u3
    --]]
    local v6 = u1:GetMouseLocation()
    local v7 = u2:GetGuiInset()
    local v8 = u3:ScreenPointToRay(v6.X + v7.X, v6.Y - v7.Y)
    return workspace:Raycast(v8.Origin, v8.Direction * (p5 or 1000), p4)
end