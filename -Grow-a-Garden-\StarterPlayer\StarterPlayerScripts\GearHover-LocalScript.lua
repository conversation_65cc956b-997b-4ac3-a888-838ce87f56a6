-- Full Path: -Grow-a-Garden-\StarterPlayer\StarterPlayerScripts\GearHover-LocalScript.lua
local v1 = game:GetService("Players")
local u2 = game:GetService("UserInputService")
local u3 = game:GetService("RunService")
game:GetService("ReplicatedStorage")
local v4 = v1.LocalPlayer
local v5 = v4:Wait<PERSON><PERSON><PERSON>hil<PERSON>("PlayerGui")
local u6 = v5:WaitF<PERSON><PERSON>hil<PERSON>("GearHover_UI")
local u7 = u6:WaitForChild("Frame")
local u8 = u7:WaitForChild("GearName")
local u9 = u7:WaitForChild("GearInfo")
local u10 = v5:WaitForChild("PlantHover_UI"):WaitForChild("Frame")
local u11 = script.Range
u6.Frame.Visible = false
local u12 = nil
local u13 = nil
local u14 = nil
local u15 = false
local u16 = 0
local u17 = {
    ["Basic Sprinkler"] = Vector3.new(20, 0.1, 20),
    ["Advanced Sprinkler"] = Vector3.new(25, 0.1, 25),
    ["Godly Sprinkler"] = Vector3.new(30, 0.1, 30),
    ["Master Sprinkler"] = Vector3.new(60, 0.1, 60),
    ["Lightning Rod"] = Vector3.new(70, 0.1, 70),
    ["Star Caller"] = Vector3.new(70, 0.1, 70),
    ["Night Staff"] = Vector3.new(0, 0, 0),
    ["Chocolate Sprinkler"] = Vector3.new(20, 0.1, 20)
}
local function u20(p18) --[[Anonymous function at line 50]]
    local v19 = p18:FindFirstAncestorOfClass("Model")
    while v19 do
        if v19:HasTag("Sprinkler") or (v19:HasTag("LightningRod") or (v19:HasTag("NightStaff") or v19:HasTag("StarCaller"))) then
            return v19
        end
        v19 = v19:FindFirstAncestorOfClass("Model")
    end
    return nil
end
local function v21() --[[Anonymous function at line 63]]
    --[[
    Upvalues:
        [1] = u13
        [2] = u12
        [3] = u14
    --]]
    if u13 then
        u13:Disconnect()
        u13 = nil
    end
    u12 = nil
    if u14 then
        u14:Destroy()
        u14 = nil
    end
end
local function u40(u22) --[[Anonymous function at line 76]]
    --[[
    Upvalues:
        [1] = u12
        [2] = u13
        [3] = u14
        [4] = u11
        [5] = u17
        [6] = u8
        [7] = u9
        [8] = u3
    --]]
    if u12 == u22 then
        return
    else
        if u13 then
            u13:Disconnect()
            u13 = nil
        end
        u12 = nil
        if u14 then
            u14:Destroy()
            u14 = nil
        end
        u12 = u22
        local v23 = u22.Name
        local v24 = nil
        if v23 == "ServerLightningRod" then
            return
        elseif u22:HasTag("Sprinkler") and "" or (u22:HasTag("LightningRod") and "" or (u22:HasTag("NightStaff") and "" or (u22:HasTag("StarCaller") and "" or v24))) then
            if u14 then
                u14:Destroy()
            end
            u14 = u11:Clone()
            u14.Parent = workspace.Visuals or workspace
            u14.Size = u17[u22.Name]
            if u22.PrimaryPart then
                local v25 = u22.PrimaryPart
                local v26 = v25.Position.Y - v25.Size.Y / 2
                local v27 = u14
                local v28 = CFrame.new
                local v29 = v25.Position.X
                local v30 = v26 + 0.05
                local v31 = v25.Position.Z
                v27.CFrame = v28((Vector3.new(v29, v30, v31)))
                u14.CanQuery = false
            end
            u8.Text = v23
            local function v39() --[[Anonymous function at line 117]]
                --[[
                Upvalues:
                    [1] = u22
                    [2] = u9
                --]]
                if u22:HasTag("Sprinkler") then
                    local v32 = u22:GetAttribute("Lifetime") or 0
                    local v33 = v32 / 60
                    local v34 = math.floor(v33)
                    local v35 = v32 % 60
                    u9.Text = string.format("%02d:%02d", v34, v35)
                    return
                elseif u22:HasTag("LightningRod") then
                    local v36 = u22:GetAttribute("Uses")
                    if v36 then
                        u9.Text = v36 == 1 and "1 Redirect Left" or string.format("%d Redirects Left", v36)
                    else
                        u9.Text = ""
                    end
                elseif u22:HasTag("NightStaff") then
                    local v37 = u22:GetAttribute("Uses")
                    if v37 then
                        u9.Text = v37 == 1 and "1 Channel Left" or string.format("%d Channels Left", v37)
                    else
                        u9.Text = ""
                    end
                else
                    if u22:HasTag("StarCaller") then
                        local v38 = u22:GetAttribute("Uses")
                        if v38 then
                            u9.Text = v38 == 1 and "1 Call Left" or string.format("%d Calls Left", v38)
                            return
                        end
                        u9.Text = ""
                    end
                    return
                end
            end
            v39()
            u13 = u3.Heartbeat:Connect(v39)
        end
    end
end
local u41 = v4:GetMouse()
local function u45() --[[Anonymous function at line 167]]
    --[[
    Upvalues:
        [1] = u10
        [2] = u7
        [3] = u13
        [4] = u12
        [5] = u14
        [6] = u41
        [7] = u20
        [8] = u40
        [9] = u2
        [10] = u6
    --]]
    if u10.Visible then
        u7.Visible = false
        if u13 then
            u13:Disconnect()
            u13 = nil
        end
        u12 = nil
        if u14 then
            u14:Destroy()
            u14 = nil
        end
        return
    else
        local v42 = u41.Target
        if v42 then
            local v43 = u20(v42)
            if v43 then
                u40(v43)
                u7.Visible = true
                local v44 = u2:GetMouseLocation()
                u6.Frame.Position = UDim2.new(0, v44.X + 20, 0, v44.Y + 10)
            else
                if u13 then
                    u13:Disconnect()
                    u13 = nil
                end
                u12 = nil
                if u14 then
                    u14:Destroy()
                    u14 = nil
                end
                u7.Visible = false
            end
        else
            if u13 then
                u13:Disconnect()
                u13 = nil
            end
            u12 = nil
            if u14 then
                u14:Destroy()
                u14 = nil
            end
            u7.Visible = false
            return
        end
    end
end
u2.TouchStarted:Connect(function(_, p46) --[[Anonymous function at line 194]]
    --[[
    Upvalues:
        [1] = u15
        [2] = u16
    --]]
    if not p46 then
        u15 = true
        u16 = tick()
    end
end)
u2.TouchEnded:Connect(function() --[[Anonymous function at line 200]]
    --[[
    Upvalues:
        [1] = u15
        [2] = u13
        [3] = u12
        [4] = u14
        [5] = u7
    --]]
    u15 = false
    if u13 then
        u13:Disconnect()
        u13 = nil
    end
    u12 = nil
    if u14 then
        u14:Destroy()
        u14 = nil
    end
    u7.Visible = false
end)
u2.InputChanged:Connect(function(p47) --[[Anonymous function at line 207]]
    --[[
    Upvalues:
        [1] = u45
    --]]
    if p47.UserInputType == Enum.UserInputType.MouseMovement then
        u45()
    end
end)
u3.Heartbeat:Connect(function() --[[Anonymous function at line 213]]
    --[[
    Upvalues:
        [1] = u7
        [2] = u2
        [3] = u6
        [4] = u15
        [5] = u16
        [6] = u45
    --]]
    if u7.Visible then
        local v48 = u2:GetMouseLocation()
        u6.Frame.Position = UDim2.new(0, v48.X + 20, 0, v48.Y + 10)
    end
    if u15 and tick() - u16 >= 0.5 then
        u45()
    end
    if u2.GamepadEnabled then
        u45()
    end
end)
v4.CharacterRemoving:Connect(v21)