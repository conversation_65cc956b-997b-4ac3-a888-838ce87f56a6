-- Full Path: -Grow-a-Garden-\ReplicatedStorage\CmdrClient\Types\URL-ModuleScript.lua
local u1 = require(script.Parent.Parent.Shared.Util)
local u4 = {
    ["Validate"] = function(p2) --[[Function name: Validate, line 4]]
        if p2:match("^https?://.+$") then
            return true
        else
            return false, "URLs must begin with http:// or https://"
        end
    end,
    ["Parse"] = function(p3) --[[Function name: Parse, line 12]]
        return p3
    end
}
return function(p5) --[[Anonymous function at line 17]]
    --[[
    Upvalues:
        [1] = u4
        [2] = u1
    --]]
    p5:RegisterType("url", u4)
    p5:RegisterType("urls", u1.MakeListableType(u4))
end