-- Full Path: -Grow-a-Garden-\\LocalScript2-LocalScript.lua
local u1 = nil
task.spawn(function() --[[Anonymous function at line 2]]
    --[[
    Upvalues:
        [1] = u1
    --]]
    while true do
        task.wait(1)
        xpcall(function() --[[Anonymous function at line 6]]
            return game:________SKIBIDI_TOILET()
        end, function() --[[Anonymous function at line 8]]
            --[[
            Upvalues:
                [1] = u1
            --]]
            local v2 = debug.info(2, "f")
            if not u1 then
                u1 = v2
            end
            if v2 == u1 then
                return
            else
                while true do

                end
            end
        end)
    end
end)