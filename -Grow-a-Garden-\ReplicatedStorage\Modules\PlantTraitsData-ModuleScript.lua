-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\PlantTraitsData-ModuleScript.lua
local u1 = {}
for v2, v3 in {
    ["Berry"] = {
        "Blueberry",
        "Cranberry",
        "Grape",
        "Raspberry",
        "Strawberry"
    },
    ["Candy"] = {
        "Blue Lollipop",
        "Candy Blossom",
        "Candy Sunflower",
        "Chocolate Carrot",
        "Easter Egg",
        "Red Lollipop"
    },
    ["Flower"] = {
        "Candy Blossom",
        "Candy Sunflower",
        "Cherry Blossom",
        "Crocus",
        "Daffodil",
        "Lotus",
        "Orange Tulip",
        "Pink Tulip"
    },
    ["Fruit"] = {
        "Apple",
        "Avocado",
        "Banana",
        "Blueberry",
        "Coconut",
        "Cranberry",
        "Dragon Fruit",
        "Durian",
        "Grape",
        "Lemon",
        "Mango",
        "Papaya",
        "Passionfruit",
        "Peach",
        "Pear",
        "Pineapple",
        "Raspberry",
        "Strawberry",
        "Watermelon"
    },
    ["<PERSON><PERSON>"] = {
        "<PERSON>",
        "Blueberry",
        "<PERSON>ran<PERSON>",
        "<PERSON>p<PERSON>",
        "<PERSON>rape",
        "Mango",
        "Peach",
        "Pineapple",
        "<PERSON>umpkin",
        "Raspberry",
        "Strawberry",
        "Tomato",
        "Watermelon",
        "Cacao",
        "Beanstalk"
    },
    ["Sour"] = { "Cranberry", "Lemon", "Passionfruit" },
    ["Sweet"] = {
        "Banana",
        "Blue Lollipop",
        "Blueberry",
        "Candy Blossom",
        "Candy Sunflower",
        "Chocolate Carrot",
        "Easter Egg",
        "Grape",
        "Mango",
        "Peach",
        "Pear",
        "Pineapple",
        "Raspberry",
        "Red Lollipop",
        "Strawberry",
        "Watermelon"
    },
    ["Tropical"] = {
        "Banana",
        "Coconut",
        "Dragon Fruit",
        "Durian",
        "Mango",
        "Papaya",
        "Passionfruit",
        "Pineapple",
        "Watermelon"
    },
    ["Vegetable"] = {
        "Carrot",
        "Chocolate Carrot",
        "Corn",
        "Eggplant",
        "Pepper",
        "Pumpkin",
        "Purple Cabbage",
        "Tomato",
        "Beanstalk"
    },
    ["Woody"] = {
        "Apple",
        "Avocado",
        "Cacao",
        "Coconut",
        "Durian",
        "Mango",
        "Papaya",
        "Peach",
        "Pear"
    },
    ["Prickly"] = {
        "Cactus",
        "Dragon Fruit",
        "Durian",
        "Pineapple",
        "Venus Fly Trap"
    },
    ["Fungus"] = { "Mega Mushroom", "Mushroom", "Glowshroom" },
    ["Night"] = {
        "Nightshade",
        "Glowshroom",
        "Mint",
        "Moonflower",
        "Starfruit",
        "Moonglow",
        "Moon Blossom",
        "Moon Mango",
        "Celestiberry"
    }
} do
    local v4 = {}
    for _, v5 in v3 do
        v4[v5] = true
    end
    u1[v2] = v4
end
function u1.HasTrait(p6, p7) --[[Anonymous function at line 156]]
    --[[
    Upvalues:
        [1] = u1
    --]]
    if p6 and p6 ~= "" then
        if p7 and p7 ~= "" then
            local v8 = u1[p7]
            return v8 and v8[p6] or false
        else
            warn("PlantTraitsData.HasTrait | No trait name given!")
            return false
        end
    else
        warn("PlantTraitsData.HasTrait | No plant name given!")
        return false
    end
end
return u1