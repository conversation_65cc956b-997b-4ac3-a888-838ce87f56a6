-- Full Path: -Grow-a-Garden-\\Remotes-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
local u2 = require(v1.Modules.ByteNet)
return {
    ["Crops"] = u2.defineNamespace("Crops", function() --[[Anonymous function at line 9]]
        --[[
        Upvalues:
            [1] = u2
        --]]
        local v3 = {
            ["Collect"] = u2.definePacket({
                ["value"] = u2.array(u2.inst)
            })
        }
        return v3
    end),
    ["DailyQuests"] = u2.defineNamespace("DailyQuests", function() --[[Anonymous function at line 17]]
        --[[
        Upvalues:
            [1] = u2
        --]]
        local v4 = {
            ["Claim"] = u2.definePacket({
                ["value"] = u2.nothing
            })
        }
        return v4
    end),
    ["SeedPack"] = u2.defineNamespace("SeedPack", function() --[[Anonymous function at line 25]]
        --[[
        Upvalues:
            [1] = u2
        --]]
        local v5 = {
            ["SpinFinished"] = u2.definePacket({
                ["value"] = u2.nothing
            }),
            ["Open"] = u2.definePacket({
                ["value"] = u2.string
            })
        }
        local v6 = u2.definePacket
        local v7 = {
            ["value"] = u2.struct({
                ["seedPackType"] = u2.string,
                ["resultIndex"] = u2.uint8
            })
        }
        v5.Result = v6(v7)
        return v5
    end),
    ["InfinitePack"] = u2.defineNamespace("InfinitePack", function() --[[Anonymous function at line 44]]
        --[[
        Upvalues:
            [1] = u2
        --]]
        local v8 = {
            ["Claim"] = u2.definePacket({
                ["value"] = u2.nothing
            })
        }
        return v8
    end),
    ["Tutorial"] = u2.defineNamespace("Tutorial", function() --[[Anonymous function at line 52]]
        --[[
        Upvalues:
            [1] = u2
        --]]
        local v9 = {
            ["Start"] = u2.definePacket({
                ["value"] = u2.optional(u2.string)
            })
        }
        return v9
    end),
    ["Market"] = u2.defineNamespace("Market", function() --[[Anonymous function at line 60]]
        --[[
        Upvalues:
            [1] = u2
        --]]
        local v10 = {
            ["FakePurchase"] = u2.definePacket({
                ["value"] = u2.float64
            })
        }
        local v11 = u2.definePacket
        local v12 = {
            ["value"] = u2.struct({
                ["type"] = u2.uint8,
                ["id"] = u2.float64
            })
        }
        v10.PromptPurchase = v11(v12)
        return v10
    end),
    ["Gift"] = u2.defineNamespace("Gift", function() --[[Anonymous function at line 75]]
        --[[
        Upvalues:
            [1] = u2
        --]]
        local v13 = {}
        local v14 = u2.definePacket
        local v15 = {
            ["value"] = u2.struct({
                ["productId"] = u2.float64,
                ["targetUserId"] = u2.float64
            })
        }
        v13.SendGiftTo = v14(v15)
        v13.GiftPrompted = u2.definePacket({
            ["value"] = u2.nothing
        })
        return v13
    end),
    ["Plant"] = u2.defineNamespace("Plant", function() --[[Anonymous function at line 90]]
        --[[
        Upvalues:
            [1] = u2
        --]]
        local v16 = {
            ["Update"] = u2.definePacket({
                ["value"] = u2.inst
            })
        }
        return v16
    end),
    ["Expansions"] = u2.defineNamespace("Expansions", function() --[[Anonymous function at line 98]]
        --[[
        Upvalues:
            [1] = u2
        --]]
        local v17 = {
            ["Expand"] = u2.definePacket({
                ["value"] = u2.uint8
            }),
            ["SkipTimer"] = u2.definePacket({
                ["value"] = u2.uint8
            })
        }
        return v17
    end)
}