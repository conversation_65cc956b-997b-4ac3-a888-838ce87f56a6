-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\FastTween-ModuleScript.lua
local u1 = game:GetService("TweenService")
return function(p2, p3, p4, p5) --[[Anonymous function at line 4]]
    --[[
    Upvalues:
        [1] = u1
    --]]
    local v6 = p5 ~= false
    local u7 = u1:Create(p2, p3, p4)
    u7.Completed:Once(function() --[[Anonymous function at line 8]]
        --[[
        Upvalues:
            [1] = u7
        --]]
        u7:Destroy()
    end)
    if v6 then
        u7:Play()
    end
    return u7
end