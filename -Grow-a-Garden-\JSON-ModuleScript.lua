-- Full Path: -Grow-a-Garden-\\JSON-ModuleScript.lua
local u1 = game:GetService("HttpService")
return function(p2) --[[Anonymous function at line 3]]
    --[[
    Upvalues:
        [1] = u1
    --]]
    p2:RegisterType("json", {
        ["Validate"] = function(p3) --[[Function name: Validate, line 5]]
            --[[
            Upvalues:
                [1] = u1
            --]]
            return pcall(u1.JSONDecode, u1, p3)
        end,
        ["Parse"] = function(p4) --[[Function name: Parse, line 9]]
            --[[
            Upvalues:
                [1] = u1
            --]]
            return u1:JSONDecode(p4)
        end
    })
end