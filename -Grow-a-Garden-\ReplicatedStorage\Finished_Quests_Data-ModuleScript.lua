-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Finished_Quests_Data-ModuleScript.lua
local v1 = {}
local v2 = game:GetService("DataStoreService")
local v3 = game.ServerStorage.Data_Set.Value
local u4 = v2:GetDataStore("C_" .. tostring(v3))
local u5 = {}
function UPD(p6)
    --[[
    Upvalues:
        [1] = u5
    --]]
    local v7 = nil
    for _, v8 in pairs(u5) do
        if v8[1] == p6.Name then
            v7 = v8[2]
        end
    end
    if v7 ~= nil then
        local v9 = p6.Finished_Quests
        for _, v10 in pairs(v7) do
            if v9:FindFirstChild(v10[1]) == nil then
                local v11 = script.String_Val:Clone()
                v11.Name = v10[1]
                v11.Value = v10[1]
                v11.Parent = v9
                local v12 = script.Number_Val:Clone()
                v12.Value = v10[2]
                v12.Name = "Completions"
                v12.Parent = v11
            end
        end
    end
end
function v1.Setup(p13) --[[Anonymous function at line 28]]
    --[[
    Upvalues:
        [1] = u4
        [2] = u5
    --]]
    local v14 = u4:GetAsync(p13.UserId)
    if v14 then
        local v15 = { p13.Name, v14 }
        local v16 = u5
        table.insert(v16, v15)
    else
        local v17 = {
            p13.Name,
            {}
        }
        local v18 = u5
        table.insert(v18, v17)
    end
    UPD(p13)
end
function v1.Save(p19) --[[Anonymous function at line 39]]
    --[[
    Upvalues:
        [1] = u5
        [2] = u4
    --]]
    local v20 = nil
    for _, v21 in pairs(u5) do
        if v21[1] == p19.Name then
            v20 = v21[2]
        end
    end
    if v20 ~= nil then
        u4:SetAsync(p19.UserId, v20)
    end
end
function v1.Finish_Quest(p22, p23) --[[Anonymous function at line 50]]
    --[[
    Upvalues:
        [1] = u5
    --]]
    for _, v24 in pairs(u5) do
        if v24[1] == p22.Name then
            local v25 = false
            for _, v26 in pairs(v24[2]) do
                if v26[1] == p23 then
                    v26[2] = v26[2] + 1
                    v25 = true
                end
            end
            if v25 == false then
                local v27 = v24[2]
                table.insert(v27, { p23, 1 })
            end
        end
    end
    for _, v28 in pairs(p22.PlayerGui.Quest_Frame.Side_Frame:GetChildren()) do
        if v28:IsA("CanvasGroup") and v28.Quest_Name.Value == p23 then
            v28:Destroy()
        end
    end
    UPD(p22)
end
function v1.Completitons(p29, p30) --[[Anonymous function at line 75]]
    --[[
    Upvalues:
        [1] = u5
    --]]
    local v31 = 0
    for _, v32 in pairs(u5) do
        if v32[1] == p29.Name then
            for _, v33 in pairs(v32[2]) do
                if v33[1] == p30 then
                    v31 = v33[2]
                end
            end
        end
    end
    return v31
end
return v1