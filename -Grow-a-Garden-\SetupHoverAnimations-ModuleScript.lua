-- Full Path: -Grow-a-Garden-\\SetupHoverAnimations-ModuleScript.lua
local u1 = game:GetService("TweenService")
return function(p2, p3) --[[Function name: SetupHoverAnimations, line 4]]
    --[[
    Upvalues:
        [1] = u1
    --]]
    local v4 = p2:Find<PERSON>irstChild("SENSOR", true)
    local v5 = p2:FindFirstChild("UIScale", true)
    local v6 = p3 or 0.05
    local v7 = v5.Scale
    local v8 = v7 * (1 + v6)
    local v9 = v7 * (1 - v6)
    u1:Create(v5, TweenInfo.new(0.25), {
        ["Scale"] = v8
    })
    u1:Create(v5, TweenInfo.new(0.25), {
        ["Scale"] = v9
    })
    u1:Create(v5, TweenInfo.new(0.25), {
        ["Scale"] = v7
    })
    return v4
end