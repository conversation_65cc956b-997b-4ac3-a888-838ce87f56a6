-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\CosmeticServices\UserInterface\CosmeticActionUserInterfaceService\Actions\Move-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
local u2 = require(v1.Modules.CosmeticServices.UserInterface.CosmeticMovementUserInterfaceService)
require(v1.Modules.GetMouseToWorld)
local u3 = require(v1.Modules.CosmeticServices.ModelMovement.CosmeticMovementService)
local u4 = require(v1.Modules.PlaySound)
return function(u5) --[[Function name: Loader, line 9]]
    --[[
    Upvalues:
        [1] = u2
        [2] = u3
        [3] = u4
    --]]
    return {
        ["InputBegan"] = function() --[[Anonymous function at line 11]]
            --[[
            Upvalues:
                [1] = u2
                [2] = u5
                [3] = u3
            --]]
            u2:Toggle(true)
            u5:Toggle(false)
            u3:SetTarget(u5.Target)
        end,
        ["InputEnded"] = function() --[[Anonymous function at line 16]]
            --[[
            Upvalues:
                [1] = u2
                [2] = u5
                [3] = u3
                [4] = u4
            --]]
            u2:Toggle(false)
            u5:Toggle(true)
            u3:SetTarget(nil)
            u4("rbxassetid://99990810464653").Volume = 0.5
        end
    }
end