-- Full Path: -Grow-a-Garden-\\PetEggs-ModuleScript.lua
local v1 = {}
local v2 = {
    ["Color"] = Color3.fromRGB(163, 120, 45),
    ["HatchTime"] = 14400
}
local v3 = {}
local v4 = {}
local v5 = {
    ["ItemOdd"] = 20,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v4["Silver Monkey"] = v5
local v6 = {
    ["ItemOdd"] = 20,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v4.Cow = v6
local v7 = {
    ["ItemOdd"] = 5,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v4["Sea Otter"] = v7
local v8 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v4.Turtle = v8
local v9 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v4["Polar Bear"] = v9
v3.Items = v4
v2.RarityData = v3
v1["Legendary Egg"] = v2
local v10 = {
    ["Color"] = Color3.fromRGB(33, 84, 185),
    ["HatchTime"] = 7200
}
local v11 = {}
local v12 = {}
local v13 = {
    ["ItemOdd"] = 20,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v12["Orange Tabby"] = v13
local v14 = {
    ["ItemOdd"] = 5,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v12.Monkey = v14
local v15 = {
    ["ItemOdd"] = 15,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v12["Spotted Deer"] = v15
local v16 = {
    ["ItemOdd"] = 10,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v12.Rooster = v16
local v17 = {
    ["ItemOdd"] = 10,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v12.Pig = v17
v11.Items = v12
v10.RarityData = v11
v1["Rare Egg"] = v10
local v18 = {
    ["Color"] = Color3.fromRGB(211, 167, 129),
    ["HatchTime"] = 1200
}
local v19 = {}
local v20 = {}
local v21 = {
    ["ItemOdd"] = 5,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v20["Black Bunny"] = v21
local v22 = {
    ["ItemOdd"] = 5,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v20.Cat = v22
local v23 = {
    ["ItemOdd"] = 5,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v20.Deer = v23
local v24 = {
    ["ItemOdd"] = 5,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v20.Chicken = v24
v19.Items = v20
v18.RarityData = v19
v1["Uncommon Egg"] = v18
local v25 = {
    ["Color"] = Color3.fromRGB(255, 255, 255),
    ["HatchTime"] = 600
}
local v26 = {}
local v27 = {}
local v28 = {
    ["ItemOdd"] = 10,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v27.Dog = v28
local v29 = {
    ["ItemOdd"] = 10,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v27["Golden Lab"] = v29
local v30 = {
    ["ItemOdd"] = 10,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v27.Bunny = v30
v26.Items = v27
v25.RarityData = v26
v1["Common Egg"] = v25
local v31 = {
    ["Color"] = Color3.fromRGB(255, 204, 0),
    ["HatchTime"] = 18400
}
local v32 = {}
local v33 = {}
local v34 = {
    ["ItemOdd"] = 20,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v33["Grey Mouse"] = v34
local v35 = {
    ["ItemOdd"] = 15,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v33.Squirrel = v35
local v36 = {
    ["ItemOdd"] = 15,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v33["Brown Mouse"] = v36
local v37 = {
    ["ItemOdd"] = 5,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v33["Red Giant Ant"] = v37
local v38 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v33["Red Fox"] = v38
v32.Items = v33
v31.RarityData = v32
v1["Mythical Egg"] = v31
local v39 = {
    ["Color"] = Color3.fromRGB(213, 255, 134),
    ["HatchTime"] = 28800
}
local v40 = {}
local v41 = {}
local v42 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v41.Dragonfly = v42
local v43 = {
    ["ItemOdd"] = 4,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v41["Praying Mantis"] = v43
local v44 = {
    ["ItemOdd"] = 25,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v41.Caterpillar = v44
local v45 = {
    ["ItemOdd"] = 30,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v41["Giant Ant"] = v45
local v46 = {
    ["ItemOdd"] = 40,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v41.Snail = v46
v40.Items = v41
v39.RarityData = v40
v1["Bug Egg"] = v39
local v47 = {
    ["Color"] = Color3.fromRGB(213, 255, 134),
    ["HatchTime"] = 30
}
local v48 = {}
local v49 = {}
local v50 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v49.Dragonfly = v50
local v51 = {
    ["ItemOdd"] = 4,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v49["Praying Mantis"] = v51
local v52 = {
    ["ItemOdd"] = 25,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v49.Caterpillar = v52
local v53 = {
    ["ItemOdd"] = 30,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v49["Giant Ant"] = v53
local v54 = {
    ["ItemOdd"] = 40,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v49.Snail = v54
v48.Items = v49
v47.RarityData = v48
v1["Exotic Bug Egg"] = v47
local v55 = {
    ["Color"] = Color3.fromRGB(170, 170, 255),
    ["HatchTime"] = 15000
}
local v56 = {}
local v57 = {}
local v58 = {
    ["ItemOdd"] = 0.1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v57.Raccoon = v58
local v59 = {
    ["ItemOdd"] = 3,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v57["Night Owl"] = v59
local v60 = {
    ["ItemOdd"] = 7,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v57["Echo Frog"] = v60
local v61 = {
    ["ItemOdd"] = 15,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v57.Frog = v61
local v62 = {
    ["ItemOdd"] = 20,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v57.Mole = v62
local v63 = {
    ["ItemOdd"] = 40,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v57.Hedgehog = v63
v56.Items = v57
v55.RarityData = v56
v1["Night Egg"] = v55
local v64 = {
    ["Color"] = Color3.fromRGB(170, 170, 255),
    ["HatchTime"] = 30
}
local v65 = {}
local v66 = {}
local v67 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v66.Raccoon = v67
local v68 = {
    ["ItemOdd"] = 3,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v66["Night Owl"] = v68
local v69 = {
    ["ItemOdd"] = 7,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v66["Echo Frog"] = v69
local v70 = {
    ["ItemOdd"] = 10,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v66.Frog = v70
local v71 = {
    ["ItemOdd"] = 16,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v66.Mole = v71
local v72 = {
    ["ItemOdd"] = 35,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v66.Hedgehog = v72
v65.Items = v66
v64.RarityData = v65
v1["Premium Night Egg"] = v64
local v73 = {
    ["Color"] = Color3.fromRGB(255, 255, 255),
    ["HatchTime"] = 300000000
}
local v74 = {}
local v75 = {}
local v76 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v75["Silver Monkey"] = v76
local v77 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v75.Kiwi = v77
local v78 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v75.Cow = v78
local v79 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v75["Sea Otter"] = v79
local v80 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v75.Turtle = v80
local v81 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v75["Polar Bear"] = v81
local v82 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v75["Orange Tabby"] = v82
local v83 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v75["Moon Cat"] = v83
local v84 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v75.Monkey = v84
local v85 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v75["Spotted Deer"] = v85
local v86 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v75.Rooster = v86
local v87 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v75.Pig = v87
local v88 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v75["Black Bunny"] = v88
local v89 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v75.Cat = v89
local v90 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v75.Deer = v90
local v91 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v75.Chicken = v91
local v92 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v75.Dog = v92
local v93 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v75["Golden Lab"] = v93
local v94 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v75.Bunny = v94
local v95 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v75.Dragonfly = v95
local v96 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v75["Praying Mantis"] = v96
local v97 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v75.Caterpillar = v97
local v98 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v75["Giant Ant"] = v98
local v99 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v75.Snail = v99
local v100 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v75.Raccoon = v100
local v101 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v75.Owl = v101
local v102 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v75["Night Owl"] = v102
local v103 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v75["Echo Frog"] = v103
local v104 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v75.Frog = v104
local v105 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v75.Mole = v105
local v106 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v75.Hedgehog = v106
local v107 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v75["Blood Hedgehog"] = v107
local v108 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v75["Blood Kiwi"] = v108
local v109 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v75["Blood Owl"] = v109
local v110 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v75["Chicken Zombie"] = v110
local v111 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v75["Grey Mouse"] = v111
local v112 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v75.Squirrel = v112
local v113 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v75["Brown Mouse"] = v113
local v114 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v75["Red Giant Ant"] = v114
local v115 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v75["Red Fox"] = v115
local v116 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v75["Red Dragon"] = v116
local v117 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v75.Firefly = v117
v74.Items = v75
v73.RarityData = v74
v1["Fake Egg"] = v73
return v1