-- Full Path: -Grow-a-Garden-\\MonsterMash-ModuleScript.lua
local v1 = {}
local _ = game.ReplicatedStorage.RainParticle
Random.new()
local u2 = workspace.CurrentCamera
require(game.ReplicatedStorage.Code.CameraShaker)
require(game.ReplicatedStorage.Code.LightningBolt)
local u3 = require(game.ReplicatedStorage.Modules.NumberUtil)
local v4 = RaycastParams.new()
v4.FilterDescendantsInstances = { workspace.Terrain, workspace }
v4.FilterType = Enum.RaycastFilterType.Include
local u5 = false
local u6 = script.Sky
local u7 = require(game.ReplicatedStorage.Modules.SkyboxManager)
u7.AddSkybox(u6)
local u8 = game.Lighting.ColorCorrection:Clone()
u8.Name = script.Name
u8.Parent = game.Lighting
local u9 = script.SFOTH
u7.AddSkybox(u9)
local function u18(p10) --[[Anonymous function at line 36]]
    local v11 = game.ReplicatedStorage.ShootingStar:Clone()
    v11.CFrame = p10
    v11.Parent = workspace
    local v12 = script.Fire:Clone()
    v12.Parent = workspace
    v12:Play()
    game.Debris:AddItem(v12, 5)
    local v13 = 0
    while v13 < 1 do
        v13 = v13 + game:GetService("RunService").Heartbeat:Wait()
        v11:PivotTo(p10:Lerp(p10 * CFrame.new(0, 200, 0), v13))
    end
    for _, v14 in v11:GetDescendants() do
        if v14:IsA("BillboardGui") then
            v14.Enabled = false
        end
    end
    game.Debris:AddItem(v11, 4)
    local v15 = game.ReplicatedStorage.FireworkBoom:Clone()
    v15.Parent = workspace
    v15.CFrame = v11.CFrame
    for _, v16 in v15:GetDescendants() do
        if v16:IsA("ParticleEmitter") then
            v16:Emit(v16:GetAttribute("EmitCount"))
        end
    end
    local v17 = script.Boom:Clone()
    v17.Parent = workspace
    v17:Play()
    game.Debris:AddItem(v17, 5)
    game.Debris:AddItem(v15, 10)
end
local function u68() --[[Anonymous function at line 83]]
    --[[
    Upvalues:
        [1] = u5
        [2] = u8
        [3] = u7
        [4] = u6
        [5] = u2
        [6] = u18
        [7] = u9
        [8] = u3
    --]]
    u5 = true
    game.TweenService:Create(u8, TweenInfo.new(2), {
        ["Brightness"] = 0.1
    }):Play()
    game.TweenService:Create(game.Lighting, TweenInfo.new(2), {
        ["Ambient"] = Color3.fromRGB(60, 162, 230),
        ["Brightness"] = 1
    }):Play()
    game.TweenService:Create(game.Lighting.Atmosphere, TweenInfo.new(2), {
        ["Density"] = 0.436,
        ["Offset"] = 1,
        ["Color"] = Color3.fromRGB(215, 244, 255),
        ["Decay"] = Color3.fromRGB(190, 238, 255)
    }):Play()
    u7.UpdateSkybox(u6, 2)
    local u19 = workspace.Visuals:WaitForChild("DJJhai")
    local u20 = u19:GetPivot()
    local u21 = u20 * CFrame.new(0, 85, 0)
    local v22 = u19:WaitForChild("jhailatte"):WaitForChild("Humanoid")
    local u23 = v22.Animator:LoadAnimation(script.Mash)
    u23.Priority = Enum.AnimationPriority.Action4
    local u24 = v22.Animator:LoadAnimation(script.Animation)
    u24:Play()
    local u25 = game.ReplicatedStorage.Assets.DanceFloor:Clone()
    local u26 = u25:GetPivot()
    u25.Parent = workspace
    local function u28() --[[Anonymous function at line 133]]
        --[[
        Upvalues:
            [1] = u25
        --]]
        for _, v27 in u25.Colour:GetChildren() do
            v27.Color = Color3.fromHSV(Random.new():NextNumber(0, 1), Random.new():NextNumber(0, 1), 1)
        end
    end
    task.spawn(function() --[[Anonymous function at line 139]]
        --[[
        Upvalues:
            [1] = u28
        --]]
        while workspace:GetAttribute("MonsterMash") do
            u28()
            task.wait(1)
        end
    end)
    local v29 = 0
    while v29 < 2 do
        v29 = v29 + game:GetService("RunService").Heartbeat:Wait()
        local v30 = game.TweenService:GetValue(v29, Enum.EasingStyle.Quad, Enum.EasingDirection.InOut)
        u19:PivotTo(u20:Lerp(u21, v30))
        u25:PivotTo(u26:Lerp(u26 * CFrame.new(0, 2, 0), v30))
    end
    workspace:GetAttribute("MonsterMashTimer")
    local u31 = Instance.new("Sound")
    u31.Volume = 0.6
    u31.Parent = workspace
    u31.SoundId = "rbxassetid://79451196298919"
    u31.Looped = true
    u31:Play()
    repeat
        task.wait()
    until workspace:GetAttribute("MonsterMashTimer") or not workspace:GetAttribute("MonsterMash")
    u31.Looped = false
    local u32 = v22.Animator:LoadAnimation(script.Up)
    local u33 = 0
    local u34 = false
    local u35 = false
    task.spawn(function() --[[Anonymous function at line 191]]
        --[[
        Upvalues:
            [1] = u5
            [2] = u19
            [3] = u31
            [4] = u35
            [5] = u34
            [6] = u2
            [7] = u33
            [8] = u24
            [9] = u23
            [10] = u32
            [11] = u8
            [12] = u18
        --]]
        local v36 = nil
        while u5 do
            task.wait(0)
            if u19:IsDescendantOf(workspace) and u19:FindFirstChild("DJBooth") then
                u19.DJBooth.Light.Attachment.PointLight.Color = Color3.fromHSV(tick() / 3 % 1, 1, 1)
            end
            local v37 = (u31.PlaybackLoudness - 200) / 700
            local v38 = math.clamp(v37, 0, 1)
            if u35 == true then
                local v39 = u31.PlaybackLoudness / 700
                v38 = math.clamp(v39, 0, 1)
            end
            if v36 and (u31.PlaybackLoudness / 1000 - v36 > 0.2 and not u34) then
                for _, v40 in u19.jhailatte.HumanoidRootPart.Attachment:GetChildren() do
                    v40:Emit(v40:GetAttribute("EmitCount"))
                end
                local _, v41 = u2:WorldToScreenPoint(u19:GetPivot().p)
                if v41 then
                    game.TweenService:Create(workspace.CurrentCamera, TweenInfo.new(0.2), {
                        ["FieldOfView"] = 68
                    }):Play()
                    task.delay(0.2, function() --[[Anonymous function at line 219]]
                        game.TweenService:Create(workspace.CurrentCamera, TweenInfo.new(0.3), {
                            ["FieldOfView"] = 70
                        }):Play()
                    end)
                end
            end
            v36 = u31.PlaybackLoudness / 1000
            if v38 > 0.45 then
                local u42 = tick()
                u33 = u42
                if not u34 then
                    u34 = true
                    if u35 then
                        u24:Stop()
                        u23:Play(0, 10)
                    else
                        u32:Play()
                    end
                    game.TweenService:Create(u8, TweenInfo.new(0.3), {
                        ["TintColor"] = Color3.fromRGB(226, 192, 255),
                        ["Brightness"] = 0.5
                    }):Play()
                    game.TweenService:Create(game.Workspace.CurrentCamera, TweenInfo.new(0.5), {
                        ["FieldOfView"] = 90
                    }):Play()
                    for _, u43 in u19.Pyro:GetDescendants() do
                        if u43:IsA("ParticleEmitter") then
                            u43.Enabled = true
                            task.delay(1.5, function() --[[Anonymous function at line 254]]
                                --[[
                                Upvalues:
                                    [1] = u43
                                --]]
                                u43.Enabled = false
                            end)
                        end
                    end
                    for _, v44 in u19.Confetti:GetDescendants() do
                        v44:Emit(v44:GetAttribute("EmitCount"))
                    end
                    task.spawn(function() --[[Anonymous function at line 264]]
                        --[[
                        Upvalues:
                            [1] = u19
                            [2] = u18
                        --]]
                        for _, u45 in u19.SpawnPoints:GetChildren() do
                            task.spawn(function() --[[Anonymous function at line 266]]
                                --[[
                                Upvalues:
                                    [1] = u18
                                    [2] = u45
                                --]]
                                u18(u45.CFrame)
                            end)
                            task.wait(0.1)
                        end
                    end)
                    task.delay(3, function() --[[Anonymous function at line 273]]
                        --[[
                        Upvalues:
                            [1] = u8
                        --]]
                        game.TweenService:Create(u8, TweenInfo.new(3), {
                            ["TintColor"] = Color3.fromRGB(255, 255, 255),
                            ["Brightness"] = 0.1
                        }):Play()
                    end)
                    task.spawn(function() --[[Anonymous function at line 277]]
                        --[[
                        Upvalues:
                            [1] = u34
                            [2] = u19
                            [3] = u2
                        --]]
                        while u34 and (u19 and u19:IsDescendantOf(workspace)) do
                            for _, v46 in u19.jhailatte.HumanoidRootPart.Attachment:GetChildren() do
                                v46:Emit(v46:GetAttribute("EmitCount"))
                            end
                            local _, v47 = u2:WorldToScreenPoint(u19:GetPivot().p)
                            if v47 then
                                game.TweenService:Create(workspace.CurrentCamera, TweenInfo.new(0.2), {
                                    ["FieldOfView"] = 68
                                }):Play()
                                task.delay(0.2, function() --[[Anonymous function at line 287]]
                                    game.TweenService:Create(workspace.CurrentCamera, TweenInfo.new(0.3), {
                                        ["FieldOfView"] = 70
                                    }):Play()
                                end)
                            end
                            task.wait(0.5)
                        end
                    end)
                end
                task.delay(1, function() --[[Anonymous function at line 297]]
                    --[[
                    Upvalues:
                        [1] = u33
                        [2] = u42
                        [3] = u35
                        [4] = u34
                        [5] = u32
                        [6] = u23
                        [7] = u24
                    --]]
                    if u33 == u42 then
                        if u35 then
                            repeat
                                task.wait()
                            until u35 == false
                        end
                        u34 = false
                        u32:Stop()
                        u23:Stop()
                        u24:Play()
                    end
                end)
            end
            for _, v48 in u19.DJBooth.Speakers:GetChildren() do
                if v48:IsA("BasePart") then
                    local v49 = v48.Mesh
                    local v50 = v38 * -1.4
                    v49.Offset = Vector3.new(0, v50, 0)
                    v48.Mesh.Scale = Vector3.new(1, 1, 1) * (v38 * 0.4 + 1)
                end
            end
        end
    end)
    task.spawn(function() --[[Anonymous function at line 323]]
        --[[
        Upvalues:
            [1] = u31
            [2] = u19
            [3] = u21
            [4] = u20
            [5] = u25
            [6] = u26
            [7] = u32
            [8] = u24
            [9] = u7
            [10] = u6
            [11] = u9
            [12] = u8
        --]]
        game.TweenService:Create(game.SoundService.Music.Tunes, TweenInfo.new(2), {
            ["Volume"] = 0
        }):Play()
        repeat
            task.wait()
        until not workspace:GetAttribute("MonsterMash")
        game.TweenService:Create(u31, TweenInfo.new(1), {
            ["Volume"] = 0
        }):Play()
        task.delay(2, function() --[[Anonymous function at line 330]]
            --[[
            Upvalues:
                [1] = u31
            --]]
            u31:Destroy()
        end)
        local v51 = 0
        while v51 < 2 do
            v51 = v51 + game:GetService("RunService").Heartbeat:Wait()
            local v52 = game.TweenService:GetValue(v51, Enum.EasingStyle.Quad, Enum.EasingDirection.InOut)
            u19:PivotTo(u21:Lerp(u20, v52))
            u25:PivotTo((u26 * CFrame.new(0, 2, 0)):Lerp(u26, v52))
        end
        game.TweenService:Create(game.SoundService.Music.Tunes, TweenInfo.new(2), {
            ["Volume"] = 1
        }):Play()
        u25:Destroy()
        u32:Destroy()
        u24:Destroy()
        if u19 then
            u19:Destroy()
        end
        u7.UpdateSkybox(u6, 0)
        u7.UpdateSkybox(u9, 0)
        game.TweenService:Create(u8, TweenInfo.new(3), {
            ["Brightness"] = 0,
            ["TintColor"] = Color3.fromRGB(255, 255, 255)
        }):Play()
        game.TweenService:Create(game.Lighting, TweenInfo.new(3), {
            ["Ambient"] = game.Lighting:GetAttribute("DefaultAmbient"),
            ["ExposureCompensation"] = game.Lighting:GetAttribute("DefaultExposure"),
            ["Brightness"] = game.Lighting:GetAttribute("DefaultBrightness")
        }):Play()
        game.TweenService:Create(game.Lighting.Atmosphere, TweenInfo.new(3), {
            ["Density"] = 0,
            ["Offset"] = 0,
            ["Color"] = Color3.fromRGB(215, 244, 255),
            ["Decay"] = Color3.fromRGB(190, 238, 255)
        }):Play()
    end)
    local v53 = u19.Screen:FindFirstChild("Timer", true)
    local u54 = u35
    while true do
        if workspace:GetAttribute("MonsterMashCurrentClock") then
            v53.Text = u3.compactFormat(workspace:GetAttribute("MonsterMashCurrentClock"))
        end
        task.wait(1)
        if workspace:GetAttribute("MonsterMashCurrentClock") <= 0 then
            game.TweenService:Create(u19.Screen, TweenInfo.new(1), {
                ["CFrame"] = u19.Screen.CFrame * CFrame.new(0, -200, 0)
            }):Play()
            u19.Screen:Destroy()
            game.TweenService:Create(u31, TweenInfo.new(1), {
                ["Volume"] = 0
            }):Play()
            task.delay(0.3, function() --[[Anonymous function at line 402]]
                --[[
                Upvalues:
                    [1] = u31
                --]]
                u31:Stop()
            end)
            local v55 = script["Dj Scratch Effect"]:Clone()
            v55.SoundId = "rbxassetid://1846552051"
            v55.Parent = workspace
            v55:Play()
            game.Debris:AddItem(v55, 5)
            game.TweenService:Create(workspace.CurrentCamera, TweenInfo.new(2), {
                ["FieldOfView"] = 65
            }):Play()
            task.wait(2)
            local v56 = script.Impact:Clone()
            v56.SoundId = "rbxassetid://9125402735"
            v56.Parent = workspace
            v56:Play()
            game.Debris:AddItem(v56, 7)
            for _ = 1, 4 do
                for _, v57 in u19.Pyro:GetDescendants() do
                    if v57:IsA("ParticleEmitter") then
                        v57:Emit(15)
                    end
                end
                local v58 = script["Air Horn Sound Effect"]:Clone()
                v58.SoundId = "rbxassetid://1542459939"
                v58.Parent = workspace
                game.Debris:AddItem(v58, 4)
                v58:Play()
                game.TweenService:Create(workspace.CurrentCamera, TweenInfo.new(0.1), {
                    ["FieldOfView"] = 68
                }):Play()
                task.delay(0.05, function() --[[Anonymous function at line 437]]
                    game.TweenService:Create(workspace.CurrentCamera, TweenInfo.new(0.05), {
                        ["FieldOfView"] = 70
                    }):Play()
                end)
                task.wait(0.15)
            end
            for _, u59 in u19.Pyro:GetDescendants() do
                if u59:IsA("ParticleEmitter") then
                    u59.Enabled = true
                    task.delay(1.5, function() --[[Anonymous function at line 449]]
                        --[[
                        Upvalues:
                            [1] = u59
                        --]]
                        u59.Enabled = false
                    end)
                end
            end
            task.spawn(function() --[[Anonymous function at line 457]]
                --[[
                Upvalues:
                    [1] = u19
                    [2] = u18
                --]]
                local v60 = {}
                for v61, u62 in u19.SpawnPoints:GetChildren() do
                    task.spawn(function() --[[Anonymous function at line 460]]
                        --[[
                        Upvalues:
                            [1] = u18
                            [2] = u62
                        --]]
                        u18(u62.CFrame)
                    end)
                    v60[#u19.SpawnPoints:GetChildren() - (v61 - 1)] = u62.CFrame
                    task.wait(0.1)
                end
                for _, u63 in v60 do
                    task.spawn(function() --[[Anonymous function at line 469]]
                        --[[
                        Upvalues:
                            [1] = u18
                            [2] = u63
                        --]]
                        u18(u63)
                    end)
                    task.wait(0.1)
                end
            end)
            task.wait(2)
            local v64 = script.Countdown:Clone()
            v64.SoundId = "rbxassetid://122907949849153"
            v64.Parent = workspace
            v64:Play()
            game.Debris:AddItem(v64, 5)
            for _ = 1, 5 do
                task.wait(1)
                game.TweenService:Create(u8, TweenInfo.new(0.3), {
                    ["TintColor"] = Color3.fromRGB(219, 188, 255),
                    ["Brightness"] = 0.3
                }):Play()
                game.TweenService:Create(workspace.CurrentCamera, TweenInfo.new(0.3), {
                    ["FieldOfView"] = 68
                }):Play()
                task.delay(0.05, function() --[[Anonymous function at line 489]]
                    --[[
                    Upvalues:
                        [1] = u8
                    --]]
                    game.TweenService:Create(u8, TweenInfo.new(0.3), {
                        ["TintColor"] = Color3.fromRGB(255, 255, 255),
                        ["Brightness"] = 0.1
                    }):Play()
                    game.TweenService:Create(workspace.CurrentCamera, TweenInfo.new(0.4), {
                        ["FieldOfView"] = 70
                    }):Play()
                end)
            end
            task.wait(0.5)
            u31.SoundId = "rbxassetid://95780928979580"
            u31.TimePosition = 0
            u31.Volume = 3
            u31:Play()
            task.delay(30, function() --[[Anonymous function at line 504]]
                --[[
                Upvalues:
                    [1] = u19
                    [2] = u54
                    [3] = u8
                    [4] = u7
                    [5] = u6
                    [6] = u9
                --]]
                for _, v65 in u19.jhailatte:GetDescendants() do
                    if v65:IsA("ParticleEmitter") then
                        v65.Color = ColorSequence.new(Color3.fromRGB(254, 119, 0))
                    end
                end
                u54 = true
                for _, v66 in u19.BassDrop:GetDescendants() do
                    if v66:IsA("ParticleEmitter") then
                        v66:Emit(v66:GetAttribute("EmitCount"))
                    end
                end
                game.TweenService:Create(game.Lighting, TweenInfo.new(0.3), {
                    ["Ambient"] = Color3.fromRGB(230, 145, 26)
                }):Play()
                game.TweenService:Create(game.Lighting.Atmosphere, TweenInfo.new(0.3), {
                    ["Density"] = 0.338,
                    ["Offset"] = 0,
                    ["Color"] = Color3.fromRGB(255, 157, 0),
                    ["Decay"] = Color3.fromRGB(255, 204, 2)
                }):Play()
                game.TweenService:Create(u8, TweenInfo.new(0.3), {
                    ["TintColor"] = Color3.fromRGB(255, 169, 83),
                    ["Brightness"] = 0.7
                }):Play()
                game.TweenService:Create(workspace.CurrentCamera, TweenInfo.new(0.3), {
                    ["FieldOfView"] = 90
                }):Play()
                task.delay(0.3, function() --[[Anonymous function at line 538]]
                    --[[
                    Upvalues:
                        [1] = u7
                        [2] = u6
                        [3] = u9
                        [4] = u19
                        [5] = u8
                    --]]
                    u7.UpdateSkybox(u6, 0)
                    u7.UpdateSkybox(u9, 2)
                    u19.DJBooth.Smoke.OutwardSmoke.Enabled = true
                    game.TweenService:Create(u8, TweenInfo.new(1), {
                        ["TintColor"] = Color3.fromRGB(255, 255, 255),
                        ["Brightness"] = 0.1
                    }):Play()
                    game.TweenService:Create(workspace.CurrentCamera, TweenInfo.new(1), {
                        ["FieldOfView"] = 70
                    }):Play()
                end)
                local v67 = game.ReplicatedStorage.Assets.DJJhaiFence:Clone()
                v67.Parent = workspace
                repeat
                    task.wait()
                until not workspace:GetAttribute("MonsterMash")
                v67:Destroy()
            end)
            return
        end
    end
end
workspace:GetAttributeChangedSignal("MonsterMash"):Connect(function() --[[Anonymous function at line 561]]
    --[[
    Upvalues:
        [1] = u68
        [2] = u5
    --]]
    if workspace:GetAttribute("MonsterMash") then
        u68()
    else
        u5 = false
    end
end)
if workspace:GetAttribute("MonsterMash") then
    task.defer(u68)
else
    u5 = false
end
return v1