-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\ScrollingController-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
game:GetService("SoundService")
game:GetService("Players")
local u2 = require(v1.Modules.Observers)
local v8 = {
    ["Start"] = function(_) --[[Function name: Start, line 10]]
        --[[
        Upvalues:
            [1] = u2
        --]]
        u2.observeTag("AutomaticScrollBarThickness", function(u3) --[[Anonymous function at line 11]]
            if not u3:IsA("ScrollingFrame") then
                return nil
            end
            local u4 = nil
            local function v6() --[[Anonymous function at line 17]]
                --[[
                Upvalues:
                    [1] = u4
                    [2] = u3
                --]]
                if u4 and coroutine.status(u4) == "suspended" then
                    pcall(task.cancel, u4)
                end
                u4 = task.delay(0.15, function() --[[Anonymous function at line 22]]
                    --[[
                    Upvalues:
                        [1] = u3
                        [2] = u4
                    --]]
                    local v5 = u3.Parent
                    if v5 and v5:IsA("GuiObject") then
                        u3.ScrollBarThickness = v5.AbsoluteSize.X * 0.025
                        u4 = nil
                    end
                end)
            end
            local u7 = u3:GetPropertyChangedSignal("AbsoluteSize"):Connect(v6)
            task.spawn(v6)
            return function() --[[Anonymous function at line 37]]
                --[[
                Upvalues:
                    [1] = u7
                --]]
                u7:Disconnect()
            end
        end)
    end
}
task.spawn(v8.Start, v8)
return v8