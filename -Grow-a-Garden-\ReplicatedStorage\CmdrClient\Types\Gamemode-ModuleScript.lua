-- Full Path: -Grow-a-Garden-\ReplicatedStorage\CmdrClient\Types\Gamemode-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
local u2 = require(script.Parent.Parent.Shared.Util)
v1:Wait<PERSON><PERSON><PERSON>hild("Seed_Models")
local u3 = { "Creative", "Adventure", "Spectator" }
local u8 = {
    ["Transform"] = function(p4) --[[Function name: Transform, line 14]]
        --[[
        Upvalues:
            [1] = u2
            [2] = u3
        --]]
        return u2.MakeFuzzyFinder(u3)(p4)
    end,
    ["Validate"] = function(p5) --[[Function name: Validate, line 20]]
        return #p5 > 0, "No seeds could be found."
    end,
    ["Autocomplete"] = function(p6) --[[Function name: Autocomplete, line 24]]
        --[[
        Upvalues:
            [1] = u2
        --]]
        return u2.GetNames(p6)
    end,
    ["Parse"] = function(p7) --[[Function name: Parse, line 28]]
        return p7[1]
    end,
    ["Default"] = function(_) --[[Function name: Default, line 32]]
        return "Creative"
    end,
    ["ArgumentOperatorAliases"] = {
        ["me"] = ".",
        ["all"] = "*",
        ["others"] = "**",
        ["random"] = "?"
    }
}
return function(p9) --[[Anonymous function at line 44]]
    --[[
    Upvalues:
        [1] = u8
    --]]
    p9:RegisterType("gamemode", u8)
end