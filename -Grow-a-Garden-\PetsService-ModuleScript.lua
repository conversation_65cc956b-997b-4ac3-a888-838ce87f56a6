-- Full Path: -Grow-a-Garden-\\PetsService-ModuleScript.lua
local v1 = {}
local u2 = game:GetService("ReplicatedStorage"):WaitFor<PERSON>hild("GameEvents"):WaitForChild("PetsService")
function v1.EquipPet(_, p3, p4) --[[Anonymous function at line 9]]
    --[[
    Upvalues:
        [1] = u2
    --]]
    u2:FireServer("EquipPet", p3, p4)
end
function v1.UnequipPet(_, p5) --[[Anonymous function at line 13]]
    --[[
    Upvalues:
        [1] = u2
    --]]
    u2:FireServer("UnequipPet", p5)
end
return v1