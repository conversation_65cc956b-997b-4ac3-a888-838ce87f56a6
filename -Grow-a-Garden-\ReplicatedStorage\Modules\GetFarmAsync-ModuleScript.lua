-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\GetFarmAsync-ModuleScript.lua
local u1 = game:GetService("Players")
local v2 = game:GetService("ReplicatedStorage")
local u3 = require(v2.Modules.GetFarm)
return function(p4) --[[Function name: GetFarmAsync, line 7]]
    --[[
    Upvalues:
        [1] = u3
        [2] = u1
    --]]
    local v5 = u3(p4)
    if v5 then
        return v5
    end
    repeat
        task.wait()
        local v6 = u3(p4)
    until not p4:IsDescendantOf(u1) or v6
    return v6
end