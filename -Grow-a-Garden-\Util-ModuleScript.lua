-- Full Path: -Grow-a-Garden-\\Util-ModuleScript.lua
local u1 = game:GetService("TextService")
local u35 = {
    ["MakeDictionary"] = function(p2) --[[Function name: MakeDictionary, line 6]]
        local v3 = {}
        for v4 = 1, #p2 do
            v3[p2[v4]] = true
        end
        return v3
    end,
    ["DictionaryKeys"] = function(p5) --[[Function name: DictionaryKeys, line 17]]
        local v6 = {}
        for v7 in pairs(p5) do
            table.insert(v6, v7)
        end
        return v6
    end,
    ["MakeFuzzyFinder"] = function(p8) --[[Function name: MakeFuzzyFinder, line 42]]
        local u9 = nil
        local u10 = {}
        if typeof(p8) == "Enum" then
            p8 = p8:GetEnumItems()
        end
        if typeof(p8) == "Instance" then
            u10 = p8:GetChildren()
            u9 = {}
            for v11 = 1, #u10 do
                u9[v11] = u10[v11].Name
            end
            ::l8::
            return function(p12, p13) --[[Anonymous function at line 70]]
                --[[
                Upvalues:
                    [1] = u9
                    [2] = u10
                --]]
                local v14 = {}
                for v15, v16 in pairs(u9) do
                    local v17
                    if u10 then
                        v17 = u10[v15] or v16
                    else
                        v17 = v16
                    end
                    if v16:lower() == p12:lower() then
                        if p13 then
                            return v17
                        end
                        table.insert(v14, 1, v17)
                    elseif v16:lower():find(p12:lower(), 1, true) then
                        v14[#v14 + 1] = v17
                    end
                end
                if p13 then
                    return v14[1]
                else
                    return v14
                end
            end
        end
        if typeof(p8) ~= "table" then
            error("MakeFuzzyFinder only accepts a table, Enum, or Instance.")
            goto l8
        end
        local v18 = p8[1]
        if typeof(v18) ~= "Instance" then
            local v19 = p8[1]
            if typeof(v19) ~= "EnumItem" then
                local v20 = p8[1]
                if typeof(v20) ~= "table" then
                    ::l15::
                    local v21 = p8[1]
                    if type(v21) == "string" then
                        u9 = p8
                    elseif p8[1] == nil then
                        u9 = {}
                    else
                        error("MakeFuzzyFinder only accepts tables of instances or strings.")
                    end
                    goto l8
                end
                local v22 = p8[1].Name
                if typeof(v22) ~= "string" then
                    goto l15
                end
            end
        end
        u9 = {}
        u10 = p8
        for v23 = 1, #p8 do
            u9[v23] = u10[v23].Name
            p8 = u10
            u10 = p8
        end
        goto l8
    end,
    ["GetNames"] = function(p24) --[[Function name: GetNames, line 98]]
        local v25 = {}
        for v26 = 1, #p24 do
            local v27 = p24[v26].Name
            if not v27 then
                local v28 = p24[v26]
                v27 = tostring(v28)
            end
            v25[v26] = v27
        end
        return v25
    end,
    ["SplitStringSimple"] = function(p29, p30) --[[Function name: SplitStringSimple, line 109]]
        local v31 = p30 == nil and "%s" or p30
        local v32 = {}
        local v33 = 1
        for v34 in string.gmatch(p29, "([^" .. v31 .. "]+)") do
            v32[v33] = v34
            v33 = v33 + 1
        end
        return v32
    end
}
local function u37(p36) --[[Anonymous function at line 122]]
    return utf8.char((tonumber(p36, 16)))
end
function u35.ParseEscapeSequences(p38) --[[Anonymous function at line 127]]
    --[[
    Upvalues:
        [1] = u37
    --]]
    return p38:gsub("\\(.)", {
        ["t"] = "\t",
        ["n"] = "\n"
    }):gsub("\\u(%x%x%x%x)", u37):gsub("\\x(%x%x)", u37)
end
function u35.EncodeEscapedOperator(p39, p40) --[[Anonymous function at line 136]]
    local v41 = p40:sub(1, 1)
    local v42 = p40:gsub(".", "%%%1")
    return p39:gsub("(" .. ("%" .. v41) .. "+)(" .. v42 .. ")", function(p43, p44) --[[Anonymous function at line 141]]
        return (p43:sub(1, #p43 - 1) .. p44):gsub(".", function(p45) --[[Anonymous function at line 142]]
            return "\\u" .. string.format("%04x", string.byte(p45), 16)
        end)
    end)
end
local u46 = { "&&", "||", ";" }
function u35.EncodeEscapedOperators(p47) --[[Anonymous function at line 149]]
    --[[
    Upvalues:
        [1] = u46
        [2] = u35
    --]]
    for _, v48 in ipairs(u46) do
        p47 = u35.EncodeEscapedOperator(p47, v48)
    end
    return p47
end
function u35.SplitString(p49, p50) --[[Anonymous function at line 177]]
    --[[
    Upvalues:
        [1] = u35
    --]]
    local v51 = nil
    local v52 = nil
    local v53 = {}
    local v54 = p50 or (1 / 0)
    for v55 in p49:gsub("\\\\", "___!CMDR_ESCAPE!___"):gsub("\\\"", "___!CMDR_QUOTE!___"):gsub("\\\'", "___!CMDR_SQUOTE!___"):gsub("\\\n", "___!CMDR_NL!___"):gmatch("[^ ]+") do
        local v56 = u35.ParseEscapeSequences(v55)
        local v57 = v56:match("^([\'\"])")
        local v58 = v56:match("([\'\"])$")
        local v59 = v56:match("(\\*)[\'\"]$")
        if v57 and not (v51 or v58) then
            v51 = v57
            v52 = v56
        elseif v52 and (v58 == v51 and #v59 % 2 == 0) then
            v56 = v52 .. " " .. v56
            v52 = nil
            v51 = nil
        elseif v52 then
            v52 = v52 .. " " .. v56
        end
        if not v52 then
            v53[#v53 + (v54 < #v53 and 0 or 1)] = v56:gsub("^([\'\"])", ""):gsub("([\'\"])$", ""):gsub("___!CMDR_ESCAPE!___", "\\"):gsub("___!CMDR_QUOTE!___", "\""):gsub("___!CMDR_NL!___", "\n")
        end
    end
    if v52 then
        v53[#v53 + (v54 < #v53 and 0 or 1)] = v52:gsub("___!CMDR_ESCAPE!___", "\\"):gsub("___!CMDR_QUOTE!___", "\""):gsub("___!CMDR_NL!___", "\n")
    end
    return v53
end
function u35.MashExcessArguments(p60, p61) --[[Anonymous function at line 209]]
    local v62 = {}
    for v63 = 1, #p60 do
        if p61 < v63 then
            v62[p61] = ("%s %s"):format(v62[p61] or "", p60[v63])
        else
            v62[v63] = p60[v63]
        end
    end
    return v62
end
function u35.TrimString(p64) --[[Anonymous function at line 222]]
    local _, v65 = string.find(p64, "^%s*")
    return v65 == #p64 and "" or string.match(p64, ".*%S", v65 + 1)
end
function u35.GetTextSize(p66, p67, p68) --[[Anonymous function at line 229]]
    --[[
    Upvalues:
        [1] = u1
    --]]
    return u1:GetTextSize(p66, p67.TextSize, p67.Font, p68 or Vector2.new(p67.AbsoluteSize.X, 0))
end
function u35.MakeEnumType(u69, p70) --[[Anonymous function at line 234]]
    --[[
    Upvalues:
        [1] = u35
    --]]
    local u71 = u35.MakeFuzzyFinder(p70)
    return {
        ["Validate"] = function(p72) --[[Function name: Validate, line 237]]
            --[[
            Upvalues:
                [1] = u71
                [2] = u69
            --]]
            return u71(p72, true) ~= nil, ("Value %q is not a valid %s."):format(p72, u69)
        end,
        ["Autocomplete"] = function(p73) --[[Function name: Autocomplete, line 240]]
            --[[
            Upvalues:
                [1] = u71
                [2] = u35
            --]]
            local v74 = u71(p73)
            local v75 = v74[1]
            if type(v75) ~= "string" then
                v74 = u35.GetNames(v74) or v74
            end
            return v74
        end,
        ["Parse"] = function(p76) --[[Function name: Parse, line 244]]
            --[[
            Upvalues:
                [1] = u71
            --]]
            return u71(p76, true)
        end
    }
end
function u35.ParsePrefixedUnionType(p77, p78) --[[Anonymous function at line 251]]
    --[[
    Upvalues:
        [1] = u35
    --]]
    local v79 = u35.SplitStringSimple(p77)
    local v80 = {}
    for v81 = 1, #v79, 2 do
        v80[#v80 + 1] = {
            ["prefix"] = v79[v81 - 1] or "",
            ["type"] = v79[v81]
        }
    end
    table.sort(v80, function(p82, p83) --[[Anonymous function at line 265]]
        return #p82.prefix > #p83.prefix
    end)
    for v84 = 1, #v80 do
        local v85 = v80[v84]
        if p78:sub(1, #v85.prefix) == v85.prefix then
            return v85.type, p78:sub(#v85.prefix + 1), v85.prefix
        end
    end
end
function u35.MakeListableType(u86, p87) --[[Anonymous function at line 280]]
    local v88 = {
        ["Listable"] = true,
        ["Transform"] = u86.Transform,
        ["Validate"] = u86.Validate,
        ["ValidateOnce"] = u86.ValidateOnce,
        ["Autocomplete"] = u86.Autocomplete,
        ["Default"] = u86.Default,
        ["ArgumentOperatorAliases"] = u86.ArgumentOperatorAliases,
        ["Parse"] = function(...) --[[Function name: Parse, line 289]]
            --[[
            Upvalues:
                [1] = u86
            --]]
            return { u86.Parse(...) }
        end
    }
    if p87 then
        for v89, v90 in pairs(p87) do
            v88[v89] = v90
        end
    end
    return v88
end
function u35.RunCommandString(p91, p92) --[[Anonymous function at line 311]]
    --[[
    Upvalues:
        [1] = u35
    --]]
    local v93 = u35.ParseEscapeSequences(p92)
    local v94 = u35.EncodeEscapedOperators(v93):split("&&")
    local v95 = ""
    for v96, v97 in ipairs(v94) do
        local v98 = v95:gsub("%$", "\\x24"):gsub("%%", "%%%%")
        local v99 = "||"
        if v95:find("%s") then
            v98 = ("%q"):format(v98) or v98
        end
        local v100 = v97:gsub(v99, v98)
        local v101 = u35.RunEmbeddedCommands(p91, v100)
        v95 = tostring(p91:EvaluateAndRun(v101))
        if v96 == #v94 then
            return v95
        end
    end
end
function u35.RunEmbeddedCommands(p102, p103) --[[Anonymous function at line 338]]
    --[[
    Upvalues:
        [1] = u35
    --]]
    local v104 = p103:gsub("\\%$", "___!CMDR_DOLLAR!___")
    local v105 = {}
    for v106 in v104:gmatch("$(%b{})") do
        local v107 = v106:sub(2, #v106 - 1)
        local v108
        if v107:match("^{.+}$") then
            v107 = v107:sub(2, #v107 - 1)
            v108 = false
        else
            v108 = true
        end
        v105[v106] = u35.RunCommandString(p102, v107)
        if v108 and (v105[v106]:find("%s") or v105[v106] == "") then
            v105[v106] = string.format("%q", v105[v106])
        end
    end
    return v104:gsub("$(%b{})", v105):gsub("___!CMDR_DOLLAR!___", "$")
end
function u35.SubstituteArgs(p109, p110) --[[Anonymous function at line 366]]
    local v111 = p109:gsub("\\%$", "___!CMDR_DOLLAR!___")
    if type(p110) == "table" then
        for v112 = 1, #p110 do
            local v113 = tostring(v112)
            p110[v113] = p110[v112]
            if p110[v113]:find("%s") then
                p110[v113] = string.format("%q", p110[v113])
            end
        end
    end
    return v111:gsub("($%d+)%b{}", "%1"):gsub("$(%w+)", p110):gsub("___!CMDR_DOLLAR!___", "$")
end
function u35.MakeAliasCommand(p114, p115) --[[Anonymous function at line 383]]
    --[[
    Upvalues:
        [1] = u35
    --]]
    local v116, v117 = unpack(p114:split("|"))
    local u118 = u35.EncodeEscapedOperators(p115)
    local v119 = {}
    local v120 = {}
    for v121 in u118:gmatch("$(%d+)") do
        if v119[v121] == nil then
            v119[v121] = true
            local v122 = u118:match((("$%*(%%b{})"):format(v121)))
            local v123, v124, v125
            if v122 then
                local v126 = v122:sub(2, #v122 - 1)
                v123, v124, v125 = unpack(v126:split("|"))
            else
                v123 = nil
                v124 = nil
                v125 = nil
            end
            local v127
            if v123 then
                v127 = v123:match("%?$") and true or false
            else
                v127 = v123
            end
            local v128 = not v123 and "string" or v123:match("^%w+")
            local v129 = v124 or ("Argument %*"):format(v121)
            table.insert(v120, {
                ["Type"] = v128,
                ["Name"] = v129,
                ["Description"] = v125 or "",
                ["Optional"] = v127
            })
        end
    end
    return {
        ["Name"] = v116,
        ["Aliases"] = {},
        ["Description"] = ("<Alias> %*"):format(v117 or u118),
        ["Group"] = "UserAlias",
        ["Args"] = v120,
        ["Run"] = function(p130) --[[Function name: Run, line 422]]
            --[[
            Upvalues:
                [1] = u35
                [2] = u118
            --]]
            return u35.RunCommandString(p130.Dispatcher, u35.SubstituteArgs(u118, p130.RawArguments))
        end
    }
end
function u35.MakeSequenceType(p131) --[[Anonymous function at line 429]]
    --[[
    Upvalues:
        [1] = u35
    --]]
    local u132 = p131 or {}
    local v133 = u132.Parse ~= nil and true or u132.Constructor ~= nil
    assert(v133, "MakeSequenceType: Must provide one of: Constructor, Parse")
    u132.TransformEach = u132.TransformEach or function(...) --[[Anonymous function at line 434]]
        return ...
    end
    u132.ValidateEach = u132.ValidateEach or function() --[[Anonymous function at line 438]]
        return true
    end
    return {
        ["Prefixes"] = u132.Prefixes,
        ["Transform"] = function(p134) --[[Function name: Transform, line 445]]
            --[[
            Upvalues:
                [1] = u35
                [2] = u132
            --]]
            return u35.Map(u35.SplitPrioritizedDelimeter(p134, { ",", "%s" }), function(p135) --[[Anonymous function at line 446]]
                --[[
                Upvalues:
                    [1] = u132
                --]]
                return u132.TransformEach(p135)
            end)
        end,
        ["Validate"] = function(p136) --[[Function name: Validate, line 451]]
            --[[
            Upvalues:
                [1] = u132
            --]]
            if u132.Length and #p136 > u132.Length then
                return false, ("Maximum of %d values allowed in sequence"):format(u132.Length)
            end
            for v137 = 1, u132.Length or #p136 do
                local v138, v139 = u132.ValidateEach(p136[v137], v137)
                if not v138 then
                    return false, v139
                end
            end
            return true
        end,
        ["Parse"] = u132.Parse or function(p140) --[[Anonymous function at line 467]]
            --[[
            Upvalues:
                [1] = u132
            --]]
            return u132.Constructor(unpack(p140))
        end
    }
end
function u35.SplitPrioritizedDelimeter(p141, p142) --[[Anonymous function at line 475]]
    --[[
    Upvalues:
        [1] = u35
    --]]
    for v143, v144 in ipairs(p142) do
        if p141:find(v144) or v143 == #p142 then
            return u35.SplitStringSimple(p141, v144)
        end
    end
end
function u35.Map(p145, p146) --[[Anonymous function at line 484]]
    local v147 = {}
    for v148, v149 in ipairs(p145) do
        v147[v148] = p146(v149, v148)
    end
    return v147
end
function u35.Each(p150, ...) --[[Anonymous function at line 495]]
    local v151 = {}
    for v152, v153 in ipairs({ ... }) do
        v151[v152] = p150(v153)
    end
    return unpack(v151)
end
function u35.EmulateTabstops(p154, p155) --[[Anonymous function at line 504]]
    local v156 = #p154
    local v157 = table.create(v156)
    local v158 = 0
    for v159 = 1, v156 do
        local v160 = string.sub(p154, v159, v159)
        if v160 == "\t" then
            local v161 = p155 - v158 % p155
            local v162 = string.rep
            table.insert(v157, v162(" ", v161))
            v158 = v158 + v161
        else
            table.insert(v157, v160)
            if v160 == "\n" then
                v158 = 0
            elseif v160 ~= "\r" then
                v158 = v158 + 1
            end
        end
    end
    return table.concat(v157)
end
function u35.Mutex() --[[Anonymous function at line 526]]
    local u163 = {}
    local u164 = false
    return function() --[[Anonymous function at line 530]]
        --[[
        Upvalues:
            [1] = u164
            [2] = u163
        --]]
        if u164 then
            local v165 = u163
            local v166 = coroutine.running
            table.insert(v165, v166())
            coroutine.yield()
        else
            u164 = true
        end
        return function() --[[Anonymous function at line 538]]
            --[[
            Upvalues:
                [1] = u163
                [2] = u164
            --]]
            if #u163 > 0 then
                coroutine.resume(table.remove(u163, 1))
            else
                u164 = false
            end
        end
    end
end
return u35