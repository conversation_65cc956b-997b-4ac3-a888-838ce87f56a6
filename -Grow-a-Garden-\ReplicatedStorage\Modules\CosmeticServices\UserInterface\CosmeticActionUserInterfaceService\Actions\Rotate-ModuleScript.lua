-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\CosmeticServices\UserInterface\CosmeticActionUserInterfaceService\Actions\Rotate-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
local u2 = require(v1.Modules.CosmeticServices.UserInterface.CosmeticRotationUserInterfaceService)
local u3 = require(v1.Modules.CosmeticServices.ModelMovement.CosmeticRotationService)
return function(u4) --[[Function name: Loader, line 6]]
    --[[
    Upvalues:
        [1] = u2
        [2] = u3
    --]]
    return {
        ["InputBegan"] = function() --[[Anonymous function at line 8]]
            --[[
            Upvalues:
                [1] = u2
                [2] = u4
                [3] = u3
            --]]
            u2:Toggle(true)
            u4:Toggle(false)
            u3:SetTarget(u4.Target)
        end,
        ["InputEnded"] = function() --[[Anonymous function at line 14]]
            --[[
            Upvalues:
                [1] = u2
                [2] = u4
                [3] = u3
            --]]
            u2:Toggle(false)
            u4:Toggle(true)
            u3:SetTarget(nil)
        end
    }
end