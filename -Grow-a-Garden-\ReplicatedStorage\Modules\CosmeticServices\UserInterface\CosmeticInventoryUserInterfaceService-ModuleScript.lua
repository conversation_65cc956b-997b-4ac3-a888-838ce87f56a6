-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\CosmeticServices\UserInterface\CosmeticInventoryUserInterfaceService-ModuleScript.lua
local u1 = Color3.fromRGB(255, 255, 255)
local u2 = Color3.fromRGB(140, 140, 140)
local u3 = game:GetService("GamepadService")
local u4 = game:GetService("UserInputService")
local v5 = game:GetService("ReplicatedStorage")
local u6 = game:GetService("TweenService")
game:GetService("RunService")
local u7 = game:GetService("Players").LocalPlayer
local v8 = u7.PlayerGui
local u9 = v8:WaitForChild("CosmeticUI"):WaitForChild("CosmeticInventory")
local u10 = v8:WaitForChild("BackpackGui")
local u11 = v8:WaitForChild("ActivePetUI")
local u12 = require(v5.Modules.WaitForDescendant)
local v13 = require(v5.Modules.AdjustBrightnessHSV)
local v14 = require(v5.Modules.DataService)
local u15 = require(v5.Modules.CosmeticServices.CosmeticService)
local u16 = require(v5.Modules.GetUIElementWithTag)
local u17 = require(v5.Modules.Notification)
local v18 = require(v5.Modules.Icon)
require(v5.Modules.UpdateService)
local u19 = require(v5.Modules.CountDictionary)
local u20 = require(v5.Modules.PlaySound)
local u21 = require(v5.Modules.PlayHoverSound)
local v22 = require(v5.Modules.PlatformService.Signal)
u12(u9, "SQUARE_INSERTION_POINT")
local u23 = u12(u9, "ACTUAL_FRAME")
local v24 = u12(u9, "MENU_BUTTON")
local u25 = u12(u9, "UISizeConstraint")
local u26 = u12(u9, "ARROW_IMAGE")
local u27 = u12(u9, "SQUARE_INSERTION_POINT")
local u28 = u27:WaitForChild("COSMETIC_TEMPLATE")
local u29 = u12(u9, "SEARCH_TEXT_BOX")
local v30 = u12(u9, "CLOSE_BUTTON")
local u31 = u12(u9, "ACTUAL_TEXTURE")
local v32 = u12(u9, "COSMETIC_WIDGET")
local u33 = u12(v32, "INVENTORY_COUNT")
local u34 = u12(v32, "PLACED_COUNT")
u12(v32, "SEPERATION_BAR")
local function u38() --[[Anonymous function at line 78]]
    --[[
    Upvalues:
        [1] = u19
        [2] = u15
        [3] = u33
        [4] = u34
    --]]
    local v35 = u19(u15:GetAllEquippedCosmetics()) or 0
    local v36 = u19(u15:GetAllCosmetics()) or 0
    local v37 = u15:GetMutableStat("MaxEquippedCosmetics")
    u33.Text = ("Total Items: %*/%*"):format(v36, (u15:GetMutableStat("MaxCosmeticsInInventory")))
    u34.Text = ("Placed Items: %*/%*"):format(v35, v37)
end
local u39 = v18.new()
u39:setImage("rbxassetid://94891023443943", "deselected")
u39:setImage("rbxassetid://106474593618951", "selected")
u39:setName("InventoryIcon")
u39:setImageScale(1.12)
u39:setOrder(-3)
u39:setCaption("Toggle the cosmetic inventory.")
u39.deselectWhenOtherIconSelected = false
local u40 = require(v5.Data.CosmeticRegistry).CosmeticList
local u41 = workspace.CurrentCamera
workspace:GetPropertyChangedSignal("CurrentCamera"):Connect(function() --[[Anonymous function at line 106]]
    --[[
    Upvalues:
        [1] = u41
    --]]
    u41 = workspace.CurrentCamera
end)
local u42 = {
    ["BigScale"] = false,
    ["State"] = false,
    ["CurrentInventoryState"] = 4,
    ["FrameRenderData"] = {
        ["CosmeticCounterFrames"] = {},
        ["UniqueCosmeticFrames"] = {}
    },
    ["OnToggled"] = v22.new()
}
local u43 = {
    function() --[[Anonymous function at line 130]]
        --[[
        Upvalues:
            [1] = u42
        --]]
        u42:Toggle(true)
    end,
    function() --[[Anonymous function at line 133]]
        --[[
        Upvalues:
            [1] = u42
        --]]
        u42:ChangeScaleMode(false)
    end,
    function() --[[Anonymous function at line 136]]
        --[[
        Upvalues:
            [1] = u42
        --]]
        u42:ChangeScaleMode(true)
    end,
    function() --[[Anonymous function at line 139]]
        --[[
        Upvalues:
            [1] = u42
        --]]
        u42:Toggle(false)
    end
}
function u42.ChangeInventoryState(_, p44) --[[Anonymous function at line 148]]
    --[[
    Upvalues:
        [1] = u42
        [2] = u43
    --]]
    u42.CurrentInventoryState = p44;
    (u43[p44] or u43[4])()
end
function u42.Toggle(_, p45) --[[Anonymous function at line 153]]
    --[[
    Upvalues:
        [1] = u3
        [2] = u9
        [3] = u42
        [4] = u6
        [5] = u10
        [6] = u11
    --]]
    u3:EnableGamepadCursor(u9)
    u42.State = p45
    u42.OnToggled:Fire(p45)
    local u46 = u6:Create(u9, TweenInfo.new(0.2), {
        ["Position"] = p45 and UDim2.fromScale(0.5, 0.97) or UDim2.fromScale(0.5, 1.5)
    })
    u46:Play()
    if p45 then
        u9.Visible = true
    else
        u46.Completed:Once(function() --[[Anonymous function at line 167]]
            --[[
            Upvalues:
                [1] = u46
                [2] = u9
            --]]
            if u46.PlaybackState == Enum.PlaybackState.Completed then
                u9.Visible = false
            end
        end)
    end
    u10.Enabled = not p45
    u11.Enabled = not p45
    if not p45 then
        u42:ChangeScaleMode(true)
    end
end
function u42.ChangeScaleMode(_, p47) --[[Anonymous function at line 181]]
    --[[
    Upvalues:
        [1] = u42
        [2] = u23
        [3] = u6
        [4] = u26
        [5] = u25
        [6] = u31
    --]]
    u42.BigScale = p47
    local v48 = u23.Size
    local v49 = u23.Size + UDim2.fromScale(0, 1)
    local v50 = u6:Create(u23, TweenInfo.new(0.2), {
        ["Size"] = v48
    })
    local v51 = u6:Create(u23, TweenInfo.new(0.2), {
        ["Size"] = v49
    })
    local v52 = u6:Create(u26, TweenInfo.new(0.2), {
        ["Position"] = u26.Position
    })
    local v53 = u6:Create(u26, TweenInfo.new(0.2), {
        ["Position"] = UDim2.fromScale(0.5, 0.5)
    })
    local v54 = u6:Create(u26, TweenInfo.new(0.2), {
        ["Rotation"] = 0
    })
    local v55 = u6:Create(u26, TweenInfo.new(0.2), {
        ["Rotation"] = 180
    })
    if p47 then
        v51 = v50 or v51
    end
    v51:Play()
    if p47 then
        v52 = v53 or v52
    end
    v52:Play()
    if p47 then
        v54 = v55 or v54
    end
    v54:Play()
    u6:Create(u25, TweenInfo.new(0.2), {
        ["MaxSize"] = p47 and Vector2.new(579.67, 115.25) or Vector2.new(579.67, 340)
    }):Play()
    u31.Image = p47 and "rbxassetid://122189350536032" or "rbxassetid://127843077446462"
end
function u42.Search(_, p56) --[[Anonymous function at line 222]]
    --[[
    Upvalues:
        [1] = u42
        [2] = u15
    --]]
    local v57 = u42.FrameRenderData.CosmeticCounterFrames
    local v58 = u42.FrameRenderData.UniqueCosmeticFrames
    local v59 = u15:GetAllCosmetics()
    if p56 == "" then
        for _, v60 in v57 do
            v60.Visible = true
            v60.LayoutOrder = 1
        end
        for _, v61 in v58 do
            v61.Visible = true
            v61.LayoutOrder = 1
        end
    else
        local v62 = string.split(p56, " ")
        for v63, v64 in v57 do
            local v65 = v63:lower()
            local v66 = 0
            for _, v67 in v62 do
                if v65:find(v67) then
                    v66 = v66 + 1
                end
            end
            if v65:find(p56) then
                v66 = v66 + 1
            end
            if v66 <= 0 then
                v64.Visible = false
            else
                v64.LayoutOrder = -v66
                v64.Visible = true
            end
        end
        for v68, v69 in v58 do
            local v70 = v59[v68].Name
            if v70 then
                local v71 = v70:lower()
                local v72 = 0
                for _, v73 in v62 do
                    if v71:find(v73) then
                        v72 = v72 + 1
                    end
                end
                if v71:find(p56) then
                    v72 = v72 + 1
                end
                if v72 <= 0 then
                    v69.Visible = false
                else
                    v69.LayoutOrder = -v72
                    v69.Visible = true
                end
            end
        end
    end
end
local u74 = u9.UIScale
local v75 = v24.SENSOR
local u76 = u6:Create(v24.ANIMATED_TEXTURE, TweenInfo.new(0.2), {
    ["ImageColor3"] = v24.ANIMATED_TEXTURE.ImageColor3
})
local u77 = u6:Create(v24.ANIMATED_TEXTURE, TweenInfo.new(0.2), {
    ["ImageColor3"] = v13(v24.ANIMATED_TEXTURE.ImageColor3, 0.2)
})
v75.MouseLeave:Connect(function() --[[Anonymous function at line 301]]
    --[[
    Upvalues:
        [1] = u76
    --]]
    u76:Play()
end)
v75.MouseEnter:Connect(function() --[[Anonymous function at line 305]]
    --[[
    Upvalues:
        [1] = u77
        [2] = u21
    --]]
    u77:Play()
    u21()
end)
v75.MouseButton1Click:Connect(function() --[[Anonymous function at line 310]]
    --[[
    Upvalues:
        [1] = u42
    --]]
    u42:ChangeScaleMode(not u42.BigScale)
end)
task.spawn(function() --[[Anonymous function at line 314]]
    --[[
    Upvalues:
        [1] = u41
        [2] = u6
        [3] = u74
    --]]
    while true do
        repeat
            task.wait(0.1)
            local v78 = u41.ViewportSize.X
        until v78 ~= nil
        local v79 = u6
        local v80 = u74
        local v81 = TweenInfo.new(0.2)
        local v82 = {}
        local v83 = 0.0011111111111111111 * v78
        v82.Scale = math.min(v83, 1.5)
        v79:Create(v80, v81, v82):Play()
    end
end)
local _ = u9.UIScale
local v84 = v30.SENSOR
local u85 = u6:Create(v30.ANIMATED_TEXTURE, TweenInfo.new(0.2), {
    ["ImageColor3"] = v30.ANIMATED_TEXTURE.ImageColor3
})
local u86 = u6:Create(v30.ANIMATED_TEXTURE, TweenInfo.new(0.2), {
    ["ImageColor3"] = v13(v30.ANIMATED_TEXTURE.ImageColor3, 0.2)
})
v84.MouseLeave:Connect(function() --[[Anonymous function at line 342]]
    --[[
    Upvalues:
        [1] = u85
    --]]
    u85:Play()
end)
v84.MouseEnter:Connect(function() --[[Anonymous function at line 346]]
    --[[
    Upvalues:
        [1] = u86
        [2] = u21
    --]]
    u86:Play()
    u21()
end)
v84.MouseButton1Click:Connect(function() --[[Anonymous function at line 351]]
    --[[
    Upvalues:
        [1] = u42
        [2] = u39
    --]]
    u42:Toggle(false)
    u39:deselect()
end)
local function u96() --[[Anonymous function at line 359]]
    --[[
    Upvalues:
        [1] = u15
    --]]
    local v87 = u15:GetAllEquippedCosmetics()
    local v88 = u15:GetAllCosmetics()
    local v89 = {
        ["ReturnedItemMap"] = {},
        ["UniqueItemMap"] = {}
    }
    local v90 = v89.ReturnedItemMap
    local v91 = v89.UniqueItemMap
    for v92, v93 in v88 do
        if not v87[v92] then
            local v94 = v93.Name
            if v94 then
                local v95 = v93.Data
                if v95 then
                    if not v90[v94] then
                        v90[v94] = 0
                    end
                    if next(v95) then
                        v91[v92] = true
                    else
                        v90[v94] = v90[v94] + 1
                    end
                else
                    warn(v92, "Cosmetic is corrupted and has no ArbitraryData")
                end
            else
                warn(v92, "Cosmetic is corrupted and has no name!")
            end
        end
    end
    return v89
end
local function u103(p97) --[[Anonymous function at line 416]]
    --[[
    Upvalues:
        [1] = u15
    --]]
    local v98 = u15:GetAllEquippedCosmetics()
    for v99, v100 in u15:GetAllCosmetics() do
        if not v98[v99] then
            local v101 = v100.Name
            if v101 then
                if v101 == p97 then
                    local v102 = v100.Data
                    if v102 then
                        if not next(v102) then
                            return v99
                        end
                    else
                        warn(v99, "Cosmetic is corrupted and has no ArbitraryData")
                    end
                end
            else
                warn(v99, "Cosmetic is corrupted and has no name!")
            end
        end
    end
end
local function u141() --[[Anonymous function at line 433]]
    --[[
    Upvalues:
        [1] = u96
        [2] = u42
        [3] = u15
        [4] = u40
        [5] = u28
        [6] = u12
        [7] = u6
        [8] = u1
        [9] = u2
        [10] = u17
        [11] = u19
        [12] = u103
        [13] = u7
        [14] = u20
        [15] = u21
        [16] = u16
        [17] = u27
    --]]
    local v104 = u96()
    local v105 = u42.FrameRenderData.UniqueCosmeticFrames
    local v106 = u42.FrameRenderData.CosmeticCounterFrames
    local v107 = v104.ReturnedItemMap
    local v108 = v104.UniqueItemMap
    local v109 = u15:GetAllCosmetics()
    for v110, _ in v106 do
        if v107[v110] then
            if v107[v110] <= 0 and v110 then
                if typeof(v110) == "string" then
                    local v111 = u42.FrameRenderData.CosmeticCounterFrames
                    local v112 = v111[v110]
                    if v112 then
                        v112:Destroy()
                        v111[v110] = nil
                    end
                end
            end
        elseif v110 then
            if typeof(v110) == "string" then
                local v113 = u42.FrameRenderData.CosmeticCounterFrames
                local v114 = v113[v110]
                if v114 then
                    v114:Destroy()
                    v113[v110] = nil
                end
            end
        end
    end
    for v115, _ in v105 do
        if not v108[v115] and v115 then
            if typeof(v115) == "string" then
                local v116 = u42.FrameRenderData.UniqueCosmeticFrames
                local v117 = v116[v115]
                if v117 then
                    v117:Destroy()
                    v116[v115] = nil
                end
            end
        end
    end
    for u118, v119 in v107 do
        if v119 <= 0 then
            return
        end
        local v120 = u40[u118]
        if v120 then
            local v121 = v106[u118]
            if not v121 then
                local u122 = u28:Clone()
                local v123 = u12(u122, "SENSOR")
                local v124 = {
                    ["BackgroundColor3"] = u1
                }
                local u125 = u6:Create(u122.ActualFrame, TweenInfo.new(0.2), v124)
                local v126 = {
                    ["BackgroundColor3"] = u2
                }
                local u127 = u6:Create(u122.ActualFrame, TweenInfo.new(0.2), v126)
                v123.MouseButton1Down:Connect(function() --[[Anonymous function at line 474]]
                    --[[
                    Upvalues:
                        [1] = u127
                    --]]
                    u127:Play()
                end)
                v123.MouseButton1Up:Connect(function() --[[Anonymous function at line 478]]
                    --[[
                    Upvalues:
                        [1] = u125
                        [2] = u15
                        [3] = u17
                        [4] = u19
                        [5] = u103
                        [6] = u118
                        [7] = u7
                        [8] = u20
                    --]]
                    u125:Play()
                    if u15:HasMaxEquipped() then
                        return u17:CreateNotification((("Cannot place anymore you have max placed objects! %*"):format((u19(u15:GetAllEquippedCosmetics())))))
                    else
                        local v128 = u103(u118)
                        if v128 then
                            if not u15:CanEquipAtLocation(u7) then
                                return u17:CreateNotification("Cannot place cosmetic out of farm!")
                            end
                            u15:Equip(v128)
                            task.delay(0.2, function() --[[Anonymous function at line 487]]
                                --[[
                                Upvalues:
                                    [1] = u20
                                --]]
                                u20("rbxassetid://99990810464653").Volume = 0.5
                            end)
                        end
                    end
                end)
                v123.MouseEnter:Connect(function() --[[Anonymous function at line 493]]
                    --[[
                    Upvalues:
                        [1] = u125
                        [2] = u21
                        [3] = u16
                        [4] = u122
                        [5] = u127
                    --]]
                    u125:Play()
                    u21()
                    task.spawn(function() --[[Anonymous function at line 497]]
                        --[[
                        Upvalues:
                            [1] = u16
                            [2] = u122
                            [3] = u127
                        --]]
                        repeat
                            task.wait()
                        until u16("CosmeticItemTemplate") ~= u122
                        u127:Play()
                    end)
                end)
                v123.MouseLeave:Connect(function() --[[Anonymous function at line 509]]
                    --[[
                    Upvalues:
                        [1] = u127
                    --]]
                    u127:Play()
                end)
                u122.Visible = true
                u122.Parent = u27
                v106[u118] = u122
                v121 = u122
            end
            local v129 = u12(v121, "COUNTER_TEXT")
            v129.Text = ("x%*"):format(v119)
            u12(v121, "ITEM_ICON").Image = v120.Icon
            v129.Visible = v119 >= 2
        else
            warn(u118, "has no attached registry data! Skipping render.")
        end
    end
    for u130 in v108 do
        local v131 = v109[u130]
        if v131 then
            local v132 = v131.Name
            local v133 = u40[v132]
            if v133 then
                local v134 = v105[u130]
                if not v134 then
                    local u135 = u28:Clone()
                    local v136 = u12(u135, "SENSOR")
                    local v137 = {
                        ["BackgroundColor3"] = u1
                    }
                    local u138 = u6:Create(u135.ActualFrame, TweenInfo.new(0.2), v137)
                    local v139 = {
                        ["BackgroundColor3"] = u2
                    }
                    local u140 = u6:Create(u135.ActualFrame, TweenInfo.new(0.2), v139)
                    v136.MouseButton1Down:Connect(function() --[[Anonymous function at line 547]]
                        --[[
                        Upvalues:
                            [1] = u140
                        --]]
                        u140:Play()
                    end)
                    v136.MouseButton1Up:Connect(function() --[[Anonymous function at line 551]]
                        --[[
                        Upvalues:
                            [1] = u138
                            [2] = u15
                            [3] = u17
                            [4] = u19
                            [5] = u7
                            [6] = u130
                            [7] = u20
                        --]]
                        u138:Play()
                        if u15:HasMaxEquipped() then
                            return u17:CreateNotification((("Cannot place anymore you have max placed objects! %*"):format((u19(u15:GetAllEquippedCosmetics())))))
                        end
                        if not u15:CanEquipAtLocation(u7) then
                            return u17:CreateNotification("Cannot place cosmetic out of farm!")
                        end
                        u15:Equip(u130)
                        u20("rbxassetid://99990810464653").Volume = 0.5
                    end)
                    v136.MouseEnter:Connect(function() --[[Anonymous function at line 562]]
                        --[[
                        Upvalues:
                            [1] = u138
                            [2] = u21
                            [3] = u16
                            [4] = u135
                            [5] = u140
                        --]]
                        u138:Play()
                        u21()
                        task.spawn(function() --[[Anonymous function at line 565]]
                            --[[
                            Upvalues:
                                [1] = u16
                                [2] = u135
                                [3] = u140
                            --]]
                            repeat
                                task.wait()
                            until u16("CosmeticItemTemplate") ~= u135
                            u140:Play()
                        end)
                    end)
                    v136.MouseLeave:Connect(function() --[[Anonymous function at line 577]]
                        --[[
                        Upvalues:
                            [1] = u140
                        --]]
                        u140:Play()
                    end)
                    u135.Visible = true
                    u135.Parent = u27
                    v105[u130] = u135
                    v134 = u135
                end
                u12(v134, "ITEM_ICON").Image = v133.Icon
            else
                warn(v132, u130, "has no attached registry data! Skipping render.")
            end
        end
    end
end
u141()
u38()
v14:GetPathSignal("CosmeticData/Inventory/@"):Connect(function() --[[Anonymous function at line 594]]
    --[[
    Upvalues:
        [1] = u141
        [2] = u38
    --]]
    u141()
    u38()
end)
v14:GetPathSignal("CosmeticData/Equipped/@"):Connect(function() --[[Anonymous function at line 599]]
    --[[
    Upvalues:
        [1] = u141
        [2] = u38
    --]]
    u141()
    u38()
end)
u29:GetPropertyChangedSignal("Text"):Connect(function() --[[Anonymous function at line 604]]
    --[[
    Upvalues:
        [1] = u42
        [2] = u29
    --]]
    u42:Search(u29.Text)
end)
u4.InputEnded:Connect(function(p142) --[[Anonymous function at line 613]]
    --[[
    Upvalues:
        [1] = u4
        [2] = u42
        [3] = u39
    --]]
    if u4:GetFocusedTextBox() then
        return
    elseif p142.KeyCode == Enum.KeyCode.B then
        u42:Toggle(not u42.State)
        if u42.State then
            u39:select()
        else
            u39:deselect()
        end
    else
        return
    end
end)
u39.toggled:Connect(function(p143, _) --[[Anonymous function at line 628]]
    --[[
    Upvalues:
        [1] = u42
    --]]
    u42:Toggle(p143)
end)
return u42