-- Full Path: -Grow-a-Garden-\\BooleanSetting-ModuleScript.lua
local v1 = game:GetService("Players")
local v2 = game:GetService("ReplicatedStorage")
local u3 = game:GetService("TweenService")
local v4 = v1.LocalPlayer.PlayerGui:Wait<PERSON><PERSON><PERSON><PERSON><PERSON>("SettingsUI")
local u5 = require(v2.Modules.WaitForDescendant)
local u6 = require(v2.Modules.SetupSounds)
require(v2.Modules.SetupBrightnessAnimationFrame)
local u7 = require(v2.Modules.SetupHoverAnimations)
local u8 = require(v2.Modules.FindDescendantsWithTag)
local u9 = require(v2.Modules.Settings.SettingsService)
local u10 = u5(v4, "SETTING_INSERTION_POINT")
local u11 = u5(v4, "TOGGLE_SETTING_TEMPLATE")
local u12 = {}
u12.__index = u12
function u12.new() --[[Anonymous function at line 25]]
    --[[
    Upvalues:
        [1] = u12
        [2] = u11
        [3] = u8
        [4] = u5
        [5] = u6
        [6] = u7
    --]]
    local v13 = u12
    local u14 = setmetatable({}, v13)
    u14.CurrentValue = true
    local v15 = u11:Clone()
    u14.Asset = v15
    local v16 = {}
    u14.UIData = v16
    v16.AllStrokes = u8(v15, "ColorStroke")
    local v17 = u5(v15, "TOGGLE_BUTTON")
    v16.TOGGLE_BUTTON = v17
    v16.TOGGLE_STATE_TEXT = u5(v15, "TOGGLE_STATE_TEXT")
    v16.SETTING_TITLE = u5(v15, "SETTING_TITLE")
    v16.SETTING_DESCRIPTION = u5(v15, "SETTING_DESCRIPTION")
    v16.BACKGROUND_TEXTURE = u5(v15, "BACKGROUND_TEXTURE")
    u6(v17)
    u7(v17)
    v17:WaitForChild("SENSOR").MouseButton1Click:Connect(function() --[[Anonymous function at line 44]]
        --[[
        Upvalues:
            [1] = u14
        --]]
        u14:Toggle()
    end)
    return u14
end
function u12.Toggle(p18) --[[Anonymous function at line 51]]
    --[[
    Upvalues:
        [1] = u9
    --]]
    local v19 = not p18.CurrentValue
    p18:Update(v19)
    local v20 = p18.Id
    if v20 then
        u9:SetSetting(v20, v19)
    end
end
function u12.SetLayoutOrder(p21, p22) --[[Anonymous function at line 61]]
    p21.Asset.LayoutOrder = p22
    return p21
end
function u12.SetId(p23, p24) --[[Anonymous function at line 66]]
    p23.Id = p24
    return p23
end
function u12.Update(p25, p26) --[[Anonymous function at line 72]]
    --[[
    Upvalues:
        [1] = u3
    --]]
    p25.CurrentValue = p26
    local v27 = p25.CurrentValue
    local v28 = p25.UIData
    local v29 = v28.TOGGLE_STATE_TEXT
    local v30 = v28.AllStrokes
    v29.Text = v27 and "On" or "Off"
    local v31
    if v27 then
        v31 = Color3.fromRGB(30, 100, 14)
    else
        v31 = Color3.fromRGB(118, 4, 7)
    end
    local v32
    if v27 then
        v32 = Color3.fromRGB(37, 238, 38)
    else
        v32 = Color3.fromRGB(206, 27, 24)
    end
    for _, v33 in v30 do
        u3:Create(v33, TweenInfo.new(0.15), {
            ["Color"] = v31
        }):Play()
    end
    u3:Create(v28.TOGGLE_BUTTON, TweenInfo.new(0.15), {
        ["BackgroundColor3"] = v32
    }):Play()
    return p25
end
function u12.SetDescription(p34, p35) --[[Anonymous function at line 101]]
    p34.UIData.SETTING_DESCRIPTION.Text = p35
    return p34
end
function u12.SetTitle(p36, p37) --[[Anonymous function at line 109]]
    p36.UIData.SETTING_TITLE.Text = p37
    return p36
end
function u12.SetBackgroundImage(p38, p39) --[[Anonymous function at line 117]]
    p38.UIData.BACKGROUND_TEXTURE.Image = p39
    return p38
end
function u12.Complete(p40) --[[Anonymous function at line 125]]
    --[[
    Upvalues:
        [1] = u10
    --]]
    local v41 = p40.Asset
    v41.Visible = true
    v41.Parent = u10
    return p40
end
return u12