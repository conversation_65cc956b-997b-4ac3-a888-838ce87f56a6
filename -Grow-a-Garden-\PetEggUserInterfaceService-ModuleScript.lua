-- Full Path: -Grow-a-Garden-\\PetEggUserInterfaceService-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
local v2 = game:GetService("RunService")
game:GetService("UserInputService")
local u3 = game:GetService("TweenService")
local u4 = game:GetService("Players").LocalPlayer:WaitFor<PERSON>hild("PlayerGui"):WaitF<PERSON><PERSON>hild("PetUI"):WaitFor<PERSON>hild("PetEggUI")
local u5 = u4:WaitForChild("CanvasGroup")
local u6 = u5:WaitForChild("EggName")
local u7 = u5:WaitForChild("EggHatchTime")
local u8 = require(v1.Modules.GetFarmAncestor)
local v9 = require(v1.Modules.CreateTagHandler)
require(v1.Modules.GetMouseToWorld)
local u10 = require(v1.<PERSON><PERSON><PERSON>.TimeHelper)
local u11 = require(v1.Data.PetRegistry).PetEggs
local u12 = {}
v9({
    ["Tag"] = "PetEggLocalHitBox",
    ["OnInstanceAdded"] = function(p13) --[[Function name: OnInstanceAdded, line 32]]
        --[[
        Upvalues:
            [1] = u12
        --]]
        local v14 = u12
        table.insert(v14, p13)
    end,
    ["OnInstanceRemoved"] = function(p15) --[[Function name: OnInstanceRemoved, line 35]]
        --[[
        Upvalues:
            [1] = u12
        --]]
        local v16 = table.find(u12, p15)
        if not v16 then
            return warn((("%* hitbox not tracked!"):format(p15.Name)))
        end
        table.remove(u12, v16)
    end
})
local u17 = workspace.CurrentCamera
workspace:GetPropertyChangedSignal("CurrentCamera"):Connect(function() --[[Anonymous function at line 46]]
    --[[
    Upvalues:
        [1] = u17
    --]]
    u17 = workspace.CurrentCamera
end)
local u18 = nil
v2.RenderStepped:Connect(function() --[[Anonymous function at line 52]]
    --[[
    Upvalues:
        [1] = u18
        [2] = u17
        [3] = u4
    --]]
    if u18 then
        local v19 = u17:WorldToScreenPoint(u18:GetPivot().Position + Vector3.new(0, 2, 0))
        u4.Position = UDim2.new(0, v19.X, 0, v19.Y)
    end
end)
local function u25() --[[Anonymous function at line 58]]
    --[[
    Upvalues:
        [1] = u18
        [2] = u11
        [3] = u3
        [4] = u6
        [5] = u10
        [6] = u7
    --]]
    if u18 then
        local v20 = u18:FindFirstAncestor("PetEgg")
        if v20 then
            local v21 = v20:GetAttribute("EggName")
            local v22 = v20:GetAttribute("TimeToHatch")
            local v23 = v20:GetAttribute("GrowthMultiplier")
            if v23 then
                v23 = v23 > 0
            end
            local v24 = u11[v21]
            u3:Create(u6, TweenInfo.new(0.2), {
                ["TextColor3"] = v24.Color
            }):Play()
            u7.Text = v22 == 0 and "Ready" or u10:GenerateColonFormatFromTime(v22)
            u6.Text = v21
            if v23 and v22 ~= 0 then
                u7.TextColor3 = Color3.new(0, 0.666667, 0)
            else
                u7.TextColor3 = Color3.new(1, 1, 1)
            end
        else
            return
        end
    else
        return
    end
end
task.spawn(function() --[[Anonymous function at line 84]]
    --[[
    Upvalues:
        [1] = u12
        [2] = u8
        [3] = u18
        [4] = u25
        [5] = u3
        [6] = u5
    --]]
    while true do
        local v26, v27
        repeat
            task.wait(0.25)
            local v28 = RaycastParams.new()
            v28.FilterDescendantsInstances = { u12 }
            v28.FilterType = Enum.RaycastFilterType.Include
            v26 = nil
            v27 = nil
            local v29 = game.Players.LocalPlayer.Character
        until v29 and v29:IsDescendantOf(workspace)
        local v30 = v29:GetPivot().p
        for _, v31 in game.CollectionService:GetTagged("PetEggServer") do
            if u8(v31) then
                local v32 = (v30 - v31:GetPivot().p).Magnitude
                if v32 < 9 and (v27 == nil or v32 < v26) then
                    v27 = v31:FindFirstChild("HitBox", true)
                    v26 = v32
                end
            end
        end
        if u8(v27) then
            u18 = v27
            u25()
        else
            u18 = nil
        end
        local v33 = {
            ["GroupTransparency"] = u18 and 0 or 1
        }
        u3:Create(u5, TweenInfo.new(0.25), v33):Play()
    end
end)
task.spawn(function() --[[Anonymous function at line 131]]
    --[[
    Upvalues:
        [1] = u25
    --]]
    while true do
        task.wait(1)
        u25()
    end
end)
return {}