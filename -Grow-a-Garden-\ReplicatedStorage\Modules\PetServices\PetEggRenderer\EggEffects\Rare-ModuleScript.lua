-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\PetServices\PetEggRenderer\EggEffects\Rare-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
local u2 = require(v1.Modules.SkinService)
local u3 = require(game.ReplicatedStorage.Data.PetRegistry).PetList
local u4 = {
    ["Black Bunny"] = "rbxassetid://137209605666415",
    ["<PERSON>"] = "rbxassetid://104391359688443",
    ["Caterpillar"] = "rbxassetid://74906642952986",
    ["<PERSON>"] = "rbxassetid://113898066898530",
    ["Cow"] = "rbxassetid://80626295210133",
    ["Giant Ant"] = "rbxassetid://110042917722868",
    ["<PERSON>"] = "rbxassetid://74848025639589",
    ["<PERSON>"] = "rbxassetid://88539564087126",
    ["Golden Lab"] = "rbxassetid://125173635572785",
    ["Monkey"] = "rbxassetid://83148221430148",
    ["Pig"] = "rbxassetid://110554034996387",
    ["Praying Mantis"] = "rbxassetid://80710971415313",
    ["Polar Bear"] = "rbxassetid://97880140636687",
    ["Snail"] = "rbxassetid://100791898582300",
    ["Silver Monkey"] = "rbxassetid://106847532475922",
    ["Purple Dragonfly"] = "rbxassetid://96794025089185",
    ["Turtle"] = "rbxassetid://93929754369579",
    ["Orange Tabby"] = "rbxassetid://96707297910956",
    ["Poof"] = "rbxassetid://80584356758887"
}
local function u8(p5) --[[Anonymous function at line 32]]
    --[[
    Upvalues:
        [1] = u4
    --]]
    local u6 = Instance.new("Sound")
    local v7 = u4[p5]
    if v7 then
        u6.SoundId = v7
        u6.Parent = workspace
        u6:Play()
        u6.Ended:Once(function() --[[Anonymous function at line 45]]
            --[[
            Upvalues:
                [1] = u6
            --]]
            u6:Destroy()
        end)
    else
        warn((("PetEggRenderer:RenderEgg | No sound found for %*"):format(p5)))
    end
end
return function(p9, u10, u11) --[[Anonymous function at line 52]]
    --[[
    Upvalues:
        [1] = u3
        [2] = u2
        [3] = u8
    --]]
    local u12 = script.EggExplode:Clone()
    u12:PivotTo(p9:GetPivot())
    local v13 = u12.Core
    v13.Parent = workspace.Visuals
    game.Debris:AddItem(v13, 60)
    u12.Parent = game.ReplicatedStorage
    v13.RareSFX:Play()
    task.wait(0.4)
    local u14 = p9:GetPivot()
    local u15 = p9:GetExtentsSize()
    local v16 = 0
    while v16 < 0.8 do
        v16 = v16 + game:GetService("RunService").Heartbeat:Wait()
        local v17 = 1 - v16 / 0.8
        local v18 = tick() * 90
        local v19 = v17 * math.sin(v18) * 7
        p9:PivotTo(u14 * CFrame.Angles(0, 0, (math.rad(v19))))
    end
    task.wait(0.5)
    local v20 = 0
    while v20 < 0.8 do
        v20 = v20 + game:GetService("RunService").Heartbeat:Wait()
        local v21 = 1 - v20 / 0.8
        local v22 = tick() * 90
        local v23 = v21 * math.sin(v22) * 9
        p9:PivotTo(u14 * CFrame.Angles(0, 0, (math.rad(v23))))
    end
    task.wait(0.5)
    local v24 = 0
    while v24 < 0.8 do
        v24 = v24 + game:GetService("RunService").Heartbeat:Wait()
        local v25 = 1 - v24 / 0.8
        local v26 = tick() * 90
        local v27 = v25 * math.sin(v26) * 13
        p9:PivotTo(u14 * CFrame.Angles(0, 0, (math.rad(v27))))
    end
    local v28 = p9:GetAttribute("EggColor")
    local v29 = p9:GetAttribute("EggMaterial")
    local v30 = p9:GetAttribute("EggTransparency")
    local v31 = p9:GetAttribute("EggMaterialVariant")
    p9:Destroy()
    u12.Parent = workspace.Visuals
    for _, v32 in u12:GetDescendants() do
        if v32 ~= v13 and (v32.Name ~= "RootPart" and v32:IsA("BasePart")) then
            v32.Anchored = false
        end
    end
    local u33
    if u10 and u10 ~= "" then
        local u34 = u3[u10]
        u33 = u34.Model:Clone()
        local v35 = u34.Variant
        if v35 then
            u2:SetSkin(u33, v35)
        end
        if u33.PrimaryPart then
            u33.PrimaryPart.Anchored = true
        end
        local v36 = script.Highlight:Clone()
        v36.Parent = u33
        v36.Adornee = u33
        u33.Parent = workspace.Visuals
        local u37 = 0
        task.spawn(function() --[[Anonymous function at line 156]]
            --[[
            Upvalues:
                [1] = u37
                [2] = u33
                [3] = u11
                [4] = u14
                [5] = u15
                [6] = u34
            --]]
            while u37 < 1 do
                u37 = u37 + game:GetService("RunService").Heartbeat:Wait()
                local v38 = game.TweenService:GetValue(u37 / 1, Enum.EasingStyle.Quad, Enum.EasingDirection.InOut)
                u33:ScaleTo(1 + (u11 - 1) * v38)
                u33:PivotTo(u14 * CFrame.new(0, -u15.Y / 2, 0) * CFrame.new(0, u33:GetExtentsSize().Y / 2, 0) * u34.WeldOffset)
            end
        end)
        local v39 = u34.Animations.Idle
        if v39 then
            u33.AnimationController:LoadAnimation(v39):Play()
        end
        for _, v40 in u33:GetDescendants() do
            if v40:IsA("BasePart") then
                v40.CanCollide = false
                v40.CanQuery = false
            end
        end
        game.TweenService:Create(v36, TweenInfo.new(0.5), {
            ["FillTransparency"] = 1
        }):Play()
        task.delay(0.5, function() --[[Anonymous function at line 190]]
            --[[
            Upvalues:
                [1] = u8
                [2] = u10
            --]]
            u8(u10)
        end)
    else
        u33 = nil
    end
    for _, v41 in u12:GetChildren() do
        if not v41:IsA("BasePart") then
            local v42, v43 = v41:GetBoundingBox()
            local v44 = Instance.new("Part")
            v44.Transparency = 1
            v44.CanCollide = false
            v44.CanQuery = false
            v44.CanTouch = false
            v44.CFrame = v42
            v44.Size = v43
            local v45 = Instance.new("WeldConstraint")
            v45.Part0 = v44
            v45.Part1 = v41.PrimaryPart
            v45.Parent = v44
            v44.Parent = v41
            local v46 = Instance.new("Attachment")
            local v47 = Instance.new("Attachment")
            v46.Parent = v44
            v47.Parent = v44
            local v48 = v43.Y / 2
            v46.Position = Vector3.new(0, v48, 0)
            local v49 = -v43.Y / 2
            v47.Position = Vector3.new(0, v49, 0)
            local v50 = script.Trail:Clone()
            v50.Attachment0 = v46
            v50.Attachment1 = v47
            v50.Color = ColorSequence.new(v28)
            v50.Parent = v46
            v50.Enabled = true
        end
    end
    for _, v51 in u12:GetDescendants() do
        if v51 ~= v13 and (v51.Name ~= "RootPart" and v51:IsA("BasePart")) then
            local v52 = v51.Position
            local v53 = CFrame.new(v13.Position, v52).LookVector * 16
            v51.Color = v28 or v51.Color
            v51.Material = v29 or v51.Material
            v51.MaterialVariant = v31 or v51.MaterialVariant
            v51.Transparency = v30 or v51.Transparency
            v51:ApplyImpulse(v53 * v51.AssemblyMass)
        end
    end
    task.delay(3, function() --[[Anonymous function at line 267]]
        --[[
        Upvalues:
            [1] = u12
        --]]
        for _, v54 in u12:GetChildren() do
            for _, v55 in v54:GetDescendants() do
                if v55:IsA("BasePart") then
                    game.TweenService:Create(v55, TweenInfo.new(1), {
                        ["Transparency"] = 1
                    }):Play()
                end
            end
        end
        game.Debris:AddItem(u12, 1)
    end)
    for _, v56 in v13.Attachment:GetDescendants() do
        if v56:IsA("ParticleEmitter") then
            if v56.Name == "ColorMe" then
                v56.Color = ColorSequence.new(v28)
            end
            v56:Emit(v56:GetAttribute("EmitCount"))
        end
    end
    for _, v57 in v13.Rare:GetDescendants() do
        if v57:IsA("ParticleEmitter") then
            v57.Color = ColorSequence.new(v28)
            v57:Emit(v57:GetAttribute("EmitCount"))
        end
    end
    task.wait(3)
    local v58 = game.ReplicatedStorage.Assets.EggPoof:Clone()
    v58.CFrame = u14
    v58.Parent = workspace.Visuals
    u8("Poof")
    for _, v59 in v58:GetChildren() do
        v59:Emit(v59:GetAttribute("EmitCount"))
    end
    game.Debris:AddItem(v58, 6)
    task.delay(0.2, function() --[[Anonymous function at line 312]]
        --[[
        Upvalues:
            [1] = u33
        --]]
        u33:Destroy()
    end)
end