-- Full Path: -Grow-a-Garden-\\JandelLazer-ModuleScript.lua
local v1 = {}
local u2 = false
local u3 = script.Sky
local u4 = require(game.ReplicatedStorage.Modules.SkyboxManager)
local u5 = require(game.ReplicatedStorage.Code.CameraShaker)
local u6 = game.Lighting.ColorCorrection:Clone()
u6.Name = script.Name
u6.Parent = game.Lighting
local u8 = u5.new(Enum.RenderPriority.Camera.Value, function(p7) --[[Anonymous function at line 17]]
    workspace.CurrentCamera.CFrame = workspace.CurrentCamera.CFrame * p7
end)
local function u39() --[[Anonymous function at line 22]]
    --[[
    Upvalues:
        [1] = u2
        [2] = u8
        [3] = u5
        [4] = u6
        [5] = u4
        [6] = u3
    --]]
    u2 = true
    u8:Start()
    tick()
    task.spawn(function() --[[Anonymous function at line 29]]
        --[[
        Upvalues:
            [1] = u8
            [2] = u5
            [3] = u6
        --]]
        u8:Shake(u5.Presets.Explosion)
        game.TweenService:Create(u6, TweenInfo.new(0.3), {
            ["TintColor"] = Color3.fromRGB(255, 219, 201),
            ["Brightness"] = 0.5
        }):Play()
        game.TweenService:Create(game.Workspace.CurrentCamera, TweenInfo.new(0.5), {
            ["FieldOfView"] = 90
        }):Play()
        task.wait(0.3)
        game.TweenService:Create(game.Workspace.CurrentCamera, TweenInfo.new(3), {
            ["FieldOfView"] = 70
        }):Play()
        game.TweenService:Create(u6, TweenInfo.new(3), {
            ["TintColor"] = Color3.fromRGB(255, 255, 255),
            ["Brightness"] = 0
        }):Play()
    end)
    local u9 = workspace.Visuals:WaitForChild("jandel")
    u9:GetPivot()
    local u10 = game.ReplicatedStorage.LazerEyes:Clone()
    u10.Parent = workspace.Visuals
    local u11 = game.ReplicatedStorage.Assets.beamTrail:Clone()
    u11.Parent = workspace.Visuals
    local _ = u9:GetPivot().Position
    local v12 = u10:FindFirstChild("LeftEye", true)
    local v13 = u10:FindFirstChild("RightEye", true)
    local function u27(p14) --[[Anonymous function at line 50]]
        --[[
        Upvalues:
            [1] = u10
            [2] = u9
            [3] = u11
        --]]
        local v15 = RaycastParams.new()
        v15.FilterDescendantsInstances = { workspace.TopBaseplate }
        v15.FilterType = Enum.RaycastFilterType.Include
        if workspace.Visuals:FindFirstChild("LazerSpot") then
            local v16 = u10:GetPivot():Lerp(workspace.Visuals.LazerSpot.CFrame, p14 * 5)
            local v17 = CFrame.new(v16.Position, u9:GetPivot().p)
            local v18 = u9:GetPivot()
            local v19 = CFrame.new(v18.Position, workspace.Visuals.LazerSpot.Position)
            local v20 = (u9.Head.CFrame.LookVector + CFrame.new(u9.Head.Position, v16.Position).LookVector * 150).Unit
            local v21 = (u9.Head.Position - workspace.Visuals.LazerSpot.Position).Magnitude * 1.2
            local v22 = workspace:Raycast(u9.Head.CFrame.Position, v20 * v21, v15)
            if not v22 then
                u9:PivotTo(v18:Lerp(v19, p14 * 5))
                u10:PivotTo(v17)
                for _, v23 in u11:GetChildren() do
                    if v23:IsA("Trail") then
                        v23.Enabled = false
                    end
                end
                u11.CFrame = CFrame.new(u10:GetPivot().Position) * CFrame.new(0, 0, -0.1)
                return
            end
            u9:PivotTo(v18:Lerp(v19, p14 * 5))
            u10:PivotTo(CFrame.new(v22.Position, u9:GetPivot().p) * CFrame.Angles(0, 4.71238898038469, 0))
            for _, v24 in u11:GetChildren() do
                if v24:IsA("Trail") then
                    v24.Enabled = true
                end
            end
            local v25 = u11.CFrame
            local v26 = CFrame.new(u10:GetPivot().Position, v25.Position).LookVector * Vector3.new(1, 0, 1)
            u11.CFrame = CFrame.new(u10:GetPivot().Position, u10:GetPivot().Position + v22.Normal + v26) * CFrame.new(0, 0, -0.1)
        end
    end
    for _, v28 in v12:GetDescendants() do
        if v28:IsA("Beam") then
            v28.Attachment0 = u9:FindFirstChild("LeftEye", true)
        end
    end
    for _, v29 in v13:GetDescendants() do
        if v29:IsA("Beam") then
            v29.Attachment0 = u9:FindFirstChild("RightEye", true)
        end
    end
    u27(1)
    task.spawn(function() --[[Anonymous function at line 145]]
        --[[
        Upvalues:
            [1] = u2
            [2] = u27
            [3] = u10
            [4] = u11
            [5] = u4
            [6] = u3
            [7] = u6
        --]]
        while u2 do
            u27((game:GetService("RunService").Heartbeat:Wait()))
        end
        u10:Destroy()
        game.Debris:AddItem(u11, 10)
        u4.UpdateSkybox(u3, 0)
        local v30 = game.TweenService
        local v31 = u6
        local v32 = TweenInfo.new(3)
        local v33 = {
            ["Brightness"] = 0,
            ["TintColor"] = Color3.fromRGB(255, 255, 255)
        }
        v30:Create(v31, v32, v33):Play()
        local v34 = game.TweenService
        local v35 = game.Lighting
        local v36 = TweenInfo.new(3)
        local v37 = {
            ["Ambient"] = game.Lighting:GetAttribute("DefaultAmbient"),
            ["ExposureCompensation"] = game.Lighting:GetAttribute("DefaultExposure"),
            ["Brightness"] = game.Lighting:GetAttribute("DefaultBrightness")
        }
        v34:Create(v35, v36, v37):Play()
        local v38 = {
            ["Cover"] = 0,
            ["Density"] = 0
        }
        game.TweenService:Create(game.Workspace.Terrain.Clouds, TweenInfo.new(3), v38):Play()
        task.delay(3, function() --[[Anonymous function at line 176]]
            game.Workspace.Terrain.Clouds.Enabled = false
        end)
    end)
    repeat
        task.wait()
    until u2 == false
    game.Debris:AddItem(u11, 3)
    u10:Destroy()
end
workspace:GetAttributeChangedSignal("JandelLazer"):Connect(function() --[[Anonymous function at line 191]]
    --[[
    Upvalues:
        [1] = u39
        [2] = u2
    --]]
    if workspace:GetAttribute("JandelLazer") then
        u39()
    else
        u2 = false
    end
end)
if workspace:GetAttribute("JandelLazer") then
    task.defer(u39)
else
    u2 = false
end
return v1