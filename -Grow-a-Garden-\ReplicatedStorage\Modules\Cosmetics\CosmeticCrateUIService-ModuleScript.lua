-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\Cosmetics\CosmeticCrateUIService-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
game:GetService("RunService")
game:GetService("UserInputService")
local u2 = game:GetService("TweenService")
local u3 = game:GetService("Players").LocalPlayer:WaitForChild("PlayerGui"):WaitForChild("CrateUI")
local u4 = u3.Size
local u5 = nil
local u6 = require(v1.Modules.GetFarmAncestor)
local v7 = require(v1.Modules.CreateTagHandler)
require(v1.Modules.GetMouseToWorld)
local u8 = require(v1.Modules.TimeHelper)
local u9 = require(v1.Data.CosmeticCrateRegistry).CosmeticCrates
local u10 = require(v1.Data.CosmeticRegistry.CosmeticList)
require(v1.Modules.EffectController.Libraries.TableUtil)
local u11 = {}
v7({
    ["Tag"] = "CosmeticCrateLocalHitBox",
    ["OnInstanceAdded"] = function(p12) --[[Function name: OnInstanceAdded, line 30]]
        --[[
        Upvalues:
            [1] = u11
        --]]
        local v13 = u11
        table.insert(v13, p12)
    end,
    ["OnInstanceRemoved"] = function(p14) --[[Function name: OnInstanceRemoved, line 33]]
        --[[
        Upvalues:
            [1] = u11
        --]]
        local v15 = table.find(u11, p14)
        if not v15 then
            return warn((("%* hitbox not tracked!"):format(p14.Name)))
        end
        table.remove(u11, v15)
    end
})
local u16 = workspace.CurrentCamera
workspace:GetPropertyChangedSignal("CurrentCamera"):Connect(function() --[[Anonymous function at line 44]]
    --[[
    Upvalues:
        [1] = u16
    --]]
    u16 = workspace.CurrentCamera
end)
local u17 = nil
local function u31() --[[Anonymous function at line 62]]
    --[[
    Upvalues:
        [1] = u17
        [2] = u9
        [3] = u2
        [4] = u8
    --]]
    if u17 then
        local v18 = u17:FindFirstAncestor("CosmeticCrate")
        if v18 then
            local v19 = v18:GetAttribute("CrateType")
            local v20 = v18:GetAttribute("TimeToOpen")
            local v21 = v18:GetAttribute("GrowthMultiplier")
            if v21 then
                v21 = v21 > 0
            end
            local v22 = v19 == "Mysterious Crate"
            local v23 = u17:FindFirstChild("CrateUI")
            if v23 then
                local v24 = v23:WaitForChild("Frame_UpdateTimer")
                v24.Visible = v22
                local v25 = v23:WaitForChild("Frame_WithContent")
                v25.Visible = not v22
                if v22 then
                    v25 = v24
                end
                local v26 = v25:WaitForChild("TextUI"):WaitForChild("Frame")
                v25:WaitForChild("ContentUI"):WaitForChild("ContentGrid")
                local v27 = v26:WaitForChild("2_TimerFrame")
                local v28 = v27:WaitForChild("2_CrateTimeToOpen")
                local v29
                if v22 then
                    v29 = v26:WaitForChild("1_CrateType")
                else
                    v29 = v27:WaitForChild("1_CrateType")
                end
                local v30 = u9[v19]
                u2:Create(v29, TweenInfo.new(0.2), {
                    ["TextColor3"] = v30.Color
                }):Play()
                v28.Text = v20 <= 0 and "Ready" or u8:GenerateColonFormatFromTime(v20)
                v29.Text = v19
                if v21 or v20 <= 0 then
                    v28.TextColor3 = Color3.new(0, 0.666667, 0)
                else
                    v28.TextColor3 = Color3.new(1, 1, 1)
                end
            else
                return
            end
        else
            return
        end
    else
        return
    end
end
local function u34(u32) --[[Anonymous function at line 103]]
    --[[
    Upvalues:
        [1] = u2
    --]]
    if u32 then
        local v33 = u2:Create(u32, TweenInfo.new(0.25), {
            ["Size"] = UDim2.new(0, 0)
        })
        v33:Play()
        v33.Completed:Once(function() --[[Anonymous function at line 108]]
            --[[
            Upvalues:
                [1] = u32
            --]]
            u32:Destroy()
        end)
    end
end
local function u39(p35) --[[Anonymous function at line 113]]
    for v36 = #p35, 1, -1 do
        local v37 = math.random(v36)
        local v38 = p35[v36]
        p35[v36] = p35[v37]
        p35[v37] = v38
    end
end
local function u55(p40) --[[Anonymous function at line 122]]
    --[[
    Upvalues:
        [1] = u17
        [2] = u34
        [3] = u5
        [4] = u3
        [5] = u2
        [6] = u4
        [7] = u9
        [8] = u10
        [9] = u39
        [10] = u31
    --]]
    if p40 == u17 then
        return
    end
    u17 = p40
    u34(u5)
    local v41 = u17:FindFirstAncestor("CosmeticCrate")
    if not v41 then
        return
    end
    local v42 = v41:GetAttribute("CrateType")
    local v43 = v42 == "Mysterious Crate"
    u5 = u3:Clone()
    u5.Parent = u17
    u5.Enabled = true
    u5.Size = UDim2.new(0, 0)
    local v44 = {
        ["Size"] = u4
    }
    u2:Create(u5, TweenInfo.new(0.25), v44):Play()
    local v45 = {}
    local v46 = u9[v42]
    if not v46 then
        warn((("No crate data for type: %*"):format(v42)))
        return
    end
    for _, v47 in v46.CosmeticRolls.Items do
        local v48 = u10[v47.Name]
        if v48 then
            local v49 = v48.Icon
            table.insert(v45, v49)
        end
    end
    u39(v45)
    local v50
    if v43 then
        v50 = u5.Frame_UpdateTimer.ContentUI.ContentGrid
    else
        v50 = u5.Frame_WithContent.ContentUI.ContentGrid
    end
    local v51 = v50.CosmeticTemplate
    for v52, v53 in v45 do
        if v52 > 6 then
            break
        end
        local v54 = v51:Clone()
        v54.Parent = v50
        v54.Visible = true
        v54.Image.Image = v53
    end
    u31()
end
task.spawn(function() --[[Anonymous function at line 170]]
    --[[
    Upvalues:
        [1] = u11
        [2] = u6
        [3] = u55
        [4] = u17
        [5] = u34
        [6] = u5
    --]]
    while true do
        local v56, v57
        repeat
            task.wait(0.25)
            local v58 = RaycastParams.new()
            v58.FilterDescendantsInstances = { u11 }
            v58.FilterType = Enum.RaycastFilterType.Include
            v56 = nil
            v57 = nil
            local v59 = game.Players.LocalPlayer.Character
        until v59 and v59:IsDescendantOf(workspace)
        local v60 = v59:GetPivot().p
        for _, v61 in game.CollectionService:GetTagged("CosmeticCrateServer") do
            if u6(v61) then
                local v62 = (v60 - v61:GetPivot().p).Magnitude
                if v62 < 9 and (v57 == nil or v62 < v56) then
                    v57 = v61:FindFirstChild("HitBox", true)
                    v56 = v62
                end
            end
        end
        if u6(v57) then
            u55(v57)
        else
            u17 = nil
            u34(u5)
        end
    end
end)
task.spawn(function() --[[Anonymous function at line 225]]
    --[[
    Upvalues:
        [1] = u31
    --]]
    while true do
        task.wait(1)
        u31()
    end
end)
return {}