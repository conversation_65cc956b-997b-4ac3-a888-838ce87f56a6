-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\TutorialController\Variants\Variant2-ModuleScript.lua
local u1 = game:GetService("ReplicatedStorage")
local u2 = game:GetService("Players").LocalPlayer
local u3 = require(u1.Modules.Notification)
require(u1.Modules.GetFarm)
local u4 = require(u1.Modules.TutorialController.TutorialUtils)
local u5 = require(u1.Arrow_Module)
return function() --[[Anonymous function at line 14]]
    --[[
    Upvalues:
        [1] = u5
        [2] = u2
        [3] = u4
        [4] = u3
        [5] = u1
    --]]
    local u6 = nil
    local u7 = {}
    local u23 = task.spawn(function() --[[Anonymous function at line 31]]
        --[[
        Upvalues:
            [1] = u5
            [2] = u2
            [3] = u7
            [4] = u4
            [5] = u3
            [6] = u1
            [7] = u6
        --]]
        local v8 = workspace.Tutorial_Points.Tutorial_Point_1.Position
        local u9 = u5.GenerateArrow(u2, v8, math.random(1, 1000000))
        local v10 = u7
        table.insert(v10, u9)
        local function v12() --[[Anonymous function at line 22]]
            --[[
            Upvalues:
                [1] = u7
                [2] = u9
                [3] = u5
            --]]
            local v11 = table.find(u7, u9)
            if v11 then
                u5.Remove_Arrow(u9)
                table.remove(u7, v11)
            end
        end
        local v13 = u4.waitForFarm()
        u4.waitForSeed()
        v12()
        local v14 = (v13.Spawn_Point.CFrame * CFrame.new(0, 0, -10)).Position
        local u15 = u5.GenerateArrow(u2, v14, math.random(1, 1000000))
        local v16 = u7
        table.insert(v16, u15)
        local function v18() --[[Anonymous function at line 22]]
            --[[
            Upvalues:
                [1] = u7
                [2] = u15
                [3] = u5
            --]]
            local v17 = table.find(u7, u15)
            if v17 then
                u5.Remove_Arrow(u15)
                table.remove(u7, v17)
            end
        end
        u4.waitUntilDistance(v14, 30)
        u4.waitUntilSeedEquipped()
        v18()
        u3:CreateNotification("Click on your plot to plant!")
        local v19 = u1.Tutorial_Parts.Tutorial_Click:Clone()
        v19.Size = Vector3.new(0.001, 0.001, 0.001)
        v19.Position = (v13.Spawn_Point.CFrame * CFrame.new(15, -5, -15)).Position
        v19.Parent = workspace.Click_Points
        u6 = v19
        while #v13.Important.Plants_Physical:GetChildren() <= 0 do
            task.wait(0.1)
        end
        u6 = nil
        v19:Destroy()
        u4.waitUntilSellableItem()
        local v20 = workspace.Tutorial_Points.Tutorial_Point_2.Position
        local v21 = u5.GenerateArrow(u2, v20, math.random(1, 1000000))
        local v22 = u7
        table.insert(v22, v21)
    end)
    return function() --[[Anonymous function at line 68]]
        --[[
        Upvalues:
            [1] = u23
            [2] = u6
            [3] = u7
            [4] = u5
        --]]
        if coroutine.status(u23) == "suspended" then
            task.cancel(u23)
        end
        if u6 then
            u6:Destroy()
            u6 = nil
        end
        for _, v24 in u7 do
            u5.Remove_Arrow(v24)
        end
        table.clear(u7)
    end
end