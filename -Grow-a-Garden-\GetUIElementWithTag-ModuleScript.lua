-- Full Path: -Grow-a-Garden-\\GetUIElementWithTag-ModuleScript.lua
local u1 = game:GetService("UserInputService")
local u2 = game:GetService("CollectionService")
local u3 = game:GetService("GuiService")
local u4 = game:GetService("Players").LocalPlayer:WaitF<PERSON><PERSON>hild("PlayerGui")
return function(p5) --[[Function name: GetUIElementWithTag, line 9]]
    --[[
    Upvalues:
        [1] = u2
        [2] = u1
        [3] = u3
        [4] = u4
    --]]
    local v6 = u2:GetTagged(p5)
    local v7 = u1:GetMouseLocation() - u3:GetGuiInset()
    local v8 = u4:GetGuiObjectsAtPosition(v7.X, v7.Y)
    for _, v9 in v6 do
        if v9:IsDescendantOf(u4) and table.find(v8, v9) then
            return v9
        end
    end
    return nil
end