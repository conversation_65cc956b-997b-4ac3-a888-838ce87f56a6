-- Full Path: -Grow-a-Garden-\\optional-ModuleScript.lua
local v1 = require(script.Parent.Parent.process.bufferWriter)
require(script.Parent.Parent.types)
local u2 = v1.bool
return function(p3) --[[Anonymous function at line 6]]
    --[[
    Upvalues:
        [1] = u2
    --]]
    local u4 = p3.read
    local u5 = p3.write
    return {
        ["read"] = function(p6, p7) --[[Function name: read, line 16]]
            --[[
            Upvalues:
                [1] = u4
            --]]
            if buffer.readu8(p6, p7) == 0 then
                return nil, 1
            end
            local v8, v9 = u4(p6, p7 + 1)
            return v8, v9 + 1
        end,
        ["write"] = function(p10) --[[Function name: write, line 27]]
            --[[
            Upvalues:
                [1] = u2
                [2] = u5
            --]]
            local v11 = p10 ~= nil
            u2(v11)
            if v11 then
                u5(p10)
            end
        end
    }
end