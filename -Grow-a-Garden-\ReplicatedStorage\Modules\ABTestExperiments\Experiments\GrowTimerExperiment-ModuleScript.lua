-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\ABTestExperiments\Experiments\GrowTimerExperiment-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
game:GetService("UserInputService")
game:GetService("GuiService")
local v2 = game:GetService("RunService")
require(v1.Modules.ABTestExperiments.ABTestTypes)
local v3 = {
    ["RemoteConfig"] = "GrowTimer",
    ["Disabled"] = false,
    ["DefaultState"] = v2:IsStudio()
}
local v6 = {
    [true] = {
        ["Server"] = function(p4, _) --[[Function name: Server, line 18]]
            p4:SetAttribute("AB_GrowTimer", true)
        end
    },
    [false] = {
        ["Server"] = function(p5, _) --[[Function name: Server, line 24]]
            p5:SetAttribute("AB_GrowTimer", nil)
        end
    }
}
v3.States = v6
return v3