-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\QuestsController-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
local v2 = game:GetService("Players")
require(v1.Modules.Trove)
require(v1.Modules.Remotes)
local u3 = require(v1.Modules.DataService)
require(v1.Data.QuestData)
local u4 = require(v1.Data.QuestData.Quests)
local u5 = require(v1.Data.QuestData.QuestRewards)
require(v1.Data.QuestData.Types)
local _ = v2.LocalPlayer
return {
    ["GetQuest"] = function(_, p6) --[[Function name: GetQuest, line 31]]
        --[[
        Upvalues:
            [1] = u4
        --]]
        return u4[p6]
    end,
    ["GetRewardInfo"] = function(_, p7) --[[Function name: GetRewardInfo, line 35]]
        --[[
        Upvalues:
            [1] = u5
        --]]
        return u5[p7]
    end,
    ["GetContainerFromId"] = function(_, p8) --[[Function name: GetContainerFromId, line 39]]
        --[[
        Upvalues:
            [1] = u3
        --]]
        local v9 = u3:GetData()
        if v9 then
            return v9.QuestContainers[p8]
        else
            return nil
        end
    end,
    ["GetQuestFromId"] = function(_, _) --[[Function name: GetQuestFromId, line 48]]
        --[[
        Upvalues:
            [1] = u3
        --]]
        local v10 = u3:GetData()
        if not v10 then
            return nil
        end
        for v11, v12 in v10.QuestContainers do
            for _, v13 in v12.Quests do
                if v13.Id == v11 then
                    return v13
                end
            end
        end
        return nil
    end
}