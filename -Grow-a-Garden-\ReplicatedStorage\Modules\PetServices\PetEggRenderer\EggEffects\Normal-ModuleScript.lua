-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\PetServices\PetEggRenderer\EggEffects\Normal-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
local v2 = require(game.ReplicatedStorage.Data.PetRegistry)
local u3 = require(v1.Modules.SkinService)
local u4 = v2.PetList
local u5 = {
    ["Black Bunny"] = "rbxassetid://137209605666415",
    ["<PERSON>"] = "rbxassetid://104391359688443",
    ["Caterpillar"] = "rbxassetid://74906642952986",
    ["<PERSON>"] = "rbxassetid://113898066898530",
    ["Cow"] = "rbxassetid://80626295210133",
    ["Giant Ant"] = "rbxassetid://110042917722868",
    ["<PERSON>"] = "rbxassetid://74848025639589",
    ["<PERSON>"] = "rbxassetid://88539564087126",
    ["Golden Lab"] = "rbxassetid://125173635572785",
    ["Monkey"] = "rbxassetid://83148221430148",
    ["Pig"] = "rbxassetid://110554034996387",
    ["Praying Mantis"] = "rbxassetid://80710971415313",
    ["Polar Bear"] = "rbxassetid://97880140636687",
    ["Snail"] = "rbxassetid://100791898582300",
    ["Silver Monkey"] = "rbxassetid://106847532475922",
    ["Purple Dragonfly"] = "rbxassetid://96794025089185",
    ["Turtle"] = "rbxassetid://93929754369579",
    ["Orange Tabby"] = "rbxassetid://96707297910956",
    ["Poof"] = "rbxassetid://80584356758887"
}
local function u9(p6) --[[Anonymous function at line 32]]
    --[[
    Upvalues:
        [1] = u5
    --]]
    local u7 = Instance.new("Sound")
    local v8 = u5[p6]
    if v8 then
        u7.SoundId = v8
        u7.Parent = workspace
        u7:Play()
        u7.Ended:Once(function() --[[Anonymous function at line 45]]
            --[[
            Upvalues:
                [1] = u7
            --]]
            u7:Destroy()
        end)
    else
        warn((("PetEggRenderer:RenderEgg | No sound found for %*"):format(p6)))
    end
end
return function(p10, u11, u12) --[[Anonymous function at line 52]]
    --[[
    Upvalues:
        [1] = u4
        [2] = u3
        [3] = u9
    --]]
    local u13 = script.EggExplode:Clone()
    u13:PivotTo(p10:GetPivot())
    local v14 = u13.Core
    v14.Parent = workspace.Visuals
    game.Debris:AddItem(v14, 60)
    u13.Parent = game.ReplicatedStorage
    v14.NormalSFX:Play()
    task.wait(0.4)
    local u15 = p10:GetPivot()
    local u16 = p10:GetExtentsSize()
    local v17 = 0
    while v17 < 0.8 do
        v17 = v17 + game:GetService("RunService").Heartbeat:Wait()
        local v18 = 1 - v17 / 0.8
        local v19 = tick() * 90
        local v20 = v18 * math.sin(v19) * 7
        p10:PivotTo(u15 * CFrame.Angles(0, 0, (math.rad(v20))))
    end
    task.wait(0.5)
    local v21 = 0
    while v21 < 0.8 do
        v21 = v21 + game:GetService("RunService").Heartbeat:Wait()
        local v22 = 1 - v21 / 0.8
        local v23 = tick() * 90
        local v24 = v22 * math.sin(v23) * 9
        p10:PivotTo(u15 * CFrame.Angles(0, 0, (math.rad(v24))))
    end
    task.wait(0.5)
    local v25 = 0
    while v25 < 0.8 do
        v25 = v25 + game:GetService("RunService").Heartbeat:Wait()
        local v26 = 1 - v25 / 0.8
        local v27 = tick() * 90
        local v28 = v26 * math.sin(v27) * 13
        p10:PivotTo(u15 * CFrame.Angles(0, 0, (math.rad(v28))))
    end
    local v29 = p10:GetAttribute("EggColor")
    local v30 = p10:GetAttribute("EggMaterial")
    local v31 = p10:GetAttribute("EggTransparency")
    local v32 = p10:GetAttribute("EggMaterialVariant")
    p10:Destroy()
    u13.Parent = workspace.Visuals
    for _, v33 in u13:GetDescendants() do
        if v33 ~= v14 and (v33.Name ~= "RootPart" and v33:IsA("BasePart")) then
            v33.Anchored = false
        end
    end
    local u34
    if u11 and u11 ~= "" then
        local u35 = u4[u11]
        u34 = u35.Model:Clone()
        local v36 = u35.Variant
        if v36 then
            u3:SetSkin(u34, v36)
        end
        if u34.PrimaryPart then
            u34.PrimaryPart.Anchored = true
        end
        local v37 = script.Highlight:Clone()
        v37.Parent = u34
        v37.Adornee = u34
        u34.Parent = workspace.Visuals
        local u38 = 0
        task.spawn(function() --[[Anonymous function at line 158]]
            --[[
            Upvalues:
                [1] = u38
                [2] = u34
                [3] = u12
                [4] = u15
                [5] = u16
                [6] = u35
            --]]
            while u38 < 1 do
                u38 = u38 + game:GetService("RunService").Heartbeat:Wait()
                local v39 = game.TweenService:GetValue(u38 / 1, Enum.EasingStyle.Quad, Enum.EasingDirection.InOut)
                u34:ScaleTo(1 + (u12 - 1) * v39)
                u34:PivotTo(u15 * CFrame.new(0, -u16.Y / 2, 0) * CFrame.new(0, u34:GetExtentsSize().Y / 2, 0) * u35.WeldOffset)
            end
        end)
        local v40 = u35.Animations.Idle
        if v40 then
            u34.AnimationController:LoadAnimation(v40):Play()
        end
        for _, v41 in u34:GetDescendants() do
            if v41:IsA("BasePart") then
                v41.CanCollide = false
                v41.CanQuery = false
            end
        end
        game.TweenService:Create(v37, TweenInfo.new(0.5), {
            ["FillTransparency"] = 1
        }):Play()
        task.delay(0.5, function() --[[Anonymous function at line 192]]
            --[[
            Upvalues:
                [1] = u9
                [2] = u11
            --]]
            u9(u11)
        end)
    else
        u34 = nil
    end
    for _, v42 in u13:GetDescendants() do
        if v42 ~= v14 and (v42.Name ~= "RootPart" and v42:IsA("BasePart")) then
            local v43 = v42.Position
            local v44 = CFrame.new(v14.Position, v43).LookVector * 8
            v42.Color = v29 or v42.Color
            v42.Material = v30 or v42.Material
            v42.MaterialVariant = v32 or v42.MaterialVariant
            v42.Transparency = v31 or v42.Transparency
            v42:ApplyImpulse(v44 * v42.AssemblyMass)
        end
    end
    task.delay(3, function() --[[Anonymous function at line 266]]
        --[[
        Upvalues:
            [1] = u13
        --]]
        for _, v45 in u13:GetChildren() do
            for _, v46 in v45:GetDescendants() do
                if v46:IsA("BasePart") then
                    game.TweenService:Create(v46, TweenInfo.new(1), {
                        ["Transparency"] = 1
                    }):Play()
                end
            end
        end
        game.Debris:AddItem(u13, 1)
    end)
    for _, v47 in v14.Attachment:GetDescendants() do
        if v47:IsA("ParticleEmitter") then
            v47:Emit(v47:GetAttribute("EmitCount"))
        end
    end
    task.wait(3)
    local v48 = game.ReplicatedStorage.Assets.EggPoof:Clone()
    v48.CFrame = u15
    v48.Parent = workspace.Visuals
    u9("Poof")
    for _, v49 in v48:GetChildren() do
        v49:Emit(v49:GetAttribute("EmitCount"))
    end
    game.Debris:AddItem(v48, 6)
    task.delay(0.2, function() --[[Anonymous function at line 310]]
        --[[
        Upvalues:
            [1] = u34
        --]]
        u34:Destroy()
    end)
end