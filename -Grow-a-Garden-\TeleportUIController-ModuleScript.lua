-- Full Path: -Grow-a-Garden-\\TeleportUIController-ModuleScript.lua
local v1 = game:GetService("Players")
local v2 = game:GetService("ReplicatedStorage")
local u3 = require(v2.Modules.GetFarm)
local u4 = v1.LocalPlayer
local u5 = u4.PlayerGui.Teleport_UI
local u6 = {}
local u7 = require(v2.Modules.Notification)
function u6.Move(_, p8) --[[Anonymous function at line 16]]
    --[[
    Upvalues:
        [1] = u4
    --]]
    local v9 = u4.Character
    if v9 then
        v9:PivotTo(p8)
    end
end
function u6.Start(_) --[[Anonymous function at line 25]]
    --[[
    Upvalues:
        [1] = u5
        [2] = u3
        [3] = u4
        [4] = u6
        [5] = u7
    --]]
    u5.Frame.Garden.Activated:Connect(function() --[[Anonymous function at line 26]]
        --[[
        Upvalues:
            [1] = u3
            [2] = u4
            [3] = u6
            [4] = u7
        --]]
        local v10 = u3(u4)
        if v10 == nil then
            u7:CreateNotification("You haven\'t been a assigned to a farm yet! Please be patient.")
        else
            u6:Move(v10.Spawn_Point.CFrame)
        end
    end)
    u5.Frame.Seeds.Activated:Connect(function() --[[Anonymous function at line 35]]
        --[[
        Upvalues:
            [1] = u6
        --]]
        u6:Move(workspace.Tutorial_Points.Tutorial_Point_1.CFrame)
    end)
    u5.Frame.Sell.Activated:Connect(function() --[[Anonymous function at line 39]]
        --[[
        Upvalues:
            [1] = u6
        --]]
        u6:Move(workspace.Tutorial_Points.Tutorial_Point_2.CFrame)
    end)
    u5.Frame.Gear.Activated:Connect(function() --[[Anonymous function at line 43]]
        --[[
        Upvalues:
            [1] = u6
        --]]
        u6:Move(workspace.Tutorial_Points.Tutorial_Point_3.CFrame)
    end)
    u5.Frame.Pets.Activated:Connect(function() --[[Anonymous function at line 47]]
        --[[
        Upvalues:
            [1] = u6
        --]]
        u6:Move(workspace.Tutorial_Points.Tutorial_Point_4.CFrame)
    end)
end
task.spawn(u6.Start, u6)
return u6