-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\ReplicationClass\TableListener-ModuleScript.lua
game:GetService("ReplicatedStorage")
game:GetService("HttpService")
local _ = script.Parent
local u1 = require(script:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("Signal"))
local u2 = {}
u2.__index = u2
function u2.new(p3) --[[Anonymous function at line 11]]
    --[[
    Upvalues:
        [1] = u2
    --]]
    local v4 = u2
    return setmetatable({
        ["Table"] = p3,
        ["PathRegistry"] = {}
    }, v4)
end
function u2.Destroy(p5) --[[Anonymous function at line 20]]
    for _, v6 in p5.PathRegistry do
        v6:Destroy()
    end
end
function u2.GetTable(p7) --[[Anonymous function at line 26]]
    return p7.Table
end
function u2.FindPathSignal(p8, p9) --[[Anonymous function at line 30]]
    local v10 = p8.PathRegistry
    if v10[p9] then
        return v10[p9]
    end
end
function u2.FreePathSignal(p11, p12) --[[Anonymous function at line 36]]
    p11.PathRegistry[p12] = nil
end
function u2.GetPathSignal(p13, p14) --[[Anonymous function at line 41]]
    --[[
    Upvalues:
        [1] = u1
    --]]
    local v15 = p13:FindPathSignal(p14)
    if v15 then
        return v15
    end
    local v16 = p13.PathRegistry
    local v17 = u1.new()
    v16[("%*"):format(p14)] = v17
    return v17
end
return u2