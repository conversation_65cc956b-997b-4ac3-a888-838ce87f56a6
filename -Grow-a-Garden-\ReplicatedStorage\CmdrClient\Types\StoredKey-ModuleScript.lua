-- Full Path: -Grow-a-Garden-\ReplicatedStorage\CmdrClient\Types\StoredKey-ModuleScript.lua
local u1 = require(script.Parent.Parent.Shared.Util)
local u2 = {
    "^%a[%w_]*$",
    "^%$%a[%w_]*$",
    "^%.%a[%w_]*$",
    "^%$%.%a[%w_]*$"
}
return function(u3) --[[Anonymous function at line 10]]
    --[[
    Upvalues:
        [1] = u2
        [2] = u1
    --]]
    local v8 = {
        ["Autocomplete"] = function(p4) --[[Function name: Autocomplete, line 12]]
            --[[
            Upvalues:
                [1] = u3
            --]]
            return u3.Cmdr.Util.MakeFuzzyFinder(u3.Cmdr.Util.DictionaryKeys(u3:GetStore("vars_used") or {}))(p4)
        end,
        ["Validate"] = function(p5) --[[Function name: Validate, line 18]]
            --[[
            Upvalues:
                [1] = u2
            --]]
            for _, v6 in ipairs(u2) do
                if p5:match(v6) then
                    return true
                end
            end
            return false, "Key names must start with an optional modifier: . $ or $. and must begin with a letter."
        end,
        ["Parse"] = function(p7) --[[Function name: Parse, line 28]]
            return p7
        end
    }
    u3:RegisterType("storedKey", v8)
    u3:RegisterType("storedKeys", u1.MakeListableType(v8))
end