-- Full Path: -Grow-a-Garden-\ReplicatedStorage\CmdrClient\Shared\Registry-ModuleScript.lua
local u1 = game:GetService("RunService")
local u2 = require(script.Parent.Util)
local u19 = {
    ["TypeMethods"] = u2.MakeDictionary({
        "Transform",
        "Validate",
        "Autocomplete",
        "Parse",
        "DisplayName",
        "Listable",
        "ValidateOnce",
        "Prefixes",
        "Default",
        "ArgumentOperatorAliases"
    }),
    ["CommandMethods"] = u2.MakeDictionary({
        "Name",
        "Aliases",
        "AutoExec",
        "Description",
        "Args",
        "Run",
        "ClientRun",
        "Data",
        "Group"
    }),
    ["CommandArgProps"] = u2.MakeDictionary({
        "Name",
        "Type",
        "Description",
        "Optional",
        "Default"
    }),
    ["Types"] = {},
    ["TypeAliases"] = {},
    ["Commands"] = {},
    ["CommandsArray"] = {},
    ["Cmdr"] = nil,
    ["Hooks"] = {
        ["BeforeRun"] = {},
        ["AfterRun"] = {}
    },
    ["Stores"] = setmetatable({}, {
        ["__index"] = function(p3, p4) --[[Function name: __index, line 20]]
            p3[p4] = {}
            return p3[p4]
        end
    }),
    ["AutoExecBuffer"] = {},
    ["RegisterType"] = function(p5, p6, p7) --[[Function name: RegisterType, line 30]]
        if not p6 or typeof(p6) ~= "string" then
            error("Invalid type name provided: nil")
        end
        if not p6:find("^[%d%l]%w*$") then
            error(("Invalid type name provided: \"%s\", type names must be alphanumeric and start with a lower-case letter or a digit."):format(p6))
        end
        for v8 in pairs(p7) do
            if p5.TypeMethods[v8] == nil then
                error("Unknown key/method in type \"" .. p6 .. "\": " .. v8)
            end
        end
        if p5.Types[p6] ~= nil then
            error(("Type \"%s\" has already been registered."):format(p6))
        end
        p7.Name = p6
        p7.DisplayName = p7.DisplayName or p6
        p5.Types[p6] = p7
        if p7.Prefixes then
            p5:RegisterTypePrefix(p6, p7.Prefixes)
        end
    end,
    ["RegisterTypePrefix"] = function(p9, p10, p11) --[[Function name: RegisterTypePrefix, line 59]]
        if not p9.TypeAliases[p10] then
            p9.TypeAliases[p10] = p10
        end
        p9.TypeAliases[p10] = ("%s %s"):format(p9.TypeAliases[p10], p11)
    end,
    ["RegisterTypeAlias"] = function(p12, p13, p14) --[[Function name: RegisterTypeAlias, line 67]]
        local v15 = p12.TypeAliases[p13] == nil
        assert(v15, ("Type alias %s already exists!"):format(p14))
        p12.TypeAliases[p13] = p14
    end,
    ["RegisterTypesIn"] = function(p16, p17) --[[Function name: RegisterTypesIn, line 73]]
        for _, v18 in pairs(p17:GetChildren()) do
            if v18:IsA("ModuleScript") then
                v18.Parent = p16.Cmdr.ReplicatedRoot.Types
                require(v18)(p16)
            else
                p16:RegisterTypesIn(v18)
            end
        end
    end
}
u19.RegisterHooksIn = u19.RegisterTypesIn
function u19.RegisterCommandObject(p20, p21, _) --[[Anonymous function at line 90]]
    --[[
    Upvalues:
        [1] = u1
    --]]
    for v22 in pairs(p21) do
        if p20.CommandMethods[v22] == nil then
            error("Unknown key/method in command " .. (p21.Name or "unknown command") .. ": " .. v22)
        end
    end
    if p21.Args then
        for v23, v24 in pairs(p21.Args) do
            if type(v24) == "table" then
                for v25 in pairs(v24) do
                    if p20.CommandArgProps[v25] == nil then
                        error(("Unknown property in command \"%s\" argument #%d: %s"):format(p21.Name or "unknown", v23, v25))
                    end
                end
            end
        end
    end
    if p21.AutoExec and u1:IsClient() then
        local v26 = p20.AutoExecBuffer
        local v27 = p21.AutoExec
        table.insert(v26, v27)
        p20:FlushAutoExecBufferDeferred()
    end
    local v28 = p20.Commands[p21.Name:lower()]
    if v28 and v28.Aliases then
        for _, v29 in pairs(v28.Aliases) do
            p20.Commands[v29:lower()] = nil
        end
    elseif not v28 then
        local v30 = p20.CommandsArray
        table.insert(v30, p21)
    end
    p20.Commands[p21.Name:lower()] = p21
    if p21.Aliases then
        for _, v31 in pairs(p21.Aliases) do
            p20.Commands[v31:lower()] = p21
        end
    end
end
function u19.RegisterCommand(p32, p33, p34, p35) --[[Anonymous function at line 135]]
    --[[
    Upvalues:
        [1] = u1
    --]]
    local v36 = require(p33)
    local v37 = typeof(v36) == "table"
    local v38 = ("Invalid return value from command script \"%*\" (CommandDefinition expected, got %*)"):format(p33.Name, (typeof(v36)))
    assert(v37, v38)
    if p34 then
        local v39 = u1:IsServer()
        assert(v39, "The commandServerScript parameter is not valid for client usage.")
        v36.Run = require(p34)
    end
    if not p35 or p35(v36) then
        p32:RegisterCommandObject(v36)
        p33.Parent = p32.Cmdr.ReplicatedRoot.Commands
    end
end
function u19.RegisterCommandsIn(p40, p41, p42) --[[Anonymous function at line 157]]
    local v43 = {}
    local v44 = {}
    for _, v45 in pairs(p41:GetChildren()) do
        if v45:IsA("ModuleScript") then
            if v45.Name:find("Server") then
                v43[v45] = true
            else
                local v46 = p41:FindFirstChild(v45.Name .. "Server")
                if v46 then
                    v44[v46] = true
                end
                p40:RegisterCommand(v45, v46, p42)
            end
        else
            p40:RegisterCommandsIn(v45, p42)
        end
    end
    for v47 in pairs(v43) do
        if not v44[v47] then
            warn("Command script " .. v47.Name .. " was skipped because it has \'Server\' in its name, and has no equivalent shared script.")
        end
    end
end
function u19.RegisterDefaultCommands(p48, u49) --[[Anonymous function at line 187]]
    --[[
    Upvalues:
        [1] = u1
        [2] = u2
    --]]
    local v50 = u1:IsServer()
    assert(v50, "RegisterDefaultCommands cannot be called from the client.")
    local v51 = type(u49) == "table"
    if v51 then
        u49 = u2.MakeDictionary(u49)
    end
    p48:RegisterCommandsIn(p48.Cmdr.DefaultCommandsFolder, v51 and function(p52) --[[Anonymous function at line 196]]
        --[[
        Upvalues:
            [1] = u49
        --]]
        return u49[p52.Group] or false
    end or u49)
end
function u19.GetCommand(p53, p54) --[[Anonymous function at line 202]]
    return p53.Commands[(p54 or ""):lower()]
end
function u19.GetCommands(p55) --[[Anonymous function at line 208]]
    return p55.CommandsArray
end
function u19.GetCommandNames(p56) --[[Anonymous function at line 213]]
    local v57 = {}
    for _, v58 in pairs(p56.CommandsArray) do
        local v59 = v58.Name
        table.insert(v57, v59)
    end
    return v57
end
u19.GetCommandsAsStrings = u19.GetCommandNames
function u19.GetTypeNames(p60) --[[Anonymous function at line 226]]
    local v61 = {}
    for v62 in pairs(p60.Types) do
        table.insert(v61, v62)
    end
    return v61
end
function u19.GetType(p63, p64) --[[Anonymous function at line 238]]
    return p63.Types[p64]
end
function u19.GetTypeName(p65, p66) --[[Anonymous function at line 243]]
    return p65.TypeAliases[p66] or p66
end
function u19.RegisterHook(p67, p68, p69, p70) --[[Anonymous function at line 248]]
    if not p67.Hooks[p68] then
        error(("Invalid hook name: %q"):format(p68), 2)
    end
    local v71 = p67.Hooks[p68]
    table.insert(v71, {
        ["callback"] = p69,
        ["priority"] = p70 or 0
    })
    table.sort(p67.Hooks[p68], function(p72, p73) --[[Anonymous function at line 254]]
        return p72.priority < p73.priority
    end)
end
u19.AddHook = u19.RegisterHook
function u19.GetStore(p74, p75) --[[Anonymous function at line 262]]
    return p74.Stores[p75]
end
function u19.FlushAutoExecBufferDeferred(u76) --[[Anonymous function at line 267]]
    --[[
    Upvalues:
        [1] = u1
    --]]
    if not u76.AutoExecFlushConnection then
        u76.AutoExecFlushConnection = u1.Heartbeat:Connect(function() --[[Anonymous function at line 272]]
            --[[
            Upvalues:
                [1] = u76
            --]]
            u76.AutoExecFlushConnection:Disconnect()
            u76.AutoExecFlushConnection = nil
            u76:FlushAutoExecBuffer()
        end)
    end
end
function u19.FlushAutoExecBuffer(p77) --[[Anonymous function at line 280]]
    for _, v78 in ipairs(p77.AutoExecBuffer) do
        for _, v79 in ipairs(v78) do
            p77.Cmdr.Dispatcher:EvaluateAndRun(v79)
        end
    end
    p77.AutoExecBuffer = {}
end
return function(p80) --[[Anonymous function at line 290]]
    --[[
    Upvalues:
        [1] = u19
    --]]
    u19.Cmdr = p80
    return u19
end