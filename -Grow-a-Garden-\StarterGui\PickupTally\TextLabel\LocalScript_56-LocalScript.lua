-- Full Path: -Grow-a-Garden-\StarterGui\PickupTally\TextLabel\LocalScript_56-LocalScript.lua
local v1 = game:GetService("Players")
local u2 = game:GetService("TweenService")
game:GetService("Debris")
game:GetService("UserInputService")
game:GetService("SoundService")
local v3 = game:GetService("ReplicatedStorage")
local v4 = v1.LocalPlayer
v4:WaitForChild("leaderstats")
require(v3.Modules.DataService)
local u5 = script.Parent
local u6 = u5.val
local u7 = TweenInfo.new(0.7, Enum.EasingStyle.Quad, Enum.EasingDirection.Out)
game.ReplicatedStorage:WaitForChild("Appear_Effect")
local u8 = require(game.ReplicatedStorage:WaitForChild("Comma_Module"))
u6:GetPropertyChangedSignal("Value"):Connect(function() --[[Anonymous function at line 60]]
    --[[
    Upvalues:
        [1] = u5
        [2] = u8
        [3] = u6
    --]]
    local v9 = u5
    local v10 = u8.Comma
    local v11 = u6.Value
    local v12 = math.round(v11)
    v9.Text = "x" .. v10((tostring(v12)))
end)
local u13 = u2:Create(u5, TweenInfo.new(0.7), {
    ["TextTransparency"] = 1
})
local u14 = u2:Create(u5.UIStroke, TweenInfo.new(0.7), {
    ["Transparency"] = 1
})
local u15 = v4.PlayerGui:WaitForChild("BackpackGui"):WaitForChild("Backpack"):WaitForChild("Hotbar")
u6:GetAttributeChangedSignal("ActualValue"):Connect(function() --[[Anonymous function at line 75]]
    --[[
    Upvalues:
        [1] = u13
        [2] = u14
        [3] = u5
        [4] = u6
        [5] = u2
        [6] = u7
        [7] = u15
    --]]
    u13:Cancel()
    u14:Cancel()
    u5.TextTransparency = 0
    u5.UIStroke.Transparency = 0
    if os.time() - u6:GetAttribute("LastUpdate") > 2 then
        u6.Value = u6:GetAttribute("ActualValue")
    else
        u2:Create(u6, u7, {
            ["Value"] = u6:GetAttribute("ActualValue")
        }):Play()
    end
    local v16 = u15.AbsolutePosition + Vector2.new(u15.AbsoluteSize.X, u15.AbsoluteSize.Y)
    u5.Position = UDim2.fromOffset(v16.X, v16.Y - 10)
    local u17 = u6:GetAttribute("ActualValue")
    task.delay(2.7, function() --[[Anonymous function at line 92]]
        --[[
        Upvalues:
            [1] = u6
            [2] = u17
            [3] = u13
            [4] = u14
        --]]
        if u6.Value == u17 then
            u13:Play()
            u14:Play()
        end
    end)
end)