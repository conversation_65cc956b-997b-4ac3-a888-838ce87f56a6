-- Full Path: -Grow-a-Garden-\\CalculatePetValue-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
local v2 = game:GetService("ServerScriptService")
local u3 = game:GetService("RunService")
local u4 = game:GetService("Players")
require(v1.Item_Module)
local u5 = require(v1.Modules.PetServices.PetUtilities)
local u6 = require(v1.Data.PetRegistry)
local u7 = require(v1.Modules.NumberUtil)
local u8 = u3:IsServer()
if u8 then
    u8 = require(v2.Modules.PetsServices.PetsService)
end
local u9 = u3:IsClient()
if u9 then
    u9 = require(v1.Modules.DataService)
end
return function(p10) --[[Function name: CalculatePetValue, line 14]]
    --[[
    Upvalues:
        [1] = u4
        [2] = u3
        [3] = u8
        [4] = u9
        [5] = u6
        [6] = u7
        [7] = u5
    --]]
    if not p10 then
        warn("CalculatePetValue | No Pet Tool given!")
        return 0
    end
    local v11 = u4:GetPlayerFromCharacter((p10:FindFirstAncestorWhichIsA("Model")))
    if not v11 then
        warn("CalculatePetValue | No Player Ancestor found!")
        return 0
    end
    local v12 = p10:GetAttribute("ItemType")
    if not v12 or v12 ~= "Pet" then
        warn("CalculatePetValue | ItemType attribute not \"Pet\"!")
    end
    local v13 = p10:GetAttribute("PET_UUID")
    if not v13 then
        warn("CalculatePetValue | No UUID!")
        return 0
    end
    local v14
    if u3:IsServer() then
        v14 = u8:GetPlayerPetData(v11, v13)
    else
        v14 = u9:GetData().PetsData.PetInventory.Data[v13]
    end
    if not v14 then
        warn("CalculatePetValue | No pet data found!")
        return 0
    end
    local v15 = v14.PetData.HatchedFrom
    if not v15 or v15 == "" then
        warn("CalculatePetValue | No HatchedFrom value!")
        return 0
    end
    local v16 = u6.PetEggs[v15]
    if not v16 then
        warn("CalculatePetValue | No egg data found!")
        return 0
    end
    local v17 = v16.RarityData.Items[v14.PetType]
    if not v17 then
        warn("CalculatePetValue | No pet data in egg!")
        return 0
    end
    local v18 = v17.GeneratedPetData.WeightRange
    if not v18 then
        warn("CalculatePetValue | No WeightRange found!")
        return 0
    end
    local v19 = u7.ReverseLerp(v18[1], v18[2], v14.PetData.BaseWeight)
    local v20 = math.lerp(0.8, 1.2, v19)
    local v21 = u5:GetLevelProgress(v14.PetData.Level)
    local v22 = v20 * math.lerp(0.15, 6, v21)
    local v23 = u6.PetList[v14.PetType].SellPrice * v22
    return math.floor(v23)
end