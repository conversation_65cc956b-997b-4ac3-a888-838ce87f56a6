-- Full Path: -Grow-a-Garden-\\LocalScript_41-LocalScript.lua
local v1 = game:GetService("TweenService")
local v2 = TweenInfo.new(0.2, Enum.EasingStyle.Quad, Enum.EasingDirection.Out, 0, false, 0)
local v3 = script.Parent.Parent.Frame.Text_Element.UIPadding
local u4 = v1:Create(v3, v2, {
    ["PaddingLeft"] = UDim.new(0.04, 0)
})
local u5 = v1:Create(v3, v2, {
    ["PaddingLeft"] = UDim.new(0, 0)
})
local v6 = script.Parent.Parent.ImageLabel
local v7 = script.Parent.Parent.ImageLabel.ImageTransparency
local u8 = v1:Create(v6, v2, {
    ["ImageTransparency"] = 0
})
local u9 = v1:Create(v6, v2, {
    ["ImageTransparency"] = v7
})
local u10 = game.SoundService.Hover
script.Parent.MouseEnter:Connect(function() --[[Anonymous function at line 12]]
    --[[
    Upvalues:
        [1] = u10
        [2] = u4
        [3] = u8
    --]]
    u10.PlaybackSpeed = 1 + math.random(-5, 5) / 100
    u10.Playing = true
    u10.TimePosition = 0
    u4:Play()
    u8:Play()
end)
script.Parent.MouseLeave:Connect(function() --[[Anonymous function at line 19]]
    --[[
    Upvalues:
        [1] = u5
        [2] = u9
    --]]
    u5:Play()
    u9:Play()
end)