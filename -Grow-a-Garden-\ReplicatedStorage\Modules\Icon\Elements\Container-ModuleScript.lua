-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\Icon\Elements\Container-ModuleScript.lua
return function(u1) --[[Anonymous function at line 1]]
    local v2 = game:GetService("GuiService")
    local v3 = u1.isOldTopbar
    local v4 = {}
    local v5 = v2:GetGuiInset()
    local v6 = v2:IsTenFootInterface()
    local v7 = v6 and 10 or (v3 and 12 or v5.Y - 46)
    local u8 = Instance.new("ScreenGui")
    u8:SetAttribute("StartInset", v7)
    u8.Name = "TopbarStandard"
    u8.Enabled = true
    u8.ZIndexBehavior = Enum.ZIndexBehavior.Sibling
    u8.IgnoreGuiInset = true
    u8.ResetOnSpawn = false
    u8.ScreenInsets = Enum.ScreenInsets.TopbarSafeInsets
    v4[u8.Name] = u8
    u8.DisplayOrder = u1.baseDisplayOrder
    u1.baseDisplayOrderChanged:Connect(function() --[[Anonymous function at line 22]]
        --[[
        Upvalues:
            [1] = u8
            [2] = u1
        --]]
        u8.DisplayOrder = u1.baseDisplayOrder
    end)
    local v9 = Instance.new("Frame")
    local v10 = v3 and 2 or 0
    local u11
    if v6 then
        v10 = v10 + 13
        u11 = 50
    else
        u11 = -2
    end
    v9.Name = "Holders"
    v9.BackgroundTransparency = 1
    v9.Position = UDim2.new(0, 0, 0, v10)
    v9.Size = UDim2.new(1, 0, 1, u11)
    v9.Visible = true
    v9.ZIndex = 1
    v9.Parent = u8
    local u12 = u8:Clone()
    local u13 = u12.Holders
    local u14 = game:GetService("GuiService")
    local function v15() --[[Anonymous function at line 44]]
        --[[
        Upvalues:
            [1] = u13
            [2] = u14
            [3] = u11
        --]]
        u13.Size = UDim2.new(1, 0, 0, u14.TopbarInset.Height + u11)
    end
    u12.Name = "TopbarCentered"
    u12.ScreenInsets = Enum.ScreenInsets.None
    u1.baseDisplayOrderChanged:Connect(function() --[[Anonymous function at line 49]]
        --[[
        Upvalues:
            [1] = u12
            [2] = u1
        --]]
        u12.DisplayOrder = u1.baseDisplayOrder
    end)
    v4[u12.Name] = u12
    u14:GetPropertyChangedSignal("TopbarInset"):Connect(v15)
    u13.Size = UDim2.new(1, 0, 0, u14.TopbarInset.Height + u11)
    local u16 = u8:Clone()
    u16.Name = u16.Name .. "Clipped"
    u16.DisplayOrder = u16.DisplayOrder + 1
    u1.baseDisplayOrderChanged:Connect(function() --[[Anonymous function at line 59]]
        --[[
        Upvalues:
            [1] = u16
            [2] = u1
        --]]
        u16.DisplayOrder = u1.baseDisplayOrder + 1
    end)
    v4[u16.Name] = u16
    local u17 = u12:Clone()
    u17.Name = u17.Name .. "Clipped"
    u17.DisplayOrder = u17.DisplayOrder + 1
    u1.baseDisplayOrderChanged:Connect(function() --[[Anonymous function at line 67]]
        --[[
        Upvalues:
            [1] = u17
            [2] = u1
        --]]
        u17.DisplayOrder = u1.baseDisplayOrder + 1
    end)
    v4[u17.Name] = u17
    if v3 then
        task.defer(function() --[[Anonymous function at line 73]]
            --[[
            Upvalues:
                [1] = u14
                [2] = u1
            --]]
            local function v18() --[[Anonymous function at line 74]]
                --[[
                Upvalues:
                    [1] = u14
                    [2] = u1
                --]]
                if u14.MenuIsOpen then
                    u1.setTopbarEnabled(false, true)
                else
                    u1.setTopbarEnabled()
                end
            end
            u14:GetPropertyChangedSignal("MenuIsOpen"):Connect(v18)
            if u14.MenuIsOpen then
                u1.setTopbarEnabled(false, true)
            else
                u1.setTopbarEnabled()
            end
        end)
    end
    local v19 = Instance.new("ScrollingFrame")
    v19:SetAttribute("IsAHolder", true)
    v19.Name = "Left"
    v19.Position = UDim2.fromOffset(v7, 0)
    v19.Size = UDim2.new(1, -24, 1, 0)
    v19.BackgroundTransparency = 1
    v19.Visible = true
    v19.ZIndex = 1
    v19.Active = false
    v19.ClipsDescendants = true
    v19.HorizontalScrollBarInset = Enum.ScrollBarInset.None
    v19.CanvasSize = UDim2.new(0, 0, 1, -1)
    v19.AutomaticCanvasSize = Enum.AutomaticSize.X
    v19.ScrollingDirection = Enum.ScrollingDirection.X
    v19.ScrollBarThickness = 0
    v19.BorderSizePixel = 0
    v19.Selectable = false
    v19.ScrollingEnabled = false
    v19.ElasticBehavior = Enum.ElasticBehavior.Never
    v19.Parent = v9
    local v20 = Instance.new("UIListLayout")
    v20.Padding = UDim.new(0, v7)
    v20.FillDirection = Enum.FillDirection.Horizontal
    v20.SortOrder = Enum.SortOrder.LayoutOrder
    v20.VerticalAlignment = Enum.VerticalAlignment.Bottom
    v20.HorizontalAlignment = Enum.HorizontalAlignment.Left
    v20.Parent = v19
    local v21 = v19:Clone()
    v21.ScrollingEnabled = false
    v21.UIListLayout.HorizontalAlignment = Enum.HorizontalAlignment.Center
    v21.Name = "Center"
    v21.Parent = u13
    local v22 = v19:Clone()
    v22.UIListLayout.HorizontalAlignment = Enum.HorizontalAlignment.Right
    v22.Name = "Right"
    v22.AnchorPoint = Vector2.new(1, 0)
    v22.Position = UDim2.new(1, -12, 0, 0)
    v22.Parent = v9
    return v4
end