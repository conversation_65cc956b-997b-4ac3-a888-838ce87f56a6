-- Full Path: -Grow-a-Garden-\\hover-ModuleScript.lua
local u1 = game:GetService("Players")
return {
    ["Name"] = "hover",
    ["Description"] = "Returns the name of the player you are hovering over.",
    ["Group"] = "DefaultUtil",
    ["Args"] = {},
    ["ClientRun"] = function() --[[Function name: ClientR<PERSON>, line 9]]
        --[[
        Upvalues:
            [1] = u1
        --]]
        local v2 = u1.LocalPlayer:GetMouse().Target
        if not v2 then
            return ""
        end
        local v3 = u1:GetPlayerFromCharacter(v2:FindFirstAncestorOfClass("Model"))
        return v3 and v3.Name or ""
    end
}