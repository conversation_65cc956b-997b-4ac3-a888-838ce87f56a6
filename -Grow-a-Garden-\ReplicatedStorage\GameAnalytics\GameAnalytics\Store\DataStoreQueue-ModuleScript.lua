-- Full Path: -Grow-a-Garden-\ReplicatedStorage\GameAnalytics\GameAnalytics\Store\DataStoreQueue-ModuleScript.lua
local u1 = {
    ["QR"] = true,
    ["Queue"] = {},
    ["Process"] = 0
}
local u2 = {}
task.spawn(function() --[[Anonymous function at line 7]]
    --[[
    Upvalues:
        [1] = u1
        [2] = u2
    --]]
    while u1.QR do
        task.wait()
        if #u1.Queue > 0 then
            local u3 = u1.Queue[1]
            table.remove(u1.Queue, 1)
            if not u2[u3.Key] then
                u2[u3.Key] = 0
            end
            local v4 = u1
            v4.Process = v4.Process + 1
            local v5 = u3.Delay + u2[u3.Key] - DateTime.now().UnixTimestamp
            local v6 = v5 <= 0 and 0 or v5
            task.delay(v6, function() --[[Anonymous function at line 21]]
                --[[
                Upvalues:
                    [1] = u2
                    [2] = u3
                    [3] = u1
                --]]
                while true do
                    u2[u3.Key] = DateTime.now().UnixTimestamp
                    local v7, v8, v9 = pcall(u3.Func)
                    if not v7 then
                        warn(v8)
                    end
                    if v7 and v8 or not u3.Delay then
                        break
                    end
                    task.wait(u3.Delay)
                    if v7 and v8 then
                        break
                    end
                end
                u3.Event:Fire(v7, v8, v9)
                local v10 = u1
                v10.Process = v10.Process - 1
                u2[u3.Key] = DateTime.now().UnixTimestamp
            end)
        end
    end
end)
function u1.AddRequest(p11, p12, p13) --[[Anonymous function at line 40]]
    --[[
    Upvalues:
        [1] = u1
    --]]
    local v14 = Instance.new("BindableEvent")
    local v15 = u1.Queue
    table.insert(v15, {
        ["Key"] = p11,
        ["Delay"] = p13,
        ["Func"] = p12,
        ["Event"] = v14
    })
    local v16, v17, v18 = v14.Event:Wait()
    return v16, v17, v18
end
function u1.RemoveKey(p19) --[[Anonymous function at line 54]]
    --[[
    Upvalues:
        [1] = u2
    --]]
    u2[p19] = nil
end
return u1