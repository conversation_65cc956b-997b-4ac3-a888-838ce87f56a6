-- Full Path: -Grow-a-Garden-\\Icon-ModuleScript.lua
game:GetService("LocalizationService")
local u1 = game:GetService("UserInputService")
game:GetService("RunService")
game:GetService("TextService")
local u2 = game:GetService("StarterGui")
local v3 = game:GetService("GuiService")
local v4 = game:GetService("Players")
local u5 = script
local v6 = require(u5.Reference)
local v7 = v6.getObject()
local v8
if v7 then
    v8 = v7.Value
else
    v8 = v7
end
if v8 and v8 ~= u5 then
    return require(v8)
end
if not v7 then
    v6.addToReplicatedStorage()
end
local u9 = require(u5.Packages.GoodSignal)
local u10 = require(u5.Packages.Janitor)
local u11 = require(u5.Utility)
require(u5.Attribute)
local u12 = require(u5.Features.Themes)
local v13 = require(u5.Features.Gamepad)
local v14 = require(u5.Features.Overflow)
local u15 = {}
u15.__index = u15
local v16 = v4.LocalPlayer
local v17 = u5.Features.Themes
local v18 = v16:WaitForChild("PlayerGui")
local u19 = {}
local u20 = u9.new()
local u21 = u5.Elements
local u22 = 0
if v3.TopbarInset.Height == 0 then
    v3:GetPropertyChangedSignal("TopbarInset"):Wait()
end
u15.baseDisplayOrderChanged = u9.new()
u15.baseDisplayOrder = 10
u15.baseTheme = require(v17.Default)
u15.isOldTopbar = v3.TopbarInset.Height == 36
u15.iconsDictionary = u19
u15.container = require(u21.Container)(u15)
u15.topbarEnabled = true
u15.iconAdded = u9.new()
u15.iconRemoved = u9.new()
u15.iconChanged = u9.new()
function u15.getIcons() --[[Anonymous function at line 112]]
    --[[
    Upvalues:
        [1] = u15
    --]]
    return u15.iconsDictionary
end
function u15.getIconByUID(p23) --[[Anonymous function at line 116]]
    --[[
    Upvalues:
        [1] = u15
    --]]
    local v24 = u15.iconsDictionary[p23]
    if v24 then
        return v24
    end
end
function u15.getIcon(p25) --[[Anonymous function at line 123]]
    --[[
    Upvalues:
        [1] = u15
        [2] = u19
    --]]
    local v26 = u15.getIconByUID(p25)
    if v26 then
        return v26
    end
    for _, v27 in pairs(u19) do
        if v27.name == p25 then
            return v27
        end
    end
end
function u15.setTopbarEnabled(p28, p29) --[[Anonymous function at line 135]]
    --[[
    Upvalues:
        [1] = u15
    --]]
    if typeof(p28) ~= "boolean" then
        p28 = u15.topbarEnabled
    end
    if not p29 then
        u15.topbarEnabled = p28
    end
    for _, v30 in pairs(u15.container) do
        v30.Enabled = p28
    end
end
function u15.modifyBaseTheme(p31) --[[Anonymous function at line 147]]
    --[[
    Upvalues:
        [1] = u12
        [2] = u15
        [3] = u19
    --]]
    local v32 = u12.getModifications(p31)
    for _, v33 in pairs(v32) do
        for _, v34 in pairs(u15.baseTheme) do
            u12.merge(v34, v33)
        end
    end
    for _, v35 in pairs(u19) do
        v35:setTheme(u15.baseTheme)
    end
end
function u15.setDisplayOrder(p36) --[[Anonymous function at line 159]]
    --[[
    Upvalues:
        [1] = u15
    --]]
    u15.baseDisplayOrder = p36
    u15.baseDisplayOrderChanged:Fire(p36)
end
task.defer(v13.start, u15)
task.defer(v14.start, u15)
for _, v37 in pairs(u15.container) do
    v37.Parent = v18
end
if u15.isOldTopbar then
    u15.modifyBaseTheme(require(v17.Classic))
end
function u15.new() --[[Anonymous function at line 179]]
    --[[
    Upvalues:
        [1] = u15
        [2] = u10
        [3] = u11
        [4] = u19
        [5] = u9
        [6] = u5
        [7] = u21
        [8] = u22
        [9] = u1
        [10] = u20
        [11] = u2
    --]]
    local u38 = {}
    local v39 = u15
    setmetatable(u38, v39)
    local v40 = u10.new()
    u38.janitor = v40
    u38.themesJanitor = v40:add(u10.new())
    u38.singleClickJanitor = v40:add(u10.new())
    u38.captionJanitor = v40:add(u10.new())
    u38.joinJanitor = v40:add(u10.new())
    u38.menuJanitor = v40:add(u10.new())
    u38.dropdownJanitor = v40:add(u10.new())
    local u41 = u11.generateUID()
    u19[u41] = u38
    v40:add(function() --[[Anonymous function at line 196]]
        --[[
        Upvalues:
            [1] = u19
            [2] = u41
        --]]
        u19[u41] = nil
    end)
    u38.selected = v40:add(u9.new())
    u38.deselected = v40:add(u9.new())
    u38.toggled = v40:add(u9.new())
    u38.viewingStarted = v40:add(u9.new())
    u38.viewingEnded = v40:add(u9.new())
    u38.stateChanged = v40:add(u9.new())
    u38.notified = v40:add(u9.new())
    u38.noticeStarted = v40:add(u9.new())
    u38.noticeChanged = v40:add(u9.new())
    u38.endNotices = v40:add(u9.new())
    u38.toggleKeyAdded = v40:add(u9.new())
    u38.fakeToggleKeyChanged = v40:add(u9.new())
    u38.alignmentChanged = v40:add(u9.new())
    u38.updateSize = v40:add(u9.new())
    u38.resizingComplete = v40:add(u9.new())
    u38.joinedParent = v40:add(u9.new())
    u38.menuSet = v40:add(u9.new())
    u38.dropdownSet = v40:add(u9.new())
    u38.updateMenu = v40:add(u9.new())
    u38.startMenuUpdate = v40:add(u9.new())
    u38.childThemeModified = v40:add(u9.new())
    u38.indicatorSet = v40:add(u9.new())
    u38.dropdownChildAdded = v40:add(u9.new())
    u38.menuChildAdded = v40:add(u9.new())
    u38.iconModule = u5
    u38.UID = u41
    u38.isEnabled = true
    u38.isSelected = false
    u38.isViewing = false
    u38.joinedFrame = false
    u38.parentIconUID = false
    u38.deselectWhenOtherIconSelected = true
    u38.totalNotices = 0
    u38.activeState = "Deselected"
    u38.alignment = ""
    u38.originalAlignment = ""
    u38.appliedTheme = {}
    u38.appearance = {}
    u38.cachedInstances = {}
    u38.cachedNamesToInstances = {}
    u38.cachedCollectives = {}
    u38.bindedToggleKeys = {}
    u38.customBehaviours = {}
    u38.toggleItems = {}
    u38.bindedEvents = {}
    u38.notices = {}
    u38.menuIcons = {}
    u38.dropdownIcons = {}
    u38.childIconsDict = {}
    u38.isOldTopbar = u15.isOldTopbar
    u38.creationTime = os.clock()
    u38.widget = v40:add(require(u21.Widget)(u38, u15))
    u38:setAlignment()
    u22 = u22 + 1
    u38:setOrder(u22)
    u38:setTheme(u15.baseTheme)
    local v42 = u38:getInstance("ClickRegion")
    local u43 = false
    local u44 = false
    v42.MouseButton1Click:Connect(function() --[[Anonymous function at line 283]]
        --[[
        Upvalues:
            [1] = u43
            [2] = u44
            [3] = u38
        --]]
        if u43 then
            return
        else
            u44 = true
            task.delay(0.01, function() --[[Anonymous function at line 288]]
                --[[
                Upvalues:
                    [1] = u44
                --]]
                u44 = false
            end)
            if u38.locked then
                return
            elseif u38.isSelected then
                u38:deselect("User", u38)
            else
                u38:select("User", u38)
            end
        end
    end)
    v42.TouchTap:Connect(function() --[[Anonymous function at line 293]]
        --[[
        Upvalues:
            [1] = u44
            [2] = u43
            [3] = u38
        --]]
        if u44 then
            return
        else
            u43 = true
            task.delay(0.01, function() --[[Anonymous function at line 300]]
                --[[
                Upvalues:
                    [1] = u43
                --]]
                u43 = false
            end)
            if u38.locked then
                return
            elseif u38.isSelected then
                u38:deselect("User", u38)
            else
                u38:select("User", u38)
            end
        end
    end)
    v40:add(u1.InputBegan:Connect(function(p45, p46) --[[Anonymous function at line 307]]
        --[[
        Upvalues:
            [1] = u38
        --]]
        if not u38.locked then
            if u38.bindedToggleKeys[p45.KeyCode] and not p46 then
                if u38.locked then
                    return
                end
                if u38.isSelected then
                    u38:deselect("User", u38)
                    return
                end
                u38:select("User", u38)
            end
        end
    end))
    local function v47() --[[Anonymous function at line 329]]
        --[[
        Upvalues:
            [1] = u38
        --]]
        if not u38.locked then
            u38.isViewing = false
            u38.viewingEnded:Fire(true)
            u38:setState(nil, "User", u38)
        end
    end
    u38.joinedParent:Connect(function() --[[Anonymous function at line 337]]
        --[[
        Upvalues:
            [1] = u38
        --]]
        if u38.isViewing then
            if u38.locked then
                return
            end
            u38.isViewing = false
            u38.viewingEnded:Fire(true)
            u38:setState(nil, "User", u38)
        end
    end)
    v42.MouseEnter:Connect(function() --[[Anonymous function at line 342]]
        --[[
        Upvalues:
            [1] = u1
            [2] = u38
        --]]
        local v48 = not u1.KeyboardEnabled
        if not u38.locked then
            u38.isViewing = true
            u38.viewingStarted:Fire(true)
            if not v48 then
                u38:setState("Viewing", "User", u38)
            end
        end
    end)
    local u49 = 0
    v40:add(u1.TouchEnded:Connect(v47))
    v42.MouseLeave:Connect(v47)
    v42.SelectionGained:Connect(function(p50) --[[Function name: viewingStarted, line 319]]
        --[[
        Upvalues:
            [1] = u38
        --]]
        if not u38.locked then
            u38.isViewing = true
            u38.viewingStarted:Fire(true)
            if not p50 then
                u38:setState("Viewing", "User", u38)
            end
        end
    end)
    v42.SelectionLost:Connect(v47)
    v42.MouseButton1Down:Connect(function() --[[Anonymous function at line 351]]
        --[[
        Upvalues:
            [1] = u38
            [2] = u1
            [3] = u49
        --]]
        if not u38.locked and u1.TouchEnabled then
            u49 = u49 + 1
            local u51 = u49
            task.delay(0.2, function() --[[Anonymous function at line 355]]
                --[[
                Upvalues:
                    [1] = u51
                    [2] = u49
                    [3] = u38
                --]]
                if u51 == u49 then
                    if u38.locked then
                        return
                    end
                    u38.isViewing = true
                    u38.viewingStarted:Fire(true)
                    u38:setState("Viewing", "User", u38)
                end
            end)
        end
    end)
    v42.MouseButton1Up:Connect(function() --[[Anonymous function at line 362]]
        --[[
        Upvalues:
            [1] = u49
        --]]
        u49 = u49 + 1
    end)
    local u52 = u38:getInstance("IconOverlay")
    u38.viewingStarted:Connect(function() --[[Anonymous function at line 368]]
        --[[
        Upvalues:
            [1] = u52
            [2] = u38
        --]]
        u52.Visible = not u38.overlayDisabled
    end)
    u38.viewingEnded:Connect(function() --[[Anonymous function at line 371]]
        --[[
        Upvalues:
            [1] = u52
        --]]
        u52.Visible = false
    end)
    v40:add(u20:Connect(function(p53) --[[Anonymous function at line 376]]
        --[[
        Upvalues:
            [1] = u38
        --]]
        if p53 ~= u38 and (u38.deselectWhenOtherIconSelected and p53.deselectWhenOtherIconSelected) then
            u38:deselect("AutoDeselect", p53)
        end
    end))
    local v54 = debug.info(2, "s")
    local v55 = string.split(v54, ".")
    local v56 = game
    local v57 = nil
    for _, v58 in pairs(v55) do
        v56 = v56:FindFirstChild(v58)
        if not v56 then
            break
        end
        if v56:IsA("ScreenGui") then
            v57 = v56
        end
    end
    if v56 and (v57 and v57.ResetOnSpawn == true) then
        u11.localPlayerRespawned(function() --[[Anonymous function at line 401]]
            --[[
            Upvalues:
                [1] = u38
            --]]
            u38:destroy()
        end)
    end
    u38:getInstance("NoticeLabel")
    u38.toggled:Connect(function(p59) --[[Anonymous function at line 408]]
        --[[
        Upvalues:
            [1] = u38
            [2] = u15
        --]]
        u38.noticeChanged:Fire(u38.totalNotices)
        for v60, _ in pairs(u38.childIconsDict) do
            local v61 = u15.getIconByUID(v60)
            v61.noticeChanged:Fire(v61.totalNotices)
            if not p59 and v61.isSelected then
                for _, _ in pairs(v61.childIconsDict) do
                    v61:deselect("HideParentFeature", u38)
                end
            end
        end
    end)
    u38.selected:Connect(function() --[[Anonymous function at line 431]]
        --[[
        Upvalues:
            [1] = u38
            [2] = u2
        --]]
        if #u38.dropdownIcons > 0 then
            if u2:GetCore("ChatActive") and u38.alignment ~= "Right" then
                u38.chatWasPreviouslyActive = true
                u2:SetCore("ChatActive", false)
            end
            if u2:GetCoreGuiEnabled("PlayerList") and u38.alignment ~= "Left" then
                u38.playerlistWasPreviouslyActive = true
                u2:SetCoreGuiEnabled(Enum.CoreGuiType.PlayerList, false)
            end
        end
    end)
    u38.deselected:Connect(function() --[[Anonymous function at line 444]]
        --[[
        Upvalues:
            [1] = u38
            [2] = u2
        --]]
        if u38.chatWasPreviouslyActive then
            u38.chatWasPreviouslyActive = nil
            u2:SetCore("ChatActive", true)
        end
        if u38.playerlistWasPreviouslyActive then
            u38.playerlistWasPreviouslyActive = nil
            u2:SetCoreGuiEnabled(Enum.CoreGuiType.PlayerList, true)
        end
    end)
    task.delay(0.1, function() --[[Anonymous function at line 459]]
        --[[
        Upvalues:
            [1] = u38
        --]]
        if u38.activeState == "Deselected" then
            u38.stateChanged:Fire("Deselected")
            u38:refresh()
        end
    end)
    u15.iconAdded:Fire(u38)
    return u38
end
function u15.setName(p62, p63) --[[Anonymous function at line 475]]
    p62.widget.Name = p63
    p62.name = p63
    return p62
end
function u15.setState(p64, p65, p66, p67) --[[Anonymous function at line 481]]
    --[[
    Upvalues:
        [1] = u11
        [2] = u20
    --]]
    local v68 = p65 or (p64.isSelected and "Selected" or "Deselected")
    local v69 = u11.formatStateName(v68)
    if p64.activeState ~= v69 then
        local v70 = p64.isSelected
        p64.activeState = v69
        if v69 == "Deselected" then
            p64.isSelected = false
            if v70 then
                p64.toggled:Fire(false, p66, p67)
                p64.deselected:Fire(p66, p67)
            end
            p64:_setToggleItemsVisible(false, p66, p67)
        elseif v69 == "Selected" then
            p64.isSelected = true
            if not v70 then
                p64.toggled:Fire(true, p66, p67)
                p64.selected:Fire(p66, p67)
                u20:Fire(p64, p66, p67)
            end
            p64:_setToggleItemsVisible(true, p66, p67)
        end
        p64.stateChanged:Fire(v69, p66, p67)
    end
end
function u15.getInstance(u71, u72) --[[Anonymous function at line 514]]
    --[[
    Upvalues:
        [1] = u12
    --]]
    local v73 = u71.cachedNamesToInstances[u72]
    if v73 then
        return v73
    end
    local function u77(u74, u75) --[[Anonymous function at line 522]]
        --[[
        Upvalues:
            [1] = u71
        --]]
        if not u71.cachedInstances[u75] then
            local v76 = u75:GetAttribute("Collective")
            if v76 then
                v76 = u71.cachedCollectives[v76]
            end
            if v76 then
                table.insert(v76, u75)
            end
            u71.cachedNamesToInstances[u74] = u75
            u71.cachedInstances[u75] = true
            u75.Destroying:Once(function() --[[Anonymous function at line 532]]
                --[[
                Upvalues:
                    [1] = u71
                    [2] = u74
                    [3] = u75
                --]]
                u71.cachedNamesToInstances[u74] = nil
                u71.cachedInstances[u75] = nil
            end)
        end
    end
    local v78 = u71.widget
    u77("Widget", v78)
    if u72 == "Widget" then
        return v78
    end
    local u79 = nil
    local function u85(p80) --[[Anonymous function at line 545]]
        --[[
        Upvalues:
            [1] = u71
            [2] = u12
            [3] = u85
            [4] = u77
            [5] = u72
            [6] = u79
        --]]
        for _, v81 in pairs(p80:GetChildren()) do
            local v82 = v81:GetAttribute("WidgetUID")
            if not v82 or v82 == u71.UID then
                local v83 = u12.getRealInstance(v81) or v81
                u85(v83)
                if v83:IsA("GuiBase") or (v83:IsA("UIBase") or v83:IsA("ValueBase")) then
                    local v84 = v83.Name
                    u77(v84, v83)
                    if v84 == u72 then
                        u79 = v83
                    end
                end
            end
        end
    end
    u85(v78)
    return u79
end
function u15.getCollective(p86, p87) --[[Anonymous function at line 575]]
    local v88 = p86.cachedCollectives[p87]
    if v88 then
        return v88
    end
    local v89 = {}
    for v90, _ in pairs(p86.cachedInstances) do
        if v90:GetAttribute("Collective") == p87 then
            table.insert(v89, v90)
        end
    end
    p86.cachedCollectives[p87] = v89
    return v89
end
function u15.getInstanceOrCollective(p91, p92) --[[Anonymous function at line 596]]
    local v93 = {}
    local v94 = p91:getInstance(p92)
    if v94 then
        table.insert(v93, v94)
    end
    if #v93 == 0 then
        v93 = p91:getCollective(p92)
    end
    return v93
end
function u15.getStateGroup(p95, p96) --[[Anonymous function at line 610]]
    local v97 = p96 or p95.activeState
    local v98 = p95.appearance[v97]
    if not v98 then
        v98 = {}
        p95.appearance[v97] = v98
    end
    return v98
end
function u15.refreshAppearance(p99, p100, p101) --[[Anonymous function at line 620]]
    --[[
    Upvalues:
        [1] = u12
    --]]
    u12.refresh(p99, p100, p101)
    return p99
end
function u15.refresh(p102) --[[Anonymous function at line 625]]
    p102:refreshAppearance(p102.widget)
    p102.updateSize:Fire()
    return p102
end
function u15.updateParent(p103) --[[Anonymous function at line 631]]
    --[[
    Upvalues:
        [1] = u15
    --]]
    local v104 = u15.getIconByUID(p103.parentIconUID)
    if v104 then
        v104.updateSize:Fire()
    end
end
function u15.setBehaviour(p105, p106, p107, p108, p109) --[[Anonymous function at line 638]]
    local v110 = p106 .. "-" .. p107
    p105.customBehaviours[v110] = p108
    if p109 then
        local v111 = p105:getInstanceOrCollective(p106)
        for _, v112 in pairs(v111) do
            p105:refreshAppearance(v112, p107)
        end
    end
end
function u15.modifyTheme(p113, p114, p115) --[[Anonymous function at line 651]]
    --[[
    Upvalues:
        [1] = u12
    --]]
    return p113, u12.modify(p113, p114, p115)
end
function u15.modifyChildTheme(p116, p117, p118) --[[Anonymous function at line 656]]
    --[[
    Upvalues:
        [1] = u15
    --]]
    p116.childModifications = p117
    p116.childModificationsUID = p118
    for v119, _ in pairs(p116.childIconsDict) do
        u15.getIconByUID(v119):modifyTheme(p117, p118)
    end
    p116.childThemeModified:Fire()
    return p116
end
function u15.removeModification(p120, p121) --[[Anonymous function at line 669]]
    --[[
    Upvalues:
        [1] = u12
    --]]
    u12.remove(p120, p121)
    return p120
end
function u15.removeModificationWith(p122, p123, p124, p125) --[[Anonymous function at line 674]]
    --[[
    Upvalues:
        [1] = u12
    --]]
    u12.removeWith(p122, p123, p124, p125)
    return p122
end
function u15.setTheme(p126, p127) --[[Anonymous function at line 679]]
    --[[
    Upvalues:
        [1] = u12
    --]]
    u12.set(p126, p127)
    return p126
end
function u15.setEnabled(p128, p129) --[[Anonymous function at line 684]]
    p128.isEnabled = p129
    p128.widget.Visible = p129
    p128:updateParent()
    return p128
end
function u15.select(p130, p131, p132) --[[Anonymous function at line 691]]
    p130:setState("Selected", p131, p132)
    return p130
end
function u15.deselect(p133, p134, p135) --[[Anonymous function at line 696]]
    p133:setState("Deselected", p134, p135)
    return p133
end
function u15.notify(p136, p137, p138) --[[Anonymous function at line 701]]
    --[[
    Upvalues:
        [1] = u21
        [2] = u15
    --]]
    if not p136.notice then
        p136.notice = require(u21.Notice)(p136, u15)
    end
    p136.noticeStarted:Fire(p137, p138)
    return p136
end
function u15.clearNotices(p139) --[[Anonymous function at line 715]]
    p139.endNotices:Fire()
    return p139
end
function u15.disableOverlay(p140, p141) --[[Anonymous function at line 720]]
    p140.overlayDisabled = p141
    return p140
end
u15.disableStateOverlay = u15.disableOverlay
function u15.setImage(p142, p143, p144) --[[Anonymous function at line 726]]
    p142:modifyTheme({
        "IconImage",
        "Image",
        p143,
        p144
    })
    return p142
end
function u15.setLabel(p145, p146, p147) --[[Anonymous function at line 731]]
    p145:modifyTheme({
        "IconLabel",
        "Text",
        p146,
        p147
    })
    return p145
end
function u15.setOrder(p148, p149, p150) --[[Anonymous function at line 736]]
    p148:modifyTheme({
        "Widget",
        "LayoutOrder",
        p149,
        p150
    })
    return p148
end
function u15.setCornerRadius(p151, p152, p153) --[[Anonymous function at line 741]]
    p151:modifyTheme({
        "IconCorners",
        "CornerRadius",
        p152,
        p153
    })
    return p151
end
function u15.align(p154, p155, p156) --[[Anonymous function at line 746]]
    --[[
    Upvalues:
        [1] = u15
    --]]
    local v157 = tostring(p155):lower()
    local v158 = (v157 == "mid" or v157 == "centre") and "center" or v157
    local v159 = v158 ~= "left" and (v158 ~= "center" and v158 ~= "right") and "left" or v158
    local v160 = v159 == "center" and u15.container.TopbarCentered or u15.container.TopbarStandard
    local v161 = v160.Holders
    local v162 = string.upper((string.sub(v159, 1, 1))) .. string.sub(v159, 2)
    if not p156 then
        p154.originalAlignment = v162
    end
    local v163 = p154.joinedFrame
    local v164 = v161[v162]
    p154.screenGui = v160
    p154.alignmentHolder = v164
    if not p154.isDestroyed then
        p154.widget.Parent = v163 or v164
    end
    p154.alignment = v162
    p154.alignmentChanged:Fire(v162)
    u15.iconChanged:Fire(p154)
    return p154
end
u15.setAlignment = u15.align
function u15.setLeft(p165) --[[Anonymous function at line 775]]
    p165:setAlignment("Left")
    return p165
end
function u15.setMid(p166) --[[Anonymous function at line 780]]
    p166:setAlignment("Center")
    return p166
end
function u15.setRight(p167) --[[Anonymous function at line 785]]
    p167:setAlignment("Right")
    return p167
end
function u15.setWidth(p168, p169, p170) --[[Anonymous function at line 790]]
    p168:modifyTheme({
        "Widget",
        "Size",
        UDim2.fromOffset(p169, p168.widget.Size.Y.Offset),
        p170
    })
    p168:modifyTheme({
        "Widget",
        "DesiredWidth",
        p169,
        p170
    })
    return p168
end
function u15.setImageScale(p171, p172, p173) --[[Anonymous function at line 800]]
    p171:modifyTheme({
        "IconImageScale",
        "Value",
        p172,
        p173
    })
    return p171
end
function u15.setImageRatio(p174, p175, p176) --[[Anonymous function at line 805]]
    p174:modifyTheme({
        "IconImageRatio",
        "AspectRatio",
        p175,
        p176
    })
    return p174
end
function u15.setTextSize(p177, p178, p179) --[[Anonymous function at line 810]]
    p177:modifyTheme({
        "IconLabel",
        "TextSize",
        p178,
        p179
    })
    return p177
end
function u15.setTextFont(p180, p181, p182, p183, p184) --[[Anonymous function at line 815]]
    local v185 = p182 or Enum.FontWeight.Regular
    local v186 = p183 or Enum.FontStyle.Normal
    local v187 = nil
    local v188 = typeof(p181)
    if v188 == "number" then
        v187 = Font.fromId(p181, v185, v186)
    elseif v188 == "EnumItem" then
        v187 = Font.fromEnum(p181)
    elseif v188 == "string" and not p181:match("rbxasset") then
        v187 = Font.fromName(p181, v185, v186)
    end
    p180:modifyTheme({
        "IconLabel",
        "FontFace",
        v187 or Font.new(p181, v185, v186),
        p184
    })
    return p180
end
function u15.bindToggleItem(p189, p190) --[[Anonymous function at line 836]]
    if not (p190:IsA("GuiObject") or p190:IsA("LayerCollector")) then
        error("Toggle item must be a GuiObject or LayerCollector!")
    end
    p189.toggleItems[p190] = true
    p189:_updateSelectionInstances()
    return p189
end
function u15.unbindToggleItem(p191, p192) --[[Anonymous function at line 845]]
    p191.toggleItems[p192] = nil
    p191:_updateSelectionInstances()
    return p191
end
function u15._updateSelectionInstances(p193) --[[Anonymous function at line 851]]
    for v194, _ in pairs(p193.toggleItems) do
        local v195 = {}
        for _, v196 in pairs(v194:GetDescendants()) do
            if (v196:IsA("TextButton") or v196:IsA("ImageButton")) and v196.Active then
                table.insert(v195, v196)
            end
        end
        p193.toggleItems[v194] = v195
    end
end
function u15._setToggleItemsVisible(p197, p198, _, p199) --[[Anonymous function at line 865]]
    for v200, _ in pairs(p197.toggleItems) do
        if not p199 or (p199 == p197 or p199.toggleItems[v200] == nil) then
            v200[v200:IsA("LayerCollector") and "Enabled" or "Visible"] = p198
        end
    end
end
function u15.bindEvent(u201, p202, u203) --[[Anonymous function at line 877]]
    local v204 = u201[p202]
    local v205
    if v204 then
        if typeof(v204) == "table" then
            v205 = v204.Connect
        else
            v205 = false
        end
    else
        v205 = v204
    end
    assert(v205, "argument[1] must be a valid topbarplus icon event name!")
    local v206 = typeof(u203) == "function"
    assert(v206, "argument[2] must be a function!")
    u201.bindedEvents[p202] = v204:Connect(function(...) --[[Anonymous function at line 881]]
        --[[
        Upvalues:
            [1] = u203
            [2] = u201
        --]]
        u203(u201, ...)
    end)
    return u201
end
function u15.unbindEvent(p207, p208) --[[Anonymous function at line 887]]
    local v209 = p207.bindedEvents[p208]
    if v209 then
        v209:Disconnect()
        p207.bindedEvents[p208] = nil
    end
    return p207
end
function u15.bindToggleKey(p210, p211) --[[Anonymous function at line 896]]
    local v212 = typeof(p211) == "EnumItem"
    assert(v212, "argument[1] must be a KeyCode EnumItem!")
    p210.bindedToggleKeys[p211] = true
    p210.toggleKeyAdded:Fire(p211)
    p210:setCaption("_hotkey_")
    return p210
end
function u15.unbindToggleKey(p213, p214) --[[Anonymous function at line 904]]
    local v215 = typeof(p214) == "EnumItem"
    assert(v215, "argument[1] must be a KeyCode EnumItem!")
    p213.bindedToggleKeys[p214] = nil
    return p213
end
function u15.call(u216, u217, ...) --[[Anonymous function at line 910]]
    local u218 = table.pack(...)
    task.spawn(function() --[[Anonymous function at line 912]]
        --[[
        Upvalues:
            [1] = u217
            [2] = u216
            [3] = u218
        --]]
        local v219 = u218
        u217(u216, table.unpack(v219))
    end)
    return u216
end
function u15.addToJanitor(p220, p221) --[[Anonymous function at line 918]]
    p220.janitor:add(p221)
    return p220
end
function u15.lock(p222) --[[Anonymous function at line 923]]
    p222:getInstance("ClickRegion").Visible = false
    p222.locked = true
    return p222
end
function u15.unlock(p223) --[[Anonymous function at line 931]]
    p223:getInstance("ClickRegion").Visible = true
    p223.locked = false
    return p223
end
function u15.debounce(p224, p225) --[[Anonymous function at line 938]]
    p224:lock()
    task.wait(p225)
    p224:unlock()
    return p224
end
function u15.autoDeselect(p226, p227) --[[Anonymous function at line 945]]
    p226.deselectWhenOtherIconSelected = p227 == nil and true or p227
    return p226
end
function u15.oneClick(u228, p229) --[[Anonymous function at line 955]]
    local v230 = u228.singleClickJanitor
    v230:clean()
    if p229 or p229 == nil then
        v230:add(u228.selected:Connect(function() --[[Anonymous function at line 961]]
            --[[
            Upvalues:
                [1] = u228
            --]]
            u228:deselect("OneClick", u228)
        end))
    end
    u228.oneClickEnabled = true
    return u228
end
function u15.setCaption(p231, p232) --[[Anonymous function at line 969]]
    --[[
    Upvalues:
        [1] = u21
    --]]
    if p232 == "_hotkey_" and p231.captionText then
        return p231
    end
    local v233 = p231.captionJanitor
    p231.captionJanitor:clean()
    if not p232 or p232 == "" then
        p231.caption = nil
        p231.captionText = nil
        return p231
    end
    local v234 = v233:add(require(u21.Caption)(p231))
    v234:SetAttribute("CaptionText", p232)
    p231.caption = v234
    p231.captionText = p232
    return p231
end
function u15.setCaptionHint(p235, p236) --[[Anonymous function at line 987]]
    local v237 = typeof(p236) == "EnumItem"
    assert(v237, "argument[1] must be a KeyCode EnumItem!")
    p235.fakeToggleKey = p236
    p235.fakeToggleKeyChanged:Fire(p236)
    p235:setCaption("_hotkey_")
    return p235
end
function u15.leave(p238) --[[Anonymous function at line 995]]
    p238.joinJanitor:clean()
    return p238
end
function u15.joinMenu(p239, p240) --[[Anonymous function at line 1001]]
    --[[
    Upvalues:
        [1] = u11
    --]]
    u11.joinFeature(p239, p240, p240.menuIcons, p240:getInstance("Menu"))
    p240.menuChildAdded:Fire(p239)
    return p239
end
function u15.setMenu(p241, p242) --[[Anonymous function at line 1007]]
    p241.menuSet:Fire(p242)
    return p241
end
function u15.setFrozenMenu(p243, p244) --[[Anonymous function at line 1012]]
    p243:freezeMenu(p244)
    p243:setMenu(p244)
end
function u15.freezeMenu(u245) --[[Anonymous function at line 1017]]
    u245:select("FrozenMenu", u245)
    u245:bindEvent("deselected", function(p246) --[[Anonymous function at line 1021]]
        --[[
        Upvalues:
            [1] = u245
        --]]
        p246:select("FrozenMenu", u245)
    end)
    u245:modifyTheme({ "IconSpot", "Visible", false })
end
function u15.joinDropdown(p247, p248) --[[Anonymous function at line 1027]]
    --[[
    Upvalues:
        [1] = u11
    --]]
    p248:getDropdown()
    u11.joinFeature(p247, p248, p248.dropdownIcons, p248:getInstance("DropdownScroller"))
    p248.dropdownChildAdded:Fire(p247)
    return p247
end
function u15.getDropdown(p249) --[[Anonymous function at line 1034]]
    --[[
    Upvalues:
        [1] = u21
    --]]
    local v250 = p249.dropdown
    if not v250 then
        v250 = require(u21.Dropdown)(p249)
        p249.dropdown = v250
        p249:clipOutside(v250)
    end
    return v250
end
function u15.setDropdown(p251, p252) --[[Anonymous function at line 1044]]
    p251:getDropdown()
    p251.dropdownSet:Fire(p252)
    return p251
end
function u15.clipOutside(p253, p254) --[[Anonymous function at line 1050]]
    --[[
    Upvalues:
        [1] = u11
    --]]
    local v255 = u11.clipOutside(p253, p254)
    p253:refreshAppearance(p254)
    return p253, v255
end
function u15.setIndicator(p256, p257) --[[Anonymous function at line 1061]]
    --[[
    Upvalues:
        [1] = u21
        [2] = u15
    --]]
    if not p256.indicator then
        p256.indicator = p256.janitor:add(require(u21.Indicator)(p256, u15))
    end
    p256.indicatorSet:Fire(p257)
end
function u15.destroy(p258) --[[Anonymous function at line 1076]]
    --[[
    Upvalues:
        [1] = u15
    --]]
    if not p258.isDestroyed then
        p258:clearNotices()
        if p258.parentIconUID then
            p258:leave()
        end
        p258.isDestroyed = true
        p258.janitor:clean()
        u15.iconRemoved:Fire(p258)
    end
end
u15.Destroy = u15.destroy
return u15