-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\PlaySound-ModuleScript.lua
local u1 = game:GetService("ReplicatedStorage")
return function(p2) --[[Function name: PlaySound, line 3]]
    --[[
    Upvalues:
        [1] = u1
    --]]
    local u3 = Instance.new("Sound")
    u3.PlaybackSpeed = Random.new():NextNumber(0.95, 1.05)
    u3.SoundId = p2
    u3.Parent = u1
    u3.Ended:Once(function() --[[Anonymous function at line 9]]
        --[[
        Upvalues:
            [1] = u3
        --]]
        u3:Destroy()
    end)
    u3:Play()
    return u3
end