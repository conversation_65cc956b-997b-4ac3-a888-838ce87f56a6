-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\EffectController\Libraries\PartCache\Table-ModuleScript.lua
local u1 = Random.new()
local u2 = table.clone(table)
for v3, v4 in table do
    u2[v3] = v4
end
function u2.contains(p5, p6) --[[Anonymous function at line 30]]
    --[[
    Upvalues:
        [1] = u2
    --]]
    return u2.indexOf(p5, p6) ~= nil
end
function u2.indexOf(p7, p8) --[[Anonymous function at line 35]]
    --[[
    Upvalues:
        [1] = u2
    --]]
    return table.find(p7, p8) or u2.keyOf(p7, p8)
end
function u2.keyOf(p9, p10) --[[Anonymous function at line 44]]
    for v11, v12 in pairs(p9) do
        if v12 == p10 then
            return v11
        end
    end
    return nil
end
function u2.skip(p13, p14) --[[Anonymous function at line 54]]
    return table.move(p13, p14 + 1, #p13, 1, table.create(#p13 - p14))
end
function u2.take(p15, p16) --[[Anonymous function at line 59]]
    return table.move(p15, 1, p16, 1, table.create(p16))
end
function u2.range(p17, p18, p19) --[[Anonymous function at line 64]]
    return table.move(p17, p18, p19, 1, table.create(p19 - p18 + 1))
end
function u2.skipAndTake(p20, p21, p22) --[[Anonymous function at line 69]]
    return table.move(p20, p21 + 1, p21 + p22, 1, table.create(p22))
end
function u2.random(p23) --[[Anonymous function at line 74]]
    --[[
    Upvalues:
        [1] = u1
    --]]
    return p23[u1:NextInteger(1, #p23)]
end
function u2.join(p24, p25) --[[Anonymous function at line 79]]
    local v26 = table.create(#p24 + #p25)
    return table.move(p25, 1, #p25, #p24 + 1, v26)
end
function u2.removeObject(p27, p28) --[[Anonymous function at line 85]]
    --[[
    Upvalues:
        [1] = u2
    --]]
    local v29 = u2.indexOf(p27, p28)
    if v29 then
        table.remove(p27, v29)
    end
end
function u2.expand(p30, p31) --[[Anonymous function at line 94]]
    if p31 < 0 then
        error("Cannot expand a table by a negative amount of objects.")
    end
    local v32 = table.create(#p30 + p31)
    for v33 = 1, #p30 do
        v32[v33] = p30[v33]
    end
    return v32
end
return u2