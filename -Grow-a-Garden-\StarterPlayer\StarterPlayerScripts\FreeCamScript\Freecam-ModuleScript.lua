-- Full Path: -Grow-a-Garden-\StarterPlayer\StarterPlayerScripts\FreeCamScript\Freecam-ModuleScript.lua
local v1 = {}
local u2 = math.abs
local u3 = math.clamp
local u4 = math.exp
local _ = math.rad
local u5 = math.sign
local _ = math.sqrt
local _ = math.tan
local u6 = game:GetService("TweenService")
local u7 = game:GetService("ContextActionService")
local u8 = game:GetService("Players")
local u9 = game:GetService("RunService")
game:GetService("StarterGui")
local u10 = game:GetService("UserInputService")
local u11 = game:GetService("Workspace")
local u12 = UserSettings().GameSettings
local u13 = u8.LocalPlayer
if not u13 then
    u8:GetPropertyChangedSignal("LocalPlayer"):Wait()
    u13 = u8.LocalPlayer
end
local v14 = script:WaitForChild("Mobile Buttons")
local u15 = u13.PlayerGui:FindFirstChild("Mobile Freecam") or Instance.new("ScreenGui")
u15.Name = "Mobile Freecam"
u15.Enabled = false
u15.Parent = u13.PlayerGui
local v16 = u15:FindFirstChild("Slow") or v14.Slow:Clone()
v16.Parent = u15
local u17 = false
if u10.TouchEnabled then
    local v18 = u13.PlayerGui:WaitForChild("TouchGui").TouchControlFrame.JumpButton
    v18.MouseButton1Down:Connect(function() --[[Anonymous function at line 39]]
        --[[
        Upvalues:
            [1] = u17
        --]]
        u17 = true
    end)
    v18.MouseButton1Up:Connect(function() --[[Anonymous function at line 42]]
        --[[
        Upvalues:
            [1] = u17
        --]]
        u17 = false
    end)
end
v16.MouseButton1Down:Connect(function() --[[Anonymous function at line 47]]
    mobileSlowButtonPressed = true
end)
v16.MouseButton1Up:Connect(function() --[[Anonymous function at line 50]]
    mobileSlowButtonPressed = false
end)
local u19 = require(u13.PlayerScripts.PlayerModule)
local u20 = u11.CurrentCamera
u11:GetPropertyChangedSignal("CurrentCamera"):Connect(function() --[[Anonymous function at line 57]]
    --[[
    Upvalues:
        [1] = u11
        [2] = u20
    --]]
    local v21 = u11.CurrentCamera
    if v21 then
        u20 = v21
    end
end)
local v22, v23 = pcall(function() --[[Anonymous function at line 66]]
    return UserSettings():IsUserFeatureEnabled("UserExitFreecamBreaksWithShiftlock")
end)
local u24 = v22 and v23
local v25, v26 = pcall(function() --[[Anonymous function at line 74]]
    return UserSettings():IsUserFeatureEnabled("UserShowGuiHideToggles")
end)
local u27 = v25 and v26
local _ = Enum.ContextActionPriority.Low.Value
local u28 = Enum.ContextActionPriority.High.Value
local _ = Vector2.new(0.75, 1) * 1
local u29 = Vector3.new(1, 1, 1)
local u30 = Vector3.new(1, 1, 1)
local u31 = {}
u31.__index = u31
function u31.new(p32, p33) --[[Anonymous function at line 102]]
    --[[
    Upvalues:
        [1] = u31
    --]]
    local v34 = u31
    local v35 = setmetatable({}, v34)
    v35.f = p32
    v35.p = p33
    v35.v = p33 * 0
    return v35
end
function u31.Update(p36, p37, p38) --[[Anonymous function at line 110]]
    --[[
    Upvalues:
        [1] = u4
    --]]
    local v39 = p36.f * 2 * 3.141592653589793
    local v40 = p36.p
    local v41 = p36.v
    local v42 = p38 - v40
    local v43 = u4(-v39 * p37)
    local v44 = p38 + (v41 * p37 - v42 * (v39 * p37 + 1)) * v43
    local v45 = (v39 * p37 * (v42 * v39 - v41) + v41) * v43
    p36.p = v44
    p36.v = v45
    return v44
end
function u31.Reset(p46, p47) --[[Anonymous function at line 127]]
    p46.p = p47
    p46.v = p47 * 0
end
local u48 = Vector3.new()
local u49 = Vector2.new()
local u50 = 0
local u51 = u31.new(4, (Vector3.new()))
local u52 = u31.new(5, Vector2.new())
local u53 = {}
local function u55(p54) --[[Anonymous function at line 157]]
    --[[
    Upvalues:
        [1] = u5
        [2] = u2
        [3] = u4
        [4] = u3
    --]]
    return u5(p54) * u3((u4(2 * ((u2(p54) - 0.15) / 0.85)) - 1) / 6.38905609893065, 0, 1)
end
local u56 = {
    ["ButtonL3"] = 0,
    ["ButtonL2"] = 0,
    ["ButtonR2"] = 0,
    ["Thumbstick1"] = Vector2.new(),
    ["Thumbstick2"] = Vector2.new()
}
local u57 = {
    ["W"] = 0,
    ["A"] = 0,
    ["S"] = 0,
    ["D"] = 0,
    ["E"] = 0,
    ["Q"] = 0,
    ["LeftShift"] = 0,
    ["RightShift"] = 0
}
local u58 = {
    ["Delta"] = Vector2.new()
}
local u59 = Vector2.new(1, 1) * 0.04908738521234052
local u60 = Vector2.new(0.6, 0.6)
local u61 = 1
function u53.Vel(_) --[[Anonymous function at line 194]]
    --[[
    Upvalues:
        [1] = u55
        [2] = u56
        [3] = u29
        [4] = u57
        [5] = u30
        [6] = u10
        [7] = u15
        [8] = u19
        [9] = u61
    --]]
    local v62 = u55(u56.Thumbstick1.X)
    local v63 = u55(u56.ButtonR2) - u55(u56.ButtonL2)
    local v64 = u55
    local v65 = -u56.Thumbstick1.Y
    local v66 = Vector3.new(v62, v63, v64(v65)) * u29
    local v67 = u57.D - u57.A
    local v68 = u57.E - u57.Q
    local v69 = u57.S - u57.W
    local v70 = Vector3.new(v67, v68, v69) * u30
    local v71 = u10:IsKeyDown(Enum.KeyCode.LeftShift) or (u56.ButtonL3 > 0 and true or mobileSlowButtonPressed)
    local v72 = Vector3.new()
    if u10.TouchEnabled then
        u15.Enabled = true
        v72 = u19:GetControls():GetMoveVector().Unit * u29
        if v72 ~= v72 then
            v72 = Vector3.new()
        end
    else
        u15.Enabled = false
    end
    return (v66 + v70 + v72) * (u61 * (v71 and 0.25 or 1))
end
function u53.Pan(p73) --[[Anonymous function at line 224]]
    --[[
    Upvalues:
        [1] = u55
        [2] = u56
        [3] = u60
        [4] = u58
        [5] = u59
    --]]
    local v74 = Vector2.new(u55(u56.Thumbstick2.Y), u55(-u56.Thumbstick2.X)) * p73 * u60 * 60
    local v75 = u58.Delta * p73 * u59 * 60 * 6
    u58.Delta = Vector2.new()
    return v74 + v75
end
function u53.VerticalMovement() --[[Anonymous function at line 234]]
    --[[
    Upvalues:
        [1] = u57
        [2] = u56
        [3] = u17
    --]]
    return (u57.E > 0 or (u56.ButtonL2 > 0 or u17)) and 1 or ((u57.Q > 0 or u56.ButtonR2 > 0) and -1 or 0)
end
local function u78(_, p76, p77) --[[Anonymous function at line 247]]
    --[[
    Upvalues:
        [1] = u57
    --]]
    u57[p77.KeyCode.Name] = p76 == Enum.UserInputState.Begin and 1 or 0
    return Enum.ContextActionResult.Sink
end
local function u81(_, p79, p80) --[[Anonymous function at line 252]]
    --[[
    Upvalues:
        [1] = u56
    --]]
    u56[p80.KeyCode.Name] = p79 == Enum.UserInputState.Begin and 1 or 0
    return Enum.ContextActionResult.Sink
end
local function u84(_, _, p82) --[[Anonymous function at line 257]]
    --[[
    Upvalues:
        [1] = u58
    --]]
    local v83 = p82.Delta
    u58.Delta = Vector2.new(-v83.y, -v83.x)
    return Enum.ContextActionResult.Sink
end
local function u86(_, _, p85) --[[Anonymous function at line 263]]
    --[[
    Upvalues:
        [1] = u56
    --]]
    u56[p85.KeyCode.Name] = p85.Position
    return Enum.ContextActionResult.Sink
end
local function u88(_, _, p87) --[[Anonymous function at line 268]]
    --[[
    Upvalues:
        [1] = u56
    --]]
    u56[p87.KeyCode.Name] = p87.Position.z
    return Enum.ContextActionResult.Sink
end
function u53.StartCapture() --[[Anonymous function at line 284]]
    --[[
    Upvalues:
        [1] = u7
        [2] = u78
        [3] = u28
        [4] = u84
        [5] = u81
        [6] = u88
        [7] = u86
    --]]
    u7:BindActionAtPriority("FreecamKeyboard", u78, false, u28, Enum.KeyCode.W, Enum.KeyCode.A, Enum.KeyCode.S, Enum.KeyCode.D, Enum.KeyCode.E, Enum.KeyCode.Q, Enum.KeyCode.Up, Enum.KeyCode.Down)
    u7:BindActionAtPriority("FreecamMousePan", u84, false, u28, Enum.UserInputType.MouseMovement, Enum.UserInputType.Touch)
    u7:BindActionAtPriority("FreecamGamepadButton", u81, false, u28, Enum.KeyCode.ButtonL3, Enum.KeyCode.ButtonR3)
    u7:BindActionAtPriority("FreecamGamepadTrigger", u88, false, u28, Enum.KeyCode.ButtonR2, Enum.KeyCode.ButtonL2)
    u7:BindActionAtPriority("FreecamGamepadThumbstick", u86, false, u28, Enum.KeyCode.Thumbstick1, Enum.KeyCode.Thumbstick2)
end
function u53.StopCapture() --[[Anonymous function at line 300]]
    --[[
    Upvalues:
        [1] = u61
        [2] = u56
        [3] = u57
        [4] = u58
        [5] = u7
    --]]
    u61 = 1
    local v89 = u56
    for v90, v91 in pairs(v89) do
        v89[v90] = v91 * 0
    end
    local v92 = u57
    for v93, v94 in pairs(v92) do
        v92[v93] = v94 * 0
    end
    local v95 = u58
    for v96, v97 in pairs(v95) do
        v95[v96] = v97 * 0
    end
    u7:UnbindAction("FreecamKeyboard")
    u7:UnbindAction("FreecamMousePan")
    u7:UnbindAction("FreecamMouseWheel")
    u7:UnbindAction("FreecamGamepadButton")
    u7:UnbindAction("FreecamGamepadTrigger")
    u7:UnbindAction("FreecamGamepadThumbstick")
end
local u98 = CFrame.new()
local function u104(p99) --[[Anonymous function at line 319]]
    --[[
    Upvalues:
        [1] = u51
        [2] = u53
        [3] = u52
        [4] = u49
        [5] = u3
        [6] = u48
        [7] = u20
        [8] = u98
    --]]
    local v100 = u51:Update(p99, u53.Vel(p99))
    u49 = u49 + u52:Update(p99, u53.Pan(p99)) * 0.1
    u49 = Vector2.new(u3(u49.x, -1.5707963267948966, 1.5707963267948966), u49.y % 6.283185307179586)
    local v101 = CFrame.new(u48) * CFrame.fromOrientation(u49.x, u49.y, 0)
    local v102 = v101.LookVector
    local v103 = v101.RightVector
    u48 = u48 + (v102 * -v100.Z + v103 * v100.X) * Vector3.new(64, 64, 64) * p99
    u48 = u48 + Vector3.new(0, 1, 0) * u53.VerticalMovement() * Vector3.new(21.333334, 21.333334, 21.333334) * p99
    u20.CFrame = v101 * u98
    u20.Focus = CFrame.new(v101.Position + v102)
end
local u105 = {}
local u106 = nil
local u107 = nil
local u108 = nil
local u109 = nil
local u110 = nil
function u105.Push() --[[Anonymous function at line 375]]
    --[[
    Upvalues:
        [1] = u110
        [2] = u20
        [3] = u107
        [4] = u109
        [5] = u108
        [6] = u24
        [7] = u8
        [8] = u12
        [9] = u106
        [10] = u10
    --]]
    u110 = u20.FieldOfView
    u20.FieldOfView = 70
    u107 = u20.CameraType
    u20.CameraType = Enum.CameraType.Custom
    u109 = u20.CFrame
    u108 = u20.Focus
    if u24 then
        local v111 = u8.LocalPlayer.DevEnableMouseLock
        local v112 = u8.LocalPlayer.DevComputerMovementMode == Enum.DevComputerMovementMode.Scriptable
        local v113 = v111 and (u12.ControlMode == Enum.ControlMode.MouseLockSwitch and u12.ComputerMovementMode ~= Enum.ComputerMovementMode.ClickToMove)
        if v113 then
            v113 = not v112
        end
        if v113 then
            u106 = Enum.MouseBehavior.Default
            ::l17::
            u10.MouseBehavior = Enum.MouseBehavior.Default
            return
        end
    end
    u106 = u10.MouseBehavior
    goto l17
end
function u105.Pop() --[[Anonymous function at line 395]]
    --[[
    Upvalues:
        [1] = u20
        [2] = u110
        [3] = u107
        [4] = u109
        [5] = u108
        [6] = u10
        [7] = u106
    --]]
    u20.FieldOfView = u110 or u20.FieldOfView
    u110 = nil
    u20.CameraType = u107 or Enum.CameraType.Custom
    u107 = nil
    u20.CFrame = u109 or u20.CFrame
    u109 = nil
    u20.Focus = u108 or u20.Focus
    u10.MouseBehavior = u106 or Enum.MouseBehavior.Default
    u106 = nil
end
local u114 = false
local u115 = nil
local u116 = TweenInfo.new(0.8, Enum.EasingStyle.Sine)
local function u120() --[[Anonymous function at line 426]]
    --[[
    Upvalues:
        [1] = u13
        [2] = u6
        [3] = u116
        [4] = u115
    --]]
    local v117 = u13.PlayerGui:FindFirstChild("FreeCam Warp Ui") or Instance.new("ScreenGui")
    v117.Name = "FreeCam Warp Ui"
    v117.Parent = u13.PlayerGui
    local v118 = game.Lighting:FindFirstChild("FreeCam Warp Blur") or Instance.new("BlurEffect")
    v118.Name = "FreeCam Warp Blur"
    v118.Size = 8
    v118.Enabled = true
    v118.Parent = game.Lighting
    local u119 = Instance.new("ImageLabel")
    u119.Image = "rbxassetid://128873538665462"
    u119.AnchorPoint = Vector2.new(0.5, 0.5)
    u119.Position = UDim2.new(0.5, 0, 0.5, 0)
    u119.BackgroundTransparency = 1
    u119.Size = UDim2.new(0.6, 0, 0.6, 0)
    u119.ImageTransparency = 0.82
    u119.Visible = true
    u119.Parent = v117
    u6:Create(u119, u116, {
        ["Size"] = UDim2.new(4, 0, 4, 0)
    }):Play()
    u6:Create(u119, u116, {
        ["ImageTransparency"] = 1
    }):Play()
    u6:Create(v118, u116, {
        ["Size"] = 0
    }):Play()
    task.delay(u116.Time, function() --[[Anonymous function at line 452]]
        --[[
        Upvalues:
            [1] = u119
        --]]
        if u119 then
            u119:Destroy()
        end
    end)
    u115 = tick()
end
u9.RenderStepped:Connect(function(p121) --[[Anonymous function at line 461]]
    --[[
    Upvalues:
        [1] = u115
        [2] = u20
        [3] = u114
    --]]
    if u115 == nil then
        return
    else
        local v122 = tick() - u115
        if v122 < 3 then
            local v123 = -v122 / 2
            local v124 = math.exp(v123) * 7
            local v125 = -v122 / 2
            local v126 = v122 * (math.exp(v125) * 8)
            u20.FieldOfView = math.sin(v126) * v124 + 70
        else
            if not u114 then
                u114 = true
            end
            local v127 = u20
            local v128 = u20.FieldOfView
            local v129 = p121 * 0.1 * 60
            v127.FieldOfView = v128 + (70 - v128) * v129
            if u20.FieldOfView >= 69.7 then
                u115 = nil
            end
        end
    end
end)
function v1.StartFreecam(_, p130, p131) --[[Anonymous function at line 481]]
    --[[
    Upvalues:
        [1] = u27
        [2] = u20
        [3] = u49
        [4] = u48
        [5] = u50
        [6] = u13
        [7] = u51
        [8] = u52
        [9] = u105
        [10] = u9
        [11] = u104
        [12] = u53
        [13] = u120
    --]]
    if u27 then
        script:SetAttribute("FreecamEnabled", true)
    end
    local v132 = u20.CFrame
    u49 = Vector2.new(v132:toEulerAnglesYXZ())
    u48 = v132.p
    u50 = u20.FieldOfView
    u13.Character.PrimaryPart.Anchored = true
    u51:Reset((Vector3.new()))
    u52:Reset(Vector2.new())
    if not p130 then
        u105.Push()
    end
    u9:BindToRenderStep("Freecam", Enum.RenderPriority.Camera.Value, u104)
    u53.StartCapture()
    if not p131 then
        u120()
    end
end
function v1.StopFreecam(_, p133) --[[Anonymous function at line 511]]
    --[[
    Upvalues:
        [1] = u27
        [2] = u13
        [3] = u15
        [4] = u17
        [5] = u53
        [6] = u9
        [7] = u105
    --]]
    if u27 then
        script:SetAttribute("FreecamEnabled", false)
    end
    u13.Character.PrimaryPart.Anchored = false
    mobileSlowButtonPressed = false
    u15.Enabled = false
    u17 = false
    u53.StopCapture()
    u9:UnbindFromRenderStep("Freecam")
    if not p133 then
        u105.Pop()
    end
end
function v1.SetFreeCamSpeed(_, p134) --[[Anonymous function at line 531]]
    --[[
    Upvalues:
        [1] = u29
        [2] = u30
    --]]
    if p134 then
        u29 = Vector3.new(p134, p134, p134)
        u30 = Vector3.new(p134, p134, p134)
    else
        u29 = Vector3.new(1, 1, 1)
        u30 = Vector3.new(1, 1, 1)
    end
end
return v1