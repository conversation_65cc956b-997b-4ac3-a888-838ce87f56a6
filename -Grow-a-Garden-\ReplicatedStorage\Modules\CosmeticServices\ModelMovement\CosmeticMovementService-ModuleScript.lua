-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\CosmeticServices\ModelMovement\CosmeticMovementService-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
local v2 = game:GetService("RunService")
local u3 = game:GetService("CollectionService")
local v4 = game:GetService("Players")
local u5 = game:GetService("TweenService")
local v6 = v4.LocalPlayer
local u7 = v6.Character or v6.CharacterAdded:Wait()
v6.CharacterAdded:Connect(function(p8) --[[Anonymous function at line 11]]
    --[[
    Upvalues:
        [1] = u7
    --]]
    u7 = p8
end)
local u9 = require(v1.Modules.GetMouseToWorld)
local v10 = require(v1.Data.CosmeticRegistry)
local u11 = require(v1.Modules.RoundToNearestNumberVector3)
local u12 = require(v1.Modules.CosmeticServices.CosmeticPhyiscalityService)
require(v1.Modules.PlaySound)
local u13 = v10.InputConfig.MOVEMENT_CONFIG
local u14 = {
    ["LastCFrame"] = nil,
    ["CollisionPartsCache"] = {},
    ["ArchivedY"] = 0,
    ["Target"] = nil
}
local u15 = workspace.CurrentCamera
workspace:GetPropertyChangedSignal("CurrentCamera"):Connect(function() --[[Anonymous function at line 33]]
    --[[
    Upvalues:
        [1] = u15
    --]]
    u15 = workspace.CurrentCamera
end)
local function u18(p16) --[[Anonymous function at line 45]]
    --[[
    Upvalues:
        [1] = u14
    --]]
    u14.CollisionPartsCache = {}
    for _, v17 in p16:GetDescendants() do
        if v17:IsA("BasePart") then
            u14.CollisionPartsCache[v17] = v17.CanCollide
            v17.CanCollide = false
        end
    end
end
local u19 = Instance.new("Sound")
u19.SoundId = "rbxassetid://100741940041305"
u19.Volume = 0.5
u19.Looped = true
u19.Parent = v1
function u14.SetTarget(_, p20) --[[Anonymous function at line 63]]
    --[[
    Upvalues:
        [1] = u14
        [2] = u19
        [3] = u18
    --]]
    u14.Target = p20
    u19[p20 and "Play" or "Stop"](u19)
    for v21, v22 in u14.CollisionPartsCache do
        v21.CanCollide = v22
    end
    table.clear(u14.CollisionPartsCache)
    if p20 then
        u18(p20)
    end
end
local u23 = nil
v2.RenderStepped:Connect(function() --[[Anonymous function at line 77]]
    --[[
    Upvalues:
        [1] = u14
        [2] = u3
        [3] = u7
        [4] = u9
        [5] = u13
        [6] = u11
        [7] = u15
        [8] = u23
        [9] = u5
        [10] = u19
        [11] = u12
    --]]
    local v24 = u14.Target
    if v24 then
        local v25 = RaycastParams.new()
        v25.FilterDescendantsInstances = u3:GetTagged("CosmeticHitBox")
        v25:AddToFilter(v24)
        v25:AddToFilter(workspace.PetsPhysical)
        v25:AddToFilter(u7)
        v25.FilterType = Enum.RaycastFilterType.Exclude
        local v26 = u9(v25, u13.MAX_RAY_DISTANCE)
        if v26 and v26.Instance then
            local v27 = v26.Position
            local v28 = v26.Normal
            v24:GetExtentsSize()
            local v29 = v27 + v28 * 0
            local v30 = u11(v29, u13.MOVEMENT_ROUNDING)
            local v31 = v30.X
            local v32 = v30.Y
            local v33 = v30.Z
            local v34 = v28.X
            local v35 = math.abs(v34)
            local v36 = v28.Y
            local v37 = math.abs(v36)
            local v38 = v28.Z
            local v39 = math.abs(v38)
            local v40 = math.max(v35, v37, v39)
            local v41 = v40 - v37
            if math.abs(v41) < u13.NEAR_ZERO_EPSILON then
                v32 = v29.Y
            else
                local v42 = v40 - v35
                if math.abs(v42) < u13.NEAR_ZERO_EPSILON then
                    v31 = v29.X
                else
                    v33 = v29.Z
                end
            end
            local v43 = Vector3.new(v31, v32, v33)
            local v44 = v28:Cross(Vector3.new(0, 0, 1))
            local v45
            if v44.Magnitude < u13.NEAR_ZERO_EPSILON then
                v45 = v28:Cross(Vector3.new(1, 0, 0)).Unit
            else
                v45 = v44.Unit
            end
            local v46 = v45:Cross(v28).Unit
            local v47 = u15.CFrame.LookVector
            local v48 = v47.X
            local v49 = v47.Z
            local v50 = Vector3.new(v48, 0, v49)
            local v51 = v50.Magnitude < u13.NEAR_ZERO_EPSILON and Vector3.new(-0, -0, -1) or v50.Unit
            local v52 = v51 - v51:Dot(v28) * v28
            local v53
            if v52.Magnitude < u13.NEAR_ZERO_EPSILON then
                v53 = v46
            else
                v53 = v52.Unit
            end
            local v54 = -2
            for _, v55 in {
                v46,
                -v46,
                v45,
                -v45
            } do
                local v56 = v53:Dot(v55)
                if v56 >= v54 then
                    v46 = v55
                    v54 = v56
                end
            end
            local v57 = v43 + v46
            local v58 = CFrame.lookAt(v43, v57, v28)
            local v59 = v24.Parent
            if u14.LastCFrame == v58 then
                if not u23 then
                    u23 = task.delay(0.25, function() --[[Anonymous function at line 163]]
                        --[[
                        Upvalues:
                            [1] = u5
                            [2] = u19
                        --]]
                        local v60 = u5:Create(u19, TweenInfo.new(0.2), {
                            ["Volume"] = 0
                        })
                        v60.Completed:Once(function() --[[Anonymous function at line 169]]
                            --[[
                            Upvalues:
                                [1] = u19
                            --]]
                            u19.Playing = false
                        end)
                        v60:Play()
                    end)
                end
            else
                if u23 then
                    task.cancel(u23)
                    u23 = nil
                    u5:Create(u19, TweenInfo.new(0), {
                        ["Volume"] = 0.5
                    }):Play()
                end
                u19.Playing = true
                u14.LastCFrame = v58
                u12:UpdateCFrame(v59:GetAttribute("CosmeticUUID"), v58)
            end
        else
            return
        end
    else
        return
    end
end)
return u14