-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\RoundToNearestNumberVector3-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
local u2 = require(v1.Modules.RoundToNearestNumber)
return function(p3, p4) --[[Function name: RoundToNearestVector3, line 5]]
    --[[
    Upvalues:
        [1] = u2
    --]]
    local v5 = p4 or 0.25
    local v6 = u2(p3.X, v5)
    local v7 = u2(p3.Y, v5)
    local v8 = u2
    local v9 = p3.Z
    return Vector3.new(v6, v7, v8(v9, v5))
end