-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\Icon\Reference-ModuleScript.lua
local u1 = game:GetService("ReplicatedStorage")
local u3 = {
    ["objectName"] = "TopbarPlusReference",
    ["addToReplicatedStorage"] = function() --[[Function name: addToReplicatedStorage, line 10]]
        --[[
        Upvalues:
            [1] = u1
            [2] = u3
        --]]
        if u1:FindFirstChild(u3.objectName) then
            return false
        end
        local v2 = Instance.new("ObjectValue")
        v2.Name = u3.objectName
        v2.Value = script.Parent
        v2.Parent = u1
        return v2
    end,
    ["getObject"] = function() --[[Function name: getObject, line 22]]
        --[[
        Upvalues:
            [1] = u1
            [2] = u3
        --]]
        return u1:FindFirstChild(u3.objectName) or false
    end
}
return u3