-- Full Path: -Grow-a-Garden-\\Variant3-ModuleScript.lua
local u1 = game:GetService("ReplicatedStorage")
local u2 = game:GetService("Players").LocalPlayer
local u3 = u2.PlayerGui
local u4 = u3.Seed_Shop
local u5 = require(u1.Modules.Notification)
require(u1.Modules.GetFarm)
local u6 = require(u1.Modules.Trove)
local u7 = require(u1.Modules.TutorialController.TutorialUtils)
local u8 = require(u1.Arrow_Module)
return function() --[[Anonymous function at line 18]]
    --[[
    Upvalues:
        [1] = u6
        [2] = u8
        [3] = u2
        [4] = u7
        [5] = u5
        [6] = u4
        [7] = u3
        [8] = u1
    --]]
    local u9 = u6.new()
    local function u13(p10) --[[Anonymous function at line 21]]
        --[[
        Upvalues:
            [1] = u9
            [2] = u8
            [3] = u2
        --]]
        local v11 = u9:Extend()
        local u12 = u8.GenerateArrow(u2, p10, math.random(1, 1000000))
        v11:Add(function() --[[Anonymous function at line 25]]
            --[[
            Upvalues:
                [1] = u8
                [2] = u12
            --]]
            u8.Remove_Arrow(u12)
        end)
        return v11:WrapClean()
    end
    workspace:SetAttribute("InTutorial", true)
    u9:Add(function() --[[Anonymous function at line 33]]
        workspace:SetAttribute("InTutorial", nil)
    end)
    u9:Add(task.spawn(function() --[[Anonymous function at line 37]]
        --[[
        Upvalues:
            [1] = u7
            [2] = u13
            [3] = u5
            [4] = u4
            [5] = u3
            [6] = u1
            [7] = u9
        --]]
        local v14 = u7.waitForFarm()
        local v15 = u13(workspace.Tutorial_Points.Tutorial_Point_1.Position)
        u7.waitUntilDistance(workspace.Tutorial_Points.Tutorial_Point_1.Position, 5)
        v15()
        u5:CreateNotification("Talk to Sam!")
        while not u4.Enabled do
            task.wait(0.1)
        end
        local u16 = u4.Frame.ScrollingFrame:WaitForChild("Carrot")
        local u18 = u7.pointToUI(function() --[[Anonymous function at line 53]]
            --[[
            Upvalues:
                [1] = u16
            --]]
            local v17 = u16.Main_Frame
            return v17.AbsolutePosition + v17.AbsoluteSize * 0.5
        end)
        local v20 = u4:GetPropertyChangedSignal("Enabled"):Connect(function() --[[Anonymous function at line 58]]
            --[[
            Upvalues:
                [1] = u4
                [2] = u18
                [3] = u7
                [4] = u16
            --]]
            if u4.Enabled then
                u18 = u7.pointToUI(function() --[[Anonymous function at line 62]]
                    --[[
                    Upvalues:
                        [1] = u16
                    --]]
                    local v19 = u16.Main_Frame
                    return v19.AbsolutePosition + v19.AbsoluteSize * 0.5
                end)
            else
                u18()
            end
        end)
        local v21 = u18
        while u16.Frame.Position.Y.Scale < 1 do
            task.wait(0.1)
        end
        v20:Disconnect()
        v21()
        local u23 = u7.pointToUI(function() --[[Anonymous function at line 76]]
            --[[
            Upvalues:
                [1] = u16
            --]]
            local v22 = u16.Frame.Sheckles_Buy
            return v22.AbsolutePosition + v22.AbsoluteSize * 0.5
        end)
        local v25 = u4:GetPropertyChangedSignal("Enabled"):Connect(function() --[[Anonymous function at line 81]]
            --[[
            Upvalues:
                [1] = u4
                [2] = u23
                [3] = u7
                [4] = u16
            --]]
            if u4.Enabled then
                u23 = u7.pointToUI(function() --[[Anonymous function at line 85]]
                    --[[
                    Upvalues:
                        [1] = u16
                    --]]
                    local v24 = u16.Frame.Sheckles_Buy
                    return v24.AbsolutePosition + v24.AbsoluteSize * 0.5
                end)
            else
                u23()
            end
        end)
        u7.waitForSeed()
        v25:Disconnect()
        u23()
        local v27 = u7.pointToUI(function() --[[Anonymous function at line 97]]
            --[[
            Upvalues:
                [1] = u4
            --]]
            local v26 = u4.Frame.Frame.ExitButton
            return v26.AbsolutePosition + v26.AbsoluteSize * 0.5
        end)
        while u4.Enabled do
            task.wait()
        end
        v27()
        local v28 = (v14.Spawn_Point.CFrame * CFrame.new(0, 0, -10)).Position
        local v29 = u13(v28)
        u7.waitUntilDistance(v28, 10)
        local v31 = u7.pointToUI(function() --[[Anonymous function at line 112]]
            --[[
            Upvalues:
                [1] = u3
            --]]
            local v30 = u3:WaitForChild("BackpackGui"):WaitForChild("Backpack"):WaitForChild("Hotbar"):WaitForChild("2")
            return v30.AbsolutePosition + Vector2.new(v30.AbsoluteSize.X * 0.5, 0)
        end)
        u7.waitUntilSeedEquipped()
        v31()
        v29()
        u5:CreateNotification("Click on your plot to plant!")
        local v32 = u1.Tutorial_Parts.Tutorial_Click:Clone()
        v32.Size = Vector3.new(0.001, 0.001, 0.001)
        v32.Position = (v14.Spawn_Point.CFrame * CFrame.new(15, -5, -15)).Position
        v32.Parent = workspace.Click_Points
        while #v14.Important.Plants_Physical:GetChildren() <= 0 do
            task.wait()
        end
        v32:Destroy()
        u5:CreateNotification("Wait for your plant to grow then Collect it")
        u7.waitUntilSellableItem()
        local v33 = u13(workspace.Tutorial_Points.Tutorial_Point_2.Position)
        u7.waitUntilDistance(workspace.Tutorial_Points.Tutorial_Point_2.Position, 5)
        v33()
        u5:CreateNotification("Talk to Steven!")
        while true do
            local u34 = u3:FindFirstChild("Billboard_UI")
            if u34 then
                u34 = u34:FindFirstChildWhichIsA("GuiObject")
            end
            if u34 and u34.Frame.Frame.Text_Element:GetAttribute("Text") == "I want to sell my inventory" then
                if u34 then
                    local v41 = u7.pointToUI(function() --[[Anonymous function at line 156]]
                        --[[
                        Upvalues:
                            [1] = u34
                            [2] = u3
                        --]]
                        local v35 = u34
                        local v36 = Vector2.zero
                        local v37 = u3:FindFirstChild("Billboard_UI")
                        if v37 then
                            local v38 = v37.Adornee or v37.Parent
                            if v38:IsA("PVInstance") then
                                local v39, v40 = workspace.Camera:WorldToScreenPoint(v38:GetPivot().Position)
                                if v40 then
                                    v36 = Vector2.new(v39.X, v39.Y)
                                else
                                    v36 = v36 + Vector2.new(0, 999999999999)
                                end
                            end
                        end
                        return v36 + Vector2.new(v35.AbsoluteSize.X * 0.5, 0)
                    end)
                    while true do
                        local v42 = u3:FindFirstChild("Billboard_UI")
                        if not (v42 and v42:FindFirstChildWhichIsA("GuiObject")) then
                            break
                        end
                        task.wait(0.1)
                    end
                    v41()
                end
                workspace:SetAttribute("InTutorial", nil)
                u9:Clean()
                return
            end
            task.wait(0.1)
        end
    end))
    return u9:WrapClean()
end