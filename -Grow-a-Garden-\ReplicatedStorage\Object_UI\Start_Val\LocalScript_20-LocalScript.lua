-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Object_UI\Start_Val\LocalScript_20-LocalScript.lua
local v1 = game:GetService("TweenService")
local v2 = TweenInfo.new(1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out, 0, false, 0)
local v3 = TweenInfo.new(0.07, Enum.EasingStyle.Quad, Enum.EasingDirection.Out, 0, false, 0)
local u4 = script.Parent.Parent.CanvasGroup.TextButton.Hold_Down_Val
local u5 = v1:Create(u4, v2, {
    ["Size"] = UDim2.new(1, 0, 1, 0)
})
local u6 = v1:Create(u4, v3, {
    ["BackgroundTransparency"] = 0.5
})
local u7 = time()
script.Parent:GetPropertyChangedSignal("Value"):Connect(function() --[[Anonymous function at line 8]]
    --[[
    Upvalues:
        [1] = u7
        [2] = u5
        [3] = u4
        [4] = u6
    --]]
    u7 = time()
    if script.Parent.Value == true then
        u5:Cancel()
        u4.Size = UDim2.new(0, 0, 1, 0)
        u4.BackgroundTransparency = 1
        u5:Play()
        u6:Play()
        u7 = time()
    else
        u5:Cancel()
        u6:Cancel()
        u4.BackgroundTransparency = 1
    end
end)
local u8 = game.ReplicatedStorage.GameEvents.Purchase_Object
local u9 = script.Parent.Parent.Object_ID
local u10 = game.Players.LocalPlayer.PlayerGui.Gradient_UI.Green_VAL
local u11 = game.Players.LocalPlayer.PlayerGui.Gradient_UI.Red_VAL
local u12 = game.SoundService.Error
u5.Completed:Connect(function() --[[Anonymous function at line 28]]
    --[[
    Upvalues:
        [1] = u7
        [2] = u8
        [3] = u9
        [4] = u10
        [5] = u11
        [6] = u12
    --]]
    if time() - u7 >= 0.95 then
        if script.Parent.Parent.Cost_Val.Value <= game.Players.LocalPlayer.leaderstats["$"].Value then
            u8:FireServer(u9.Value)
            u10.Value = true
        else
            u11.Value = true
            u12.PlaybackSpeed = 1 + math.random(-15, 15) / 100
            u12.TimePosition = 0
            u12.Playing = true
        end
        script.Parent.Value = false
    end
end)