-- Full Path: -Grow-a-Garden-\\CutsceneButtonTest-LocalScript.lua
local v1 = game:GetService("ReplicatedStorage")
local u2 = require(v1.Modules.CutsceneService)
local v3 = require(v1.Modules.Chalk)
local u4 = require(v1.Modules.Notification)
local u5 = game:GetService("CollectionService")
local u6 = game.Players.LocalPlayer
local u7 = ("You just got a %* %*! "):format(v3.color(Color3.fromRGB(255, 255, 255)).bold("CHICKEN"), (v3.color(Color3.fromRGB(49, 255, 114)).bold("ZOMBIE")))
task.spawn(function() --[[Anonymous function at line 11]]
    --[[
    Upvalues:
        [1] = u2
    --]]
    u2.attemptDelayedPreload(game.ReplicatedStorage.Assets.Cutscenes.BloodMoon)
end)
game.ReplicatedStorage.GameEvents.CutsceneHandler.OnClientEvent:Connect(function(p8) --[[Anonymous function at line 16]]
    --[[
    Upvalues:
        [1] = u2
        [2] = u5
        [3] = u6
        [4] = u4
        [5] = u7
    --]]
    u2.Play(p8, true)
    if u5:HasTag(u6, "GIVEN_CHICKEN_ZOMBIE") then
        u4:CreateNotification(u7)
    end
end)