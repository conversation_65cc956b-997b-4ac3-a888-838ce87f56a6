-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Rate_UI\CanvasGroup\TextButton\LocalScript_51-LocalScript.lua
local u1 = game.SoundService.Hover
local u2 = game.SoundService.Click
local v3 = game:GetService("UserInputService")
v3.InputBegan:Connect(function(p4, _) --[[Anonymous function at line 5]]
    --[[
    Upvalues:
        [1] = u2
    --]]
    if p4.KeyCode == Enum.KeyCode.ButtonR2 or p4.KeyCode == Enum.KeyCode.E then
        script.Parent.Parent.Parent.Start_Val.Value = true
        u2.PlaybackSpeed = 1 + math.random(-15, 15) / 100
        u2.TimePosition = 0
        u2.Playing = true
    end
end)
v3.InputEnded:Connect(function(p5, _) --[[Anonymous function at line 13]]
    if p5.KeyCode == Enum.KeyCode.ButtonR2 or p5.KeyCode == Enum.KeyCode.E then
        script.Parent.Parent.Parent.Start_Val.Value = false
    end
end)
script.Parent.MouseButton1Down:Connect(function() --[[Anonymous function at line 18]]
    --[[
    Upvalues:
        [1] = u2
    --]]
    script.Parent.Parent.Parent.Start_Val.Value = true
    u2.PlaybackSpeed = 1 + math.random(-15, 15) / 100
    u2.TimePosition = 0
    u2.Playing = true
end)
script.Parent.MouseLeave:Connect(function() --[[Anonymous function at line 25]]
    script.Parent.Parent.Parent.Start_Val.Value = false
end)
script.Parent.MouseButton1Up:Connect(function() --[[Anonymous function at line 29]]
    script.Parent.Parent.Parent.Start_Val.Value = false
end)
script.Parent.MouseEnter:Connect(function() --[[Anonymous function at line 33]]
    --[[
    Upvalues:
        [1] = u1
    --]]
    u1.PlaybackSpeed = 1 + math.random(-15, 15) / 100
    u1.TimePosition = 0
    u1.Playing = true
end)