-- Full Path: -Grow-a-Garden-\\BaseCharacterController-ModuleScript.lua
local v1 = script.Parent.Parent:Wait<PERSON><PERSON><PERSON>hil<PERSON>("CommonUtils")
local u2 = require(v1:WaitFor<PERSON>hild("ConnectionUtil"))
local u3 = Vector3.new()
local u4 = {}
u4.__index = u4
function u4.new() --[[Anonymous function at line 33]]
    --[[
    Upvalues:
        [1] = u4
        [2] = u3
        [3] = u2
    --]]
    local v5 = u4
    local v6 = setmetatable({}, v5)
    v6.enabled = false
    v6.moveVector = u3
    v6.moveVectorIsCameraRelative = true
    v6.isJumping = false
    v6._connectionUtil = u2.new()
    return v6
end
function u4.GetMoveVector(p7) --[[Anonymous function at line 45]]
    return p7.moveVector
end
function u4.IsMoveVectorCameraRelative(p8) --[[Anonymous function at line 49]]
    return p8.moveVectorIsCameraRelative
end
function u4.GetIsJumping(p9) --[[Anonymous function at line 53]]
    return p9.isJumping
end
function u4.Enable(_, _) --[[Anonymous function at line 59]]
    error("BaseCharacterController:Enable must be overridden in derived classes and should not be called.")
    return false
end
return u4