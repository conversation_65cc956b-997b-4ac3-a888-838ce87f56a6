-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\Fingers-ModuleScript.lua
local v1 = game:GetService("UserInputService")
local u2 = {}
v1.InputBegan:Connect(function(p3) --[[Anonymous function at line 5]]
    --[[
    Upvalues:
        [1] = u2
    --]]
    if p3.UserInputType == Enum.UserInputType.Touch then
        u2[p3] = true
    end
end)
v1.InputEnded:Connect(function(p4) --[[Anonymous function at line 10]]
    --[[
    Upvalues:
        [1] = u2
    --]]
    if p4.UserInputType == Enum.UserInputType.Touch then
        u2[p4] = nil
    end
end)
return {
    ["Get"] = function(_) --[[Function name: Get, line 17]]
        --[[
        Upvalues:
            [1] = u2
        --]]
        return u2
    end,
    ["GetOne"] = function(_) --[[Function name: GetOne, line 21]]
        --[[
        Upvalues:
            [1] = u2
        --]]
        return next(u2)
    end
}