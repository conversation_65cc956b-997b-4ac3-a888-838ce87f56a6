-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\EggRarityUIController-ModuleScript.lua
local v1 = game:GetService("Players")
local v2 = game:GetService("ReplicatedStorage")
require(v2.Modules.GetFarm)
local u3 = v1.LocalPlayer.PlayerGui:WaitFor<PERSON>hild("EggRarityDisplay_UI")
require(v2.Modules.WaitForDescendant)
local v4 = require(v2.Data.PetEggData)
local u5 = require(v2.Data.PetRegistry)
local u6 = require(game.ReplicatedStorage.Frame_Popup_Module)
local v7 = u3:WaitForChild("RarityDisplay"):WaitForChild("Main"):WaitFor<PERSON>hild("Holder")
local u8 = v7:WaitFor<PERSON>hild("Header"):WaitForChild("Exit"):WaitForChild("SENSOR")
local u9 = v7:WaitForChild("ScrollingFrame"):WaitForChild("Content")
local u10 = u9:WaitForChild("SEGMENT")
local u11 = {}
local u12 = {}
local u13 = {
    ["Common Egg"] = "rbxassetid://110495383642358",
    ["Uncommon Egg"] = "rbxassetid://116378505754760",
    ["Rare Egg"] = "rbxassetid://111372276806205",
    ["Mythical Egg"] = "rbxassetid://83210224500991",
    ["Legendary Egg"] = "rbxassetid://97799911854888",
    ["Bug Egg"] = "rbxassetid://83970205286930"
}
for v14, v15 in v4 do
    table.insert(u11, {
        ["Key"] = v14,
        ["Value"] = v15
    })
end
table.sort(u11, function(p16, p17) --[[Anonymous function at line 43]]
    local v18 = p16.Value.StockChance
    local v19 = p17.Value.StockChance
    if v18 == v19 then
        return p16.Value.EggName < p17.Value.EggName
    else
        return v19 < v18
    end
end)
local u20 = false
function u12.Show(_, p21) --[[Anonymous function at line 54]]
    --[[
    Upvalues:
        [1] = u20
        [2] = u8
        [3] = u12
        [4] = u6
        [5] = u3
    --]]
    if p21 and u20 == false then
        u8.Activated:Connect(function() --[[Anonymous function at line 57]]
            --[[
            Upvalues:
                [1] = u12
            --]]
            u12:Show(false)
        end)
        u12:Populate()
        u20 = true
    end
    if p21 == true then
        u6.Show(u3.RarityDisplay)
    else
        u6.Hide(u3.RarityDisplay)
    end
    print("ya")
    u3.Enabled = p21
end
function u12.Populate(_) --[[Anonymous function at line 72]]
    --[[
    Upvalues:
        [1] = u9
        [2] = u11
        [3] = u10
        [4] = u13
        [5] = u5
    --]]
    for _, v22 in u9:GetChildren() do
        if v22.Name == "SEGMENT_UNUSED" then
            v22:Destroy()
        end
    end
    local u23 = {}
    for _, v24 in u11 do
        local v25 = {
            ["name"] = v24.Value.EggName,
            ["chance"] = 1 / v24.Value.StockChance
        }
        table.insert(u23, v25)
    end
    for _, v26 in u11 do
        local v27 = u10:Clone()
        local v28 = v27:WaitForChild("Display")
        local v29 = v28:WaitForChild("DisplayBox"):WaitForChild("EGG_IMAGE")
        local v30 = v28:WaitForChild("RARITY_NAME")
        local v31 = v28:WaitForChild("RARITY_PERCENTAGE")
        v27.Parent = u10.Parent
        v29.Image = u13[v26.Value.EggName]
        v30.Text = v26.Value.EggName
        v30.TextColor3 = u5.PetEggs[v26.Value.EggName].Color
        local u32 = v26.Value.EggName
        local v35 = (1 - (function() --[[Function name: SingleSlotNoEgg, line 97]]
            --[[
            Upvalues:
                [1] = u23
                [2] = u32
            --]]
            local v33 = 1
            for _, v34 in ipairs(u23) do
                if v34.name == u32 then
                    return v33 * (1 - v33 * v34.chance)
                end
                v33 = v33 * (1 - v34.chance)
            end
            return v33
        end)() ^ 3) * 100
        v31.Text = ("%*%% of shops"):format((("%.0f"):format(v35)))
        v31.TextColor3 = Color3.fromRGB(255, 255, 255)
        v27.LayoutOrder = 1000 / v35
    end
    wait(1)
    u10:Destroy()
end
return u12