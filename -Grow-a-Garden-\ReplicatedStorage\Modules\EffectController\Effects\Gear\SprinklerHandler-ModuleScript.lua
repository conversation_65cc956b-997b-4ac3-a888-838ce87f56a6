-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\EffectController\Effects\Gear\SprinklerHandler-ModuleScript.lua
local u1 = game:GetService("ReplicatedStorage")
game:GetService("CollectionService")
local u2 = game:GetService("Players")
local u3 = require(u1.Modules.GetFarm)
require(u1.Modules.EffectController.Types)
local u4 = {
    ["Basic Sprinkler"] = CFrame.new(0, -0.85, 0),
    ["Advanced Sprinkler"] = CFrame.new(0, -0.65, 0),
    ["Godly Sprinkler"] = CFrame.new(0, -0.85, 0),
    ["Master Sprinkler"] = CFrame.new(0, -0.85, 0),
    ["Chocolate Sprinkler"] = CFrame.new(0, -0.85, 0)
}
local u5 = {
    ["Basic Sprinkler"] = 0.6,
    ["Advanced Sprinkler"] = 0.2,
    ["Godly Sprinkler"] = 0.1,
    ["Master Sprinkler"] = 0.05,
    ["Chocolate Sprinkler"] = 0.25
}
local u6 = {
    ["Basic Sprinkler"] = 30,
    ["Advanced Sprinkler"] = 20,
    ["Godly Sprinkler"] = 75,
    ["Master Sprinkler"] = 144,
    ["Chocolate Sprinkler"] = 25
}
return {
    ["Create"] = function(u7) --[[Function name: Create, line 38]]
        --[[
        Upvalues:
            [1] = u1
            [2] = u2
            [3] = u3
            [4] = u4
            [5] = u6
            [6] = u5
        --]]
        local v8 = u7.Parameters.ID
        u7.Cache[v8] = {}
        local u9 = u7.Parameters.SprinklerType
        local u10 = u7.Parameters.SprinklerCFrame
        local v11 = u1.ObjectModels:FindFirstChild(u9)
        if not v11 then
            return
        end
        local v12 = u7.Parameters.OWNER
        if not v12 then
            return
        end
        local v13 = u2:FindFirstChild(v12)
        if not v13 then
            return
        end
        local v14 = u3(v13)
        if not v14 then
            return
        end
        local v15 = v14:FindFirstChild("Important")
        if v15 then
            v15 = v15:FindFirstChild("Objects_Physical")
        end
        if not v15 then
            return
        end
        local u16 = v11:Clone()
        u16:SetAttribute("EndTime", u7.Parameters.EndTime)
        u16.PrimaryPart.CFrame = u10 * CFrame.new(0, 1, 0)
        u16.Parent = workspace.Visuals
        local v17 = u7.Parameters.OBJECT_UUID
        for _, u18 in v15:GetChildren() do
            if u18:GetAttribute("OBJECT_UUID") == v17 then
                u18:GetAttributeChangedSignal("Lifetime"):Connect(function() --[[Anonymous function at line 70]]
                    --[[
                    Upvalues:
                        [1] = u16
                        [2] = u18
                    --]]
                    u16:SetAttribute("EndTime", u18:GetAttribute("Lifetime"))
                end)
                u18.Destroying:Connect(function() --[[Anonymous function at line 74]]
                    --[[
                    Upvalues:
                        [1] = u16
                    --]]
                    u16:Destroy()
                end)
                break
            end
        end
        local v19 = u7.Default:CreateEffect({
            ["Object"] = script.PlaceEffect,
            ["Emit"] = true,
            ["Position"] = u10 * CFrame.Angles(0, 3.141592653589793, 3.141592653589793),
            ["DebrisTime"] = 2
        })
        u7.Default:PlaySound(script.PlaceSFX, v19)
        local v20 = u7.Libraries.BoatTween
        local v21 = u16.PrimaryPart
        local v22 = {
            ["Time"] = 0.8,
            ["EasingStyle"] = "Smoother",
            ["EasingDirection"] = "In",
            ["StepType"] = "Heartbeat",
            ["Goal"] = {
                ["CFrame"] = u10 * u4[u9]
            }
        }
        v20:Create(v21, v22):Play()
        task.spawn(function() --[[Anonymous function at line 103]]
            --[[
            Upvalues:
                [1] = u7
                [2] = u9
                [3] = u16
                [4] = u10
                [5] = u6
                [6] = u5
            --]]
            local v23 = u7.Default:CreateEffect({
                ["Object"] = script[string.format("%s VFX", u9)],
                ["Parent"] = u16,
                ["Position"] = u10
            })
            u7.Default:PlaySound(script.SprinklerLoop, v23)
            local v24 = u16:WaitForChild("Top"):WaitForChild("Root")
            while u16 and u16.Parent do
                local v25 = v24.CFrame
                local v26 = CFrame.Angles
                local v27 = u6[u9]
                v24.CFrame = v25 * v26(0, math.rad(v27), 0)
                local v28 = u5[u9] * 100
                local v29 = task.wait
                local v30 = math.random
                local v31 = v28 / 3
                v29(v30(math.floor(v31), v28) / 100)
            end
        end)
        u7.Cache[v8].Sprinkler = u16
        u7.Cache[v8].SprinklerEffect = u7.Cache[v8].SprinklerEffect
    end,
    ["Destroy"] = function(p32) --[[Function name: Destroy, line 125]]
        local v33 = p32.Parameters.ID
        local v34 = p32.Cache[v33]
        if v34 and typeof(v34) == "table" then
            local u35 = v34.Sprinkler
            if u35 then
                local v36 = v34.SprinklerEffect
                if v36 then
                    p32.Default:UpdateStatus(v36, false, {})
                    local v37 = p32.Libraries.BoatTween
                    local v38 = u35.PrimaryPart
                    local v39 = {
                        ["Time"] = 0.6,
                        ["EasingStyle"] = "ExitExpressive",
                        ["EasingDirection"] = "In",
                        ["StepType"] = "Heartbeat",
                        ["Goal"] = {
                            ["CFrame"] = p32.Parameters.SprinklerCFrame * CFrame.new(0, 3, 0)
                        }
                    }
                    v37:Create(v38, v39):Play()
                    task.delay(0.6, function() --[[Anonymous function at line 144]]
                        --[[
                        Upvalues:
                            [1] = u35
                        --]]
                        if u35 then
                            u35:Destroy()
                        end
                    end)
                end
            else
                return
            end
        else
            return
        end
    end,
    ["Cancel"] = function(p40) --[[Function name: Cancel, line 151]]
        p40.Container:Clean()
        p40.Cache = {}
    end
}