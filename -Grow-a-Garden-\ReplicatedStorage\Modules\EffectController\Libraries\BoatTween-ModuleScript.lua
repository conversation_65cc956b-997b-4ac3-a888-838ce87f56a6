-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\EffectController\Libraries\BoatTween-ModuleScript.lua
local u1 = game:GetService("RunService")
local v2 = require(script.TweenFunctions)
local u3 = require(script.Lerps)
local v4 = {}
local u5 = {
    ["Heartbeat"] = true,
    ["Stepped"] = true,
    ["RenderStepped"] = true
}
if not u1:IsClient() then
    u5.RenderStepped = nil
end
local u6 = {
    ["FabricAccelerate"] = {
        ["In"] = v2.InFabricAccelerate,
        ["Out"] = v2.OutFabricAccelerate,
        ["InOut"] = v2.InOutFabricAccelerate,
        ["OutIn"] = v2.OutInFabricAccelerate
    },
    ["UWPAccelerate"] = {
        ["In"] = v2.InUWPAccelerate,
        ["Out"] = v2.OutUWPAccelerate,
        ["InOut"] = v2.InOutUWPAccelerate,
        ["OutIn"] = v2.OutInUWPAccelerate
    },
    ["Circ"] = {
        ["In"] = v2.InCirc,
        ["Out"] = v2.OutCirc,
        ["InOut"] = v2.InOutCirc,
        ["OutIn"] = v2.OutInCirc
    },
    ["RevBack"] = {
        ["In"] = v2.InRevBack,
        ["Out"] = v2.OutRevBack,
        ["InOut"] = v2.InOutRevBack,
        ["OutIn"] = v2.OutInRevBack
    },
    ["Spring"] = {
        ["In"] = v2.InSpring,
        ["Out"] = v2.OutSpring,
        ["InOut"] = v2.InOutSpring,
        ["OutIn"] = v2.OutInSpring
    },
    ["Standard"] = {
        ["In"] = v2.InStandard,
        ["Out"] = v2.OutStandard,
        ["InOut"] = v2.InOutStandard,
        ["OutIn"] = v2.OutInStandard
    },
    ["StandardExpressive"] = {
        ["In"] = v2.InStandardExpressive,
        ["Out"] = v2.OutStandardExpressive,
        ["InOut"] = v2.InOutStandardExpressive,
        ["OutIn"] = v2.OutInStandardExpressive
    },
    ["Linear"] = {
        ["In"] = v2.InLinear,
        ["Out"] = v2.OutLinear,
        ["InOut"] = v2.InOutLinear,
        ["OutIn"] = v2.OutInLinear
    },
    ["ExitProductive"] = {
        ["In"] = v2.InExitProductive,
        ["Out"] = v2.OutExitProductive,
        ["InOut"] = v2.InOutExitProductive,
        ["OutIn"] = v2.OutInExitProductive
    },
    ["Deceleration"] = {
        ["In"] = v2.InDeceleration,
        ["Out"] = v2.OutDeceleration,
        ["InOut"] = v2.InOutDeceleration,
        ["OutIn"] = v2.OutInDeceleration
    },
    ["Smoother"] = {
        ["In"] = v2.InSmoother,
        ["Out"] = v2.OutSmoother,
        ["InOut"] = v2.InOutSmoother,
        ["OutIn"] = v2.OutInSmoother
    },
    ["FabricStandard"] = {
        ["In"] = v2.InFabricStandard,
        ["Out"] = v2.OutFabricStandard,
        ["InOut"] = v2.InOutFabricStandard,
        ["OutIn"] = v2.OutInFabricStandard
    },
    ["RidiculousWiggle"] = {
        ["In"] = v2.InRidiculousWiggle,
        ["Out"] = v2.OutRidiculousWiggle,
        ["InOut"] = v2.InOutRidiculousWiggle,
        ["OutIn"] = v2.OutInRidiculousWiggle
    },
    ["MozillaCurve"] = {
        ["In"] = v2.InMozillaCurve,
        ["Out"] = v2.OutMozillaCurve,
        ["InOut"] = v2.InOutMozillaCurve,
        ["OutIn"] = v2.OutInMozillaCurve
    },
    ["Expo"] = {
        ["In"] = v2.InExpo,
        ["Out"] = v2.OutExpo,
        ["InOut"] = v2.InOutExpo,
        ["OutIn"] = v2.OutInExpo
    },
    ["Sine"] = {
        ["In"] = v2.InSine,
        ["Out"] = v2.OutSine,
        ["InOut"] = v2.InOutSine,
        ["OutIn"] = v2.OutInSine
    },
    ["Cubic"] = {
        ["In"] = v2.InCubic,
        ["Out"] = v2.OutCubic,
        ["InOut"] = v2.InOutCubic,
        ["OutIn"] = v2.OutInCubic
    },
    ["EntranceExpressive"] = {
        ["In"] = v2.InEntranceExpressive,
        ["Out"] = v2.OutEntranceExpressive,
        ["InOut"] = v2.InOutEntranceExpressive,
        ["OutIn"] = v2.OutInEntranceExpressive
    },
    ["Elastic"] = {
        ["In"] = v2.InElastic,
        ["Out"] = v2.OutElastic,
        ["InOut"] = v2.InOutElastic,
        ["OutIn"] = v2.OutInElastic
    },
    ["Quint"] = {
        ["In"] = v2.InQuint,
        ["Out"] = v2.OutQuint,
        ["InOut"] = v2.InOutQuint,
        ["OutIn"] = v2.OutInQuint
    },
    ["EntranceProductive"] = {
        ["In"] = v2.InEntranceProductive,
        ["Out"] = v2.OutEntranceProductive,
        ["InOut"] = v2.InOutEntranceProductive,
        ["OutIn"] = v2.OutInEntranceProductive
    },
    ["Bounce"] = {
        ["In"] = v2.InBounce,
        ["Out"] = v2.OutBounce,
        ["InOut"] = v2.InOutBounce,
        ["OutIn"] = v2.OutInBounce
    },
    ["Smooth"] = {
        ["In"] = v2.InSmooth,
        ["Out"] = v2.OutSmooth,
        ["InOut"] = v2.InOutSmooth,
        ["OutIn"] = v2.OutInSmooth
    },
    ["Back"] = {
        ["In"] = v2.InBack,
        ["Out"] = v2.OutBack,
        ["InOut"] = v2.InOutBack,
        ["OutIn"] = v2.OutInBack
    },
    ["Quart"] = {
        ["In"] = v2.InQuart,
        ["Out"] = v2.OutQuart,
        ["InOut"] = v2.InOutQuart,
        ["OutIn"] = v2.OutInQuart
    },
    ["StandardProductive"] = {
        ["In"] = v2.InStandardProductive,
        ["Out"] = v2.OutStandardProductive,
        ["InOut"] = v2.InOutStandardProductive,
        ["OutIn"] = v2.OutInStandardProductive
    },
    ["Quad"] = {
        ["In"] = v2.InQuad,
        ["Out"] = v2.OutQuad,
        ["InOut"] = v2.InOutQuad,
        ["OutIn"] = v2.OutInQuad
    },
    ["FabricDecelerate"] = {
        ["In"] = v2.InFabricDecelerate,
        ["Out"] = v2.OutFabricDecelerate,
        ["InOut"] = v2.InOutFabricDecelerate,
        ["OutIn"] = v2.OutInFabricDecelerate
    },
    ["Acceleration"] = {
        ["In"] = v2.InAcceleration,
        ["Out"] = v2.OutAcceleration,
        ["InOut"] = v2.InOutAcceleration,
        ["OutIn"] = v2.OutInAcceleration
    },
    ["SoftSpring"] = {
        ["In"] = v2.InSoftSpring,
        ["Out"] = v2.OutSoftSpring,
        ["InOut"] = v2.InOutSoftSpring,
        ["OutIn"] = v2.OutInSoftSpring
    },
    ["ExitExpressive"] = {
        ["In"] = v2.InExitExpressive,
        ["Out"] = v2.OutExitExpressive,
        ["InOut"] = v2.InOutExitExpressive,
        ["OutIn"] = v2.OutInExitExpressive
    },
    ["Sharp"] = {
        ["In"] = v2.InSharp,
        ["Out"] = v2.OutSharp,
        ["InOut"] = v2.InOutSharp,
        ["OutIn"] = v2.OutInSharp
    }
}
function v4.Create(_, u7, p8) --[[Anonymous function at line 315]]
    --[[
    Upvalues:
        [1] = u5
        [2] = u1
        [3] = u6
        [4] = u3
    --]]
    if not u7 or typeof(u7) ~= "Instance" then
        return warn("Invalid object to tween:", u7)
    end
    local u9 = type(p8) == "table" and (p8 or {}) or {}
    local u10 = u5[u9.StepType] and u1[u9.StepType] or u1.Stepped
    local u11 = u6[u9.EasingStyle or "Quad"][u9.EasingDirection or "In"]
    local v12 = u9.Time
    local v13 = type(v12) == "number" and (u9.Time or 1) or 1
    local u14 = math.max(v13, 0.001)
    local v15 = u9.Goal
    local v16 = type(v15) == "table" and (u9.Goal or {}) or {}
    local v17 = u9.DelayTime
    local u18
    if type(v17) == "number" and u9.DelayTime > 0.027 then
        u18 = u9.DelayTime
    else
        u18 = false
    end
    local v19 = u9.RepeatCount
    local v20
    if type(v19) == "number" then
        local v21 = u9.RepeatCount
        v20 = math.max(v21, -1) or 0
    else
        v20 = 0
    end
    local u22 = v20 + 1
    local u23 = {}
    for v24, v25 in pairs(v16) do
        u23[v24] = u3[typeof(v25)](u7[v24], v25)
    end
    local u26 = Instance.new("BindableEvent")
    local u27 = Instance.new("BindableEvent")
    local u28 = Instance.new("BindableEvent")
    local u29 = nil
    local u30 = os.clock()
    local u31 = 0
    local u32 = {
        ["Instance"] = u7,
        ["PlaybackState"] = Enum.PlaybackState.Begin,
        ["Completed"] = u26.Event,
        ["Resumed"] = u28.Event,
        ["Stopped"] = u27.Event,
        ["Destroy"] = function() --[[Function name: Destroy, line 353]]
            --[[
            Upvalues:
                [1] = u29
                [2] = u26
                [3] = u27
                [4] = u28
                [5] = u32
            --]]
            if u29 then
                u29:Disconnect()
                u29 = nil
            end
            u26:Destroy()
            u27:Destroy()
            u28:Destroy()
            u32 = {}
        end
    }
    local u33 = false
    local u34 = 0
    local function u46(p35, u36) --[[Anonymous function at line 369]]
        --[[
        Upvalues:
            [1] = u29
            [2] = u22
            [3] = u32
            [4] = u26
            [5] = u33
            [6] = u34
            [7] = u18
            [8] = u30
            [9] = u31
            [10] = u10
            [11] = u14
            [12] = u23
            [13] = u7
            [14] = u46
            [15] = u9
            [16] = u11
        --]]
        if u29 then
            u29:Disconnect()
            u29 = nil
        end
        local u37 = p35 or 1
        if u22 == 0 or u22 >= u37 then
            u34 = u37
            if u36 then
                u33 = true
            end
            if u18 then
                u32.PlaybackState = Enum.PlaybackState.Delayed
                task.wait(u18)
            end
            u30 = os.clock() - u31
            u29 = u10:Connect(function() --[[Anonymous function at line 399]]
                --[[
                Upvalues:
                    [1] = u31
                    [2] = u30
                    [3] = u14
                    [4] = u36
                    [5] = u23
                    [6] = u7
                    [7] = u29
                    [8] = u46
                    [9] = u37
                    [10] = u9
                    [11] = u11
                --]]
                u31 = os.clock() - u30
                if u14 <= u31 then
                    if u36 then
                        for v38, v39 in pairs(u23) do
                            u7[v38] = v39(0)
                        end
                    else
                        for v40, v41 in pairs(u23) do
                            u7[v40] = v41(1)
                        end
                    end
                    u29:Disconnect()
                    u29 = nil
                    if u36 then
                        u31 = 0
                        u46(u37 + 1, false)
                        return
                    elseif u9.Reverses then
                        u31 = 0
                        u46(u37, true)
                    else
                        u31 = 0
                        u46(u37 + 1, false)
                    end
                else
                    local v42 = u11(u36 and 1 - u31 / u14 or u31 / u14)
                    local v43 = math.clamp(v42, 0, 1)
                    for v44, v45 in pairs(u23) do
                        u7[v44] = v45(v43)
                    end
                    return
                end
            end)
            u32.PlaybackState = Enum.PlaybackState.Playing
        else
            u32.PlaybackState = Enum.PlaybackState.Completed
            u26:Fire()
            u33 = false
            u34 = 1
        end
    end
    function u32.Play() --[[Anonymous function at line 439]]
        --[[
        Upvalues:
            [1] = u31
            [2] = u46
        --]]
        u31 = 0
        u46(1, false)
    end
    function u32.Stop() --[[Anonymous function at line 444]]
        --[[
        Upvalues:
            [1] = u29
            [2] = u32
            [3] = u27
        --]]
        if u29 then
            u29:Disconnect()
            u29 = nil
            u32.PlaybackState = Enum.PlaybackState.Cancelled
            u27:Fire()
        end
    end
    function u32.Resume() --[[Anonymous function at line 453]]
        --[[
        Upvalues:
            [1] = u46
            [2] = u34
            [3] = u33
            [4] = u28
        --]]
        u46(u34, u33)
        u28:Fire()
    end
    return u32
end
return v4