-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\PlayerLuck-ModuleScript.lua
local function u8(p1, p2, p3) --[[Anonymous function at line 9]]
    local v4 = {
        {
            ["Name"] = "Default",
            ["Modifier"] = 1
        }
    }
    if p3 == "Plant" or (p3 == "Grow" or p3 == "BottomDisplay") then
        local v5
        if p2 then
            v5 = p2:GetAttribute("FruitVariantLuck")
        else
            v5 = nil
        end
        if type(v5) == "number" and v5 ~= 1 then
            local v6 = {
                ["Name"] = "FruitVariantLuck",
                ["Modifier"] = v5 - 1
            }
            table.insert(v4, v6)
        end
    end
    if p3 == "BottomDisplay" then
        local v7 = p1:GetAttribute("SessionTimeLuck")
        if v7 and v7 ~= 0 then
            table.insert(v4, {
                ["Name"] = "SessionTimeLuck",
                ["Modifier"] = v7
            })
        end
    end
    return v4
end
return {
    ["GetModifiers"] = u8,
    ["GetLuck"] = function(p9, p10, p11, p12) --[[Function name: PlayerLuck, line 41]]
        --[[
        Upvalues:
            [1] = u8
        --]]
        local v13 = 0
        for _, v14 in u8(p9, p10, p11) do
            if not (p12 and table.find(p12, v14.Name)) then
                v13 = v13 + v14.Modifier
            end
        end
        return v13
    end,
    ["Listen"] = function(p15, p16) --[[Function name: Listen, line 53]]
        local u17 = {}
        local v18 = p15:GetAttributeChangedSignal("SessionTimeLuck")
        table.insert(u17, v18:Connect(p16))
        local u19 = task.spawn(p16)
        return function() --[[Anonymous function at line 60]]
            --[[
            Upvalues:
                [1] = u19
                [2] = u17
            --]]
            if coroutine.status(u19) == "suspended" then
                pcall(task.cancel, u19)
            end
            for _, v20 in u17 do
                if v20.Connected then
                    v20:Disconnect()
                end
            end
            table.clear(u17)
        end
    end
}