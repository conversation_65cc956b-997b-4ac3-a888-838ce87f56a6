-- Full Path: -Grow-a-Garden-\\Field_Of_View_Module-ModuleScript.lua
local v1 = {}
local u2 = game:GetService("TweenService")
local u3 = game.Players.LocalPlayer
function v1.Setup() --[[Anonymous function at line 4]]
    --[[
    Upvalues:
        [1] = u3
    --]]
    u3:SetAttribute("Core_FOV", 70)
end
function v1.Change_FOV_CORE(p4) --[[Anonymous function at line 7]]
    --[[
    Upvalues:
        [1] = u3
    --]]
    if u3 and p4 then
        u3:SetAttribute("Core_FOV", p4)
    end
end
function v1.Return_Core_FOV() --[[Anonymous function at line 12]]
    --[[
    Upvalues:
        [1] = u3
    --]]
    return u3:GetAttribute("Core_FOV")
end
function v1.Change_FOV(p5, p6) --[[Anonymous function at line 15]]
    --[[
    Upvalues:
        [1] = u3
        [2] = u2
    --]]
    if game.Workspace.CurrentCamera then
        local v7 = p6 == nil and 0.5 or p6
        if p5 == 70 then
            p5 = u3:GetAttribute("Core_FOV")
        end
        local v8 = TweenInfo.new(v7 * (math.random(95, 105) * 0.01), Enum.EasingStyle.Sine, Enum.EasingDirection.Out, 0, false, 0)
        local v9 = u2:Create(game.Workspace.CurrentCamera, v8, {
            ["FieldOfView"] = p5
        })
        v9:Play()
        game.Debris:AddItem(v9, v8.Time)
    end
end
return v1