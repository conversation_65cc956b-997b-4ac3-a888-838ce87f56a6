-- Full Path: -Grow-a-Garden-\ReplicatedStorage\NPC_MOD-ModuleScript.lua
local v1 = {}
local u2 = require(game.ReplicatedStorage.Field_Of_View_Module)
local u3 = require(game.ReplicatedStorage.Frame_Popup_Module)
function v1.Can_Speak(_) --[[Anonymous function at line 4]]
    --[[
    Upvalues:
        [1] = u2
    --]]
    return u2.Return_Core_FOV() == 70
end
function v1.Start_Speak(_) --[[Anonymous function at line 13]]
    --[[
    Upvalues:
        [1] = u2
    --]]
    u2.Change_FOV_CORE(65)
    u2.Change_FOV(65, 2)
end
function v1.End_Speak(_) --[[Anonymous function at line 17]]
    --[[
    Upvalues:
        [1] = u3
        [2] = u2
    --]]
    if u3.Can() == true then
        u2.Change_FOV_CORE(70)
        u2.Change_FOV(70, 1)
    end
end
return v1