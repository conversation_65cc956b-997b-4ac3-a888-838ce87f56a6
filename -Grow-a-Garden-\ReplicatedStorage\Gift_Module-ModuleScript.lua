-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Gift_Module-ModuleScript.lua
local v1 = {}
game:GetService("RunService")
local u2 = game.ReplicatedStorage.Gift_Notification
function v1.Gift(p3, p4, p5, _, p6) --[[Anonymous function at line 6]]
    --[[
    Upvalues:
        [1] = u2
    --]]
    print(p3, p4)
    local v7 = u2:Clone()
    v7.Parent = p3.PlayerGui.Gift_Notification.Frame
    v7.Holder.TextLabel.Text = p5.Name
    v7.Holder.Tool_OBJ.Value = p5
    v7.Holder.String_OF.Value = p6
    v7.Holder.PLR_OBJ.Value = p4
    v7.Holder.PLR_String.Value = p4.Name
end
return v1