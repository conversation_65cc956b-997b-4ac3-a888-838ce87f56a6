-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Data\SeedPackData-ModuleScript.lua
game:GetService("RunService"):IsStudio()
local v1 = table.freeze
local v2 = {}
local v3 = {
    ["DisplayName"] = "Normal Seed Pack",
    ["Items"] = {
        {
            ["Type"] = "Seed",
            ["RewardId"] = "Pumpkin",
            ["Chance"] = 25
        },
        {
            ["Type"] = "Seed",
            ["RewardId"] = "Watermelon",
            ["Chance"] = 20
        },
        {
            ["Type"] = "Seed",
            ["RewardId"] = "Peach",
            ["Chance"] = 20
        },
        {
            ["Type"] = "Seed",
            ["RewardId"] = "Raspberry",
            ["Chance"] = 10
        },
        {
            ["Type"] = "Seed",
            ["RewardId"] = "Dragon Fruit",
            ["Chance"] = 10
        },
        {
            ["Type"] = "Seed",
            ["RewardId"] = "Cactus",
            ["Chance"] = 10
        },
        {
            ["Type"] = "Seed",
            ["RewardId"] = "Pineapple",
            ["Chance"] = 5
        }
    }
}
v2.Normal = v3
local v4 = {
    ["DisplayName"] = "Exotic Seed Pack",
    ["Items"] = {
        {
            ["Type"] = "Seed",
            ["RewardId"] = "Papaya",
            ["Icon"] = "rbxassetid://137358951402692",
            ["Chance"] = 40
        },
        {
            ["Type"] = "Seed",
            ["RewardId"] = "Banana",
            ["Icon"] = "rbxassetid://118516260773130",
            ["Chance"] = 38
        },
        {
            ["Type"] = "Seed",
            ["RewardId"] = "Passionfruit",
            ["Icon"] = "rbxassetid://139621938553116",
            ["Chance"] = 20
        },
        {
            ["Type"] = "Seed",
            ["RewardId"] = "Soul Fruit",
            ["Icon"] = "rbxassetid://111245465505679",
            ["Chance"] = 1.5
        },
        {
            ["Type"] = "Seed",
            ["RewardId"] = "Cursed Fruit",
            ["Icon"] = "rbxassetid://89675695188581",
            ["Chance"] = 0.5
        }
    }
}
v2.Exotic = v4
local v5 = {
    ["DisplayName"] = "Basic Seed Pack",
    ["Items"] = {
        {
            ["Type"] = "Seed",
            ["RewardId"] = "Raspberry",
            ["Icon"] = "rbxassetid://86654246455569",
            ["Chance"] = 40,
            ["RemoveChance"] = true
        },
        {
            ["Type"] = "Seed",
            ["RewardId"] = "Cranberry",
            ["Icon"] = "rbxassetid://84025739268823",
            ["Chance"] = 30,
            ["RemoveChance"] = true
        },
        {
            ["Type"] = "Seed",
            ["RewardId"] = "Durian",
            ["Icon"] = "rbxassetid://80039191190165",
            ["Chance"] = 21,
            ["RemoveChance"] = true
        },
        {
            ["Type"] = "Seed",
            ["RewardId"] = "Eggplant",
            ["Icon"] = "rbxassetid://121511136640913",
            ["Chance"] = 8.735,
            ["RemoveChance"] = true
        },
        {
            ["Type"] = "Seed",
            ["RewardId"] = "Lotus",
            ["Icon"] = "rbxassetid://104511535272743",
            ["Chance"] = 0.25,
            ["RemoveChance"] = true
        },
        {
            ["Type"] = "Seed",
            ["RewardId"] = "Venus Fly Trap",
            ["Icon"] = "rbxassetid://139210236985330",
            ["Chance"] = 0.01,
            ["RemoveChance"] = true
        },
        {
            ["Type"] = "Pack",
            ["DisplayName"] = "Rainbow Sack",
            ["RewardId"] = "RainbowSeedSackBasic",
            ["Icon"] = "rbxassetid://102551065819622",
            ["Chance"] = 0.005,
            ["RemoveChance"] = true
        }
    }
}
v2.SeedSackBasic = v5
local v6 = {
    ["DisplayName"] = "Premium Seed Pack",
    ["Items"] = {
        {
            ["Type"] = "Seed",
            ["RewardId"] = "Cranberry",
            ["Icon"] = "rbxassetid://84025739268823",
            ["Chance"] = 45
        },
        {
            ["Type"] = "Seed",
            ["RewardId"] = "Durian",
            ["Icon"] = "rbxassetid://80039191190165",
            ["Chance"] = 35
        },
        {
            ["Type"] = "Seed",
            ["RewardId"] = "Eggplant",
            ["Icon"] = "rbxassetid://121511136640913",
            ["Chance"] = 16
        },
        {
            ["Type"] = "Seed",
            ["RewardId"] = "Lotus",
            ["Icon"] = "rbxassetid://104511535272743",
            ["Chance"] = 2.5
        },
        {
            ["Type"] = "Seed",
            ["RewardId"] = "Venus Fly Trap",
            ["Icon"] = "rbxassetid://139210236985330",
            ["Chance"] = 1
        },
        {
            ["Type"] = "Pack",
            ["DisplayName"] = "Rainbow Sack",
            ["RewardId"] = "RainbowSeedSackPremium",
            ["Icon"] = "rbxassetid://102551065819622",
            ["Chance"] = 0.5
        }
    }
}
v2.SeedSackPremium = v6
local v7 = {
    ["DisplayName"] = "Rainbow Basic Seed Pack",
    ["Items"] = {
        {
            ["Type"] = "RainbowSeed",
            ["RewardId"] = "Cranberry",
            ["Icon"] = "rbxassetid://84025739268823",
            ["Chance"] = 55,
            ["RemoveChance"] = true
        },
        {
            ["Type"] = "RainbowSeed",
            ["RewardId"] = "Durian",
            ["Icon"] = "rbxassetid://80039191190165",
            ["Chance"] = 36,
            ["RemoveChance"] = true
        },
        {
            ["Type"] = "RainbowSeed",
            ["RewardId"] = "Eggplant",
            ["Icon"] = "rbxassetid://121511136640913",
            ["Chance"] = 8.735,
            ["RemoveChance"] = true
        },
        {
            ["Type"] = "RainbowSeed",
            ["RewardId"] = "Lotus",
            ["Icon"] = "rbxassetid://104511535272743",
            ["Chance"] = 0.25,
            ["RemoveChance"] = true
        },
        {
            ["Type"] = "RainbowSeed",
            ["RewardId"] = "Venus Fly Trap",
            ["Icon"] = "rbxassetid://139210236985330",
            ["Chance"] = 0.01,
            ["RemoveChance"] = true
        }
    }
}
v2.RainbowSeedSackBasic = v7
local v8 = {
    ["DisplayName"] = "Rainbow Premium Seed Pack",
    ["Items"] = {
        {
            ["Type"] = "RainbowSeed",
            ["RewardId"] = "Cranberry",
            ["Icon"] = "rbxassetid://84025739268823",
            ["Chance"] = 45
        },
        {
            ["Type"] = "RainbowSeed",
            ["RewardId"] = "Durian",
            ["Icon"] = "rbxassetid://80039191190165",
            ["Chance"] = 35
        },
        {
            ["Type"] = "RainbowSeed",
            ["RewardId"] = "Eggplant",
            ["Icon"] = "rbxassetid://121511136640913",
            ["Chance"] = 16
        },
        {
            ["Type"] = "RainbowSeed",
            ["RewardId"] = "Lotus",
            ["Icon"] = "rbxassetid://104511535272743",
            ["Chance"] = 2.5
        },
        {
            ["Type"] = "RainbowSeed",
            ["RewardId"] = "Venus Fly Trap",
            ["Icon"] = "rbxassetid://139210236985330",
            ["Chance"] = 1
        }
    }
}
v2.RainbowSeedSackPremium = v8
local v9 = {
    ["DisplayName"] = "Night Seed Pack",
    ["Items"] = {
        {
            ["Type"] = "Seed",
            ["RewardId"] = "Nightshade",
            ["Chance"] = 40
        },
        {
            ["Type"] = "Seed",
            ["RewardId"] = "Glowshroom",
            ["Chance"] = 20
        },
        {
            ["Type"] = "Seed",
            ["RewardId"] = "Mint",
            ["Chance"] = 15
        },
        {
            ["Type"] = "Seed",
            ["RewardId"] = "Moonflower",
            ["Chance"] = 10
        },
        {
            ["Type"] = "Seed",
            ["RewardId"] = "Starfruit",
            ["Chance"] = 9.5
        },
        {
            ["Type"] = "Seed",
            ["RewardId"] = "Moonglow",
            ["Chance"] = 5
        },
        {
            ["Type"] = "Seed",
            ["RewardId"] = "Moon Blossom",
            ["Chance"] = 0.5
        }
    }
}
v2.Night = v9
local v10 = {
    ["DisplayName"] = "Premium Night Seed Pack",
    ["Items"] = {
        {
            ["Type"] = "Seed",
            ["RewardId"] = "Glowshroom",
            ["Chance"] = 30
        },
        {
            ["Type"] = "Seed",
            ["RewardId"] = "Mint",
            ["Chance"] = 22
        },
        {
            ["Type"] = "Seed",
            ["RewardId"] = "Moonflower",
            ["Chance"] = 18
        },
        {
            ["Type"] = "Seed",
            ["RewardId"] = "Starfruit",
            ["Chance"] = 15.5
        },
        {
            ["Type"] = "Seed",
            ["RewardId"] = "Moonglow",
            ["Chance"] = 12
        },
        {
            ["Type"] = "Seed",
            ["RewardId"] = "Moon Blossom",
            ["Chance"] = 2.5
        }
    }
}
v2.NightPremium = v10
return {
    ["Packs"] = v1(v2),
    ["GetTextDisplayForItem"] = function(_, p11) --[[Function name: GetTextDisplayForItem, line 380]]
        if p11.DisplayName then
            return p11.DisplayName
        else
            return p11.RewardId
        end
    end
}