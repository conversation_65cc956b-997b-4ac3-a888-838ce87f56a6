-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\ReplicationClass-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
v1:Wait<PERSON><PERSON><PERSON>hild("Modules")
local u2 = v1:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("GameEvents"):Wait<PERSON><PERSON><PERSON><PERSON><PERSON>("DataStream")
local v3 = require(script:<PERSON><PERSON><PERSON><PERSON>hil<PERSON>("class"))
local u4 = require(script:<PERSON><PERSON><PERSON><PERSON>hil<PERSON>("Signal"))
local u5 = require(script:Wait<PERSON><PERSON><PERSON>hild("TableListener"))
local u6 = require(script:<PERSON><PERSON><PERSON><PERSON>hild("DeepClone"))
local u7 = {}
local u8 = {}
u2.OnClientEvent:Connect(function(...) --[[Anonymous function at line 17]]
    --[[
    Upvalues:
        [1] = u7
        [2] = u5
        [3] = u6
        [4] = u8
    --]]
    local v9 = { ... }
    local v10 = v9[1]
    if v10 == "InitData" then
        local v11 = v9[2]
        local v12 = v9[3]
        if v11 and v12 then
            u7[v11] = u5.new(v12)
        end
    else
        if v10 == "UpdateData" then
            local u13 = v9[2]
            local u14 = u7[u13]
            if not u14 then
                return
            end
            for _, u15 in v9[3] do
                task.spawn(function() --[[Anonymous function at line 35]]
                    --[[
                    Upvalues:
                        [1] = u14
                        [2] = u15
                        [3] = u6
                        [4] = u8
                        [5] = u13
                    --]]
                    local v16 = u14:GetTable()
                    local v17 = u15[1]
                    local v18 = u15[2]
                    local v19 = v17:split("/")
                    if #v19 < 1 then
                        return
                    end
                    table.remove(v19, 1)
                    local v20 = table.concat(v19, "/", 1, #v19)
                    local v21 = table.concat(v19, "/", 1, #v19 == 1 and 1 or #v19 - 1)
                    local v22 = nil
                    for v23, v24 in v19 do
                        local v25 = tonumber(v24) or v24
                        if v22 then
                            v22 = ("%*/%*"):format(v22, v25)
                        else
                            v22 = v25
                        end
                        local v26 = u14:FindPathSignal((("%*/@"):format(v22)))
                        if v26 then
                            v26:Fire(v25, v18, v20)
                        end
                        if v23 == #v19 then
                            local v27 = v16[v25]
                            if type(v18) == "table" then
                                local v28 = v16[v25]
                                if type(v28) == "table" then
                                    v16[v25] = u6(v18)
                                    goto l18
                                end
                            end
                            v16[v25] = v18
                            ::l18::
                            local v29 = u14:FindPathSignal((("%*"):format(v21)))
                            if v29 then
                                v29:Fire(v25, v18, v27, v21)
                            end
                            local v30 = u14:FindPathSignal((("%*/*"):format(v21)))
                            if v30 then
                                v30:Fire(v25, v18, v27, v20)
                            end
                            local v31 = u8[u13]
                            if v31 then
                                v31.GlobalChanged:Fire(v25, v18, v27, v20)
                            end
                            local v32 = u14:FindPathSignal((("%*/%*"):format(v21, v25)))
                            if v32 then
                                v32:Fire()
                                return
                            end
                            break
                        end
                        local v33 = v16[v25] or {}
                        v16[v25] = v33
                        if v25 == #v19 - 1 then
                            v16 = v33
                        else
                            v16 = v33
                        end
                    end
                end)
            end
        end
        return
    end
end)
local v36 = v3({
    ["Constructor"] = function(p34, p35) --[[Function name: Constructor, line 116]]
        --[[
        Upvalues:
            [1] = u4
            [2] = u2
            [3] = u8
        --]]
        p34.Name = p35
        p34.GlobalChanged = u4.new()
        u2:FireServer("InitData")
        u8[p35] = p34
    end,
    ["Data"] = nil
})
function v36.GetData(p37) --[[Anonymous function at line 127]]
    --[[
    Upvalues:
        [1] = u7
    --]]
    return u7[p37.Name]
end
function v36.YieldUntilData(p38, p39) --[[Anonymous function at line 132]]
    local v40 = os.clock()
    local v41 = p39 or (1 / 0)
    while true do
        local v42 = p38:GetData()
        if v42 or v41 <= os.clock() - v40 then
            break
        end
        task.wait()
    end
    return v42
end
function v36.GetPathSignal(p43, p44) --[[Anonymous function at line 145]]
    local v45 = p43:YieldUntilData()
    if v45 then
        return v45:GetPathSignal(p44)
    end
end
return v36