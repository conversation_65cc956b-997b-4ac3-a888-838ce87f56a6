-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\ParticleCullingController-ModuleScript.lua
local u1 = game:GetService("CollectionService")
local u2 = game:GetService("UserInputService")
local u3 = game:GetService("RunService")
local _ = game:GetService("Players").LocalPlayer
local u4 = workspace.CurrentCamera
local v25 = {
    ["Start"] = function(_) --[[Function name: Start, line 15]]
        --[[
        Upvalues:
            [1] = u2
            [2] = u3
            [3] = u4
            [4] = u1
        --]]
        local u5 = u2.TouchEnabled and not u2.MouseEnabled and 40 or 80
        local u6 = table.create(6)
        local u7 = {}
        for _, v8 in workspace.Farm:GetChildren() do
            local u9 = v8.Important.Data.Farm_Number.Value
            local v10 = v8:WaitForChild("Center_Point", 5)
            if v10 then
                u6[u9] = v10:GetPivot()
                local function v13(p11) --[[Anonymous function at line 43]]
                    --[[
                    Upvalues:
                        [1] = u9
                        [2] = u7
                    --]]
                    if p11:IsA("ParticleEmitter") or (p11:IsA("Trail") or p11:IsA("Beam")) then
                        p11:AddTag((("%*_Particle"):format(u9)))
                        local v12 = u7[u9] == true
                        p11.Enabled = v12
                        if not v12 and p11.ClassName ~= "Beam" then
                            p11:Clear()
                        end
                    end
                end
                v8.DescendantAdded:Connect(v13)
                for _, v14 in v8:GetDescendants() do
                    task.spawn(v13, v14)
                end
            else
                print("Center point part not found for farm", v8)
            end
        end
        local u15 = 0
        local u16 = nil
        u3.PostSimulation:Connect(function(p17) --[[Anonymous function at line 60]]
            --[[
            Upvalues:
                [1] = u15
                [2] = u4
                [3] = u16
                [4] = u6
                [5] = u5
                [6] = u7
                [7] = u1
            --]]
            u15 = u15 + p17
            if u15 > 0.1 then
                u15 = 0
                local v18 = u4.CFrame.Position
                local v19 = v18 // 5
                if u16 == nil or u16 ~= v19 then
                    u16 = v19
                    for v20, v21 in u6 do
                        local v22 = v18 - v21.Position
                        local v23 = vector.magnitude(v22) <= u5
                        if u7[v20] ~= v23 then
                            u7[v20] = v23
                            for _, v24 in u1:GetTagged((("%*_Particle"):format(v20))) do
                                v24.Enabled = v23
                                if not v23 and v24.ClassName ~= "Beam" then
                                    v24:Clear()
                                end
                            end
                        end
                    end
                end
            end
        end)
    end
}
task.spawn(v25.Start, v25)
return v25