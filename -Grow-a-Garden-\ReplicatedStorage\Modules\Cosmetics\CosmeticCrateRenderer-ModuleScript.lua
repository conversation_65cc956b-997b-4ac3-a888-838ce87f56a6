-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\Cosmetics\CosmeticCrateRenderer-ModuleScript.lua
local u1 = game:GetService("ReplicatedStorage")
game:GetService("ServerScriptService")
local u2 = game:GetService("RunService")
local _ = u1.Assets.SFX
local _ = u1.Assets.VFX
local u3 = {}
require(u1.Modules.TweenModel)
local v4 = require(u1.Modules.CreateTagHandler)
require(u1.Modules.Notification)
require(u1.Modules.UpdateService)
local v5 = u1:WaitForChild("Assets")
local u6 = v5:WaitForChild("Models"):WaitForChild("CrateModels")
local u7 = require(u1.Data.CosmeticRegistry).CosmeticList
local u8 = require(u1.Data.CosmeticCrateRegistry).CosmeticCrates
local u9 = v5.SFX
local _ = v5.VFX
game:GetService("TweenService")
local u10 = u1.GameEvents.CosmeticCrateService
local _ = u1.GameEvents.CosmeticCrateSkipped
local v11 = u1.GameEvents.CrateReadyToOpen_RE
local u12 = {}
local u13 = {}
local u14 = game.Players.LocalPlayer
function easeOutBack(p15)
    local v16 = p15 - 1
    local v17 = math.pow(v16, 3) * 2.70158 + 1
    local v18 = p15 - 1
    return v17 + math.pow(v18, 2) * 1.70158
end
function easeInBack(p19)
    return 1.70158 * p19 * p19 * (2.70158 * p19 - 1.70158)
end
local function u60(u20, u21) --[[Anonymous function at line 61]]
    --[[
    Upvalues:
        [1] = u12
        [2] = u13
        [3] = u9
        [4] = u7
        [5] = u2
        [6] = u3
    --]]
    if u20 then
        local v22 = u12[u20]
        if v22 then
            local u23 = v22.Asset
            if u23 then
                local v24 = u20:GetAttribute("OBJECT_UUID")
                if v24 then
                    if not u23:GetAttribute("DoingOpen") then
                        u23:SetAttribute("DoingOpen", true)
                        u23.Parent = workspace.Visuals
                        local v25 = u13[v24]
                        u20:GetAttribute("CrateType")
                        for _, v26 in u23:GetDescendants() do
                            if v26:IsA("Weld") then
                                v26.Enabled = false
                            end
                        end
                        local u27 = u20:GetPivot()
                        local u28 = u20:GetExtentsSize()
                        local u29 = u9.CrateOpen:Clone()
                        u29.Parent = workspace
                        u29:Play()
                        u29.Ended:Once(function() --[[Anonymous function at line 44]]
                            --[[
                            Upvalues:
                                [1] = u29
                            --]]
                            u29:Destroy()
                        end)
                        if v25 and v25 ~= "" then
                            local u30 = u7[v25].Model:Clone()
                            if u30.PrimaryPart then
                                u30.PrimaryPart.Anchored = true
                            end
                            u30.Parent = workspace.Visuals
                            for _, v31 in u30:GetDescendants() do
                                if v31:IsA("BasePart") then
                                    v31.CanCollide = false
                                    v31.CanQuery = false
                                end
                            end
                            task.spawn(function() --[[Anonymous function at line 109]]
                                --[[
                                Upvalues:
                                    [1] = u30
                                    [2] = u27
                                    [3] = u28
                                    [4] = u2
                                    [5] = u21
                                    [6] = u9
                                --]]
                                local v32 = u30:GetExtentsSize()
                                local u33 = u27 * CFrame.new(0, -u28.Y / 2, 0) * CFrame.new(0, v32.Y / 2, 0)
                                local v34 = tick()
                                u30:ScaleTo(0.01)
                                u30:PivotTo(u33)
                                local u35 = 0
                                local u36 = true
                                task.spawn(function() --[[Anonymous function at line 126]]
                                    --[[
                                    Upvalues:
                                        [1] = u36
                                        [2] = u30
                                        [3] = u2
                                        [4] = u35
                                        [5] = u33
                                    --]]
                                    while u36 and u30:IsDescendantOf(workspace) do
                                        u35 = u35 + u2.Heartbeat:Wait()
                                        local v37 = u35 * 3.141592653589793 * 2 / 1.5
                                        local v38 = math.sin(v37) * 1.5
                                        local v39 = u35 * 65
                                        local v40 = math.rad(v39)
                                        u30:PivotTo(u33 * CFrame.new(0, v38, 0) * CFrame.Angles(0, v40, 0))
                                    end
                                end)
                                local v41 = 0
                                while v41 < 0.8 and u30:IsDescendantOf(workspace) do
                                    v41 = v41 + u2.Heartbeat:Wait()
                                    local v42 = v41 / 0.8
                                    local v43 = math.clamp(v42, 0, 1)
                                    u30:ScaleTo(0.01 + (u21 - 0.01) * easeOutBack(v43))
                                end
                                while tick() - v34 < 3 and u30:IsDescendantOf(workspace) do
                                    u2.Heartbeat:Wait()
                                end
                                local v44 = u21
                                local v45 = 0
                                while v45 < 0.8 and u30:IsDescendantOf(workspace) do
                                    v45 = v45 + u2.Heartbeat:Wait()
                                    local v46 = v45 / 0.8
                                    local v47 = math.clamp(v46, 0, 1)
                                    local v48 = easeInBack(v47)
                                    local v49 = v44 * (1 - math.clamp(v48, 0, 1.2))
                                    u30:ScaleTo((math.max(v49, 0.01)))
                                end
                                u36 = false
                                local u50 = u9.PopSound:Clone()
                                u50.Parent = workspace
                                u50:Play()
                                u50.Ended:Once(function() --[[Anonymous function at line 44]]
                                    --[[
                                    Upvalues:
                                        [1] = u50
                                    --]]
                                    u50:Destroy()
                                end)
                                u30:Destroy()
                            end)
                        end
                        local v51 = u23:GetBoundingBox().Position
                        local v52 = Random.new()
                        for _, v53 in u23:GetDescendants() do
                            if v53:IsA("BasePart") then
                                v53.Anchored = false
                                v53.CanCollide = false
                                v53.CanQuery = false
                                v53.CanTouch = false
                                local v54 = (v53.Position - v51).Unit * 15
                                local v55 = NumberRange.new(20, 40)
                                local v56 = v52:NextNumber(-10, 10)
                                local v57 = v52:NextNumber(v55.Min, v55.Max)
                                v53.AssemblyLinearVelocity = v54 + Vector3.new(v56, v57, v52:NextNumber(-10, 10))
                                local v58 = v52:NextNumber(-3, 3)
                                local v59 = v52:NextNumber(-3, 3)
                                v53.AssemblyAngularVelocity = Vector3.new(v58, v59, v52:NextNumber(-3, 3))
                            end
                        end
                        task.delay(5, function() --[[Anonymous function at line 206]]
                            --[[
                            Upvalues:
                                [1] = u23
                                [2] = u3
                                [3] = u20
                            --]]
                            u23:SetAttribute("DoingOpen", false)
                            u3:DerenderCrate(u20)
                        end)
                    end
                else
                    return warn("DoOpenAnim | No Crate uuid!")
                end
            else
                warn("DoOpenAnim | No asset!")
                return
            end
        else
            warn("DoOpenAnim | No state data for server Crate")
            return
        end
    else
        warn("DoOpenAnim | No server Crate!")
        return
    end
end
local function u66(p61, p62) --[[Anonymous function at line 213]]
    --[[
    Upvalues:
        [1] = u12
        [2] = u60
    --]]
    if p61 then
        local v63 = u12[p61]
        if v63 then
            local v64 = v63.Asset
            if v64 then
                local v65 = v64:FindFirstChild("ProximityPrompt")
                if not v65 then
                    return warn((("CosmeticCrateRenderer:RenderCrate | No Proximity Prompt found for %*"):format(v64)))
                end
                v65:Destroy()
                u60(p61, p62)
            else
                warn("ActivateCrate | No asset!")
            end
        else
            warn("ActivateCrate | No state data for server Crate")
            return
        end
    else
        warn("ActivateCrate | No server Crate!")
        return
    end
end
local function u75(p67) --[[Anonymous function at line 230]]
    --[[
    Upvalues:
        [1] = u14
        [2] = u12
        [3] = u8
    --]]
    if p67:GetAttribute("OWNER") == u14.Name then
        local v68 = p67:GetAttribute("TimeToOpen")
        local v69 = u12[p67]
        if v69 then
            local v70 = v69.Asset
            if v70 then
                local v71 = v70:FindFirstChild("ProximityPrompt")
                if not v71 then
                    return warn((("CheckCrateTimer | No Proximity Prompt found for %*"):format(v70)))
                end
                local v72 = p67:GetAttribute("CrateType")
                if not v72 then
                    return warn((("CheckCrateTimer | No CrateType for %*"):format(p67)))
                end
                local v73 = u8[v72].TimeIsTimestamp ~= true
                v71.Enabled = v68 <= 0
                if v73 then
                    local v74 = v70:FindFirstChild("SkipPrompt")
                    if not v74 then
                        return warn((("CheckCrateTimer | No Skip Prompt found for %*"):format(v70)))
                    end
                    v74.Enabled = v68 > 0
                end
            else
                warn("CheckCrateTimer | No asset!")
            end
        else
            warn("CheckCrateTimer | No state data for server Crate")
            return
        end
    else
        return
    end
end
function u3.RenderCrate(_, u76) --[[Anonymous function at line 253]]
    --[[
    Upvalues:
        [1] = u6
        [2] = u75
        [3] = u12
        [4] = u14
        [5] = u1
        [6] = u10
        [7] = u8
    --]]
    if not u76:GetAttribute("READY") then
        u76:GetAttributeChangedSignal("READY"):Wait()
    end
    local v77 = u76:GetAttribute("CrateType")
    if not v77 then
        local v78 = 0
        repeat
            task.wait(0.5)
            v77 = u76:GetAttribute("CrateType")
            print("tryting to CrateType for", u76:GetFullName(), v77)
            v78 = v78 + 1
        until v78 >= 5 or v77
    end
    if v77 then
        local v79 = u6:FindFirstChild(v77)
        if v79 then
            local u80 = v79:Clone()
            u80.PrimaryPart.Anchored = true
            local u81 = Vector3.new(0, 1.5, 0)
            local u82 = 0
            task.spawn(function() --[[Anonymous function at line 293]]
                --[[
                Upvalues:
                    [1] = u82
                    [2] = u81
                    [3] = u80
                    [4] = u76
                --]]
                while u82 < 0.25 do
                    u82 = u82 + game:GetService("RunService").Heartbeat:Wait()
                    u81 = (Vector3.new(0, 3, 0)):Lerp(Vector3.new(0, 0, 0), (game.TweenService:GetValue(u82 / 0.25, Enum.EasingStyle.Quad, Enum.EasingDirection.Out)))
                    local v83 = u80:GetExtentsSize().Y
                    u80:PivotTo(u76:GetPivot() * CFrame.new(0, u81.Y + v83 * 0.5, 0))
                end
            end)
            u76:GetAttributeChangedSignal("TimeToOpen"):Connect(function() --[[Anonymous function at line 306]]
                --[[
                Upvalues:
                    [1] = u75
                    [2] = u76
                --]]
                u75(u76)
            end)
            u12[u76] = {
                ["Asset"] = u80
            }
            local v84 = u80:GetExtentsSize().Y
            u80:PivotTo(u76:GetPivot() * CFrame.new(0, u81.Y + v84 * 0.4, 0))
            u80.Parent = u76
            if u76:GetAttribute("OWNER") == u14.Name then
                local v85 = u1.ProximityPrompt:Clone()
                if not v85 then
                    return warn("CosmeticCrateRenderer:RenderCrate | No Proximity Prompt found in ReplicatedStorage!")
                end
                v85.Parent = u80
                v85.ActionText = "Open!"
                v85.HoldDuration = 1
                v85.Enabled = false
                v85.Triggered:Connect(function() --[[Anonymous function at line 327]]
                    --[[
                    Upvalues:
                        [1] = u10
                        [2] = u76
                    --]]
                    u10:FireServer("OpenCrate", u76)
                end)
                if u8[v77].TimeIsTimestamp ~= true then
                    local v86 = u1.ProximityPrompt:Clone()
                    if not v86 then
                        return warn("CosmeticCrateRenderer:RenderCrate | No Proximity Prompt found in ReplicatedStorage!")
                    end
                    v86.Parent = u80
                    v86.ActionText = "Skip Open Time"
                    v86.Name = "SkipPrompt"
                    v86.HoldDuration = 1
                    v86.Enabled = false
                    v86.Triggered:Connect(function() --[[Anonymous function at line 342]]
                        --[[
                        Upvalues:
                            [1] = u10
                            [2] = u76
                        --]]
                        u10:FireServer("AuthorisePurchase", u76)
                    end)
                end
                u75(u76)
            end
        else
            return warn((("Could not find Crate model associated with %*"):format(v77)))
        end
    else
        return
    end
end
function u3.DerenderCrate(_, p87) --[[Anonymous function at line 351]]
    --[[
    Upvalues:
        [1] = u12
    --]]
    if p87 then
        local v88 = u12[p87]
        if v88 then
            local v89 = v88.Asset
            if v89 then
                if not v89:GetAttribute("DoingOpen") then
                    v89:Destroy()
                    u12[p87] = nil
                end
            else
                warn("CosmeticCrateRenderer:DerenderCrate | No asset to derender")
            end
        else
            warn("CosmeticCrateRenderer:DerenderCrate | No state data for server Crate")
            return
        end
    else
        warn("CosmeticCrateRenderer:DerenderCrate | No server Crate to derender")
        return
    end
end
v4({
    ["Tag"] = "CosmeticCrateServer",
    ["OnInstanceAdded"] = function(p90) --[[Function name: OnInstanceAdded, line 370]]
        --[[
        Upvalues:
            [1] = u3
        --]]
        if p90:IsDescendantOf(workspace) then
            u3:RenderCrate(p90)
        end
    end,
    ["OnInstanceRemoved"] = function(p91) --[[Function name: OnInstanceRemoved, line 374]]
        --[[
        Upvalues:
            [1] = u3
        --]]
        u3:DerenderCrate(p91)
    end
})
v11.OnClientEvent:Connect(function(p92, p93) --[[Anonymous function at line 379]]
    --[[
    Upvalues:
        [1] = u13
    --]]
    u13[p93] = p92
end)
u10.OnClientEvent:Connect(function(p94, p95) --[[Anonymous function at line 383]]
    --[[
    Upvalues:
        [1] = u66
    --]]
    u66(p94, p95)
end)
return u3