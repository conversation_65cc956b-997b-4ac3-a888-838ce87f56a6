-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\Settings\SettingsService-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
local u2 = require(v1.Modules.DataService)
local u3 = v1:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("GameEvents"):Wait<PERSON><PERSON><PERSON>hild("SettingsService")
return {
    ["SetSetting"] = function(_, p4, p5) --[[Function name: SetSetting, line 10]]
        --[[
        Upvalues:
            [1] = u3
        --]]
        u3:FireServer("SetSetting", p4, p5)
    end,
    ["GetSetting"] = function(_, p6) --[[Function name: GetSetting, line 14]]
        --[[
        Upvalues:
            [1] = u2
        --]]
        local v7 = u2:GetData()
        if v7 then
            return v7.Settings[p6]
        end
    end
}