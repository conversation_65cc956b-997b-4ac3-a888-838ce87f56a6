-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\ClaimableCodeService-ModuleScript.lua
local u1 = game:GetService("ReplicatedStorage"):WaitForChild("GameEvents"):WaitForChild("ClaimableCodeService")
return {
    ["ClaimCode"] = function(_, p2) --[[Function name: ClaimCode, line 8]]
        --[[
        Upvalues:
            [1] = u1
        --]]
        return u1:FireServer("ClaimCode", p2)
    end
}