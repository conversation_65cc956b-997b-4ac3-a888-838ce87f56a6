-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\ByteNet\namespaces\namespacesDependencies-ModuleScript.lua
local u1 = nil
local u2 = nil
return {
    ["start"] = function(p3) --[[Function name: start, line 10]]
        --[[
        Upvalues:
            [1] = u1
            [2] = u2
        --]]
        u1 = {}
        u2 = p3
    end,
    ["add"] = function(p4) --[[Function name: add, line 15]]
        --[[
        Upvalues:
            [1] = u1
        --]]
        if u1 then
            local v5 = u1
            table.insert(v5, p4)
        end
    end,
    ["currentLength"] = function() --[[Function name: currentLength, line 23]]
        --[[
        Upvalues:
            [1] = u1
        --]]
        return u1 and #u1 or 0
    end,
    ["currentName"] = function() --[[Function name: currentName, line 27]]
        --[[
        Upvalues:
            [1] = u2
        --]]
        return u2
    end,
    ["empty"] = function() --[[Function name: empty, line 31]]
        --[[
        Upvalues:
            [1] = u1
        --]]
        if u1 == nil then
            return {}
        end
        local v6 = u1
        u1 = nil
        return v6
    end
}