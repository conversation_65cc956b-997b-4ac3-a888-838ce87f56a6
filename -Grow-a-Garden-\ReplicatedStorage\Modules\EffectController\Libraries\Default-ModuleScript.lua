-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\EffectController\Libraries\Default-ModuleScript.lua
local u1 = game:GetService("Lighting")
game:GetService("ReplicatedStorage")
local u2 = game:GetService("RunService")
local u3 = game:GetService("TweenService")
local u4 = require(script.Parent.Parent.Utility.Utility)
local u5 = require(script.Parent.Parent.Utility.Tables)
local u6 = require(script.Parent.BoatTween)
local u52 = {
    ["CreateEffect"] = function(_, p7) --[[Function name: CreateEffect, line 34]]
        --[[
        Upvalues:
            [1] = u52
            [2] = u4
        --]]
        local v8 = p7.Object:Clone()
        v8.Parent = p7.Parent or workspace.Visuals
        if p7.Position then
            local v9 = p7.Position
            v8:PivotTo(typeof(v9) == "Vector3" and CFrame.new(p7.Position) or p7.Position)
        end
        if p7.Emit then
            task.delay(p7.Emit<PERSON><PERSON><PERSON>, u52.Emit, u52, v8, 1)
        end
        if p7.Attach and (v8 and (p7.Parent and (p7.Parent:IsA("Part") or p7.Parent:IsA("BasePart")))) then
            if v8:IsA("Model") then
                u52:Attach(v8.PrimaryPart, p7.Parent)
            else
                u52:Attach(v8, p7.Parent)
            end
        end
        if p7.DebrisTime then
            u4:AddItem(v8, p7.DebrisTime)
        end
        if p7.RaycastColored then
            local v10 = workspace:Raycast(p7.RaycastColored.Origin, p7.RaycastColored.Direction, u4.RayParams)
            if not v10 then
                return
            end
            for _, v11 in v8:GetDescendants() do
                if v11:IsA("ParticleEmitter") and v11:GetAttribute("Raycast") then
                    v11.Color = ColorSequence.new(v10.Instance.Color, v10.Instance.Color)
                end
            end
        end
        return v8
    end,
    ["Emit"] = function(_, p12, u13, p14) --[[Function name: Emit, line 76]]
        --[[
        Upvalues:
            [1] = u3
            [2] = u2
        --]]
        for _, u15 in p12:GetDescendants() do
            if not (p14 and p14[u15.Name]) then
                if u15:IsA("ParticleEmitter") then
                    local v16 = u15:GetAttribute("EmitDuration") or 0
                    if v16 < 0.05 then
                        task.delay(u15:GetAttribute("EmitDelay"), function() --[[Anonymous function at line 86]]
                            --[[
                            Upvalues:
                                [1] = u15
                            --]]
                            u15:Emit((u15:GetAttribute("EmitCount")))
                        end)
                    else
                        local u17 = os.clock() + v16
                        task.spawn(function() --[[Anonymous function at line 92]]
                            --[[
                            Upvalues:
                                [1] = u15
                                [2] = u17
                            --]]
                            repeat
                                u15:Emit((u15:GetAttribute("EmitCount")))
                                task.wait(0.1)
                            until u17 - os.clock() <= 0
                        end)
                    end
                elseif u15:IsA("PointLight") or (u15:IsA("SpotLight") or u15:IsA("SurfaceLight")) then
                    u3:Create(u15, TweenInfo.new(u13 or 1), {
                        ["Brightness"] = 0
                    }):Play()
                elseif u15:IsA("Decal") then
                    u3:Create(u15, TweenInfo.new(u13 or 1), {
                        ["Transparency"] = 1
                    }):Play()
                elseif u15:IsA("Beam") then
                    local function u19(p18) --[[Anonymous function at line 104]]
                        --[[
                        Upvalues:
                            [1] = u15
                        --]]
                        u15.Transparency = NumberSequence.new({ NumberSequenceKeypoint.new(0, p18), NumberSequenceKeypoint.new(1, p18) })
                    end
                    task.spawn(function() --[[Anonymous function at line 111]]
                        --[[
                        Upvalues:
                            [1] = u15
                            [2] = u13
                            [3] = u3
                            [4] = u19
                            [5] = u2
                        --]]
                        local v20 = os.clock()
                        local v21 = u15.Transparency.Keypoints[2].Value
                        repeat
                            local v22 = (os.clock() - v20) / u13
                            u19((u3:GetValue(math.clamp(v22, v21, 1), Enum.EasingStyle.Quad, Enum.EasingDirection.InOut)))
                            u2.Heartbeat:Wait()
                        until u13 < os.clock() - v20
                        u19(1)
                    end)
                end
            end
        end
    end,
    ["Attach"] = function(_, p23, p24, p25) --[[Function name: Attach, line 130]]
        --[[
        Upvalues:
            [1] = u4
        --]]
        local v26 = Instance.new("WeldConstraint")
        p23.Massless = true
        p23.CanCollide = false
        v26.Part0 = p24
        v26.Part1 = p23
        v26.Parent = p23
        if p25 then
            u4:AddItem(v26, p25)
        end
    end,
    ["PlaySound"] = function(_, p27, p28) --[[Function name: PlaySound, line 146]]
        --[[
        Upvalues:
            [1] = u4
        --]]
        local v29 = p27:Clone()
        v29.Parent = p28
        v29:Play()
        u4:AddItem(v29, p27.TimeLength > 0.1 and p27.TimeLength or 1)
        return v29
    end,
    ["SetScale"] = function(_, p30, p31, p32) --[[Function name: SetScale, line 156]]
        for _, v33 in p30:GetDescendants() do
            if v33:IsA("ParticleEmitter") then
                if not (table.find(p32, v33) or table.find(p32, v33.Name)) then
                    local v34 = {}
                    for _, v35 in ipairs(v33.Size.Keypoints) do
                        local v36 = NumberSequenceKeypoint.new
                        local v37 = v35.Time
                        local v38 = v35.Value * p31
                        local v39 = v35.Envelope * p31
                        table.insert(v34, v36(v37, v38, v39))
                    end
                    local v40 = NumberSequence.new(v34)
                    local v41 = NumberRange.new(v33.Speed.Min * p31, v33.Speed.Max * p31)
                    local v42 = v33.Acceleration * p31
                    v33.Size = v40
                    v33.Speed = v41
                    v33.Acceleration = v42
                end
            elseif v33:IsA("Attachment") then
                v33.Position = v33.Position * p31
            elseif v33:IsA("Beam") then
                v33.CurveSize0 = v33.CurveSize0 * p31
                v33.CurveSize1 = v33.CurveSize1 * p31
                v33.Width0 = v33.Width0 * p31
                v33.Width1 = v33.Width1 * p31
            end
        end
        p30:SetAttribute("Scale", p31)
    end,
    ["ScaleTo"] = function(_, u43, u44, u45, p46) --[[Function name: ScaleTo, line 189]]
        --[[
        Upvalues:
            [1] = u3
            [2] = u52
            [3] = u2
        --]]
        local u47 = p46 or ({} or p46)
        if u47 then
            task.spawn(function() --[[Anonymous function at line 196]]
                --[[
                Upvalues:
                    [1] = u43
                    [2] = u45
                    [3] = u3
                    [4] = u44
                    [5] = u52
                    [6] = u47
                    [7] = u2
                --]]
                local v48 = os.clock()
                local v49 = u43:GetAttribute("Scale") or 1
                repeat
                    local v50 = (os.clock() - v48) / u45
                    local v51 = u3:GetValue(math.clamp(v50, 0, 1), Enum.EasingStyle.Sine, Enum.EasingDirection.InOut)
                    u52:SetScale(u43, v49 + (u44 - v49) * v51, u47)
                    u2.Heartbeat:Wait()
                until u45 < os.clock() - v48 or not u43
                if u43 then
                    u52:SetScale(u43, u44, u47)
                end
            end)
        end
    end,
    ["AdjustSpeed"] = function(_) --[[Function name: AdjustSpeed, line 218]] end,
    ["Recolor"] = function(_) --[[Function name: Recolor, line 222]] end
}
if u2:IsClient() then
    local u53 = require(script.Parent.CameraShaker)
    local u54 = workspace.CurrentCamera
    local u56 = u53.new(Enum.RenderPriority.Camera.Value, function(p55) --[[Anonymous function at line 230]]
        --[[
        Upvalues:
            [1] = u54
        --]]
        u54.CFrame = u54.CFrame * p55
    end)
    u56:Start()
    function u52.ShakeCamera(_, p57) --[[Anonymous function at line 236]]
        --[[
        Upvalues:
            [1] = u56
            [2] = u53
        --]]
        u56:Shake(u53.Presets[p57])
    end
    function u52.ShakeSustain(_, p58) --[[Anonymous function at line 240]]
        --[[
        Upvalues:
            [1] = u56
            [2] = u53
        --]]
        return u56:ShakeSustain(u53.Presets[p58])
    end
    function u52.ShakeOnce(_, p59) --[[Anonymous function at line 244]]
        --[[
        Upvalues:
            [1] = u5
            [2] = u56
        --]]
        local v60 = u5.Reconcile(p59, {
            ["Magnitude"] = 6,
            ["Roughness"] = 4,
            ["FadeInTime"] = 0.05,
            ["FadeOutTime"] = 0.75,
            ["PositionInfluence"] = Vector3.new(0.15, 0.15, 0.15),
            ["RotationInfluence"] = Vector3.new(1, 1, 1)
        })
        u56:ShakeOnce(v60.Magnitude, v60.Roughness, v60.FadeInTime, v60.FadeOutTime, v60.PosInfluence, v60.RotInfluence)
    end
end
function u52.CreateLighting(_, p61, p62, p63) --[[Anonymous function at line 261]]
    --[[
    Upvalues:
        [1] = u1
        [2] = u4
    --]]
    local v64 = Instance.new(p61)
    v64.Parent = u1
    for v65, v66 in p62 do
        if v64[v65] then
            v64[v65] = v66
        end
    end
    if p63 then
        u4:AddItem(v64, p63)
    end
    return v64
end
function u52.UpdateStatus(_, p67, u68, p69) --[[Anonymous function at line 278]]
    --[[
    Upvalues:
        [1] = u6
        [2] = u3
    --]]
    if typeof(p67) == "Instance" then
        p67 = p67:GetDescendants() or p67
    end
    for _, u70 in p67 do
        if not (p69 and p69[u70.Name]) then
            task.spawn(function() --[[Anonymous function at line 286]]
                --[[
                Upvalues:
                    [1] = u70
                    [2] = u68
                    [3] = u6
                    [4] = u3
                --]]
                if u70:IsA("ParticleEmitter") then
                    u70.Enabled = u68
                    return
                elseif u70:IsA("Beam") or u70:IsA("Trail") then
                    if u68 then
                        u70.Enabled = true
                    else
                        local u71 = u6:Create(u70, {
                            ["Time"] = 1,
                            ["EasingStyle"] = "EntranceExpressive",
                            ["EasingDirection"] = "Out",
                            ["StepType"] = "RenderStepped",
                            ["Goal"] = u70:IsA("Beam") and {
                                ["Transparency"] = NumberSequence.new(1, 1),
                                ["TextureSpeed"] = 0
                            } or {
                                ["Transparency"] = NumberSequence.new(1, 1)
                            }
                        })
                        u71:Play()
                        u71.Completed:Connect(function() --[[Anonymous function at line 310]]
                            --[[
                            Upvalues:
                                [1] = u71
                            --]]
                            u71:Destroy()
                        end)
                    end
                else
                    if u70:IsA("PointLight") or u70:IsA("SpotLight") then
                        if u68 then
                            u70.Enabled = false
                            return
                        end
                        u3:Create(u70, TweenInfo.new(1), {
                            ["Brightness"] = 0
                        }):Play()
                    end
                    return
                end
            end)
        end
    end
end
return u52