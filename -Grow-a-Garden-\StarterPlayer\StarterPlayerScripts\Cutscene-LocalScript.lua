-- Full Path: -Grow-a-Garden-\StarterPlayer\StarterPlayerScripts\Cutscene-LocalScript.lua
local u1 = require(game.ReplicatedStorage:WaitFor<PERSON>hild("Cutscene_Module"))
local u2 = game.Players.LocalPlayer
game:GetService("TweenService")
local u3 = game:GetService("Debris")
TweenInfo.new(0.5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out, 0, false, 0)
local u4 = TweenInfo.new(0.4, Enum.EasingStyle.Quad, Enum.EasingDirection.Out, 0, false, 0)
require(game.ReplicatedStorage:WaitForChild("RichText"))
local u5 = game:GetService("TweenService")
TweenInfo.new(0.2, Enum.EasingStyle.Sine, Enum.EasingDirection.Out, 0, false, 0)
local u6 = game.SoundService.NPC_SFX.Sam
local _ = game.SoundService.Response_Text
function Create_Text(p7)
    --[[
    Upvalues:
        [1] = u2
        [2] = u5
        [3] = u4
        [4] = u3
        [5] = u6
    --]]
    local v8 = u2.PlayerGui:WaitForChild("Rejoin_UI")
    for _, v9 in pairs(v8:GetDescendants()) do
        if v9:IsA("TextLabel") then
            u5:Create(v9, u4, {
                ["TextStrokeTransparency"] = 1,
                ["TextTransparency"] = 1
            }):Play()
            u3:AddItem(v9.Parent, u4.Time)
        end
    end
    local v10 = script.Frame:Clone()
    v10.TextLabel.Text = p7
    u5:Create(v10.TextLabel, u4, {
        ["TextTransparency"] = 0,
        ["TextStrokeTransparency"] = 0
    }):Play()
    v10.Parent = v8
    local v11 = string.len(p7)
    while v11 >= 1 do
        task.wait()
        if u6.TimePosition > 0.075 or u6.Playing == false then
            u6.TimePosition = 0
            u6.Playing = true
            u6.PlaybackSpeed = 1 + math.random(-5, 5) / 100
        end
        v11 = v11 - 1
        local v12 = v10.TextLabel
        v12.MaxVisibleGraphemes = v12.MaxVisibleGraphemes + 1
    end
end
function End()
    --[[
    Upvalues:
        [1] = u2
        [2] = u5
        [3] = u4
        [4] = u3
    --]]
    local v13 = u2.PlayerGui:WaitForChild("Rejoin_UI")
    for _, v14 in pairs(v13:GetDescendants()) do
        if v14:IsA("TextLabel") then
            u5:Create(v14, u4, {
                ["TextStrokeTransparency"] = 1,
                ["TextTransparency"] = 1
            }):Play()
            u3:AddItem(v14.Parent, u4.Time)
        end
    end
end
game.ReplicatedStorage.GameEvents:WaitForChild("Start_Cutscene").OnClientEvent:Connect(function(p15) --[[Anonymous function at line 53]]
    --[[
    Upvalues:
        [1] = u1
        [2] = u2
        [3] = u5
        [4] = u4
        [5] = u3
    --]]
    Create_Text("Your garden grew while you were gone!")
    task.wait(3)
    End()
    task.wait(1)
    Create_Text("When you left it looked like this...")
    task.wait(0.5)
    task.wait(p15 - 0.5)
    u1.Flash(u2, 0.75)
    task.wait(1.125)
    Create_Text("This is your garden now!")
    task.spawn(function() --[[Anonymous function at line 66]]
        --[[
        Upvalues:
            [1] = u2
            [2] = u5
            [3] = u4
            [4] = u3
        --]]
        task.wait(2)
        local v16 = u2.PlayerGui:WaitForChild("Rejoin_UI")
        for _, v17 in pairs(v16:GetDescendants()) do
            if v17:IsA("TextLabel") then
                u5:Create(v17, u4, {
                    ["TextStrokeTransparency"] = 1,
                    ["TextTransparency"] = 1
                }):Play()
                u3:AddItem(v17.Parent, u4.Time)
            end
        end
    end)
end)