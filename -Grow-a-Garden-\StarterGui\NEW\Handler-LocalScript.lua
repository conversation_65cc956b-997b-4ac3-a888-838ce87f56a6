-- Full Path: -Grow-a-Garden-\StarterGui\NEW\Handler-LocalScript.lua
game:GetService("ReplicatedStorage")
local u1 = game:GetService("TweenService")
game:GetService("RunService")
local v2 = require(script:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("WaitForDescendant"))
local v3 = require(script:<PERSON><PERSON><PERSON><PERSON>hil<PERSON>("AdjustBrightnessHSV"))
local v4 = script.Parent
v2(v4, "SQUARE_INSERTION_POINT")
local v5 = v2(v4, "ACTUAL_FRAME")
local v6 = v2(v4, "MENU_BUTTON")
local u7 = v2(v4, "UISizeConstraint")
local v8 = v2(v4, "ARROW_IMAGE")
local u9 = workspace.CurrentCamera
workspace:GetPropertyChangedSignal("CurrentCamera"):Connect(function() --[[Anonymous function at line 25]]
    --[[
    Upvalues:
        [1] = u9
    --]]
    u9 = workspace.CurrentCamera
end)
local u10 = v4.Cosmetic.UIScale
local v11 = v6.SENSOR
local v12 = v5.Size
local v13 = v5.Size + UDim2.fromScale(0, 1)
local u14 = u1:Create(v6, TweenInfo.new(0.2), {
    ["BackgroundColor3"] = v6.BackgroundColor3
})
local u15 = u1:Create(v6, TweenInfo.new(0.2), {
    ["BackgroundColor3"] = v3(v6.BackgroundColor3, 0.2)
})
v11.MouseLeave:Connect(function() --[[Anonymous function at line 43]]
    --[[
    Upvalues:
        [1] = u14
    --]]
    u14:Play()
end)
v11.MouseEnter:Connect(function() --[[Anonymous function at line 47]]
    --[[
    Upvalues:
        [1] = u15
    --]]
    print("enter :D")
    u15:Play()
end)
local u16 = false
local u17 = u1:Create(v5, TweenInfo.new(0.2), {
    ["Size"] = v12
})
local u18 = u1:Create(v5, TweenInfo.new(0.2), {
    ["Size"] = v13
})
local u19 = u1:Create(v8, TweenInfo.new(0.2), {
    ["Position"] = v8.Position
})
local u20 = u1:Create(v8, TweenInfo.new(0.2), {
    ["Position"] = UDim2.fromScale(0.5, 0.5)
})
local u21 = u1:Create(v8, TweenInfo.new(0.2), {
    ["Rotation"] = 0
})
local u22 = u1:Create(v8, TweenInfo.new(0.2), {
    ["Rotation"] = 180
})
local function u23() --[[Anonymous function at line 84]]
    --[[
    Upvalues:
        [1] = u16
        [2] = u17
        [3] = u18
        [4] = u20
        [5] = u19
        [6] = u22
        [7] = u21
        [8] = u1
        [9] = u7
    --]]
    (u16 and u17 or u18):Play();
    (u16 and u20 or u19):Play();
    (u16 and u22 or u21):Play()
    u1:Create(u7, TweenInfo.new(0.2), {
        ["MaxSize"] = u16 and Vector2.new(579.67, 115.25) or Vector2.new(579.67, 340)
    }):Play()
end
v11.MouseButton1Click:Connect(function() --[[Anonymous function at line 96]]
    --[[
    Upvalues:
        [1] = u16
        [2] = u23
    --]]
    u16 = not u16
    u23()
end)
u23()
task.spawn(function() --[[Anonymous function at line 104]]
    --[[
    Upvalues:
        [1] = u9
        [2] = u1
        [3] = u10
    --]]
    while true do
        repeat
            task.wait(0.1)
            local v24 = u9.ViewportSize.X
        until v24 ~= nil
        local v25 = u1
        local v26 = u10
        local v27 = TweenInfo.new(0.2)
        local v28 = {}
        local v29 = 0.0011111111111111111 * v24
        v28.Scale = math.min(v29, 1)
        v25:Create(v26, v27, v28):Play()
    end
end)