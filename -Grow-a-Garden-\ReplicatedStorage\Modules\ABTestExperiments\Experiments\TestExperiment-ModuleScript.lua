-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\ABTestExperiments\Experiments\TestExperiment-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
require(v1.Modules.ABTestExperiments.ABTestTypes)
local v2 = {
    ["RemoteConfig"] = "Test",
    ["Disabled"] = false,
    ["DefaultState"] = true
}
local v3 = {
    [true] = {
        ["Server"] = function(_, _) --[[Function name: Server, line 15]] end,
        ["Client"] = function(_, _) --[[Function name: Client, line 19]] end
    },
    [false] = {
        ["Server"] = function(_, _) --[[Function name: Server, line 25]] end,
        ["Client"] = function(_, _) --[[Function name: Client, line 29]] end
    }
}
v2.States = v3
return v2