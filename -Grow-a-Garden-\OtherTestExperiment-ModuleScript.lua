-- Full Path: -Grow-a-Garden-\\OtherTestExperiment-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
require(v1.Modules.ABTestExperiments.ABTestTypes)
local v2 = {
    ["RemoteConfig"] = "TestKeyTeleport",
    ["Disabled"] = false,
    ["DefaultState"] = false
}
local v7 = {
    [true] = {
        ["Server"] = function(p3, _) --[[Function name: Server, line 15]]
            p3:Set<PERSON>ttribute("TestKeyTeleport", true)
        end,
        ["Client"] = function(p4, _) --[[Function name: Client, line 19]]
            p4:SetAttribute("TestKeyTeleport", true)
        end
    },
    [false] = {
        ["Server"] = function(p5, _) --[[Function name: Server, line 26]]
            p5:Set<PERSON>ttribute("TestKeyTeleport", false)
        end,
        ["Client"] = function(p6, _) --[[Function name: Client, line 31]]
            p6:Set<PERSON>ttribute("TestKeyTeleport", false)
        end
    }
}
v2.States = v7
return v2