-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\VariantVisuals-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
local v2 = require(v1.Modules.CreateTagHandler)
local u3 = v1:Wait<PERSON><PERSON><PERSON>hild("Gold_Reference")
local u4 = v1.Assets.SFX:WaitFor<PERSON>hild("Gold_SFX")
local u5 = v1:WaitF<PERSON><PERSON>hild("Rainbow_Reference")
local u6 = v1.Assets.SFX:WaitForChild("Rainbow_SFX")
local u7 = {}
Color3.fromRGB(255, 225, 0)
local function u12(p8, p9) --[[Anonymous function at line 19]]
    local v10 = {}
    for _, v11 in p9:GetChildren() do
        v11:Clone().Parent = p8
        if v11:IsA("Texture") then
            v11.Transparency = 1
        end
        if v11:IsA("ParticleEmitter") then
            table.insert(v10, v11)
        end
    end
    return v10
end
local function u20(p13) --[[Anonymous function at line 38]]
    --[[
    Upvalues:
        [1] = u3
        [2] = u12
        [3] = u4
    --]]
    local function u17(p14) --[[Anonymous function at line 39]]
        --[[
        Upvalues:
            [1] = u17
            [2] = u3
            [3] = u12
        --]]
        for _, u15 in p14:GetChildren() do
            if u15:IsA("Model") then
                u17(u15)
            end
            if u15:IsA("BasePart") or u15:IsA("UnionOperation") then
                if u15:IsA("UnionOperation") then
                    u15.UsePartColor = true
                end
                u15.Color = u3.Color
                u15.Material = u3.Material
                u15.MaterialVariant = u3.MaterialVariant
                if u15.Transparency == 1 then
                    local u16 = nil
                    u16 = u15.Changed:Connect(function() --[[Anonymous function at line 61]]
                        --[[
                        Upvalues:
                            [1] = u15
                            [2] = u16
                            [3] = u12
                            [4] = u3
                        --]]
                        if u15.Transparency == 0 then
                            u16:Disconnect()
                            u12(u15, u3)
                        end
                    end)
                else
                    u12(u15, u3)
                end
            end
        end
    end
    u17(p13)
    local v18 = u4:Clone()
    local v19 = p13:FindFirstChild("Handle") or (p13:FindFirstChild("1") or p13:FindFirstChild("Base"))
    if not v19 then
        warn((("VariantVisuals:Gold | %* doesn\'t have a parent for SFX!"):format(p13.Name)))
    end
    v18.Parent = v19 or p13
    v18.Looped = true
    v18.Playing = true
    v18.Volume = 0.01
    v18.PlaybackSpeed = 1 + math.random(-5, 5) / 100
end
local function u26(p21) --[[Anonymous function at line 89]]
    --[[
    Upvalues:
        [1] = u12
        [2] = u5
        [3] = u6
    --]]
    if p21 then
        (function(p22) --[[Function name: LoopThroughObject, line 95]]
            --[[
            Upvalues:
                [1] = u12
                [2] = u5
            --]]
            for _, v23 in ipairs(p22:GetDescendants()) do
                if v23:IsA("BasePart") then
                    if v23:IsA("UnionOperation") then
                        v23.UsePartColor = true
                    end
                    v23.Reflectance = 0.25
                    u12(v23, u5)
                    v23:AddTag("RainbowPart")
                end
            end
        end)(p21)
        local v24 = u6:Clone()
        local v25 = p21:FindFirstChild("Handle") or (p21:FindFirstChild("1") or p21)
        if not v25 then
            warn("VariantVisuals:Rainbow | %s doesn\'t have a parent for SFX!"):format(p21.Name)
        end
        v24.Parent = v25 or p21
        v24.Looped = true
        v24.Playing = true
        v24.Volume = 0.01
        v24.PlaybackSpeed = 1 + math.random(-5, 5) / 100
    else
        warn("VariantVisuals:Rainbow | no plant provided!")
    end
end
local u29 = {
    ["Gold"] = function(p27, _) --[[Anonymous function at line 129]]
        --[[
        Upvalues:
            [1] = u20
        --]]
        return u20(p27)
    end,
    ["Rainbow"] = function(p28, _) --[[Anonymous function at line 132]]
        --[[
        Upvalues:
            [1] = u26
        --]]
        return u26(p28)
    end
}
function u7.SetVisuals(_, u30, u31) --[[Anonymous function at line 138]]
    --[[
    Upvalues:
        [1] = u29
    --]]
    if u30 then
        task.spawn(function() --[[Anonymous function at line 144]]
            --[[
            Upvalues:
                [1] = u30
                [2] = u29
                [3] = u31
            --]]
            local v32 = u30:WaitForChild("Item_Seed")
            if not v32 then
                return warn((("VariantVisuals:SetVisuals | %* doesn\'t have a seed!"):format(u30.Name)))
            end
            math.randomseed(v32.Value)
            local v33 = u30:FindFirstChild("Variant")
            local v34 = v33 and v33.Value or ""
            local v35 = u29[v34]
            if v35 and (not u30:GetAttribute("SetVariant") and (v34 ~= "" and v34 ~= "Normal")) then
                u30:SetAttribute("SetVariant", true)
                repeat
                    task.wait(0.5)
                until u30:HasTag("PlantGenerated")
                v35(u30, u31)
            end
        end)
    else
        warn("VariantVisuals:SetVisuals | plant is nil!")
    end
end
v2({
    ["Tag"] = "PlantGenerated",
    ["OnInstanceAdded"] = function(p36) --[[Function name: OnInstanceAdded, line 169]]
        --[[
        Upvalues:
            [1] = u7
        --]]
        u7:SetVisuals(p36)
    end,
    ["OnInstanceRemoved"] = function(_) --[[Function name: OnInstanceRemoved, line 172]] end
})
return u7