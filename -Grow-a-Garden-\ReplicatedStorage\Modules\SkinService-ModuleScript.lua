-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\SkinService-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage"):WaitForChild("Skins")
local u4 = {
    ["Rainbow"] = {
        ["SkinAdded"] = function(_, p2, _) --[[Function name: SkinAdded, line 45]]
            p2:AddTag("RainbowPart")
        end,
        ["SkinRemoved"] = function(_, p3, _) --[[Function name: SkinRemoved, line 48]]
            p3:RemoveTag("RainbowPart")
        end
    }
}
local u5 = {}
local v6 = {
    ["ColorsList"] = {
        ["SkinIndex1"] = Color3.fromRGB(0, 85, 0),
        ["SkinIndex2"] = Color3.fromRGB(97, 78, 51),
        ["SkinIndex3"] = Color3.fromRGB(0, 0, 0)
    },
    ["MaterialList"] = {
        ["SkinIndex1"] = Enum.Material.Grass,
        ["SkinIndex2"] = Enum.Material.Mud,
        ["SkinIndex3"] = Enum.Material.Marble
    },
    ["MaterialVariantList"] = {},
    ["SkinFunctionList"] = {
        ["SkinIndex3"] = "Rainbow"
    }
}
u5.Debug = v6
local u7 = {
    "Color",
    "Material",
    "Transparency",
    "Reflectance",
    "MaterialVariant"
}
local u8 = {}
for _, v9 in v1:GetChildren() do
    local v10 = v9.Name
    if not u5[v10] then
        local v11 = {
            ["SkinFunctionList"] = {}
        }
        for _, v12 in u7 do
            v11[("%*List"):format(v12)] = {}
        end
        u5[v10] = v11
    end
    local v13 = u5[v10]
    if not v13 then
        return warn("Somehow no skin data??")
    end
    for _, v14 in v9:GetChildren() do
        local v15 = v14.Name
        for _, v16 in u7 do
            v13[("%*List"):format(v16)][v15] = v14[v16]
        end
        local v17 = v14:GetAttribute("SkinFunction")
        if v17 then
            v13.SkinFunctionList[v15] = v17
        end
    end
end
function u8.SetSkin(_, p18, p19) --[[Anonymous function at line 122]]
    --[[
    Upvalues:
        [1] = u8
        [2] = u5
        [3] = u7
        [4] = u4
    --]]
    if not p18 then
        return warn("SkinService:SetSkin | No ParentModel provided!")
    end
    u8:RemoveSkin(p18)
    local v20 = u5[p19]
    if not v20 then
        return warn((("SkinService:SetSkin | %* does not exist as a skin!"):format(p19)))
    end
    p18:SetAttribute("CurrentSkin", p19)
    for _, v21 in p18:GetDescendants() do
        if v21:IsA("BasePart") then
            local v22 = v21:GetAttribute("SkinIndex")
            if v22 then
                for _, v23 in u7 do
                    local v24 = v20[("%*List"):format(v23)]
                    if v24 then
                        local v25 = v24[v22]
                        if v25 then
                            v21:SetAttribute(("SkinPrevious%*"):format(v23), v21[v23])
                            v21[v23] = v25
                        end
                    end
                end
                local v26 = v20.SkinFunctionList[v22]
                if v26 then
                    v26 = u4[v26]
                end
                if v26 then
                    v26:SkinAdded(v21, p19)
                end
            end
        end
    end
end
function u8.RemoveSkin(_, p27) --[[Anonymous function at line 158]]
    --[[
    Upvalues:
        [1] = u5
        [2] = u7
        [3] = u4
    --]]
    local v28 = p27:GetAttribute("CurrentSkin")
    if v28 then
        local v29 = u5[v28]
        if not v29 then
            return warn((("SkinService:RemoveSkin | %* does not exist as a skin!"):format(v28)))
        end
        for _, v30 in p27:GetDescendants() do
            if v30:IsA("BasePart") then
                local v31 = v30:GetAttribute("SkinIndex")
                if v31 then
                    for _, v32 in u7 do
                        if v29[("%*List"):format(v32)] then
                            local v33 = ("SkinPrevious%*"):format(v32)
                            local v34 = v30:GetAttribute(v33)
                            if v34 then
                                v30[v32] = v34
                                v30:SetAttribute(v33, nil)
                            end
                        end
                    end
                    local v35 = v29.SkinFunctionList[v31]
                    if v35 then
                        v35 = u4[v35]
                    end
                    if v35 then
                        v35:SkinRemoved(v30, v28)
                    end
                end
            end
        end
        p27:SetAttribute("CurrentSkin", nil)
    end
end
return u8