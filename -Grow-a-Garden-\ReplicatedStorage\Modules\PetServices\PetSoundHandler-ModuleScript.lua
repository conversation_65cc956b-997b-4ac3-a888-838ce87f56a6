-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\PetServices\PetSoundHandler-ModuleScript.lua
local u1 = {}
u1.__index = u1
local u2 = game:GetService("ReplicatedStorage")
local u3 = game:GetService("SoundService")
local u4 = {
    ["Dog"] = {
        ["Idle"] = "rbxassetid://74516706583472",
        ["Walk"] = "rbxassetid://95883386499521",
        ["Dig"] = "rbxassetid://82347082239944"
    },
    ["Golden Lab"] = {
        ["Idle"] = "rbxassetid://74516706583472",
        ["Walk"] = "rbxassetid://95883386499521",
        ["Dig"] = "rbxassetid://82347082239944"
    },
    ["Cat"] = {
        ["Idle"] = "rbxassetid://126430736259456",
        ["Walk"] = "rbxassetid://75691085685258",
        ["Nap"] = "rbxassetid://88617197262546"
    },
    ["Orange Tabby"] = {
        ["Idle"] = "rbxassetid://126430736259456",
        ["Walk"] = "rbxassetid://75691085685258",
        ["Nap"] = "rbxassetid://136807561780379"
    },
    ["Moon Cat"] = {
        ["Idle"] = "rbxassetid://126430736259456",
        ["Walk"] = "rbxassetid://75691085685258",
        ["Nap"] = "rbxassetid://136807561780379"
    },
    ["Bunny"] = {
        ["Idle"] = "rbxassetid://80830019163546",
        ["Walk"] = "rbxassetid://117228920028311",
        ["Chomp"] = "rbxassetid://91545917920155"
    },
    ["Black Bunny"] = {
        ["Idle"] = "rbxassetid://80830019163546",
        ["Walk"] = "rbxassetid://117228920028311",
        ["Chomp"] = "rbxassetid://91545917920155"
    },
    ["Chicken"] = {
        ["Idle"] = "rbxassetid://98158160605550",
        ["Walk"] = "rbxassetid://139844945801226"
    },
    ["Rooster"] = {
        ["Idle"] = "rbxassetid://98158160605550",
        ["Walk"] = "rbxassetid://139844945801226"
    },
    ["Pig"] = {
        ["Idle"] = "rbxassetid://135865211196363",
        ["Walk"] = "rbxassetid://116135751746611",
        ["Frenzy"] = "rbxassetid://116342918915952"
    },
    ["Cow"] = {
        ["Idle"] = "rbxassetid://109628519201421",
        ["Walk"] = "rbxassetid://129633521888708"
    },
    ["Dragonfly"] = {
        ["Idle"] = "rbxassetid://77220774723390",
        ["Walk"] = "rbxassetid://88913485838830"
    },
    ["Monkey"] = {
        ["Idle"] = "rbxassetid://139413487045598",
        ["Walk"] = "rbxassetid://112777046526411",
        ["Steal"] = "rbxassetid://13772418142759"
    },
    ["Silver Monkey"] = {
        ["Idle"] = "rbxassetid://139413487045598",
        ["Walk"] = "rbxassetid://112777046526411",
        ["Steal"] = "rbxassetid://13772418142759"
    },
    ["Snail"] = {
        ["Walk"] = "rbxassetid://84898089107661",
        ["Idle"] = "rbxassetid://123370432487085"
    },
    ["Giant Ant"] = {
        ["Walk"] = "rbxassetid://85188904272858",
        ["Idle"] = "rbxassetid://139448026417736",
        ["Dance"] = "rbxassetid://104549164564414"
    },
    ["Turtle"] = {
        ["Idle"] = "rbxassetid://72972534645980",
        ["Walk"] = "rbxassetid://95903584684041"
    },
    ["Deer"] = {
        ["Idle"] = "rbxassetid://109504476401833",
        ["Walk"] = "rbxassetid://72482977771929",
        ["Stomp"] = "rbxassetid://110481608328857"
    },
    ["Spotted Deer"] = {
        ["Idle"] = "rbxassetid://109504476401833",
        ["Walk"] = "rbxassetid://72482977771929",
        ["Stomp"] = "rbxassetid://110481608328857"
    },
    ["Caterpillar"] = {
        ["Walk"] = "rbxassetid://122412833570405",
        ["Idle"] = "rbxassetid://118926491137336"
    },
    ["Praying Mantis"] = {
        ["Walk"] = "rbxassetid://84713477229850",
        ["Idle"] = "rbxassetid://97641683064349",
        ["Pray"] = "rbxassetid://73332209725156"
    },
    ["Sea Otter"] = {
        ["Idle"] = "rbxassetid://98663428034599",
        ["Spray"] = "rbxassetid://132032269231520",
        ["Walk"] = "rbxassetid://111713760896073"
    },
    ["Polar Bear"] = {
        ["Idle"] = "rbxassetid://100399217826845",
        ["Roar"] = "rbxassetid://100918889424478",
        ["Walk"] = "rbxassetid://104191740768506"
    },
    ["Panda"] = {
        ["Idle"] = "rbxassetid://85123076795821",
        ["Walk"] = "rbxassetid://126208503221327",
        ["Chomp"] = "rbxassetid://106994678664275"
    },
    ["Hedgehog"] = {
        ["Idle"] = "rbxassetid://99389171870232",
        ["Walk"] = "rbxassetid://79078872328055",
        ["Curl"] = "rbxassetid://109183921043480"
    },
    ["Kiwi"] = {
        ["Idle"] = "rbxassetid://70643795779986",
        ["Walk"] = "rbxassetid://113954129903942",
        ["Nurse"] = "rbxassetid://126248680195169"
    },
    ["Mole"] = {
        ["Idle"] = "rbxassetid://95599243108942",
        ["Walk"] = "rbxassetid://109159558883532",
        ["DigDown"] = "rbxassetid://122838135787975",
        ["DigUp"] = "rbxassetid://87901949070969"
    },
    ["Frog"] = {
        ["Idle"] = "rbxassetid://105829201448144",
        ["Walk"] = "rbxassetid://104191740768506",
        ["Croak"] = "rbxassetid://97143990552632"
    },
    ["Echo Frog"] = {
        ["Idle"] = "rbxassetid://134398514718150",
        ["Walk"] = "rbxassetid://104191740768506",
        ["Croak"] = "rbxassetid://113309295117031"
    },
    ["Owl"] = {
        ["Idle"] = "rbxassetid://129074703805811",
        ["Walk"] = "rbxassetid://104191740768506",
        ["Fly"] = "rbxassetid://103608467045533",
        ["FlyDown"] = "rbxassetid://126267742639216",
        ["FlyUp"] = "rbxassetid://113660155265682"
    },
    ["Night Owl"] = {
        ["Idle"] = "rbxassetid://129074703805811",
        ["Walk"] = "rbxassetid://104191740768506",
        ["Fly"] = "rbxassetid://103608467045533",
        ["FlyDown"] = "rbxassetid://126267742639216",
        ["FlyUp"] = "rbxassetid://113660155265682"
    },
    ["Raccoon"] = {
        ["Idle"] = "rbxassetid://134230704254748",
        ["Walk"] = "rbxassetid://104191740768506",
        ["Steal"] = "rbxassetid://98750147885192"
    },
    ["Blood Kiwi"] = {
        ["Idle"] = "rbxassetid://70643795779986",
        ["Walk"] = "rbxassetid://113954129903942",
        ["Nurse"] = "rbxassetid://126248680195169"
    },
    ["Blood Hedgehog"] = {
        ["Idle"] = "rbxassetid://99389171870232",
        ["Walk"] = "rbxassetid://79078872328055",
        ["Curl"] = "rbxassetid://109183921043480"
    },
    ["Blood Owl"] = {
        ["Idle"] = "rbxassetid://129074703805811",
        ["Walk"] = "rbxassetid://72938304206168",
        ["Fly"] = "rbxassetid://103608467045533",
        ["FlyDown"] = "rbxassetid://126267742639216",
        ["FlyUp"] = "rbxassetid://113660155265682"
    },
    ["Chicken Zombie"] = {
        ["Idle"] = "rbxassetid://81537385466343",
        ["Walk"] = "rbxassetid://77478760027344"
    },
    ["Grey Mouse"] = {
        ["Idle"] = "rbxassetid://97778347635709",
        ["Walk"] = "rbxassetid://89252475842825"
    },
    ["Squirrel"] = {
        ["Idle"] = "rbxassetid://92747344416384",
        ["Walk"] = "rbxassetid://73793157974906"
    },
    ["Brown Mouse"] = {
        ["Idle"] = "rbxassetid://97778347635709",
        ["Walk"] = "rbxassetid://89252475842825"
    },
    ["Red Giant Ant"] = {
        ["Walk"] = "rbxassetid://76161666777473",
        ["Idle"] = "rbxassetid://131780886746478"
    },
    ["Fox"] = {
        ["Idle"] = "rbxassetid://130458837573687",
        ["Walk"] = "rbxassetid://129545821321602",
        ["Steal"] = "rbxassetid://98750147885192"
    }
}
local u5 = {}
function u1.new(p6, p7, p8) --[[Anonymous function at line 219]]
    --[[
    Upvalues:
        [1] = u1
    --]]
    local v9 = u1
    local v10 = setmetatable({}, v9)
    v10.PetType = p6
    v10.PetModel = p7
    v10.UUID = p8
    v10.currentSound = nil
    return v10
end
function u1.playSound(u11, p12) --[[Anonymous function at line 230]]
    --[[
    Upvalues:
        [1] = u2
        [2] = u3
        [3] = u4
        [4] = u5
    --]]
    u11:stopSound()
    if p12 == "Idle" then
        local v13 = u2.Assets:FindFirstChild("PetSFX")
        local v14 = v13 and v13:FindFirstChild(u11.PetType)
        if v14 then
            local v15 = v14:FindFirstChild("Idle")
            if v15 and #v15:GetChildren() > 0 then
                local v16 = v15:GetChildren()
                local u17 = v16[math.random(1, #v16)]:Clone()
                u17.Parent = u11.PetModel.PrimaryPart or u11.PetModel
                u17.SoundGroup = u3.Music
                u17.RollOffMode = Enum.RollOffMode.Inverse
                u17.RollOffMaxDistance = 100
                u17.RollOffMinDistance = 5
                u17.EmitterSize = 10
                u17.Volume = 1
                u17:Play()
                u11.currentSound = u17
                u17.Ended:Connect(function() --[[Anonymous function at line 255]]
                    --[[
                    Upvalues:
                        [1] = u17
                        [2] = u11
                    --]]
                    u17:Destroy()
                    if u11.currentSound == u17 then
                        u11.currentSound = nil
                    end
                end)
                return
            end
        end
    end
    local v18 = u4[u11.PetType]
    if v18 then
        local v19 = v18[p12]
        if v19 then
            if not u5[v19] then
                local v20 = Instance.new("Sound")
                v20.SoundGroup = u3.Music
                v20.SoundId = v19
                v20.Volume = 1
                v20.RollOffMode = Enum.RollOffMode.Inverse
                v20.RollOffMaxDistance = 100
                v20.RollOffMinDistance = 5
                v20.EmitterSize = 10
                u5[v19] = v20
            end
            local u21 = u5[v19]:Clone()
            u21.SoundGroup = u3.Music
            u21.Parent = u11.PetModel.PrimaryPart or u11.PetModel
            if p12 == "Walk" then
                u21.Loaded:Connect(function() --[[Anonymous function at line 291]]
                    --[[
                    Upvalues:
                        [1] = u21
                    --]]
                    if u21.IsLoaded and u21.TimeLength > 2 then
                        u21.TimePosition = math.random() * (u21.TimeLength - 1)
                    end
                end)
            end
            u21:Play()
            u11.currentSound = u21
            u21.Ended:Connect(function() --[[Anonymous function at line 301]]
                --[[
                Upvalues:
                    [1] = u21
                    [2] = u11
                --]]
                u21:Destroy()
                if u11.currentSound == u21 then
                    u11.currentSound = nil
                end
            end)
        end
    else
        return
    end
end
function u1.stopSound(p22) --[[Anonymous function at line 309]]
    if p22.currentSound then
        local u23 = p22.currentSound
        p22.currentSound = nil
        local u24 = u23.Volume
        for u25 = 1, 10 do
            task.delay((u25 - 1) * 0.1, function() --[[Anonymous function at line 318]]
                --[[
                Upvalues:
                    [1] = u23
                    [2] = u24
                    [3] = u25
                --]]
                if u23 then
                    u23.Volume = u24 * (1 - u25 / 10)
                end
            end)
        end
        task.delay(1, function() --[[Anonymous function at line 325]]
            --[[
            Upvalues:
                [1] = u23
            --]]
            if u23 then
                u23:Stop()
                u23:Destroy()
            end
        end)
    end
end
function u1.destroy(p26) --[[Anonymous function at line 334]]
    p26:stopSound()
end
return u1