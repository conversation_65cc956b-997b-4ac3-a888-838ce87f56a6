-- Full Path: -Grow-a-Garden-\\MathOperator-ModuleScript.lua
return function(p1) --[[Anonymous function at line 1]]
    p1:RegisterType("mathOperator", p1.Cmdr.Util.MakeEnumType("Math Operator", {
        {
            ["Name"] = "+",
            ["Perform"] = function(p2, p3) --[[Function name: Perform, line 5]]
                return p2 + p3
            end
        },
        {
            ["Name"] = "-",
            ["Perform"] = function(p4, p5) --[[Function name: Perform, line 11]]
                return p4 - p5
            end
        },
        {
            ["Name"] = "*",
            ["Perform"] = function(p6, p7) --[[Function name: Perform, line 17]]
                return p6 * p7
            end
        },
        {
            ["Name"] = "/",
            ["Perform"] = function(p8, p9) --[[Function name: Perform, line 23]]
                return p8 / p9
            end
        },
        {
            ["Name"] = "**",
            ["Perform"] = function(p10, p11) --[[Function name: Perform, line 29]]
                return p10 ^ p11
            end
        },
        {
            ["Name"] = "%",
            ["Perform"] = function(p12, p13) --[[Function name: Perform, line 35]]
                return p12 % p13
            end
        }
    }))
end