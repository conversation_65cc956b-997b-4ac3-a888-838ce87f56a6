-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\PetEggShopController-ModuleScript.lua
local u1 = game:GetService("ReplicatedStorage")
game:GetService("TweenService")
game:GetService("ServerScriptService")
local v2 = game.Players.LocalPlayer.PlayerGui
local u3 = require(u1.Modules.DataService)
local u4 = require(u1.Modules.WaitForDescendant)
local u5 = require(u1.Modules.GiftController)
local u6 = require(u1.Modules.MarketController)
local u7 = require(u1.Modules.GuiController)
local u8 = require(u1.Modules.NumberUtil)
local u9 = require(u1.Data.DecimalNumberFormat)
local u10 = require(u1.Modules.CommaFormatNumber)
require(u1.Item_Module)
require(u1.Comma_Module)
local u11 = require(u1.Data.PetEggShopData)
local u12 = require(u1.Data.PetEggData)
local u13 = require(u1.Data.PetRegistry)
local u14 = require(u1.Frame_Popup_Module)
local u15 = u13.PetList
local v16 = {}
local u17 = u1:WaitForChild("Assets"):WaitForChild("Models"):WaitForChild("EggModels")
local u18 = workspace:WaitForChild("NPCS"):WaitForChild("Pet Stand")
local u19 = u18:WaitForChild("Timer"):WaitForChild("SurfaceGui"):WaitForChild("ResetTimeLabel")
local u20 = u18:FindFirstChildWhichIsA("ProximityPrompt", true)
local u21 = v2.ConfirmPetEggPurchase
local u22 = u4(u21, "GiftButton")
local u23 = u4(u21, "RobuxButton")
local u24 = u4(u21, "ShecklesButton")
local u25 = u4(u21, "PetChanceDetails")
local u26 = u4(u21, "PetItemTemplate")
local u27 = 0
local u28 = nil
local function u32() --[[Anonymous function at line 65]]
    --[[
    Upvalues:
        [1] = u3
        [2] = u11
        [3] = u19
        [4] = u8
        [5] = u1
    --]]
    local v29 = workspace:GetServerTimeNow()
    local v30 = u3:GetData()
    local v31 = v30.PetEggStock.ForcedEggEndTimestamp and (v30.PetEggStock.ForcedEggEndTimestamp - v29 or -1) or -1
    if v31 < 0 then
        v31 = u11.RefreshTime - v29 % u11.RefreshTime
    end
    u19.Text = v31 <= 0 and "Restocking" or u8.compactFormat(v31)
    if v31 <= 1 then
        require(u1.Modules.Notification):CreateNotification("<font color=\"#DF8A0F\"><b>Your Pet Shop stock has been reset!</b></font>")
    end
    task.wait(1)
end
local function u66() --[[Anonymous function at line 80]]
    --[[
    Upvalues:
        [1] = u3
        [2] = u7
        [3] = u21
        [4] = u18
        [5] = u12
        [6] = u17
        [7] = u13
        [8] = u10
        [9] = u1
        [10] = u4
        [11] = u25
        [12] = u26
        [13] = u15
        [14] = u9
        [15] = u24
        [16] = u6
        [17] = u23
        [18] = u28
        [19] = u27
        [20] = u14
    --]]
    local v33 = u3:GetData()
    u7:Close(u21)
    local v34 = u18.EggLocations:GetChildren()
    for _, v35 in ipairs(v34) do
        if v35:IsA("Model") then
            v35:Destroy()
        end
    end
    for u36, v37 in pairs(v33.PetEggStock.Stocks) do
        local u38 = u12[v37.EggName]
        if u38 then
            local v39 = u17:FindFirstChild(u38.EggName)
            if v39 then
                local u40 = v39:Clone()
                u40.Parent = u18.EggLocations
                u40:PivotTo(v34[u36].CFrame)
                u40.HitBox.Anchored = true
                if v37.Stock <= 0 then
                    u40:SetAttribute("RobuxEggOnly", true)
                    for _, v41 in u40:GetDescendants() do
                        if v41:IsA("BasePart") and v41.Name ~= "HitBox" then
                            v41.Transparency = 0.65
                        end
                    end
                end
                local u42 = v34[u36]:FindFirstChild("SurfaceGui", true)
                local v43 = u42:WaitForChild("EggNameTextLabel")
                local v44 = u42:WaitForChild("RarityTextLabel")
                local v45 = u42:WaitForChild("PriceTextLabel")
                local v46 = u13.PetEggs[u38.EggName]
                local v47 = v46 and v46.Color or Color3.new(1, 1, 1)
                v43.Text = u38.EggName
                v44.Text = u38.EggRarity
                v44.TextColor3 = v47
                v45.Text = ("%*\194\162"):format((u10(u38.Price)))
                local v48 = u1.ProximityPrompt:Clone()
                if not v48 then
                    return warn("PetEggShopController:GenerateStall | No Proximity Prompt found in ReplicatedStorage!")
                end
                v48.Parent = u40
                v48.ActionText = "Buy Egg"
                v48.HoldDuration = 0
                v48.Enabled = true
                v48.Triggered:Connect(function() --[[Anonymous function at line 146]]
                    --[[
                    Upvalues:
                        [1] = u4
                        [2] = u21
                        [3] = u13
                        [4] = u38
                        [5] = u25
                        [6] = u26
                        [7] = u15
                        [8] = u9
                        [9] = u24
                        [10] = u10
                        [11] = u6
                        [12] = u23
                        [13] = u28
                        [14] = u27
                        [15] = u36
                        [16] = u40
                        [17] = u14
                        [18] = u7
                    --]]
                    u4(u21, "PetName")
                    local v49 = u4(u21, "Rarity")
                    local v50 = u4(u21, "HeaderTitle")
                    local v51 = u13.PetEggs
                    if not v51 then
                        return warn((("PetEggShopController:GenerateStall | Cannot find Pet Rarity Data for \"%*\"!"):format(u38.EggName)))
                    end
                    local v52 = v51[u38.EggName]
                    local v53 = v52.Color
                    local v54 = v52.RarityData.Items
                    local v55 = v53.R * 255
                    local v56 = math.floor(v55)
                    local v57 = v53.G * 255
                    local v58 = math.floor(v57)
                    local v59 = v53.B * 255
                    local v60 = math.floor(v59)
                    local v61 = string.format("#%02X%02X%02X", v56, v58, v60)
                    v49.TextColor3 = v53
                    v50.Text = string.format("Purchase <font color=\"%s\">%s</font>", v61, u38.EggName)
                    for _, v62 in u25:GetChildren() do
                        if v62:IsA("Frame") and v62.Visible then
                            v62:Destroy()
                        end
                    end
                    for v63, v64 in v54 do
                        local v65 = u26:Clone()
                        v65.Parent = u25
                        v65.Visible = true
                        if u15[v63] and u15[v63].Icon then
                            v65.Detail.PetImageLabel.Image = u15[v63].Icon
                        end
                        v65.PetChance.Text = ("%*%%"):format((u9(v64.NormalizedOdd)))
                        v65.LayoutOrder = 100 / v64.NormalizedOdd
                        v65.PetName.Text = v64.Name
                    end
                    u24.Text = u10(u38.Price) .. "\194\162"
                    task.spawn(function() --[[Anonymous function at line 189]]
                        --[[
                        Upvalues:
                            [1] = u6
                            [2] = u23
                            [3] = u38
                        --]]
                        u6:SetPriceLabel(u23, u38.PurchaseID, ":robux::value:")
                    end)
                    u28 = u38
                    u27 = u36
                    if u40:GetAttribute("RobuxEggOnly") then
                        u24.Text = "NO STOCK"
                        u24.UIStroke.Color = Color3.fromRGB(85, 0, 0)
                        u24.Parent.BackgroundColor3 = Color3.fromRGB(255, 0, 0)
                        u24.Parent.UIStroke.Color = Color3.fromRGB(85, 0, 0)
                    else
                        u24.Text = u10(u38.Price) .. "\194\162"
                        u24.UIStroke.Color = Color3.fromRGB(30, 100, 14)
                        u24.Parent.BackgroundColor3 = Color3.fromRGB(37, 238, 38)
                        u24.Parent.UIStroke.Color = Color3.fromRGB(30, 100, 14)
                    end
                    u24.AutoButtonColor = not u40:GetAttribute("RobuxEggOnly")
                    u24.Active = not u40:GetAttribute("RobuxEggOnly")
                    u14.Show(u21)
                    u7:Open(u21)
                end)
                v48.PromptShown:Connect(function() --[[Anonymous function at line 214]]
                    --[[
                    Upvalues:
                        [1] = u42
                    --]]
                    u42.Enabled = true
                end)
                v48.PromptHidden:Connect(function() --[[Anonymous function at line 218]]
                    --[[
                    Upvalues:
                        [1] = u42
                    --]]
                    u42.Enabled = false
                end)
            else
                warn((("GenerateStall | No egg model found for name \"%*\"!"):format(u38.EggName)))
            end
        else
            warn((("GenerateStall | No egg found for egg \"%*\"!"):format(v37.EggName)))
        end
    end
end
local function u67() --[[Anonymous function at line 228]]
    --[[
    Upvalues:
        [1] = u4
        [2] = u21
        [3] = u7
        [4] = u23
        [5] = u6
        [6] = u28
        [7] = u22
        [8] = u5
        [9] = u24
        [10] = u1
        [11] = u27
    --]]
    u4(u21, "ExitButton").Activated:Connect(function() --[[Anonymous function at line 231]]
        --[[
        Upvalues:
            [1] = u7
            [2] = u21
        --]]
        u7:Close(u21)
    end)
    u23.Activated:Connect(function() --[[Anonymous function at line 235]]
        --[[
        Upvalues:
            [1] = u6
            [2] = u28
            [3] = u7
            [4] = u21
        --]]
        u6:PromptPurchase(u28.PurchaseID, Enum.InfoType.Product)
        u7:Close(u21)
    end)
    u22.Activated:Connect(function() --[[Anonymous function at line 240]]
        --[[
        Upvalues:
            [1] = u28
            [2] = u5
            [3] = u7
            [4] = u21
        --]]
        if u28.GiftPurchaseID then
            u5:PromptGiftFromGiftId(u28.GiftPurchaseID)
        end
        u7:Close(u21)
    end)
    u24.Activated:Connect(function() --[[Anonymous function at line 247]]
        --[[
        Upvalues:
            [1] = u1
            [2] = u27
            [3] = u7
            [4] = u21
        --]]
        u1.GameEvents.BuyPetEgg:FireServer(u27)
        u7:Close(u21)
    end)
end
function v16.Start(_) --[[Anonymous function at line 253]]
    --[[
    Upvalues:
        [1] = u3
        [2] = u66
        [3] = u67
        [4] = u20
        [5] = u6
        [6] = u32
    --]]
    task.spawn(function() --[[Anonymous function at line 254]]
        --[[
        Upvalues:
            [1] = u3
            [2] = u66
        --]]
        local v68 = u3:GetPathSignal("PetEggStock/@")
        if v68 then
            v68:Connect(u66)
        end
        local v69 = u3:GetPathSignal("PetEggStock")
        if v69 then
            v69:Connect(u66)
        end
    end)
    task.spawn(u66)
    task.spawn(u67)
    task.spawn(function() --[[Anonymous function at line 269]]
        --[[
        Upvalues:
            [1] = u20
            [2] = u6
        --]]
        u20.Triggered:Connect(function() --[[Anonymous function at line 270]]
            --[[
            Upvalues:
                [1] = u6
            --]]
            u6:PromptPurchase(3276329270, Enum.InfoType.Product)
        end)
    end)
    task.spawn(function() --[[Anonymous function at line 277]]
        --[[
        Upvalues:
            [1] = u32
        --]]
        while true do
            u32()
        end
    end)
end
task.spawn(v16.Start, v16)
return v16