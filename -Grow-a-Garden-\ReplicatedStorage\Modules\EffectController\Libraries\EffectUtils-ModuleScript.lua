-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\EffectController\Libraries\EffectUtils-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
local u2 = game:GetService("ReplicatedStorage")
local u3 = game:GetService("TweenService")
local u4 = game:GetService("Debris")
local u5 = require(v1.Modules.EffectController.Utility.Utility)
local u6 = {}
local u7 = {
    "rbxassetid://13080396681",
    "rbxassetid://13080396467",
    "rbxassetid://13080396272",
    "rbxassetid://13080396046",
    "rbxassetid://13080395739",
    "rbxassetid://13080395411",
    "rbxassetid://13080395112",
    "rbxassetid://13080394784",
    "rbxassetid://13080394488",
    "rbxassetid://13080394240",
    "rbxassetid://13080394013",
    "rbxassetid://13080393801",
    "rbxassetid://13080393553",
    "rbxassetid://13080393316",
    "rbxassetid://13080393039",
    "rbxassetid://13080392801",
    "rbxassetid://13080392599",
    "rbxassetid://13080392299",
    "rbxassetid://13080392045",
    "rbxassetid://13080391819",
    "rbxassetid://13080391566",
    "rbxassetid://13080391273",
    "rbxassetid://13080391030",
    "rbxassetid://13080391030",
    "rbxassetid://13080390759",
    "rbxassetid://13080389960"
}
local u271 = {
    ["EmitParticles"] = function(p8, p9, p10, p11) --[[Function name: EmitParticles, line 44]]
        --[[
        Upvalues:
            [1] = u3
        --]]
        local v12 = next
        local v13, v14 = p8:GetDescendants()
        local v15 = p11 or false
        local v16 = p10 or 0.5
        local v17 = p9 or 2
        for _, u18 in v12, v13, v14 do
            if u18:IsA("ParticleEmitter") then
                if u18:GetAttribute("EmitDelay") then
                    task.delay(u18:GetAttribute("EmitDelay"), function() --[[Anonymous function at line 52]]
                        --[[
                        Upvalues:
                            [1] = u18
                        --]]
                        u18:Emit(u18:GetAttribute("EmitCount"))
                    end)
                else
                    u18:Emit(u18:GetAttribute("EmitCount"))
                end
            elseif (u18:IsA("PointLight") or u18:IsA("SpotLight")) and v15 == false then
                u3:Create(u18, TweenInfo.new(v16, Enum.EasingStyle.Cubic, Enum.EasingDirection.Out, 0, true, 0), {
                    ["Brightness"] = v17
                }):Play()
            end
        end
    end,
    ["ParticleHandler"] = function(p19, p20, p21) --[[Function name: ParticleHandler, line 68]]
        --[[
        Upvalues:
            [1] = u3
        --]]
        local v22 = next
        local v23, v24 = p19:GetDescendants()
        for _, v25 in v22, v23, v24 do
            if v25:IsA("ParticleEmitter") then
                v25.Enabled = p20
            elseif v25:IsA("PointLight") or v25:IsA("SpotLight") then
                u3:Create(v25, TweenInfo.new(0.5, Enum.EasingStyle.Cubic, Enum.EasingDirection.Out), {
                    ["Brightness"] = p21
                }):Play()
            end
        end
    end,
    ["OwnTrails"] = function(u26, p27, p28, u29, p30) --[[Function name: OwnTrails, line 80]]
        --[[
        Upvalues:
            [1] = u271
            [2] = u4
        --]]
        local u31 = p28 or 15
        local u32 = p27 or 15
        local u33 = p30 or 3
        coroutine.wrap(function() --[[Anonymous function at line 87]]
            --[[
            Upvalues:
                [1] = u26
                [2] = u29
                [3] = u32
                [4] = u31
                [5] = u271
                [6] = u4
                [7] = u33
            --]]
            local v34 = u26.Position
            local v35 = u29.Position
            local v36 = (v34 + v35) / 2
            local v37 = math.random(-u32, u32)
            local v38 = math.random(-u32, u32)
            local v39 = math.random
            local v40 = -u32
            local v41 = u32
            local v42 = v36 + Vector3.new(v37, v38, v39(v40, v41))
            for v43 = 1, u31 do
                local v44 = v43 / u31
                local v45 = v34 + (v42 - v34) * v44
                u26.Position = v45 + (v42 + (v35 - v42) * v44 - v45) * v44
                v35 = u29.Position
                task.wait()
            end
            u271.ParticleHandler(u26, false, 0)
            u4:AddItem(u26, u33)
        end)()
    end,
    ["CreateTrails"] = function(p46, p47, u48, p49, p50, p51, p52) --[[Function name: CreateTrails, line 108]]
        --[[
        Upvalues:
            [1] = u4
        --]]
        local u53 = p47 or 15
        local u54 = p46 or 15
        local v55 = p52 or 2
        local v56 = p49 or { Color3.fromRGB(255, 255, 255) }
        local v57 = v56[math.random(1, #v56)]
        local u58
        if (p51 or false) and math.random(1, v55) == v55 then
            u58 = script.Trails.Trail2:Clone()
            u58.Attachment1.Trail.WidthScale = NumberSequence.new({ NumberSequenceKeypoint.new(0, 0), NumberSequenceKeypoint.new(0.3, u54 * 0.01), NumberSequenceKeypoint.new(1, 0) })
        else
            u58 = script.Trails.Trail:Clone()
        end
        u58.Attachment1.Trail.LightEmission = 0.5
        u58.CFrame = u48.CFrame * CFrame.new(math.random(-u54, u54), math.random(-u54, u54), math.random(-u54, u54))
        u58.Attachment1.Trail.Color = ColorSequence.new({ ColorSequenceKeypoint.new(0, v57), ColorSequenceKeypoint.new(1, v57) })
        u58.Parent = p50
        coroutine.wrap(function() --[[Anonymous function at line 138]]
            --[[
            Upvalues:
                [1] = u58
                [2] = u48
                [3] = u54
                [4] = u53
                [5] = u4
            --]]
            local v59 = u58.Position
            local v60 = u48.Position
            local v61 = (v59 + v60) / 2
            local v62 = math.random(-u54, u54)
            local v63 = math.random(-u54, u54)
            local v64 = math.random
            local v65 = -u54
            local v66 = u54
            local v67 = v61 + Vector3.new(v62, v63, v64(v65, v66))
            for v68 = 1, u53 do
                local v69 = v68 / u53
                local v70 = v59 + (v67 - v59) * v69
                u58.Position = v70 + (v67 + (v60 - v67) * v69 - v70) * v69
                v60 = u48.Position
                task.wait()
            end
            u4:AddItem(u58, u58.Attachment1.Trail.Lifetime)
        end)()
    end,
    ["CreatePartTrails"] = function(p71, p72, u73) --[[Function name: CreatePartTrails, line 158]]
        --[[
        Upvalues:
            [1] = u2
            [2] = u4
            [3] = u3
        --]]
        local u74 = p72 or 15
        local v75 = p71 or 15
        local v76 = u73.Position
        local v77 = math.random(-v75, v75)
        local v78 = math.random(-v75, v75)
        local v79 = math.random
        local v80 = -v75
        local u81 = v76 + Vector3.new(v77, v78, v79(v80, v75))
        local u82 = u73.Position
        local v83 = (u81 + u82) / 2
        local v84 = math.random(-v75, v75)
        local v85 = math.random(-v75, v75)
        local v86 = math.random
        local v87 = -v75
        local u88 = v83 + Vector3.new(v84, v85, v86(v87, v75))
        coroutine.wrap(function() --[[Anonymous function at line 166]]
            --[[
            Upvalues:
                [1] = u74
                [2] = u81
                [3] = u88
                [4] = u82
                [5] = u2
                [6] = u4
                [7] = u3
                [8] = u73
            --]]
            for v89 = 1, u74 do
                local v90 = v89 / u74
                local v91 = (v89 + 1) / u74
                local v92 = u81
                local v93 = v92 + (u88 - v92) * v90
                local v94 = u88
                local v95 = v94 + (u82 - v94) * v90
                local v96 = u81
                local v97 = v96 + (u88 - v96) * v91
                local v98 = u88
                local v99 = v98 + (u82 - v98) * v91
                local v100 = v93 + (v95 - v93) * v90
                local v101 = v97 + (v99 - v97) * v91
                local v102 = (v101 - v100).Magnitude
                local v103 = u2.GoIn:Clone()
                u4:AddItem(v103, 0.025)
                v103.Position = v100
                v103.CFrame = CFrame.lookAt(v103.Position, v101)
                v103.Parent = workspace.FX
                u3:Create(v103, TweenInfo.new(0.025), {
                    ["Size"] = Vector3.new(0.2, 0.2, v102)
                }):Play()
                u82 = u73.Position
                task.wait()
            end
        end)()
    end,
    ["Flipbook"] = function(p104, u105, u106) --[[Function name: Flipbook, line 195]]
        local u107 = p104:FindFirstChildOfClass("Decal")
        if u107 and u106 then
            u107.Transparency = 1
            coroutine.wrap(function() --[[Anonymous function at line 199]]
                --[[
                Upvalues:
                    [1] = u106
                    [2] = u107
                    [3] = u105
                --]]
                for v108 = 1, #u106 do
                    local v109 = u107
                    v109.Transparency = v109.Transparency - #u106 * 0.025
                    u107.Texture = u106[v108]
                    task.wait(1 / u105)
                end
                u107.Texture = ""
            end)()
        end
    end,
    ["GroundSmoke"] = function(p110) --[[Function name: GroundSmoke, line 211]]
        --[[
        Upvalues:
            [1] = u3
            [2] = u271
            [3] = u7
        --]]
        local v111 = Random.new():NextNumber(125, 500)
        local u112 = script.SmokeMesh:Clone()
        u112.Decal.ZIndex = math.random(1, 10000000)
        u112.Decal.Color3 = Color3.fromRGB(1 * v111, 1 * v111, 1 * v111)
        local v113 = p110.CFrame * CFrame.new(0, -1, 0)
        local v114 = CFrame.Angles
        local v115 = math.random(-360, 360)
        u112.CFrame = v113 * v114(0, math.rad(v115), 0)
        u112.Parent = workspace.World.Alive
        u3:Create(u112.Mesh, TweenInfo.new(0.75), {
            ["Scale"] = Vector3.new(2, 0, 2) * Random.new():NextNumber(0.5, 1.25)
        }):Play()
        u3:Create(u112, TweenInfo.new(0.75), {
            ["CFrame"] = u112.CFrame * CFrame.Angles(0, -2.0943951023931953, 0) * CFrame.new(0, -2.5, 0)
        }):Play()
        u3:Create(u112.Decal, TweenInfo.new(0.125), {
            ["Transparency"] = 0
        }):Play()
        task.delay(0.1, function() --[[Anonymous function at line 223]]
            --[[
            Upvalues:
                [1] = u3
                [2] = u112
            --]]
            u3:Create(u112.Decal, TweenInfo.new(0.625), {
                ["Transparency"] = 1
            }):Play()
        end)
        u271.Flipbook(u112, 48, u7)
    end,
    ["ImpactFrame"] = function(p116, p117) --[[Function name: ImpactFrame, line 230]]
        --[[
        Upvalues:
            [1] = u4
        --]]
        local v118 = next
        local v119, v120 = game.Lighting:GetChildren()
        local u121 = p117 or 0.06
        for _, v122 in v118, v119, v120 do
            if v122:IsA("ColorCorrectionEffect") then
                v122.Enabled = false
            end
        end
        for v123 = 1, #p116 do
            local u124 = Instance.new("Highlight")
            u124.FillTransparency = 0
            u124.OutlineTransparency = 1
            u124.FillColor = Color3.new(1, 1, 1)
            u124.DepthMode = Enum.HighlightDepthMode.AlwaysOnTop
            u124.Parent = p116[v123]
            task.delay(u121 * 0.5, function() --[[Anonymous function at line 246]]
                --[[
                Upvalues:
                    [1] = u124
                --]]
                u124.FillColor = Color3.new(0, 0, 0)
            end)
            u4:AddItem(u124, u121)
        end
        local u125 = Instance.new("ColorCorrectionEffect")
        u125.TintColor = Color3.new(0, 0, 0)
        u125.Brightness = 3
        task.delay(u121 * 0.5, function() --[[Anonymous function at line 255]]
            --[[
            Upvalues:
                [1] = u125
                [2] = u121
            --]]
            u125.TintColor = Color3.new(1, 1, 1)
            task.wait(u121 * 0.5)
            local v126 = next
            local v127, v128 = game.Lighting:GetChildren()
            for _, v129 in v126, v127, v128 do
                if v129:IsA("ColorCorrectionEffect") then
                    v129.Enabled = true
                end
            end
        end)
        u125.Parent = game.Lighting
        u4:AddItem(u125, u121)
    end,
    ["BlackWhite"] = function(p130) --[[Function name: BlackWhite, line 270]]
        --[[
        Upvalues:
            [1] = u4
        --]]
        local u131 = p130 or 0.08
        local u132 = Instance.new("ColorCorrectionEffect")
        u4:AddItem(u132, 1)
        u132.Contrast = 5
        u132.Saturation = -1
        u132.Enabled = false
        u132.Parent = game.Lighting
        local u133 = Instance.new("ColorCorrectionEffect")
        u4:AddItem(u133, 1)
        u133.Contrast = -3
        u133.Saturation = -1
        u133.Enabled = false
        u133.Parent = game.Lighting
        u132.Enabled = true
        task.delay(u131, function() --[[Anonymous function at line 288]]
            --[[
            Upvalues:
                [1] = u132
                [2] = u133
                [3] = u131
            --]]
            u132.Enabled = false
            u133.Enabled = true
            task.wait(u131)
            u133.Enabled = false
        end)
    end,
    ["BloomBlur"] = function() --[[Function name: BloomBlur, line 296]]
        --[[
        Upvalues:
            [1] = u4
            [2] = u3
        --]]
        local u134 = TweenInfo.new
        local u135 = Instance.new("BlurEffect")
        local u136 = Instance.new("BloomEffect")
        u135.Parent = game.Lighting
        u136.Parent = game.Lighting
        u4:AddItem(u135, 0.5)
        u4:AddItem(u136, 0.5)
        u135.Name = "Blur"
        u136.Name = "Bloom"
        u3:Create(u135, u134(0.15, Enum.EasingStyle.Sine, Enum.EasingDirection.Out), {
            ["Size"] = math.random(8, 12)
        }):Play()
        u3:Create(u136, u134(0.05, Enum.EasingStyle.Sine, Enum.EasingDirection.Out), {
            ["Intensity"] = 3,
            ["Size"] = 35,
            ["Threshold"] = 1
        }):Play()
        task.delay(0.1, function() --[[Anonymous function at line 312]]
            --[[
            Upvalues:
                [1] = u3
                [2] = u135
                [3] = u134
                [4] = u136
            --]]
            u3:Create(u135, u134(0.1, Enum.EasingStyle.Sine, Enum.EasingDirection.Out), {
                ["Size"] = 0
            }):Play()
            u3:Create(u136, u134(0.1, Enum.EasingStyle.Sine, Enum.EasingDirection.Out), {
                ["Intensity"] = 1,
                ["Size"] = 24,
                ["Threshold"] = 2
            }):Play()
        end)
    end,
    ["JustBlur"] = function(p137) --[[Function name: JustBlur, line 319]]
        --[[
        Upvalues:
            [1] = u4
            [2] = u3
        --]]
        local u138 = TweenInfo.new
        local u139 = Instance.new("BlurEffect")
        u139.Parent = game.Lighting
        u4:AddItem(u139, p137)
        u139.Name = "Blur"
        u3:Create(u139, u138(0.15, Enum.EasingStyle.Sine, Enum.EasingDirection.Out), {
            ["Size"] = math.random(8, 12)
        }):Play()
        task.delay(p137 - 0.5, function() --[[Anonymous function at line 331]]
            --[[
            Upvalues:
                [1] = u3
                [2] = u139
                [3] = u138
            --]]
            u3:Create(u139, u138(0.1, Enum.EasingStyle.Sine, Enum.EasingDirection.Out), {
                ["Size"] = 0
            }):Play()
        end)
    end,
    ["JustBloom"] = function(p140) --[[Function name: JustBloom, line 336]]
        --[[
        Upvalues:
            [1] = u4
            [2] = u3
        --]]
        local v141 = p140 or 1
        local u142 = TweenInfo.new
        local u143 = Instance.new("BloomEffect")
        u143.Parent = game.Lighting
        u4:AddItem(u143, v141)
        u143.Name = "Bloom"
        u3:Create(u143, u142(0.05, Enum.EasingStyle.Sine, Enum.EasingDirection.Out), {
            ["Intensity"] = 3,
            ["Size"] = 35,
            ["Threshold"] = 1
        }):Play()
        task.delay(v141 - 0.5, function() --[[Anonymous function at line 345]]
            --[[
            Upvalues:
                [1] = u3
                [2] = u143
                [3] = u142
            --]]
            u3:Create(u143, u142(0.5, Enum.EasingStyle.Sine, Enum.EasingDirection.Out), {
                ["Intensity"] = 1,
                ["Size"] = 24,
                ["Threshold"] = 2
            }):Play()
        end)
    end,
    ["AddColorDepth"] = function(p144, p145) --[[Function name: AddColorDepth, line 350]]
        --[[
        Upvalues:
            [1] = u4
            [2] = u3
        --]]
        local u146 = p144 or 1
        local u147 = p145 or 0.35
        local u148 = Instance.new("ColorCorrectionEffect")
        u4:AddItem(u148, u146 + u147)
        u148.Saturation = 0.2
        u148.Contrast = 0.1
        u148.Parent = game.Lighting
        task.delay(u147, function() --[[Anonymous function at line 360]]
            --[[
            Upvalues:
                [1] = u3
                [2] = u148
                [3] = u146
                [4] = u147
            --]]
            u3:Create(u148, TweenInfo.new(0.3), {
                ["Saturation"] = 0.2,
                ["Contrast"] = 0.1
            }):Play()
            task.wait(u146 - u147)
            u3:Create(u148, TweenInfo.new(0.3), {
                ["Saturation"] = 0,
                ["Contrast"] = 0
            }):Play()
        end)
    end,
    ["GroundRocks"] = function(p149, p150, p151, p152, p153, u154, u155) --[[Function name: GroundRocks, line 367]]
        --[[
        Upvalues:
            [1] = u5
            [2] = u3
        --]]
        local v156 = p150 or -10
        for _ = 1, p151 do
            local v157 = CFrame.new(p149)
            local v158 = workspace:Raycast(v157.Position, Vector3.new(0, v156, 0), u5.RayParams)
            if v158 and (v158.Instance and v158.Position) then
                local u159 = Instance.new("Part")
                u159.Parent = workspace.World.ClientEffects
                u159.Name = "Rock"
                u159.Anchored = false
                u159.CanCollide = true
                u159.CollisionGroup = "Humanoid"
                local v160 = math.random(10, p152 * 100) / 100
                local v161 = v160 / 1.5
                local v162 = v160 / 1.5
                u159.Size = Vector3.new(v160, v161, v162)
                local v163 = CFrame.new(v158.Position)
                local v164 = CFrame.Angles
                local v165 = math.random(-360, 360)
                local v166 = math.rad(v165)
                local v167 = math.random(-360, 360)
                local v168 = math.rad(v167)
                local v169 = math.random(-360, 360)
                u159.CFrame = v163 * v164(v166, v168, (math.rad(v169)))
                u159.Material = v158.Material
                u159.MaterialVariant = v158.Instance.MaterialVariant
                u159.Color = v158.Instance.Color
                local v170 = Random.new():NextInteger(-2, 2)
                local v171 = Random.new():NextInteger(-2, 2)
                local v172 = (v158.Position + Vector3.new(0, 5, 0) - (v158.Position + Vector3.new(v170, 1, v171))).Unit
                local u173 = Instance.new("BodyVelocity")
                u173.Name = "Fly"
                u173.Parent = u159
                u173.P = 2000
                u173.MaxForce = Vector3.new(25000, 25000, 25000)
                u173.Velocity = v172 * p153
                task.delay(0.2, function() --[[Anonymous function at line 401]]
                    --[[
                    Upvalues:
                        [1] = u173
                        [2] = u154
                        [3] = u3
                        [4] = u159
                        [5] = u155
                    --]]
                    u173:Destroy()
                    task.wait(u154)
                    u3:Create(u159, TweenInfo.new(u155, Enum.EasingStyle.Back, Enum.EasingDirection.In), {
                        ["Size"] = Vector3.new()
                    }):Play()
                    task.wait(1)
                    u159:Destroy()
                end)
            end
        end
    end,
    ["JuanGroundRock"] = function(p174, u175) --[[Function name: JuanGroundRock, line 412]]
        --[[
        Upvalues:
            [1] = u5
            [2] = u4
            [3] = u3
        --]]
        local v176 = math.random(0, 30)
        local v177 = p174.Position
        local v178 = u5.RayParams
        local v179 = workspace:Raycast(p174.Position, Vector3.new(0, -25, 0), v178)
        if v179 then
            local u180 = script.Rocks.Rock:Clone()
            local v181 = u180.Size.X * Random.new():NextNumber(1, 2.25)
            local v182 = u180.Size.Y * Random.new():NextNumber(1, 2.25)
            local v183 = u180.Size.Z * Random.new():NextNumber(1, 1.9)
            local v184 = Vector3.new(v181, v182, v183)
            u4:AddItem(u180, 3)
            u180.CFrame = p174.CFrame * CFrame.new(-u175 * Random.new():NextNumber(1, 1.5), -v179.Distance, 0)
            u180.Size = Vector3.new(0, 0, 0)
            u180.Parent = workspace.World.ClientEffects
            local v185 = CFrame.lookAt(u180.Position, v177)
            local v186 = CFrame.Angles
            local v187 = math.rad(v176)
            local v188 = math.random(-10, 10)
            local v189 = math.rad(v188)
            local v190 = math.random(-10, 10)
            u180.CFrame = v185 * v186(v187, v189, (math.rad(v190)))
            u180.BrickColor = BrickColor.new("Dark grey")
            u180.Material = Enum.Material.Concrete
            u180.MaterialVariant = v179.Instance.MaterialVariant
            u3:Create(u180, TweenInfo.new(0.3), {
                ["Size"] = v184
            }):Play()
            local u191 = 90 - v176
            task.delay(1.7, function() --[[Anonymous function at line 433]]
                --[[
                Upvalues:
                    [1] = u3
                    [2] = u180
                    [3] = u175
                    [4] = u191
                --]]
                local v192 = u3
                local v193 = u180
                local v194 = TweenInfo.new(1)
                local v195 = {}
                local v196 = u191
                v195.CFrame = u180.CFrame * CFrame.new(-(u175 * 2), -11.5, 0) * CFrame.Angles(math.rad(v196), 0, 0)
                v192:Create(v193, v194, v195):Play()
            end)
        end
    end,
    ["throwRock"] = function(p197, p198, p199, p200) --[[Function name: throwRock, line 440]]
        --[[
        Upvalues:
            [1] = u4
            [2] = u3
        --]]
        local v201 = p200 or 180
        local u202 = script.Rocks["Rock" .. math.random(1, 3)]:Clone()
        u202.Size = u202.Size * Random.new():NextNumber(1.01, p198 or 2)
        u202.CanCollide = false
        local v203 = p197 * CFrame.new(math.random(-5, 5), math.random(0, 5), math.random(-5, 5))
        local v204 = CFrame.Angles
        local v205 = math.random(-v201, v201)
        local v206 = math.rad(v205)
        local v207 = math.random(-v201, v201)
        u202.CFrame = v203 * v204(v206, 0, (math.rad(v207)))
        u202.Parent = workspace.World.ClientEffects
        local v208 = Instance.new("BodyVelocity")
        v208.MaxForce = Vector3.new(100000000, 100000000, 100000000)
        v208.P = 1000
        v208.Velocity = u202.CFrame.UpVector * math.random(15, p199 or 75)
        v208.Parent = u202
        u4:AddItem(v208, 0.2)
        task.delay(0.1, function() --[[Anonymous function at line 457]]
            --[[
            Upvalues:
                [1] = u202
            --]]
            u202.CanCollide = true
        end)
        task.delay(2, function() --[[Anonymous function at line 460]]
            --[[
            Upvalues:
                [1] = u202
                [2] = u3
                [3] = u4
            --]]
            u202.Start.Trail.Enabled = false
            u3:Create(u202, TweenInfo.new(0.5), {
                ["Size"] = Vector3.new(0, 0, 0)
            }):Play()
            u4:AddItem(u202, 0.5)
        end)
    end,
    ["LeftRightRock"] = function(p209, p210, p211, p212) --[[Function name: LeftRightRock, line 467]]
        --[[
        Upvalues:
            [1] = u5
            [2] = u4
            [3] = u3
        --]]
        local v213 = -p210 or -25
        local v214 = u5.RayParams
        local v215 = workspace:Raycast(p209.Position, Vector3.new(0, v213, 0), v214)
        if v215 then
            local u216 = Instance.new("Part")
            u4:AddItem(u216, 3)
            u216.Anchored = true
            u216.CanCollide = true
            u216.CFrame = CFrame.new(v215.Position, v215.Position + v215.Normal) * CFrame.Angles(-1.5707963267948966, 0, 0)
            u216.Position = u216.Position + p209.CFrame.RightVector * p211
            local v217 = math.random(-360, 360)
            local v218 = math.random(-360, 360)
            local v219 = math.random
            u216.Orientation = Vector3.new(v217, v218, v219(-360, 360))
            u216.Material = v215.Instance.Material
            u216.MaterialVariant = v215.Instance.MaterialVariant
            u216.Color = v215.Instance.Color
            u216.Parent = workspace.World.ClientEffects
            local u220 = u216:Clone()
            u4:AddItem(u220, 3)
            u220.CFrame = CFrame.new(v215.Position, v215.Position + v215.Normal) * CFrame.Angles(-1.5707963267948966, 0, 0)
            u220.Position = u220.Position + p209.CFrame.RightVector * -p211
            local v221 = math.random(-360, 360)
            local v222 = math.random(-360, 360)
            local v223 = math.random
            u220.Orientation = Vector3.new(v221, v222, v223(-360, 360))
            u220.Parent = workspace.World.ClientEffects
            u3:Create(u216, TweenInfo.new(0.2), {
                ["Size"] = Vector3.new(1, 1, 1) * p212
            }):Play()
            u3:Create(u220, TweenInfo.new(0.2), {
                ["Size"] = Vector3.new(1, 1, 1) * p212
            }):Play()
            task.delay(2.5, function() --[[Anonymous function at line 496]]
                --[[
                Upvalues:
                    [1] = u3
                    [2] = u216
                    [3] = u220
                --]]
                u3:Create(u216, TweenInfo.new(0.5), {
                    ["Size"] = Vector3.new(0, 0, 0)
                }):Play()
                u3:Create(u220, TweenInfo.new(0.5), {
                    ["Size"] = Vector3.new(0, 0, 0)
                }):Play()
            end)
        end
    end,
    ["Crater"] = function(p224, p225, p226, p227, p228, p229, p230) --[[Function name: Crater, line 503]]
        --[[
        Upvalues:
            [1] = u5
            [2] = u4
            [3] = u3
        --]]
        local v231 = p228 or 1
        local v232 = p229 or 360
        local v233 = p230 or 0
        local v234 = p227 or 5
        local v235 = p225 or 12
        local v236 = u5.RayParams
        local v237 = workspace:Raycast(p224.Position, Vector3.new(0, p226 or -25, 0), v236)
        local v238 = 0
        if v237 then
            local v239 = (p224.CFrame * CFrame.new(0, -v237.Distance, -1)).Position
            local v240 = script.Part:Clone()
            u4:AddItem(v240, 1)
            v240.Anchored = true
            v240.CanCollide = true
            v240.CFrame = CFrame.new(v237.Position, v237.Position + v237.Normal) * CFrame.Angles(-1.5707963267948966, 0, 0)
            v240.CFrame = CFrame.lookAt(v240.Position, v239) * CFrame.Angles(0, math.rad(v233), 0)
            v240.Parent = workspace.World.ClientEffects
            for _ = 1, v235 do
                v240.CFrame = CFrame.lookAt(v240.Position, v239) * CFrame.Angles(0, math.rad(v233), 0) * CFrame.Angles(0, math.rad(v238), 0)
                local u241 = Instance.new("Part")
                u4:AddItem(u241, 3)
                u241.Anchored = true
                u241.CanCollide = true
                u241.CFrame = v240.CFrame * CFrame.new(0, 0, -v234)
                local v242 = math.random(-360, 360)
                local v243 = math.random(-360, 360)
                local v244 = math.random
                u241.Orientation = Vector3.new(v242, v243, v244(-360, 360))
                u241.Material = v237.Instance.Material
                u241.MaterialVariant = v237.Instance.MaterialVariant
                u241.Color = v237.Instance.Color
                u241.Parent = workspace.World.ClientEffects
                v238 = v238 + v232 / v235
                u3:Create(u241, TweenInfo.new(0.2), {
                    ["Size"] = Vector3.new(1, 1, 1) * v231
                }):Play()
                task.delay(2.5, function() --[[Anonymous function at line 544]]
                    --[[
                    Upvalues:
                        [1] = u3
                        [2] = u241
                    --]]
                    u3:Create(u241, TweenInfo.new(0.5), {
                        ["Size"] = Vector3.new(0, 0, 0)
                    }):Play()
                end)
            end
        end
    end,
    ["DecreaseBeamSize"] = function(p245, p246) --[[Function name: DecreaseBeamSize, line 552]]
        --[[
        Upvalues:
            [1] = u3
        --]]
        local v247 = next
        local v248, v249 = p245:GetDescendants()
        for _, v250 in v247, v248, v249 do
            if v250:IsA("Beam") then
                u3:Create(v250, TweenInfo.new(p246), {
                    ["Width0"] = 0,
                    ["Width1"] = 0
                }):Play()
            end
        end
    end,
    ["CreateWeld"] = function(p251, p252) --[[Function name: CreateWeld, line 569]]
        local v253 = Instance.new("Weld")
        v253.Part0 = p251
        v253.Part1 = p252
        v253.Parent = p251
        return v253
    end,
    ["RememberParticleSize"] = function(p254) --[[Function name: RememberParticleSize, line 577]]
        --[[
        Upvalues:
            [1] = u6
        --]]
        local v255 = next
        local v256, v257 = p254:GetDescendants()
        for _, v258 in v255, v256, v257 do
            if v258:IsA("ParticleEmitter") then
                u6[v258.Name] = v258.Size.Keypoints[2].Value
            end
        end
    end,
    ["UnRememberParticleSize"] = function(p259) --[[Function name: UnRememberParticleSize, line 585]]
        --[[
        Upvalues:
            [1] = u6
        --]]
        local v260 = next
        local v261, v262 = p259:GetDescendants()
        for _, v263 in v260, v261, v262 do
            if v263:IsA("ParticleEmitter") then
                u6[v263.Name] = nil
            end
        end
    end,
    ["ScaleParticles"] = function(p264, p265) --[[Function name: ScaleParticles, line 593]]
        --[[
        Upvalues:
            [1] = u6
        --]]
        local v266 = next
        local v267, v268 = p264:GetDescendants()
        for _, v269 in v266, v267, v268 do
            if v269:IsA("ParticleEmitter") then
                if u6[v269.Name] then
                    local v270 = u6[v269.Name] + p265
                    v269.Size = NumberSequence.new({ NumberSequenceKeypoint.new(0, 0), NumberSequenceKeypoint.new(1, v270) })
                else
                    warn(v269.Name, "Has No Saved Scale Value")
                end
            end
        end
    end
}
return u271