-- Full Path: -Grow-a-Garden-\\ProximityPromptScript-LocalScript.lua
local u1 = game:GetService("UserInputService")
local v2 = game:GetService("ProximityPromptService")
local u3 = game:GetService("TweenService")
local u4 = game:GetService("TextService")
local u5 = game:GetService("Players").LocalPlayer:WaitForChild("PlayerGui")
local u6 = {
    [Enum.KeyCode.ButtonX] = "rbxasset://textures/ui/Controls/xboxX.png",
    [Enum.KeyCode.ButtonY] = "rbxasset://textures/ui/Controls/xboxY.png",
    [Enum.KeyCode.ButtonA] = "rbxasset://textures/ui/Controls/xboxA.png",
    [Enum.KeyCode.ButtonB] = "rbxasset://textures/ui/Controls/xboxB.png",
    [Enum.KeyCode.DPadLeft] = "rbxasset://textures/ui/Controls/dpadLeft.png",
    [Enum.KeyCode.DPadRight] = "rbxasset://textures/ui/Controls/dpadRight.png",
    [Enum.KeyCode.DPadUp] = "rbxasset://textures/ui/Controls/dpadUp.png",
    [Enum.KeyCode.DPadDown] = "rbxasset://textures/ui/Controls/dpadDown.png",
    [Enum.KeyCode.ButtonSelect] = "rbxasset://textures/ui/Controls/xboxmenu.png",
    [Enum.KeyCode.ButtonL1] = "rbxasset://textures/ui/Controls/xboxLS.png",
    [Enum.KeyCode.ButtonR1] = "rbxasset://textures/ui/Controls/xboxRS.png"
}
local u7 = {
    [Enum.KeyCode.Backspace] = "rbxasset://textures/ui/Controls/backspace.png",
    [Enum.KeyCode.Return] = "rbxasset://textures/ui/Controls/return.png",
    [Enum.KeyCode.LeftShift] = "rbxasset://textures/ui/Controls/shift.png",
    [Enum.KeyCode.RightShift] = "rbxasset://textures/ui/Controls/shift.png",
    [Enum.KeyCode.Tab] = "rbxasset://textures/ui/Controls/tab.png"
}
local u8 = {
    ["\'"] = "rbxasset://textures/ui/Controls/apostrophe.png",
    [","] = "rbxasset://textures/ui/Controls/comma.png",
    ["`"] = "rbxasset://textures/ui/Controls/graveaccent.png",
    ["."] = "rbxasset://textures/ui/Controls/period.png",
    [" "] = "rbxasset://textures/ui/Controls/spacebar.png"
}
local u9 = {
    [Enum.KeyCode.LeftControl] = "Ctrl",
    [Enum.KeyCode.RightControl] = "Ctrl",
    [Enum.KeyCode.LeftAlt] = "Alt",
    [Enum.KeyCode.RightAlt] = "Alt",
    [Enum.KeyCode.F1] = "F1",
    [Enum.KeyCode.F2] = "F2",
    [Enum.KeyCode.F3] = "F3",
    [Enum.KeyCode.F4] = "F4",
    [Enum.KeyCode.F5] = "F5",
    [Enum.KeyCode.F6] = "F6",
    [Enum.KeyCode.F7] = "F7",
    [Enum.KeyCode.F8] = "F8",
    [Enum.KeyCode.F9] = "F9",
    [Enum.KeyCode.F10] = "F10",
    [Enum.KeyCode.F11] = "F11",
    [Enum.KeyCode.F12] = "F12"
}
local function u214(u10, p11, p12) --[[Anonymous function at line 83]]
    --[[
    Upvalues:
        [1] = u3
        [2] = u6
        [3] = u1
        [4] = u7
        [5] = u8
        [6] = u9
        [7] = u4
    --]]
    local u13 = {}
    local u14 = {}
    local u15 = {}
    local u16 = {}
    local v17 = TweenInfo.new(u10.HoldDuration, Enum.EasingStyle.Linear, Enum.EasingDirection.Out)
    TweenInfo.new(0.5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out)
    local u18 = TweenInfo.new(0.2, Enum.EasingStyle.Quad, Enum.EasingDirection.Out)
    local u19 = TweenInfo.new(0.06, Enum.EasingStyle.Linear, Enum.EasingDirection.Out)
    local v20 = TweenInfo.new(0, Enum.EasingStyle.Linear, Enum.EasingDirection.Out)
    local u21 = nil
    local v22 = u10:GetAttribute("Theme")
    if v22 then
        local v23 = script:FindFirstChild(v22)
        if v23 then
            u21 = v23:Clone()
        end
    end
    if u21 == nil then
        u21 = script.Default:Clone()
    end
    u21.Enabled = true
    local v24 = u21.PromptFrame
    local v25 = v24.InputFrame
    local u26 = v24.ActionText
    local u27 = v24.ObjectText
    local v28 = v24.BackgroundTransparency
    local v29 = v24.ImageTransparency
    v24.BackgroundTransparency = 1
    v24.ImageTransparency = 1
    local v30 = u3
    local v31 = {
        ["Size"] = UDim2.fromScale(0.5, 1),
        ["BackgroundTransparency"] = 1,
        ["ImageTransparency"] = 1
    }
    table.insert(u13, v30:Create(v24, u18, v31))
    local v32 = u3
    local v33 = {
        ["Size"] = UDim2.fromScale(1, 1),
        ["BackgroundTransparency"] = v28,
        ["ImageTransparency"] = v29
    }
    table.insert(u14, v32:Create(v24, u18, v33))
    local v34 = u3
    local v35 = {
        ["Size"] = UDim2.fromScale(0.5, 1),
        ["BackgroundTransparency"] = 1,
        ["ImageTransparency"] = 1
    }
    table.insert(u15, v34:Create(v24, u18, v35))
    local v36 = u3
    local v37 = {
        ["Size"] = UDim2.fromScale(1, 1),
        ["BackgroundTransparency"] = v28,
        ["ImageTransparency"] = v29
    }
    table.insert(u16, v36:Create(v24, u18, v37))
    local function u52(p38) --[[Anonymous function at line 126]]
        --[[
        Upvalues:
            [1] = u13
            [2] = u3
            [3] = u18
            [4] = u14
            [5] = u15
            [6] = u16
        --]]
        local v39 = p38.Transparency
        p38.Transparency = 1
        local v40 = u13
        local v41 = u3
        local v42 = u18
        table.insert(v40, v41:Create(p38, v42, {
            ["Transparency"] = 1
        }))
        local v43 = u14
        local v44 = u3
        local v45 = u18
        table.insert(v43, v44:Create(p38, v45, {
            ["Transparency"] = v39
        }))
        local v46 = u15
        local v47 = u3
        local v48 = u18
        table.insert(v46, v47:Create(p38, v48, {
            ["Transparency"] = 1
        }))
        local v49 = u16
        local v50 = u3
        local v51 = u18
        table.insert(v49, v50:Create(p38, v51, {
            ["Transparency"] = v39
        }))
    end
    local function u67(p53) --[[Anonymous function at line 135]]
        --[[
        Upvalues:
            [1] = u13
            [2] = u3
            [3] = u18
            [4] = u14
            [5] = u15
            [6] = u16
        --]]
        local v54 = p53.BackgroundTransparency
        p53.BackgroundTransparency = 1
        local v55 = u13
        local v56 = u3
        local v57 = u18
        table.insert(v55, v56:Create(p53, v57, {
            ["BackgroundTransparency"] = 1
        }))
        local v58 = u14
        local v59 = u3
        local v60 = u18
        table.insert(v58, v59:Create(p53, v60, {
            ["BackgroundTransparency"] = v54
        }))
        local v61 = u15
        local v62 = u3
        local v63 = u18
        table.insert(v61, v62:Create(p53, v63, {
            ["BackgroundTransparency"] = 1
        }))
        local v64 = u16
        local v65 = u3
        local v66 = u18
        table.insert(v64, v65:Create(p53, v66, {
            ["BackgroundTransparency"] = v54
        }))
    end
    local function u83(p68) --[[Anonymous function at line 144]]
        --[[
        Upvalues:
            [1] = u13
            [2] = u3
            [3] = u18
            [4] = u14
            [5] = u15
            [6] = u16
        --]]
        local v69 = p68.TextTransparency
        local v70 = p68.TextStrokeTransparency
        p68.TextTransparency = 1
        p68.TextStrokeTransparency = 1
        local v71 = u13
        local v72 = u3
        local v73 = u18
        table.insert(v71, v72:Create(p68, v73, {
            ["TextTransparency"] = 1,
            ["TextStrokeTransparency"] = 1
        }))
        local v74 = u14
        local v75 = u3
        local v76 = u18
        table.insert(v74, v75:Create(p68, v76, {
            ["TextTransparency"] = v69,
            ["TextStrokeTransparency"] = v70
        }))
        local v77 = u15
        local v78 = u3
        local v79 = u18
        table.insert(v77, v78:Create(p68, v79, {
            ["TextTransparency"] = 1,
            ["TextStrokeTransparency"] = 1
        }))
        local v80 = u16
        local v81 = u3
        local v82 = u18
        table.insert(v80, v81:Create(p68, v82, {
            ["TextTransparency"] = v69,
            ["TextStrokeTransparency"] = v70
        }))
    end
    local function u98(p84) --[[Anonymous function at line 155]]
        --[[
        Upvalues:
            [1] = u13
            [2] = u3
            [3] = u18
            [4] = u14
            [5] = u15
            [6] = u16
        --]]
        local v85 = p84.ImageTransparency
        p84.ImageTransparency = 1
        local v86 = u13
        local v87 = u3
        local v88 = u18
        table.insert(v86, v87:Create(p84, v88, {
            ["ImageTransparency"] = 1
        }))
        local v89 = u14
        local v90 = u3
        local v91 = u18
        table.insert(v89, v90:Create(p84, v91, {
            ["ImageTransparency"] = v85
        }))
        local v92 = u15
        local v93 = u3
        local v94 = u18
        table.insert(v92, v93:Create(p84, v94, {
            ["ImageTransparency"] = 1
        }))
        local v95 = u16
        local v96 = u3
        local v97 = u18
        table.insert(v95, v96:Create(p84, v97, {
            ["ImageTransparency"] = v85
        }))
    end
    local function u101(p99) --[[Anonymous function at line 164]]
        --[[
        Upvalues:
            [1] = u52
            [2] = u67
            [3] = u83
            [4] = u98
            [5] = u101
        --]]
        if p99:IsA("UIStroke") then
            u52(p99)
        elseif not p99:IsA("UIGradient") and p99:IsA("GuiObject") then
            u67(p99)
            if p99:IsA("TextLabel") then
                u83(p99)
            elseif p99:IsA("ImageLabel") then
                u98(p99)
            end
        end
        for _, v100 in pairs(p99:GetChildren()) do
            u101(v100)
        end
    end
    local v102 = {
        [v25] = false,
        [u26] = true,
        [u27] = true
    }
    for _, v103 in pairs(v24:GetChildren()) do
        if v102[v103] == nil then
            u101(v103)
        elseif v102[v103] == true then
            for _, v104 in pairs(v103:GetChildren()) do
                u101(v104)
            end
        end
    end
    local v105 = v25.Frame
    local v106 = v105.UIScale
    local v107 = p11 == Enum.ProximityPromptInputType.Touch and 1.6 or 1.33
    local v108 = u3
    table.insert(u13, v108:Create(v106, u18, {
        ["Scale"] = v107
    }))
    local v109 = u3
    table.insert(u14, v109:Create(v106, u18, {
        ["Scale"] = 1
    }))
    u83(u26)
    u83(u27)
    local u110 = v105.ButtonFrame;
    (function() --[[Function name: setupButtonFrameTweens, line 211]]
        --[[
        Upvalues:
            [1] = u110
            [2] = u15
            [3] = u3
            [4] = u19
            [5] = u16
        --]]
        local v111 = u15
        local v112 = u3
        local v113 = u110
        local v114 = u19
        table.insert(v111, v112:Create(v113, v114, {
            ["BackgroundTransparency"] = 1,
            ["ImageTransparency"] = 1
        }))
        local v115 = u16
        local v116 = u3
        local v117 = u110
        local v118 = u19
        local v119 = {
            ["BackgroundTransparency"] = u110.BackgroundTransparency,
            ["ImageTransparency"] = u110.ImageTransparency
        }
        table.insert(v115, v116:Create(v117, v118, v119))
        for _, v120 in pairs(u110:getChildren()) do
            if v120:IsA("UIStroke") then
                local v121 = u15
                local v122 = u3
                local v123 = u19
                table.insert(v121, v122:Create(v120, v123, {
                    ["Transparency"] = 1
                }))
                local v124 = u16
                local v125 = u3
                local v126 = u19
                local v127 = {
                    ["Transparency"] = v120.Transparency
                }
                table.insert(v124, v125:Create(v120, v126, v127))
            end
        end
    end)()
    local u128 = v105.ButtonImage
    local u129 = v105.ButtonText
    local u130 = v105.ButtonTextImage
    local function v150() --[[Anonymous function at line 233]]
        --[[
        Upvalues:
            [1] = u129
            [2] = u15
            [3] = u3
            [4] = u19
            [5] = u16
        --]]
        local v131 = u129.TextTransparency
        local v132 = u129.TextStrokeTransparency
        local v133 = u129.BackgroundTransparency
        u129.BackgroundTransparency = 1
        u129.TextStrokeTransparency = 1
        u129.TextTransparency = 1
        local v134 = u15
        local v135 = u3
        local v136 = u129
        local v137 = u19
        table.insert(v134, v135:Create(v136, v137, {
            ["TextTransparency"] = 1,
            ["TextStrokeTransparency"] = 1,
            ["BackgroundTransparency"] = 1
        }))
        local v138 = u16
        local v139 = u3
        local v140 = u129
        local v141 = u19
        table.insert(v138, v139:Create(v140, v141, {
            ["TextTransparency"] = v131,
            ["TextStrokeTransparency"] = v132,
            ["BackgroundTransparency"] = v133
        }))
        for _, v142 in pairs(u129:getChildren()) do
            if v142:IsA("UIStroke") then
                local v143 = u15
                local v144 = u3
                local v145 = u19
                table.insert(v143, v144:Create(v142, v145, {
                    ["Transparency"] = 1
                }))
                local v146 = u16
                local v147 = u3
                local v148 = u19
                local v149 = {
                    ["Transparency"] = v142.Transparency
                }
                table.insert(v146, v147:Create(v142, v148, v149))
            end
        end
    end
    local function v161() --[[Anonymous function at line 253]]
        --[[
        Upvalues:
            [1] = u128
            [2] = u15
            [3] = u3
            [4] = u19
            [5] = u16
        --]]
        local v151 = u128.ImageTransparency
        local v152 = u128.BackgroundTransparency
        u128.BackgroundTransparency = 1
        u128.ImageTransparency = 1
        local v153 = u15
        local v154 = u3
        local v155 = u128
        local v156 = u19
        table.insert(v153, v154:Create(v155, v156, {
            ["ImageTransparency"] = 1,
            ["BackgroundTransparency"] = 1
        }))
        local v157 = u16
        local v158 = u3
        local v159 = u128
        local v160 = u19
        table.insert(v157, v158:Create(v159, v160, {
            ["ImageTransparency"] = v151,
            ["BackgroundTransparency"] = v152
        }))
    end
    local function v172() --[[Anonymous function at line 262]]
        --[[
        Upvalues:
            [1] = u130
            [2] = u15
            [3] = u3
            [4] = u19
            [5] = u16
        --]]
        local v162 = u130.BackgroundTransparency
        local v163 = u130.ImageTransparency
        u130.BackgroundTransparency = 1
        u130.ImageTransparency = 1
        local v164 = u15
        local v165 = u3
        local v166 = u130
        local v167 = u19
        table.insert(v164, v165:Create(v166, v167, {
            ["ImageTransparency"] = 1,
            ["BackgroundTransparency"] = 1
        }))
        local v168 = u16
        local v169 = u3
        local v170 = u130
        local v171 = u19
        table.insert(v168, v169:Create(v170, v171, {
            ["ImageTransparency"] = v163,
            ["BackgroundTransparency"] = v162
        }))
    end
    if p11 == Enum.ProximityPromptInputType.Gamepad then
        if u6[u10.GamepadKeyCode] then
            v172()
            u130.Image = u6[u10.GamepadKeyCode]
            u129.Visible = false
            u128.Visible = false
            u130.Visible = true
        end
    elseif p11 == Enum.ProximityPromptInputType.Touch then
        v161()
        u128.Image = "rbxasset://textures/ui/Controls/TouchTapIcon.png"
        u129.Visible = false
        u130.Visible = false
        u128.Visible = true
    else
        v161()
        u128.Visible = true
        local v173 = u1:GetStringForKeyCode(u10.KeyboardKeyCode)
        local v174 = u7[u10.KeyboardKeyCode]
        if v174 == nil then
            v174 = u8[v173]
        end
        if v174 == nil then
            v173 = u9[u10.KeyboardKeyCode] or v173
        end
        if v174 then
            v172()
            u130.Image = v174
            u129.Visible = false
            u130.Visible = true
        elseif v173 == nil or v173 == "" then
            local v175 = error
            local v176 = u10.Name
            local v177 = u10.KeyboardKeyCode
            v175("ProximityPrompt \'" .. v176 .. "\' has an unsupported keycode for rendering UI: " .. tostring(v177))
        else
            if string.len(v173) > 2 then
                local v178 = u129.TextSize * 6 / 7
                u129.TextSize = math.round(v178)
            end
            v150()
            u129.Text = v173
            u130.Visible = false
            u129.Visible = true
        end
    end
    if p11 == Enum.ProximityPromptInputType.Touch or u10.ClickablePrompt then
        local v179 = u21.TextButton
        local u180 = false
        v179.InputBegan:Connect(function(p181) --[[Anonymous function at line 336]]
            --[[
            Upvalues:
                [1] = u10
                [2] = u180
            --]]
            if (p181.UserInputType == Enum.UserInputType.Touch or p181.UserInputType == Enum.UserInputType.MouseButton1) and p181.UserInputState ~= Enum.UserInputState.Change then
                u10:InputHoldBegin()
                u180 = true
            end
        end)
        v179.InputEnded:Connect(function(p182) --[[Anonymous function at line 343]]
            --[[
            Upvalues:
                [1] = u180
                [2] = u10
            --]]
            if (p182.UserInputType == Enum.UserInputType.Touch or p182.UserInputType == Enum.UserInputType.MouseButton1) and u180 then
                u180 = false
                u10:InputHoldEnd()
            end
        end)
        u21.Active = true
    end
    if u10.HoldDuration > 0 then
        local v183 = v105.ProgressBar
        local u184 = v183.LeftGradient.ProgressBarImage.UIGradient
        local u185 = v183.RightGradient.ProgressBarImage.UIGradient
        v183.Progress.Changed:Connect(function(p186) --[[Anonymous function at line 76]]
            --[[
            Upvalues:
                [1] = u184
                [2] = u185
            --]]
            local v187 = p186 * 360
            local v188 = math.clamp(v187, 0, 360)
            u184.Rotation = math.clamp(v188, 180, 360)
            u185.Rotation = math.clamp(v188, 0, 180)
        end)
        local v189 = u3
        local v190 = v183.Progress
        table.insert(u13, v189:Create(v190, v17, {
            ["Value"] = 1
        }))
        local v191 = u3
        local v192 = v183.Progress
        table.insert(u14, v191:Create(v192, v20, {
            ["Value"] = 0
        }))
    end
    local u193, u194
    if u10.HoldDuration > 0 then
        u193 = u10.PromptButtonHoldBegan:Connect(function() --[[Anonymous function at line 368]]
            --[[
            Upvalues:
                [1] = u13
            --]]
            for _, v195 in ipairs(u13) do
                v195:Play()
            end
        end)
        u194 = u10.PromptButtonHoldEnded:Connect(function() --[[Anonymous function at line 374]]
            --[[
            Upvalues:
                [1] = u14
            --]]
            for _, v196 in ipairs(u14) do
                v196:Play()
            end
        end)
    else
        u193 = nil
        u194 = nil
    end
    local u198 = u10.Triggered:Connect(function() --[[Anonymous function at line 381]]
        --[[
        Upvalues:
            [1] = u15
        --]]
        for _, v197 in ipairs(u15) do
            v197:Play()
        end
    end)
    local u200 = u10.TriggerEnded:Connect(function() --[[Anonymous function at line 387]]
        --[[
        Upvalues:
            [1] = u16
        --]]
        for _, v199 in ipairs(u16) do
            v199:Play()
        end
    end)
    local function v210() --[[Anonymous function at line 393]]
        --[[
        Upvalues:
            [1] = u10
            [2] = u26
            [3] = u4
            [4] = u27
            [5] = u21
        --]]
        local v201 = Instance.new("GetTextBoundsParams")
        v201.Text = u10.ActionText
        v201.Font = u26.FontFace
        v201.Size = u26.TextSize
        v201.Width = 1000
        local v202 = u4:GetTextBoundsAsync(v201)
        local v203 = Instance.new("GetTextBoundsParams")
        v203.Text = u10.ObjectText
        v203.Font = u27.FontFace
        v203.Size = u27.TextSize
        v203.Width = 1000
        local v204 = u4:GetTextBoundsAsync(v203)
        local v205 = v202.X
        local v206 = v204.X
        local v207 = math.max(v205, v206)
        local v208 = (u10.ActionText == nil or u10.ActionText == "") and (u10.ObjectText == nil or u10.ObjectText == "") and 72 or v207 + 72 + 24
        local v209 = (u10.ObjectText == nil or u10.ObjectText == "") and 0 or 9
        u26.Position = UDim2.new(0.5, 72 - v208 / 2, 0, v209)
        u27.Position = UDim2.new(0.5, 72 - v208 / 2, 0, -10)
        u26.Text = u10.ActionText
        u27.Text = u10.ObjectText
        u26.AutoLocalize = u10.AutoLocalize
        u26.RootLocalizationTable = u10.RootLocalizationTable
        u27.AutoLocalize = u10.AutoLocalize
        u27.RootLocalizationTable = u10.RootLocalizationTable
        u21.Size = UDim2.fromOffset(v208, 72)
        u21.SizeOffset = Vector2.new(u10.UIOffset.X / u21.Size.Width.Offset, u10.UIOffset.Y / u21.Size.Height.Offset)
    end
    local u211 = u10.Changed:Connect(v210)
    v210()
    u21.Adornee = u10.Parent
    u21.Parent = p12
    for _, v212 in ipairs(u16) do
        v212:Play()
    end
    return function() --[[Function name: cleanup, line 448]]
        --[[
        Upvalues:
            [1] = u193
            [2] = u194
            [3] = u198
            [4] = u200
            [5] = u211
            [6] = u15
            [7] = u21
        --]]
        if u193 then
            u193:Disconnect()
        end
        if u194 then
            u194:Disconnect()
        end
        u198:Disconnect()
        u200:Disconnect()
        u211:Disconnect()
        for _, v213 in ipairs(u15) do
            v213:Play()
        end
        wait(0.2)
        u21.Parent = nil
    end
end
v2.PromptShown:Connect(function(p215, p216) --[[Anonymous function at line 474]]
    --[[
    Upvalues:
        [1] = u5
        [2] = u214
    --]]
    if p215:GetAttribute("DontShow") == nil then
        if p215.Style == Enum.ProximityPromptStyle.Default then
            return
        end
        local v217 = u5:FindFirstChild("ProximityPrompts")
        if v217 == nil then
            v217 = Instance.new("ScreenGui")
            v217.Name = "ProximityPrompts"
            v217.ResetOnSpawn = false
            v217.Parent = u5
        end
        local v218 = u214(p215, p216, v217)
        p215.PromptHidden:Wait()
        v218()
    end
end)