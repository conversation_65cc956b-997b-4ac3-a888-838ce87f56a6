-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\ByteNet\process\bufferWriter-ModuleScript.lua
require(script.Parent.Parent.types)
local u1 = nil
local u2 = nil
local u3 = nil
local u4 = nil
local u5 = nil
return {
    ["alloc"] = function(p6) --[[Function name: alloc, line 27]]
        --[[
        Upvalues:
            [1] = u3
            [2] = u2
            [3] = u4
        --]]
        if u2 <= u3 + p6 then
            local v7 = u2 * 1.5
            u2 = math.floor(v7)
            local v8 = buffer.create(u2)
            buffer.copy(v8, 0, u4)
            u4 = v8
        end
    end,
    ["dyn_alloc"] = function(p9) --[[Function name: dyn_alloc, line 40]]
        --[[
        Upvalues:
            [1] = u3
            [2] = u2
            [3] = u4
        --]]
        while u2 <= u3 + p9 do
            local v10 = u2 * 1.5
            u2 = math.floor(v10)
        end
        local v11 = buffer.create(u2)
        buffer.copy(v11, 0, u4)
        u4 = v11
    end,
    ["u8"] = function(p12) --[[Function name: u8, line 56]]
        --[[
        Upvalues:
            [1] = u3
            [2] = u2
            [3] = u4
        --]]
        if u2 <= u3 + 1 then
            local v13 = u2 * 1.5
            u2 = math.floor(v13)
            local v14 = buffer.create(u2)
            buffer.copy(v14, 0, u4)
            u4 = v14
        end
        local v15 = u4
        local v16 = u3
        buffer.writeu8(v15, v16, p12)
        u3 = u3 + 1
    end,
    ["i8"] = function(p17) --[[Function name: i8, line 62]]
        --[[
        Upvalues:
            [1] = u3
            [2] = u2
            [3] = u4
        --]]
        if u2 <= u3 + 1 then
            local v18 = u2 * 1.5
            u2 = math.floor(v18)
            local v19 = buffer.create(u2)
            buffer.copy(v19, 0, u4)
            u4 = v19
        end
        local v20 = u4
        local v21 = u3
        buffer.writei8(v20, v21, p17)
        u3 = u3 + 1
    end,
    ["reference"] = function(p22) --[[Function name: reference, line 69]]
        --[[
        Upvalues:
            [1] = u5
            [2] = u4
            [3] = u3
        --]]
        local v23 = u5
        table.insert(v23, p22)
        local v24 = #u5
        local v25 = u4
        local v26 = u3
        buffer.writeu8(v25, v26, v24)
        u3 = u3 + 1
    end,
    ["u16"] = function(p27) --[[Function name: u16, line 78]]
        --[[
        Upvalues:
            [1] = u3
            [2] = u2
            [3] = u4
        --]]
        if u2 <= u3 + 2 then
            local v28 = u2 * 1.5
            u2 = math.floor(v28)
            local v29 = buffer.create(u2)
            buffer.copy(v29, 0, u4)
            u4 = v29
        end
        local v30 = u4
        local v31 = u3
        buffer.writeu16(v30, v31, p27)
        u3 = u3 + 2
    end,
    ["i16"] = function(p32) --[[Function name: i16, line 84]]
        --[[
        Upvalues:
            [1] = u3
            [2] = u2
            [3] = u4
        --]]
        if u2 <= u3 + 2 then
            local v33 = u2 * 1.5
            u2 = math.floor(v33)
            local v34 = buffer.create(u2)
            buffer.copy(v34, 0, u4)
            u4 = v34
        end
        local v35 = u4
        local v36 = u3
        buffer.writeu16(v35, v36, p32)
        u3 = u3 + 2
    end,
    ["u32"] = function(p37) --[[Function name: u32, line 90]]
        --[[
        Upvalues:
            [1] = u3
            [2] = u2
            [3] = u4
        --]]
        if u2 <= u3 + 4 then
            local v38 = u2 * 1.5
            u2 = math.floor(v38)
            local v39 = buffer.create(u2)
            buffer.copy(v39, 0, u4)
            u4 = v39
        end
        local v40 = u4
        local v41 = u3
        buffer.writeu32(v40, v41, p37)
        u3 = u3 + 4
    end,
    ["writestring"] = function(p42) --[[Function name: writestring, line 96]]
        --[[
        Upvalues:
            [1] = u4
            [2] = u3
        --]]
        buffer.writestring(u4, u3, p42)
        u3 = u3 + string.len(p42)
    end,
    ["i32"] = function(p43) --[[Function name: i32, line 101]]
        --[[
        Upvalues:
            [1] = u3
            [2] = u2
            [3] = u4
        --]]
        if u2 <= u3 + 4 then
            local v44 = u2 * 1.5
            u2 = math.floor(v44)
            local v45 = buffer.create(u2)
            buffer.copy(v45, 0, u4)
            u4 = v45
        end
        local v46 = u4
        local v47 = u3
        buffer.writei32(v46, v47, p43)
        u3 = u3 + 4
    end,
    ["f32NoAlloc"] = function(p48) --[[Function name: f32NoAlloc, line 107]]
        --[[
        Upvalues:
            [1] = u4
            [2] = u3
        --]]
        local v49 = u4
        local v50 = u3
        buffer.writef32(v49, v50, p48)
        u3 = u3 + 4
    end,
    ["f64NoAlloc"] = function(p51) --[[Function name: f64NoAlloc, line 112]]
        --[[
        Upvalues:
            [1] = u4
            [2] = u3
        --]]
        local v52 = u4
        local v53 = u3
        buffer.writef64(v52, v53, p51)
        u3 = u3 + 4
    end,
    ["f32"] = function(p54) --[[Function name: f32, line 117]]
        --[[
        Upvalues:
            [1] = u3
            [2] = u2
            [3] = u4
        --]]
        if u2 <= u3 + 4 then
            local v55 = u2 * 1.5
            u2 = math.floor(v55)
            local v56 = buffer.create(u2)
            buffer.copy(v56, 0, u4)
            u4 = v56
        end
        local v57 = u4
        local v58 = u3
        buffer.writef32(v57, v58, p54)
        u3 = u3 + 4
    end,
    ["f64"] = function(p59) --[[Function name: f64, line 123]]
        --[[
        Upvalues:
            [1] = u3
            [2] = u2
            [3] = u4
        --]]
        if u2 <= u3 + 8 then
            local v60 = u2 * 1.5
            u2 = math.floor(v60)
            local v61 = buffer.create(u2)
            buffer.copy(v61, 0, u4)
            u4 = v61
        end
        local v62 = u4
        local v63 = u3
        buffer.writef64(v62, v63, p59)
        u3 = u3 + 8
    end,
    ["copy"] = function(p64) --[[Function name: copy, line 129]]
        --[[
        Upvalues:
            [1] = u4
            [2] = u3
        --]]
        buffer.copy(u4, u3, p64)
        u3 = u3 + buffer.len(p64)
    end,
    ["bool"] = function(p65) --[[Function name: bool, line 134]]
        --[[
        Upvalues:
            [1] = u3
            [2] = u2
            [3] = u4
        --]]
        if u2 <= u3 + 1 then
            local v66 = u2 * 1.5
            u2 = math.floor(v66)
            local v67 = buffer.create(u2)
            buffer.copy(v67, 0, u4)
            u4 = v67
        end
        local v68 = u4
        local v69 = u3
        buffer.writeu8(v68, v69, p65 and 1 or 0)
        u3 = u3 + 1
    end,
    ["load"] = function(p70) --[[Function name: load, line 140]]
        --[[
        Upvalues:
            [1] = u1
            [2] = u2
            [3] = u3
            [4] = u5
            [5] = u4
        --]]
        u1 = p70
        u2 = p70.size
        u3 = p70.cursor
        u5 = p70.references
        u4 = p70.buff
    end,
    ["export"] = function() --[[Function name: export, line 148]]
        --[[
        Upvalues:
            [1] = u1
            [2] = u2
            [3] = u3
            [4] = u5
            [5] = u4
        --]]
        u1.size = u2
        u1.cursor = u3
        u1.references = u5
        u1.buff = u4
        return u1
    end
}