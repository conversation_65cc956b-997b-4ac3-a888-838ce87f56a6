-- Full Path: -Grow-a-Garden-\StarterPlayer\StarterPlayerScripts\WeatherClient\Rain-ModuleScript.lua
local v1 = {}
local u2 = game.ReplicatedStorage.RainParticle
local u3 = Random.new()
local u4 = workspace.CurrentCamera
local u5 = RaycastParams.new()
u5.FilterDescendantsInstances = { workspace.Terrain, workspace }
u5.FilterType = Enum.RaycastFilterType.Include
local u6 = {}
local u7 = false
local u8 = game.Lighting.ColorCorrection:Clone()
u8.Name = script.Name
u8.Parent = game.Lighting
local function u12(p9) --[[Anonymous function at line 25]]
    --[[
    Upvalues:
        [1] = u2
        [2] = u6
    --]]
    local v10 = {
        ["particle"] = u2:Clone(),
        ["position"] = p9,
        ["spawnTime"] = os.clock(),
        ["visible"] = false,
        ["lastupdate"] = 0
    }
    game.TweenService:Create(v10.particle, TweenInfo.new(0.7), {
        ["Transparency"] = 0.2
    }):Play()
    local v11 = u6
    table.insert(v11, v10)
end
local u13 = script.Sky
local u14 = require(game.ReplicatedStorage.Modules.SkyboxManager)
u14.AddSkybox(u13)
local function u22() --[[Anonymous function at line 72]]
    --[[
    Upvalues:
        [1] = u7
        [2] = u14
        [3] = u13
        [4] = u8
        [5] = u6
        [6] = u3
        [7] = u4
        [8] = u12
    --]]
    u7 = true
    u14.UpdateSkybox(u13, 2)
    game.TweenService:Create(game.Lighting, TweenInfo.new(3), {
        ["Ambient"] = Color3.fromRGB(196, 240, 255),
        ["ExposureCompensation"] = 0.4,
        ["Brightness"] = 0.7
    }):Play()
    game.TweenService:Create(u8, TweenInfo.new(3), {
        ["Brightness"] = 0.1,
        ["TintColor"] = Color3.fromRGB(215, 240, 255)
    }):Play()
    game.Workspace.Terrain.Clouds.Enabled = true
    game.TweenService:Create(game.Workspace.Terrain.Clouds, TweenInfo.new(3), {
        ["Cover"] = 0.905,
        ["Density"] = 0.083
    }):Play()
    task.spawn(function() --[[Anonymous function at line 95]]
        --[[
        Upvalues:
            [1] = u7
            [2] = u6
            [3] = u3
            [4] = u4
            [5] = u12
            [6] = u14
            [7] = u13
            [8] = u8
        --]]
        while u7 do
            task.wait(0.05)
            if #u6 <= 50 then
                for _ = 1, 3 do
                    local v15 = u3:NextNumber(10, 180)
                    local v16 = 2 * v15
                    local v17 = u4.FieldOfView / 2
                    local v18 = math.rad(v17)
                    local v19 = v16 * math.tan(v18)
                    local v20 = v19 * (u4.ViewportSize.X / u4.ViewportSize.Y)
                    local v21 = u4.CFrame * CFrame.new(u3:NextNumber(-v20 / 2, v20 / 2), u3:NextNumber(-v19 / 2, v19 / 2 + 20), -v15)
                    if not workspace:Raycast(v21.Position, Vector3.new(0, 150, 0)) then
                        u12(v21.Position)
                    end
                end
            end
        end
        u14.UpdateSkybox(u13, 0)
        game.TweenService:Create(u8, TweenInfo.new(3), {
            ["Brightness"] = 0,
            ["TintColor"] = Color3.fromRGB(255, 255, 255)
        }):Play()
        game.TweenService:Create(game.Lighting, TweenInfo.new(3), {
            ["Ambient"] = game.Lighting:GetAttribute("DefaultAmbient"),
            ["ExposureCompensation"] = game.Lighting:GetAttribute("DefaultExposure"),
            ["Brightness"] = game.Lighting:GetAttribute("DefaultBrightness")
        }):Play()
        game.TweenService:Create(game.Workspace.Terrain.Clouds, TweenInfo.new(3), {
            ["Cover"] = 0,
            ["Density"] = 0
        }):Play()
        task.delay(3, function() --[[Anonymous function at line 164]]
            game.Workspace.Terrain.Clouds.Enabled = false
        end)
    end)
end
workspace:GetAttributeChangedSignal("RainEvent"):Connect(function() --[[Anonymous function at line 175]]
    --[[
    Upvalues:
        [1] = u22
        [2] = u7
    --]]
    if workspace:GetAttribute("RainEvent") then
        u22()
    else
        u7 = false
    end
end)
local u23 = game.ReplicatedStorage.RainSplash:Clone()
u23.Parent = workspace.WeatherVisuals
task.spawn(function() --[[Anonymous function at line 185]]
    --[[
    Upvalues:
        [1] = u6
        [2] = u5
        [3] = u4
        [4] = u23
        [5] = u3
    --]]
    while true do
        local v24 = game:GetService("RunService").RenderStepped:Wait()
        local v25 = os.clock()
        local v26 = v24 * 3
        local v27 = v24 * 5
        local v28 = math.min(1, v27)
        debug.profilebegin("Weather_" .. script.Name)
        local v29 = {}
        local v30 = {}
        for v31, v32 in u6 do
            local _ = v25 - v32.spawnTime + v28
            local v33 = v32.position
            local v34 = -20 * v28
            local v35 = Vector3.new(0, v34, 0)
            local v36 = workspace:Spherecast(v33, 0.15, v35, u5)
            local v37
            if v36 then
                v33 = v36.Position
                v37 = true
            else
                v32.position = v33 + v35
                v37 = nil
            end
            local v38, v39 = u4:WorldToScreenPoint(v33)
            local v40 = v32.visible
            local v41 = (u4.CFrame.Position - v33).Magnitude / 120
            local v42 = v41 * v41
            local v43 = 1 / math.random(60, 120)
            local v44 = v26 * v42 + 0.016666666666666666
            if v41 > 1.5 then
                v32.particle:Destroy()
                table.remove(u6, v31)
            elseif v25 - v32.lastupdate + v43 > v44 then
                v32.lastupdate = v25
                if v39 and v38.Z < 200 then
                    v32.visible = true
                    local v45 = v32.particle
                    table.insert(v29, v45)
                    local v46 = CFrame.new(v33, u4.CFrame.Position) * CFrame.Angles(1.5707963267948966, 0, 0)
                    table.insert(v30, v46)
                else
                    v32.visible = false
                end
                if v32.visible ~= v40 then
                    if v32.visible then
                        v32.particle.Parent = workspace.WeatherVisuals
                    else
                        v32.particle.Parent = nil
                    end
                end
                if v37 then
                    v32.particle:Destroy()
                    u23.CFrame = CFrame.new(v33)
                    u23.Attachment.ParticleEmitter:Emit(u3:NextInteger(1, 2))
                    table.remove(u6, v31)
                elseif os.clock() - v32.spawnTime > 7 then
                    game.Debris:AddItem(v32.particle, 0.3)
                    game.TweenService:Create(v32.particle, TweenInfo.new(0.3), {
                        ["Transparency"] = 1
                    }):Play()
                    table.remove(u6, v31)
                end
            end
        end
        workspace:BulkMoveTo(v29, v30, Enum.BulkMoveMode.FireCFrameChanged)
        debug.profileend()
    end
end)
if workspace:GetAttribute("RainEvent") then
    task.defer(u22)
else
    u7 = false
end
return v1