-- Full Path: -Grow-a-Garden-\ReplicatedStorage\CmdrClient\Commands\pick-ModuleScript.lua
local v3 = {
    ["Name"] = "pick",
    ["Aliases"] = {},
    ["Description"] = "Picks a value out of a comma-separated list.",
    ["Group"] = "DefaultUtil",
    ["Args"] = {
        {
            ["Type"] = "integer",
            ["Name"] = "Index to pick",
            ["Description"] = "The index of the item you want to pick"
        },
        {
            ["Type"] = "string",
            ["Name"] = "CSV",
            ["Description"] = "The comma-separated list"
        }
    },
    ["Run"] = function(_, p1, p2) --[[Function name: Run, line 19]]
        return p2:split(",")[p1] or ""
    end
}
return v3