-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\PetServices\PetEggRenderer\EggEffects\Template-ModuleScript.lua
local u1 = require(game.ReplicatedStorage.Modules.TweenModel)
require(game.ReplicatedStorage.Modules.SkinService)
local _ = require(game.ReplicatedStorage.Data.PetRegistry).PetList
local v2 = game.ReplicatedStorage:WaitForChild("Assets")
v2:WaitForChild("Models"):WaitForChild("EggModels")
local u3 = v2.SFX
local u4 = v2.VFX
return function(u5) --[[Anonymous function at line 25]]
    --[[
    Upvalues:
        [1] = u3
        [2] = u1
        [3] = u4
    --]]
    task.spawn(function() --[[Anonymous function at line 26]]
        --[[
        Upvalues:
            [1] = u5
            [2] = u3
            [3] = u1
            [4] = u4
        --]]
        local v6 = u5:GetPivot() * CFrame.new(0, 3, 0)
        local v7 = u5:GetScale() * 2
        local u8 = u3.RisingAccent:Clone()
        u8.Parent = workspace
        u8:Play()
        u8.Ended:Once(function() --[[Anonymous function at line 19]]
            --[[
            Upvalues:
                [1] = u8
            --]]
            u8:Destroy()
        end)
        u1(u5, TweenInfo.new(1), {
            ["CFrame"] = v6
        }):Play()
        task.wait(0.1)
        local v9 = u1(u5, TweenInfo.new(2), {
            ["Scale"] = v7
        })
        v9:Play()
        v9.Completed:Connect(function() --[[Anonymous function at line 53]]
            --[[
            Upvalues:
                [1] = u3
                [2] = u5
                [3] = u4
            --]]
            os.clock()
            local u10 = u3.Pop:Clone()
            u10.Parent = workspace
            u10:Play()
            u10.Ended:Once(function() --[[Anonymous function at line 19]]
                --[[
                Upvalues:
                    [1] = u10
                --]]
                u10:Destroy()
            end)
            local v11 = Instance.new("Part")
            v11.Size = u5:GetExtentsSize()
            v11.CFrame = u5:GetPivot()
            v11.Anchored = true
            v11.CanCollide = false
            v11.Transparency = 1
            v11.Parent = workspace.Visuals
            local v12 = u4.EggPop.PopParticles:Clone()
            v12.Color = ColorSequence.new(u5.PrimaryPart.Color)
            v12.Parent = v11
            v12:Emit(20)
            warn((("TryDoHatchAnim | Couldnt get PetType from %* in time!"):format((u5:GetFullName()))))
            u5:Destroy()
            task.wait(2)
            v11:Destroy()
        end)
    end)
end