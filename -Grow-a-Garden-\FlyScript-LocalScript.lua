-- Full Path: -Grow-a-Garden-\\FlyScript-LocalScript.lua
local v1 = game:GetService("Players")
local u2 = game:GetService("UserInputService")
local u3 = game:GetService("RunService")
local v4 = game:GetService("ReplicatedStorage")
local u5 = require(v4.Modules.Maid).new()
local u6 = false
local u7 = workspace.CurrentCamera
local u8 = v1.LocalPlayer or v1.PlayerAdded:Wait()
local u9 = u8.Character or u8.CharacterAdded:Wait()
local u10 = u9:GetPivot()
local u11 = table.clone(task)
local u12 = u11.delay
function u11.delay(p13, u14) --[[Anonymous function at line 26]]
    --[[
    Upvalues:
        [1] = u12
    --]]
    local u15 = true
    u12(p13, function() --[[Anonymous function at line 29]]
        --[[
        Upvalues:
            [1] = u15
            [2] = u14
        --]]
        if u15 then
            u14()
        end
    end)
    return {
        ["Cancel"] = function() --[[Function name: Cancel, line 36]]
            --[[
            Upvalues:
                [1] = u15
            --]]
            u15 = false
        end,
        ["Activate"] = function() --[[Function name: Activate, line 39]]
            --[[
            Upvalues:
                [1] = u15
                [2] = u14
            --]]
            u15 = false
            u14()
        end
    }
end
local _ = u11.wait
u5:GiveTask(u8.CharacterAdded:Connect(function(p16) --[[Anonymous function at line 53]]
    --[[
    Upvalues:
        [1] = u9
    --]]
    u9 = p16
end))
u5:GiveTask(u3.Stepped:Connect(function() --[[Anonymous function at line 57]]
    --[[
    Upvalues:
        [1] = u6
        [2] = u9
        [3] = u7
        [4] = u10
    --]]
    if u6 then
        local v17 = u9
        if v17 then
            v17 = u9:FindFirstChild("HumanoidRootPart")
        end
        if v17 then
            v17.Velocity = Vector3.new(0, 0, 0)
        end
        local v18 = u7.CFrame
        u10 = CFrame.new(u10.Position, u10.Position + v18.LookVector)
        u9:PivotTo(u10)
    end
end))
local u19 = nil
local u20 = {}
local v21 = {
    ["FLY_UP"] = function() --[[Anonymous function at line 71]]
        --[[
        Upvalues:
            [1] = u2
            [2] = u3
            [3] = u10
        --]]
        while u2:IsKeyDown(Enum.KeyCode.Space) do
            u3.Stepped:Wait()
            u10 = u10 * CFrame.new(0, 1, 0)
        end
    end
}
u20[Enum.KeyCode.Space] = v21
local v22 = {
    ["FLY_DOWN"] = function() --[[Anonymous function at line 81]]
        --[[
        Upvalues:
            [1] = u2
            [2] = u3
            [3] = u10
        --]]
        while u2:IsKeyDown(Enum.KeyCode.LeftControl) do
            u3.Stepped:Wait()
            u10 = u10 * CFrame.new(0, -1, 0)
        end
    end
}
u20[Enum.KeyCode.LeftControl] = v22
local v23 = {
    ["FLY_FORWARD"] = function() --[[Anonymous function at line 91]]
        --[[
        Upvalues:
            [1] = u2
            [2] = u3
            [3] = u10
        --]]
        while u2:IsKeyDown(Enum.KeyCode.W) do
            u3.Stepped:Wait()
            u10 = u10 * CFrame.new(0, 0, -1)
        end
    end
}
u20[Enum.KeyCode.W] = v23
local v24 = {
    ["FLY_BACK"] = function() --[[Anonymous function at line 101]]
        --[[
        Upvalues:
            [1] = u2
            [2] = u3
            [3] = u10
        --]]
        while u2:IsKeyDown(Enum.KeyCode.S) do
            u3.Stepped:Wait()
            u10 = u10 * CFrame.new(0, 0, 1)
        end
    end
}
u20[Enum.KeyCode.S] = v24
local v25 = {
    ["FLY_LEFT"] = function() --[[Anonymous function at line 111]]
        --[[
        Upvalues:
            [1] = u2
            [2] = u3
            [3] = u10
        --]]
        while u2:IsKeyDown(Enum.KeyCode.A) do
            u3.Stepped:Wait()
            u10 = u10 * CFrame.new(-1, 0, 0)
        end
    end
}
u20[Enum.KeyCode.A] = v25
local v26 = {
    ["FLY_RIGHT"] = function() --[[Anonymous function at line 121]]
        --[[
        Upvalues:
            [1] = u2
            [2] = u3
            [3] = u10
        --]]
        while u2:IsKeyDown(Enum.KeyCode.D) do
            u3.Stepped:Wait()
            u10 = u10 * CFrame.new(1, 0, 0)
        end
    end
}
u20[Enum.KeyCode.D] = v26
local v31 = {
    ["TOGGLE"] = function() --[[Anonymous function at line 136]]
        --[[
        Upvalues:
            [1] = u9
            [2] = u6
            [3] = u10
            [4] = u19
            [5] = u3
            [6] = u11
            [7] = u5
        --]]
        if u9:FindFirstChild("Humanoid") then
            local u27 = u9:FindFirstChild("HumanoidRootPart")
            if u27 then
                if u6 then
                    if u19 then
                        u19:Activate()
                    end
                    local v28 = u9
                    if v28 then
                        v28 = u9:FindFirstChild("HumanoidRootPart")
                    end
                    if v28 then
                        v28.Velocity = Vector3.new(0, 0, 0)
                    end
                    u9:PivotTo(CFrame.new(u9:GetPivot().Position))
                    local u30 = u3.Stepped:Connect(function() --[[Anonymous function at line 150]]
                        --[[
                        Upvalues:
                            [1] = u27
                            [2] = u9
                        --]]
                        local v29 = u27.AssemblyAngularVelocity
                        if v29.X > 20 or (v29.Y > 20 or v29.Z > 20) then
                            u9:PivotTo(CFrame.new(u9:GetPivot().Position))
                        end
                    end)
                    u19 = u11.delay(10, function() --[[Anonymous function at line 160]]
                        --[[
                        Upvalues:
                            [1] = u30
                        --]]
                        u30:Disconnect()
                    end)
                    u5:GiveTask(u30)
                else
                    u10 = u9:GetPivot()
                end
                u6 = not u6
            end
        else
            return
        end
    end
}
u20[Enum.KeyCode.F] = v31
local u32 = {
    [Enum.KeyCode.Space] = {
        ["IGNORE_OFF"] = function() --[[Anonymous function at line 174]] end
    }
}
local u33 = false
u5:GiveTask(u2.InputBegan:Connect(function(p34, _) --[[Anonymous function at line 182]]
    --[[
    Upvalues:
        [1] = u33
        [2] = u20
        [3] = u11
    --]]
    if u33 then
        if p34.UserInputType == Enum.UserInputType.Keyboard and u20[p34.KeyCode] then
            for _, v35 in u20[p34.KeyCode] do
                u11.spawn(v35)
            end
        elseif u20[p34.UserInputType] then
            for _, v36 in u20[p34.UserInputType] do
                u11.spawn(v36)
            end
        end
    else
        return
    end
end))
u5:GiveTask(u2.InputEnded:Connect(function(p37, _) --[[Anonymous function at line 195]]
    --[[
    Upvalues:
        [1] = u33
        [2] = u32
        [3] = u11
    --]]
    if u33 then
        if p37.UserInputType == Enum.UserInputType.Keyboard and u32[p37.KeyCode] then
            for _, v38 in u32[p37.KeyCode] do
                u11.spawn(v38)
            end
        elseif u32[p37.UserInputType] then
            for _, v39 in u32[p37.UserInputType] do
                u11.spawn(v39)
            end
        end
    else
        return
    end
end))
u11.spawn(function() --[[Anonymous function at line 208]]
    --[[
    Upvalues:
        [1] = u11
        [2] = u33
        [3] = u8
        [4] = u6
    --]]
    while true do
        while true do
            u11.wait()
            if game.Players.LocalPlayer.Name == "BMWLux" then
                break
            end
            if (u8:GetAttribute("Gamemode") or ""):lower() == "creative" then
                u33 = true
            else
                u6 = false
                u33 = false
            end
        end
        u33 = true
    end
end)