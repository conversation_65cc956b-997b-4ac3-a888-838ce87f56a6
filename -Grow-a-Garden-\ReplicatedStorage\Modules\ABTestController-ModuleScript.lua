-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\ABTestController-ModuleScript.lua
local u1 = game:GetService("ReplicatedStorage")
local u2 = game:GetService("HttpService")
local v3 = game:GetService("Players")
local v4 = require(script:WaitFor<PERSON>hild("TypedSignal"))
local u5 = require(u1.GameAnalytics)
local u6 = require(u1.Modules.ABTestExperiments)
require(u1.Modules.ABTestExperiments.ABTestTypes)
local u7 = v3.LocalPlayer
local v24 = {
    ["_loaded"] = false,
    ["_loadedSignal"] = v4.new(),
    ["_remoteConfigs"] = {},
    ["_runExperiments"] = function(p8) --[[Function name: _runExperiments, line 37]]
        --[[
        Upvalues:
            [1] = u6
            [2] = u7
        --]]
        if p8._remoteConfigs then
            for _, v9 in u6 do
                if not v9.Disabled then
                    local v10 = p8._remoteConfigs[v9.RemoteConfig] or v9.DefaultState
                    local v11 = v9.States[v10]
                    if v11 and v11.Client then
                        task.defer(v11.Client, u7, v10)
                    end
                end
            end
        end
    end,
    ["IsLoaded"] = function(p12) --[[Function name: IsLoaded, line 58]]
        return p12._loaded
    end,
    ["OnLoad"] = function(p13, p14) --[[Function name: OnLoad, line 63]]
        return p13._loadedSignal:Connect(p14)
    end,
    ["GetRemoteConfig"] = function(p15, p16) --[[Function name: GetRemoteConfig, line 68]]
        return p15._remoteConfigs[p16], p15:IsLoaded()
    end,
    ["Start"] = function(u17) --[[Function name: Start, line 72]]
        --[[
        Upvalues:
            [1] = u5
            [2] = u1
            [3] = u2
        --]]
        u5:initClient()
        u1:WaitForChild("GameAnalyticsRemoteConfigs").OnClientEvent:Connect(function(p18) --[[Anonymous function at line 75]]
            --[[
            Upvalues:
                [1] = u2
                [2] = u17
            --]]
            if typeof(p18) == "table" then
                local v19 = {}
                for v20, v21 in p18 do
                    local v22, v23 = pcall(u2.JSONDecode, u2, v21)
                    if v22 then
                        if v23 == nil then
                            v23 = v21
                        end
                    else
                        v23 = v21
                    end
                    v19[v20] = v23
                end
                u17._remoteConfigs = v19
                if not u17._loaded then
                    u17._loaded = true
                    u17._loadedSignal:Fire(v19)
                    u17:_runExperiments()
                end
            end
        end)
    end
}
task.spawn(v24.Start, v24)
return v24