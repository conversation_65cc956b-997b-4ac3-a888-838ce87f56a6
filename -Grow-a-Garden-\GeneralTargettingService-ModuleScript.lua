-- Full Path: -Grow-a-Garden-\\GeneralTargettingService-ModuleScript.lua
local v1 = game:GetService("UserInputService")
local v2 = game:GetService("ReplicatedStorage")
local v3 = game:GetService("RunService")
local u4 = require(v2.Modules.GetMouseToWorld)
local u5 = script:Wait<PERSON><PERSON><PERSON><PERSON><PERSON>("Arrows")
local u6 = u5.Attachment1
local u7 = u5.Attachment2
local u8 = {
    ["Handler"] = nil,
    ["CurrentTarget"] = nil,
    ["LastHover"] = nil
}
local u12 = {
    ["Plant"] = function(p9) --[[Anonymous function at line 28]]
        local v10 = p9:FindFirstAncestorWhichIsA("Model")
        if v10 then
            local v11 = v10:FindFirstChild("Grow")
            if v11 then
                if v11:FindFirstChild("Age") then
                    return v10
                end
            end
        else
            return
        end
    end
}
local u13 = Instance.new("Highlight")
u13.FillTransparency = 1
local u14 = false
function u8.CreateTargetingHandler(_, p15) --[[Anonymous function at line 68]]
    --[[
    Upvalues:
        [1] = u14
        [2] = u8
        [3] = u13
        [4] = u5
    --]]
    u14 = true
    u8.LastHover = nil
    u8.CurrentTarget = nil
    u8.Handler = nil
    u13.Parent = nil
    u5.Parent = script
    u8.Handler = p15
    p15.OnHover = p15.OnHover or function() --[[Anonymous function at line 73]] end
    p15.OnUnhover = p15.OnUnhover or function() --[[Anonymous function at line 74]] end
    p15.OnCancelled = p15.OnCancelled or function() --[[Anonymous function at line 75]] end
    p15.OnConfirm = p15.OnConfirm or function() --[[Anonymous function at line 76]] end
    task.delay(0.5, function() --[[Anonymous function at line 78]]
        --[[
        Upvalues:
            [1] = u14
        --]]
        u14 = false
    end)
end
v3.RenderStepped:Connect(function() --[[Anonymous function at line 82]]
    --[[
    Upvalues:
        [1] = u8
        [2] = u12
        [3] = u4
        [4] = u5
        [5] = u13
        [6] = u6
        [7] = u7
    --]]
    local v16 = u8.Handler
    if v16 then
        local v17 = u12[v16.TargetType]
        if v17 then
            local v18 = u4(RaycastParams.new(), 100)
            local v19
            if v18 then
                v19 = v18.Instance
            else
                v19 = v18
            end
            local v20
            if v19 then
                v20 = v17(v19)
            else
                v20 = v19
            end
            u8.CurrentTarget = v20
            u5.Parent = v19 and workspace or script
            u13.Adornee = v20
            u13.Parent = v20
            if v18 then
                u6.WorldCFrame = v16.Targeter:GetPivot()
                u7.WorldCFrame = v20 and v20:GetPivot() or CFrame.new(v18.Position)
            end
            local v21 = u8.LastHover
            if v21 ~= v20 then
                v16.OnUnhover(v21)
                v16.OnHover(v20)
            end
        else
            return
        end
    else
        return
    end
end)
local u22 = {
    [Enum.UserInputType.MouseButton1] = {}
}
local u23 = {}
local v25 = { function(_) --[[Anonymous function at line 115]]
        --[[
        Upvalues:
            [1] = u14
            [2] = u8
            [3] = u13
            [4] = u5
        --]]
        if u14 then
            return
        else
            local v24 = u8.Handler
            if v24 then
                v24.OnUnhover(u8.LastHover)
                if u8.CurrentTarget then
                    v24.OnConfirm(u8.CurrentTarget)
                    u8.LastHover = nil
                    u8.CurrentTarget = nil
                    u8.Handler = nil
                    u13.Parent = nil
                    u5.Parent = script
                else
                    v24.OnCancelled()
                    u8.LastHover = nil
                    u8.CurrentTarget = nil
                    u8.Handler = nil
                    u13.Parent = nil
                    u5.Parent = script
                end
            else
                return
            end
        end
    end }
u23[Enum.UserInputType.MouseButton1] = v25
v1.InputBegan:Connect(function(u26, _) --[[Anonymous function at line 121]]
    --[[
    Upvalues:
        [1] = u22
    --]]
    local v27 = u22[u26.KeyCode] or u22[u26.UserInputType]
    if v27 then
        for _, u28 in v27 do
            task.spawn(function() --[[Anonymous function at line 125]]
                --[[
                Upvalues:
                    [1] = u28
                    [2] = u26
                --]]
                u28(u26)
            end)
        end
    end
end)
v1.InputEnded:Connect(function(u29, _) --[[Anonymous function at line 131]]
    --[[
    Upvalues:
        [1] = u23
    --]]
    local v30 = u23[u29.KeyCode] or u23[u29.UserInputType]
    if v30 then
        for _, u31 in v30 do
            task.spawn(function() --[[Anonymous function at line 135]]
                --[[
                Upvalues:
                    [1] = u31
                    [2] = u29
                --]]
                u31(u29)
            end)
        end
    end
end)
v1.TouchTap:Connect(function() --[[Anonymous function at line 141]]
    --[[
    Upvalues:
        [1] = u14
        [2] = u8
        [3] = u13
        [4] = u5
    --]]
    if u14 then
        return
    else
        local v32 = u8.Handler
        if v32 then
            v32.OnUnhover(u8.LastHover)
            if u8.CurrentTarget then
                v32.OnConfirm(u8.CurrentTarget)
                u8.LastHover = nil
                u8.CurrentTarget = nil
                u8.Handler = nil
                u13.Parent = nil
                u5.Parent = script
            else
                v32.OnCancelled()
                u8.LastHover = nil
                u8.CurrentTarget = nil
                u8.Handler = nil
                u13.Parent = nil
                u5.Parent = script
            end
        else
            return
        end
    end
end)
return u8