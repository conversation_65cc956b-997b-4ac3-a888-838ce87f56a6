-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Data\PetRegistry\PassiveRegistry-ModuleScript.lua
local v1 = {}
local v2 = {
    ["Description"] = "All plants within <Range> studs grow <Multiplier>x faster!"
}
local v3 = {
    ["Range"] = {
        ["Base"] = 10,
        ["Scale"] = 0.1
    },
    ["Multiplier"] = {
        ["Base"] = 1.25,
        ["Scale"] = 0.0125
    }
}
v2.States = v3
v1["Milk of the Land"] = v2
local v4 = {
    ["Description"] = "<Chance>% chance harvested crops duplicate! Rarer crops have lower chance to duplicate."
}
local v5 = {
    ["Chance"] = {
        ["Base"] = 10,
        ["Scale"] = 0.1
    }
}
v4.States = v5
v1["For the Blue Colony"] = v4
local v6 = {
    ["Description"] = "<Chance>% chance harvested crops duplicate! Rarer crops have lower chance to duplicate."
}
local v7 = {
    ["Chance"] = {
        ["Base"] = 5,
        ["Scale"] = 0.1
    }
}
v6.States = v7
v1["For the Red Colony"] = v6
local v8 = {
    ["Description"] = "<Chance>% extra chance for Candy type crops to duplicate!"
}
local v9 = {
    ["Chance"] = {
        ["Base"] = 5,
        ["Scale"] = 0.15
    }
}
v8.States = v9
v1["Candy Harvester"] = v8
local v10 = {
    ["Description"] = "<Chance>% extra chance harvested plants drop seeds. Rarer plants have lower chance to duplicate."
}
local v11 = {
    ["Chance"] = {
        ["Base"] = 5,
        ["Scale"] = 0.05
    }
}
v10.States = v11
v1["Slow and Steady"] = v10
local v12 = {
    ["Description"] = "Every <Cooldown>s, <Chance>% chance to dig up a random seed!"
}
local v13 = {
    ["Cooldown"] = {
        ["Min"] = 5,
        ["Base"] = 60,
        ["Scale"] = -0.3
    },
    ["Chance"] = {
        ["Base"] = 5,
        ["Scale"] = 0.05
    }
}
v12.States = v13
v1["Digging Buddy"] = v12
local v14 = {
    ["Description"] = "Every <Cooldown>s, <Chance>% chance to dig up a random seed!"
}
local v15 = {
    ["Cooldown"] = {
        ["Min"] = 5,
        ["Base"] = 60,
        ["Scale"] = -0.5
    },
    ["Chance"] = {
        ["Base"] = 10,
        ["Scale"] = 0.1
    }
}
v14.States = v15
v1["Digging Friend"] = v14
local v16 = {
    ["Description"] = "Every <Cooldown>s, eats a carrot for <Multiplier>x value bonus!"
}
local v17 = {
    ["Cooldown"] = {
        ["Min"] = 5,
        ["Base"] = 45,
        ["Scale"] = -0.225
    },
    ["Multiplier"] = {
        ["Base"] = 1.5,
        ["Scale"] = 0.015
    }
}
v16.States = v17
v1["Carrot Chomper"] = v16
local v18 = {
    ["Description"] = "Every <Cooldown>s, eats a carrot for <Multiplier>x value bonus!"
}
local v19 = {
    ["Cooldown"] = {
        ["Min"] = 5,
        ["Base"] = 40,
        ["Scale"] = -0.2
    },
    ["Multiplier"] = {
        ["Base"] = 3,
        ["Scale"] = 0.03
    }
}
v18.States = v19
v1["Carrot Devourer"] = v18
local v20 = {
    ["Description"] = "Every <Cooldown>s, naps for <Duration>s. New fruit within <Range> studs will be <Multiplier>x larger!"
}
local v21 = {
    ["Cooldown"] = {
        ["Min"] = 5,
        ["Base"] = 80,
        ["Scale"] = -0.4
    },
    ["Duration"] = {
        ["Base"] = 10,
        ["Scale"] = 0.1
    },
    ["Range"] = {
        ["Base"] = 10,
        ["Scale"] = 0.1
    },
    ["Multiplier"] = {
        ["Base"] = 1.25,
        ["Scale"] = 0.0025
    }
}
v20.States = v21
v1["Cat Nap"] = v20
local v22 = {
    ["Description"] = "Every <Cooldown>s, naps for <Duration>s. New fruit within <Range> studs will be <Multiplier>x larger!"
}
local v23 = {
    ["Cooldown"] = {
        ["Min"] = 5,
        ["Base"] = 90,
        ["Scale"] = -0.4
    },
    ["Duration"] = {
        ["Base"] = 15,
        ["Scale"] = 0.15
    },
    ["Range"] = {
        ["Base"] = 15,
        ["Scale"] = 0.15
    },
    ["Multiplier"] = {
        ["Base"] = 1.5,
        ["Scale"] = 0.01
    }
}
v22.States = v23
v1["Lazy Nap"] = v22
local v24 = {
    ["Description"] = "<Chance>% chance berry fruit stays after harvest!"
}
local v25 = {
    ["Chance"] = {
        ["Max"] = 25,
        ["Base"] = 2.5,
        ["Scale"] = 0.025
    }
}
v24.States = v25
v1.Forester = v24
local v26 = {
    ["Description"] = "<Chance>% chance berry fruit stays after harvest!"
}
local v27 = {
    ["Chance"] = {
        ["Max"] = 30,
        ["Base"] = 5,
        ["Scale"] = 0.05
    }
}
v26.States = v27
v1["Spotted Forester"] = v26
local v28 = {
    ["Description"] = "Every <Cooldown>s, sprays water on a nearby plant."
}
local v29 = {
    ["Cooldown"] = {
        ["Min"] = 5,
        ["Base"] = 30,
        ["Scale"] = -0.15
    }
}
v28.States = v29
v1["Water Spray"] = v28
local v30 = {
    ["Description"] = "Increases egg hatch speed by <Multiplier>%!"
}
local v31 = {
    ["Multiplier"] = {
        ["Base"] = 10,
        ["Scale"] = 0.1
    }
}
v30.States = v31
v1.Eggcelerator = v30
local v32 = {
    ["Description"] = "Increases egg hatch speed by <Multiplier>%!"
}
local v33 = {
    ["Multiplier"] = {
        ["Base"] = 20,
        ["Scale"] = 0.2
    }
}
v32.States = v33
v1["Better Eggcelerator"] = v32
local v34 = {
    ["Description"] = "Every <Cooldown>s, emits an aura for <Duration>s granting <Multiplier>x chance for new fruit to grow as variants within <Range> studs!"
}
local v35 = {
    ["Cooldown"] = {
        ["Min"] = 5,
        ["Base"] = 120,
        ["Scale"] = -0.6
    },
    ["Duration"] = {
        ["Base"] = 15,
        ["Scale"] = 0.15
    },
    ["Range"] = {
        ["Base"] = 15,
        ["Scale"] = 0.15
    },
    ["Multiplier"] = {
        ["Base"] = 2,
        ["Scale"] = 0.01
    }
}
v34.States = v35
v1["Fertilizer Frenzy"] = v34
local v36 = {
    ["Description"] = "All Sprinklers last <Multiplier>% longer!"
}
local v37 = {
    ["Range"] = {
        ["Base"] = 35,
        ["Scale"] = 0.5
    },
    ["Multiplier"] = {
        ["Base"] = 20,
        ["Scale"] = 0.2
    }
}
v36.States = v37
v1["Turtle Tinkerer"] = v36
local v38 = {
    ["Description"] = "<Chance>% chance to refund fruit back to your inventory. Rarer plants have lower chance to refund!"
}
local v39 = {
    ["Chance"] = {
        ["Base"] = 2.5,
        ["Scale"] = 0.025
    }
}
v38.States = v39
v1["Cheeky Refund"] = v38
local v40 = {
    ["Description"] = "<Chance>% chance to refund fruit back to your inventory. Rarer plants have lower chance to refund!"
}
local v41 = {
    ["Chance"] = {
        ["Base"] = 7.5,
        ["Scale"] = 0.075
    }
}
v40.States = v41
v1["Premium Cheeky Refund"] = v40
local v42 = {
    ["Description"] = " "
}
local v43 = {
    ["Cooldown"] = {
        ["Min"] = 8,
        ["Base"] = 8,
        ["Scale"] = 0
    }
}
v42.States = v43
v1["Movement Variation"] = v42
local v44 = {
    ["Description"] = "Every <Cooldown>m, turns one random fruit gold!"
}
local v45 = {
    ["Cooldown"] = {
        ["Min"] = 5,
        ["Base"] = 300,
        ["Scale"] = -3,
        ["Formatter"] = "ColonTime"
    }
}
v44.States = v45
v1.Transmutation = v44
local v46 = {
    ["Description"] = "Every <Cooldown>s, <Chance>% chance a nearby fruit becomes Chilled or Frozen!"
}
local v47 = {
    ["Cooldown"] = {
        ["Min"] = 10,
        ["Base"] = 80,
        ["Scale"] = -0.4
    },
    ["Chance"] = {
        ["Base"] = 10,
        ["Scale"] = 0.1
    }
}
v46.States = v47
v1["Polar Express"] = v46
local v48 = {
    ["Description"] = "Every <Cooldown>m, eats bamboo for <Multiplier>x value bonus!"
}
local v49 = {
    ["Cooldown"] = {
        ["Min"] = 60,
        ["Base"] = 180,
        ["Scale"] = -0.9,
        ["Formatter"] = "ColonTime"
    },
    ["Multiplier"] = {
        ["Base"] = 1.5,
        ["Scale"] = 0.015
    }
}
v48.States = v49
v1.Bamboozle = v48
local v50 = {
    ["Description"] = "Passive: All leafy plants grow <Multiplier>x faster!"
}
local v51 = {
    ["Range"] = {
        ["Base"] = 25,
        ["Scale"] = 0
    },
    ["Multiplier"] = {
        ["Base"] = 1.45,
        ["Scale"] = 0.15
    }
}
v50.States = v51
v1["Leaf Lover"] = v50
local v52 = {
    ["Description"] = "Every <Cooldown>s, prays for <Duration>s granting <Multiplier>x variant chance within <Range> studs!"
}
local v53 = {
    ["Cooldown"] = {
        ["Min"] = 5,
        ["Base"] = 80,
        ["Scale"] = -0.3
    },
    ["Duration"] = {
        ["Base"] = 10,
        ["Scale"] = 0.1
    },
    ["Range"] = {
        ["Base"] = 10,
        ["Scale"] = 0.1
    },
    ["Multiplier"] = {
        ["Base"] = 1.5,
        ["Scale"] = 0.005
    }
}
v52.States = v53
v1["Zen Zone"] = v52
local v54 = {
    ["Description"] = "Grants prickly fruit a <Multiplier>x size bonus!"
}
local v55 = {
    ["Multiplier"] = {
        ["Base"] = 1.5,
        ["Scale"] = 0.015
    },
    ["Range"] = {
        ["Base"] = 25,
        ["Scale"] = 0
    }
}
v54.States = v55
v1["Prickly Lover"] = v54
local v56 = {
    ["Description"] = "Every <Cooldown>s, goes to the egg with the highest hatch time, and reduces its hatch time by <Amount>s!"
}
local v57 = {
    ["Cooldown"] = {
        ["Min"] = 10,
        ["Base"] = 60,
        ["Scale"] = -0.3
    },
    ["Amount"] = {
        ["Base"] = 25,
        ["Scale"] = 0.25
    }
}
v56.States = v57
v1["Nocturnal Nursery"] = v56
local v58 = {
    ["Description"] = "Every <Cooldown>s, digs down underground to find treasure. Can dig up gear or sheckles!"
}
local v59 = {
    ["Cooldown"] = {
        ["Min"] = 5,
        ["Base"] = 80,
        ["Scale"] = -0.4
    }
}
v58.States = v59
v1["Treasure Hunter"] = v58
local v60 = {
    ["Description"] = "Every <Cooldown>m, croaks and a random nearby plant will advance growth by 24 hours!"
}
local v61 = {
    ["Cooldown"] = {
        ["Min"] = 200,
        ["Base"] = 904.5,
        ["Scale"] = -4.5,
        ["Formatter"] = "ColonTime"
    }
}
v60.States = v61
v1.Croak = v60
local v62 = {
    ["Description"] = "Every <Cooldown>m, croaks and a random nearby plant will advance growth by 24 hours!"
}
local v63 = {
    ["Cooldown"] = {
        ["Min"] = 200,
        ["Base"] = 603,
        ["Scale"] = -3,
        ["Formatter"] = "ColonTime"
    }
}
v62.States = v63
v1["Echo Croak"] = v62
local v64 = {
    ["Description"] = "All active pets gain an additional <Chance> XP/s!"
}
local v65 = {
    ["Chance"] = {
        ["Base"] = 0.2,
        ["Scale"] = 0.01
    }
}
v64.States = v65
v1["Prince of the Night"] = v64
local v66 = {
    ["Description"] = "All active pets gain an additional <Chance> XP/s!"
}
local v67 = {
    ["Chance"] = {
        ["Base"] = 0.2,
        ["Scale"] = 0.01
    }
}
v66.States = v67
v1["King of the Night"] = v66
local v68 = {
    ["Description"] = "Every <Cooldown>m, goes to another player\'s plot and steals (duplicate) a random crop and gives it to you!"
}
local v69 = {
    ["Cooldown"] = {
        ["Min"] = 5,
        ["Base"] = 900,
        ["Scale"] = -4.5,
        ["Formatter"] = "ColonTime"
    }
}
v68.States = v69
v1.Rascal = v68
local v70 = {
    ["Description"] = "Every <Cooldown>m, gains additional <Amount> bonus experience!"
}
local v71 = {
    ["Cooldown"] = {
        ["Min"] = 10,
        ["Base"] = 603,
        ["Scale"] = -3,
        ["Formatter"] = "ColonTime"
    },
    ["Amount"] = {
        ["Base"] = 500,
        ["Scale"] = 8
    }
}
v70.States = v71
v1["Whisker Wisdom"] = v70
local v72 = {
    ["Description"] = "Grants additional <Chance>% increase to player movement speed!"
}
local v73 = {
    ["Chance"] = {
        ["Base"] = 10,
        ["Scale"] = 0.1
    }
}
v72.States = v73
v1.Scamper = v72
local v74 = {
    ["Description"] = "<Chance>% chance to not consume the seed when planting. Rarer seeds have lower chance to recover!"
}
local v75 = {
    ["Chance"] = {
        ["Base"] = 2.5,
        ["Scale"] = 0.025
    }
}
v74.States = v75
v1["Seed Stash"] = v74
local v76 = {
    ["Description"] = "Every <Cooldown>m, gains additional <Amount> bonus experience!"
}
local v77 = {
    ["Cooldown"] = {
        ["Min"] = 10,
        ["Base"] = 503,
        ["Scale"] = -3,
        ["Formatter"] = "ColonTime"
    },
    ["Amount"] = {
        ["Base"] = 750,
        ["Scale"] = 11
    }
}
v76.States = v77
v1["Whiskier Wisdom"] = v76
local v78 = {
    ["Description"] = "Grants additional <Chance>% increase to player jump height!"
}
local v79 = {
    ["Chance"] = {
        ["Base"] = 10,
        ["Scale"] = 0.1
    }
}
v78.States = v79
v1["Cheese Hop"] = v78
local v80 = {
    ["Description"] = "<Chance>% extra chance for Fruit type crops to duplicate!"
}
local v81 = {
    ["Chance"] = {
        ["Base"] = 5,
        ["Scale"] = 0.1
    }
}
v80.States = v81
v1["Fruit Harvester"] = v80
local v82 = {
    ["Description"] = "Every <Cooldown>m, goes to another player\'s random crop tries to get a seed from it and gives it to you. Rarer seeds have less chance to succesfully steal!"
}
local v83 = {
    ["Cooldown"] = {
        ["Min"] = 10,
        ["Base"] = 543,
        ["Scale"] = -3,
        ["Formatter"] = "ColonTime"
    }
}
v82.States = v83
v1.Scoundrel = v82
local v84 = {
    ["Description"] = "Every <Cooldown>m, <Chance>% chance a nearby fruit becomes Zombified!"
}
local v85 = {
    ["Cooldown"] = {
        ["Min"] = 10,
        ["Base"] = 1800,
        ["Scale"] = -10,
        ["Formatter"] = "ColonTime"
    },
    ["Chance"] = {
        ["Base"] = 20,
        ["Scale"] = 0.2
    }
}
v84.States = v85
v1.Zombify = v84
local v86 = {
    ["Description"] = "Every <Cooldown>s, goes to the egg with the highest hatch time, and reduces its hatch time by <Amount>s!"
}
local v87 = {
    ["Cooldown"] = {
        ["Min"] = 10,
        ["Base"] = 60,
        ["Scale"] = -0.3
    },
    ["Amount"] = {
        ["Base"] = 45,
        ["Scale"] = 0.45
    }
}
v86.States = v87
v1["Crimson Cradle"] = v86
local v88 = {
    ["Description"] = "All active pets gain an additional <Chance> XP/s!"
}
local v89 = {
    ["Chance"] = {
        ["Base"] = 0.5,
        ["Scale"] = 0.03
    }
}
v88.States = v89
v1["Monarch of Midnight"] = v88
local v90 = {
    ["Description"] = "Grants all prickly fruit a <Multiplier>x size bonus!"
}
local v91 = {
    ["Multiplier"] = {
        ["Base"] = 2,
        ["Scale"] = 0.02
    },
    ["Range"] = {
        ["Base"] = 25,
        ["Scale"] = 0
    }
}
v90.States = v91
v1["Sanguine Spike"] = v90
local v92 = {
    ["Description"] = "Grants all prickly fruit a <Multiplier>x variant bonus!"
}
local v93 = {
    ["Multiplier"] = {
        ["Base"] = 1.15,
        ["Scale"] = 0.0115
    },
    ["Range"] = {
        ["Base"] = 25,
        ["Scale"] = 0
    }
}
v92.States = v93
v1["Prickly Blessing"] = v92
local v94 = {
    ["Description"] = "Every <Cooldown>s, naps for <Duration>s. New fruit within <Range> studs will be <Multiplier>x larger!"
}
local v95 = {
    ["Cooldown"] = {
        ["Min"] = 5,
        ["Base"] = 60,
        ["Scale"] = -0.3
    },
    ["Duration"] = {
        ["Base"] = 20,
        ["Scale"] = 0.2
    },
    ["Range"] = {
        ["Base"] = 20,
        ["Scale"] = 0.2
    },
    ["Multiplier"] = {
        ["Base"] = 1.5,
        ["Scale"] = 0.01
    }
}
v94.States = v95
v1["Moon Nap"] = v94
local v96 = {
    ["Description"] = "<Chance>% chance Moon type fruit stays after harvest!"
}
local v97 = {
    ["Chance"] = {
        ["Max"] = 25,
        ["Base"] = 6,
        ["Scale"] = 0.06
    }
}
v96.States = v97
v1["Moon Harvest"] = v96
local v98 = {
    ["Description"] = "Every <Cooldown>s, <Chance>% chance nearby fruit becomes Burnt!"
}
local v99 = {
    ["Cooldown"] = {
        ["Min"] = 5,
        ["Base"] = 80,
        ["Scale"] = -0.4
    },
    ["Chance"] = {
        ["Base"] = 20,
        ["Scale"] = 0.2
    }
}
v98.States = v99
v1["Scorched Soil"] = v98
local v100 = {
    ["Description"] = "Every <Cooldown>s, <Chance>% chance a nearby fruit becomes Shocked!"
}
local v101 = {
    ["Cooldown"] = {
        ["Min"] = 10,
        ["Base"] = 80,
        ["Scale"] = -0.4
    },
    ["Chance"] = {
        ["Base"] = 3,
        ["Scale"] = 0.03
    }
}
v100.States = v101
v1["Lightning Bug"] = v100
return v1