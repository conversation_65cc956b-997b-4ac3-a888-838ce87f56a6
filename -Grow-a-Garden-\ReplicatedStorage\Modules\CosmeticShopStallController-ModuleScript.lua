-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\CosmeticShopStallController-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
game:GetService("TweenService")
game:GetService("ServerScriptService")
local _ = game.Players.LocalPlayer.PlayerGui
require(v1.Modules.DataService)
require(v1.Modules.WaitForDescendant)
require(v1.Modules.GiftController)
require(v1.Modules.MarketController)
require(v1.Modules.GuiController)
require(v1.Modules.NumberUtil)
require(v1.Data.DecimalNumberFormat)
require(v1.Modules.CommaFormatNumber)
require(v1.Item_Module)
require(v1.Comma_Module)
return {}