-- Full Path: -Grow-a-Garden-\ReplicatedStorage\CmdrClient\CmdrInterface\AutoComplete-ModuleScript.lua
local u1 = game:GetService("Players").LocalPlayer
return function(p2) --[[Anonymous function at line 5]]
    --[[
    Upvalues:
        [1] = u1
    --]]
    local u3 = {
        ["Items"] = {},
        ["ItemOptions"] = {},
        ["SelectedItem"] = 0
    }
    local u4 = p2.Util
    local u5 = u1:WaitForChild("PlayerGui"):WaitForChild("Cmdr"):WaitForChild("Autocomplete")
    local u6 = u5:WaitForChild("TextButton")
    local u7 = u5:WaitFor<PERSON>hild("Title")
    local u8 = u5:WaitForChild("Description")
    local u9 = u5.Parent:WaitForChild("Frame"):WaitForChild("Entry")
    u6.Parent = nil
    local u10 = u5.ScrollBarThickness
    local function u15(p11, p12, p13, p14) --[[Anonymous function at line 24]]
        --[[
        Upvalues:
            [1] = u4
        --]]
        p11.Visible = p13 ~= nil
        p12.Text = p13 or ""
        if p14 then
            p12.Size = UDim2.new(0, u4.GetTextSize(p13 or "", p12, Vector2.new(1000, 1000), 1, 0).X, p11.Size.Y.Scale, p11.Size.Y.Offset)
        end
    end
    local function u23() --[[Anonymous function at line 38]]
        --[[
        Upvalues:
            [1] = u5
            [2] = u7
        --]]
        local v16 = u5
        local v17 = UDim2.new
        local v18 = u7.Field.TextBounds.X + u7.Field.Type.TextBounds.X
        local v19 = u5.Size.X.Offset
        local v20 = math.max(v18, v19)
        local v21 = u5.UIListLayout.AbsoluteContentSize.Y
        local v22 = u5.Parent.AbsoluteSize.Y - u5.AbsolutePosition.Y - 10
        v16.Size = v17(0, v20, 0, (math.min(v21, v22)))
    end
    local function u29(p24) --[[Anonymous function at line 48]]
        --[[
        Upvalues:
            [1] = u15
            [2] = u7
            [3] = u8
            [4] = u5
            [5] = u23
            [6] = u10
        --]]
        u15(u7, u7.Field, p24.name, true)
        local v25 = u15
        local v26 = u7.Field.Type
        local v27 = u7.Field.Type
        local v28 = p24.type
        if v28 then
            v28 = ": " .. p24.type:sub(1, 1):upper() .. p24.type:sub(2)
        end
        v25(v26, v27, v28)
        u15(u8, u8.Label, p24.description)
        u8.Label.TextColor3 = p24.invalid and Color3.fromRGB(255, 73, 73) or Color3.fromRGB(255, 255, 255)
        u8.Size = UDim2.new(1, 0, 0, 40)
        while not u8.Label.TextFits do
            u8.Size = u8.Size + UDim2.new(0, 0, 0, 2)
            if u8.Size.Y.Offset > 500 then
                break
            end
        end
        task.wait()
        u5.UIListLayout:ApplyLayout()
        u23()
        u5.ScrollBarThickness = u10
    end
    function u3.Show(p30, p31, p32) --[[Anonymous function at line 88]]
        --[[
        Upvalues:
            [1] = u5
            [2] = u6
            [3] = u9
            [4] = u4
            [5] = u29
        --]]
        local v33 = p32 or {}
        for _, v34 in pairs(p30.Items) do
            if v34.gui then
                v34.gui:Destroy()
            end
        end
        p30.SelectedItem = 1
        p30.Items = p31
        p30.Prefix = v33.prefix or ""
        p30.LastItem = v33.isLast or false
        p30.Command = v33.command
        p30.Arg = v33.arg
        p30.NumArgs = v33.numArgs
        p30.IsPartial = v33.isPartial
        u5.ScrollBarThickness = 0
        local v35 = 200
        for v36, v37 in pairs(p30.Items) do
            local v38 = v37[1]
            local v39 = v37[2]
            local v40 = u6:Clone()
            v40.Name = v38 .. v39
            v40.BackgroundTransparency = v36 == p30.SelectedItem and 0.5 or 1
            local v41, v42 = string.find(v39:lower(), v38:lower(), 1, true)
            v40.Typed.Text = string.rep(" ", v41 - 1) .. v38
            local v43 = v40.Suggest
            local v44 = v41 - 1
            local v45 = string.sub(v39, 0, v44)
            local v46 = string.rep(" ", #v38)
            local v47 = v42 + 1
            v43.Text = v45 .. v46 .. string.sub(v39, v47)
            v40.Parent = u5
            v40.LayoutOrder = v36
            local v48 = v40.Typed.TextBounds.X
            local v49 = v40.Suggest.TextBounds.X
            local v50 = math.max(v48, v49) + 20
            if v35 < v50 then
                v35 = v50
            end
            v37.gui = v40
        end
        u5.UIListLayout:ApplyLayout()
        local v51 = u9.TextBox.Text
        local v52 = u4.SplitString(v51)
        if v51:sub(#v51, #v51) == " " and not v33.at then
            v52[#v52 + 1] = "e"
        end
        table.remove(v52, #v52)
        local v53 = (v33.at and v33.at or #table.concat(v52, " ") + 1) * 7
        u5.Position = UDim2.new(0, u9.TextBox.AbsolutePosition.X - 10 + v53, 0, u9.TextBox.AbsolutePosition.Y + 30)
        u5.Size = UDim2.new(0, v35, 0, u5.UIListLayout.AbsoluteContentSize.Y)
        u5.Visible = true
        local v54 = u29
        if p30.Items[1] then
            v33 = p30.Items[1].options or v33
        end
        v54(v33)
    end
    function u3.GetSelectedItem(_) --[[Anonymous function at line 161]]
        --[[
        Upvalues:
            [1] = u5
            [2] = u3
        --]]
        if u5.Visible == false then
            return nil
        else
            return u3.Items[u3.SelectedItem]
        end
    end
    function u3.Hide(_) --[[Anonymous function at line 170]]
        --[[
        Upvalues:
            [1] = u5
        --]]
        u5.Visible = false
    end
    function u3.IsVisible(_) --[[Anonymous function at line 175]]
        --[[
        Upvalues:
            [1] = u5
        --]]
        return u5.Visible
    end
    function u3.Select(p55, p56) --[[Anonymous function at line 180]]
        --[[
        Upvalues:
            [1] = u5
            [2] = u7
            [3] = u8
            [4] = u6
            [5] = u29
        --]]
        if u5.Visible then
            p55.SelectedItem = p55.SelectedItem + p56
            if p55.SelectedItem > #p55.Items then
                p55.SelectedItem = 1
            elseif p55.SelectedItem < 1 then
                p55.SelectedItem = #p55.Items
            end
            for v57, v58 in pairs(p55.Items) do
                v58.gui.BackgroundTransparency = v57 == p55.SelectedItem and 0.5 or 1
            end
            local v59 = u5
            local v60 = Vector2.new
            local v61 = u7.Size.Y.Offset + u8.Size.Y.Offset + p55.SelectedItem * u6.Size.Y.Offset - u5.Size.Y.Offset
            v59.CanvasPosition = v60(0, (math.max(0, v61)))
            if p55.Items[p55.SelectedItem] and p55.Items[p55.SelectedItem].options then
                u29(p55.Items[p55.SelectedItem].options or {})
            end
        end
    end
    u5.Parent:GetPropertyChangedSignal("AbsoluteSize"):Connect(u23)
    return u3
end