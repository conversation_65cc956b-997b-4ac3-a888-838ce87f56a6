-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\EffectController\Libraries\RocksModule-ModuleScript.lua
game:GetService("ReplicatedStorage")
local v1 = {}
local u2 = game:GetService("TweenService")
local v3 = require(script.Parent.PartCache)
local v4 = RaycastParams.new()
v4.FilterType = Enum.RaycastFilterType.Include
v4.FilterDescendantsInstances = { workspace }
local u5
if workspace:FindFirstChild("Debris") then
    u5 = workspace.Debris
else
    u5 = Instance.new("Folder")
    u5.Name = "Debris"
    u5.Parent = workspace
end
local u6 = v3.new(Instance.new("Part"), 1000, u5)
function v1.Ground(u7, u8, u9, p10, u11, u12, p13) --[[Anonymous function at line 22]]
    --[[
    Upvalues:
        [1] = u6
        [2] = u5
        [3] = u2
    --]]
    local u14 = Random.new()
    local u15 = 30
    local u16 = 360 / u11
    local u17 = basicUtility.RayParams
    local u18 = p10 and Vector3.new(1, 1, 1) * p10 or Vector3.new(2, 2, 2)
    local u19 = p13 or 3
    local function v31() --[[Anonymous function at line 32]]
        --[[
        Upvalues:
            [1] = u11
            [2] = u7
            [3] = u15
            [4] = u9
            [5] = u8
            [6] = u17
            [7] = u16
            [8] = u6
            [9] = u14
            [10] = u18
            [11] = u5
            [12] = u12
            [13] = u19
            [14] = u2
        --]]
        for _ = 1, u11 do
            local v20 = u15
            local v21 = CFrame.new(u7) * CFrame.fromEulerAnglesXYZ(0, math.rad(v20), 0) * CFrame.new(u9 / 2 + u9 / 2.7, 10, 0)
            local v22 = workspace:Raycast(v21.Position, u8, u17)
            u15 = u15 + u16
            if v22 then
                local u23 = u6:GetPart()
                local u24 = u6:GetPart()
                u23.CFrame = CFrame.new(v22.Position - Vector3.new(0, 0.5, 0), u7) * CFrame.fromEulerAnglesXYZ(u14:NextNumber(-0.25, 0.5), u14:NextNumber(-0.25, 0.25), u14:NextNumber(-0.25, 0.25))
                local v25 = u18.X * 1.3
                local v26 = u18.Y / 1.4
                local v27 = u18.Z * 1.3
                u23.Size = Vector3.new(v25, v26, v27) * u14:NextNumber(1, 1.5)
                local v28 = u23.Size.X * 1.01
                local v29 = u23.Size.Y * 0.25
                local v30 = u23.Size.Z * 1.01
                u24.Size = Vector3.new(v28, v29, v30)
                u24.CFrame = u23.CFrame * CFrame.new(0, u23.Size.Y / 2 - u24.Size.Y / 2.1, 0)
                u23.Parent = u5
                u24.Parent = u5
                if v22.Instance.Material == Enum.Material.Concrete or (v22.Instance.Material == Enum.Material.Air or (v22.Instance.Material == Enum.Material.Wood or (v22.Instance.Material == Enum.Material.Neon or v22.Instance.Material == Enum.Material.WoodPlanks))) then
                    u23.Material = v22.Instance.Material
                    u24.Material = v22.Instance.Material
                    u23.MaterialVariant = v22.Instance.MaterialVariant
                    u24.MaterialVariant = v22.Instance.MaterialVariant
                else
                    u23.Material = Enum.Material.Concrete
                    u24.Material = v22.Instance.Material
                    u24.MaterialVariant = v22.Instance.MaterialVariant
                end
                u23.BrickColor = BrickColor.new("Dark grey")
                u23.Anchored = true
                u23.CanTouch = false
                u23.CanCollide = false
                u24.BrickColor = v22.Instance.BrickColor
                u24.Anchored = true
                u24.CanTouch = false
                u24.CanCollide = false
                if u12 then
                    u23.BrickColor = BrickColor.new("Pastel light blue")
                    u24.BrickColor = BrickColor.new("Lily white")
                    u23.Material = Enum.Material.Ice
                    u24.Material = Enum.Material.Sand
                end
                task.delay(u19, function() --[[Anonymous function at line 79]]
                    --[[
                    Upvalues:
                        [1] = u2
                        [2] = u23
                        [3] = u24
                        [4] = u6
                    --]]
                    u2:Create(u23, TweenInfo.new(0.6, Enum.EasingStyle.Quad, Enum.EasingDirection.InOut), {
                        ["Size"] = Vector3.new(0.01, 0.01, 0.01)
                    }):Play()
                    u2:Create(u24, TweenInfo.new(0.6, Enum.EasingStyle.Quad, Enum.EasingDirection.InOut), {
                        ["Size"] = Vector3.new(0.01, 0.01, 0.01),
                        ["CFrame"] = u23.CFrame * CFrame.new(0, u23.Size.Y / 2 - u23.Size.Y / 2.1, 0)
                    }):Play()
                    task.delay(0.6, function() --[[Anonymous function at line 83]]
                        --[[
                        Upvalues:
                            [1] = u6
                            [2] = u23
                            [3] = u24
                        --]]
                        u6:ReturnPart(u23)
                        u6:ReturnPart(u24)
                    end)
                end)
            end
        end
    end
    (function() --[[Function name: InnerRocksLoop, line 92]]
        --[[
        Upvalues:
            [1] = u11
            [2] = u7
            [3] = u15
            [4] = u9
            [5] = u8
            [6] = u17
            [7] = u16
            [8] = u6
            [9] = u18
            [10] = u14
            [11] = u5
            [12] = u12
            [13] = u19
            [14] = u2
        --]]
        for _ = 1, u11 do
            local v32 = u15
            local v33 = CFrame.new(u7) * CFrame.fromEulerAnglesXYZ(0, math.rad(v32), 0) * CFrame.new(u9 / 2 + u9 / 10, 10, 0)
            local v34 = game.Workspace:Raycast(v33.Position, u8, u17)
            u15 = u15 + u16
            if v34 then
                local u35 = u6:GetPart()
                local u36 = u6:GetPart()
                local v37 = CFrame.new
                local v38 = v34.Position
                local v39 = u18.Y * 0.4
                u35.CFrame = v37(v38 - Vector3.new(0, v39, 0), u7) * CFrame.fromEulerAnglesXYZ(u14:NextNumber(-1, -0.3), u14:NextNumber(-0.15, 0.15), u14:NextNumber(-0.15, 0.15))
                local v40 = u18.X * 1.3
                local v41 = u18.Y * 0.7
                local v42 = u18.Z * 1.3
                u35.Size = Vector3.new(v40, v41, v42) * u14:NextNumber(1, 1.5)
                local v43 = u35.Size.X * 1.01
                local v44 = u35.Size.Y * 0.25
                local v45 = u35.Size.Z * 1.01
                u36.Size = Vector3.new(v43, v44, v45)
                u36.CFrame = u35.CFrame * CFrame.new(0, u35.Size.Y / 2 - u36.Size.Y / 2.1, 0)
                u35.Parent = u5
                u36.Parent = u5
                if v34.Instance.Material == Enum.Material.Concrete or (v34.Instance.Material == Enum.Material.Air or (v34.Instance.Material == Enum.Material.Wood or (v34.Instance.Material == Enum.Material.Neon or v34.Instance.Material == Enum.Material.WoodPlanks))) then
                    u35.Material = v34.Instance.Material
                    u36.Material = v34.Instance.Material
                    u35.MaterialVariant = v34.Instance.MaterialVariant
                    u36.MaterialVariant = v34.Instance.MaterialVariant
                else
                    u35.Material = Enum.Material.Concrete
                    u36.Material = v34.Instance.Material
                    u36.MaterialVariant = v34.Instance.MaterialVariant
                end
                u35.BrickColor = BrickColor.new("Dark grey")
                u35.Anchored = true
                u35.CanTouch = false
                u35.CanCollide = false
                u36.BrickColor = v34.Instance.BrickColor
                u36.Anchored = true
                u36.CanTouch = false
                u36.CanCollide = false
                if u12 then
                    u35.BrickColor = BrickColor.new("Pastel light blue")
                    u36.BrickColor = BrickColor.new("Lily white")
                    u35.Material = Enum.Material.Ice
                    u36.Material = Enum.Material.Sand
                end
                task.delay(u19, function() --[[Anonymous function at line 139]]
                    --[[
                    Upvalues:
                        [1] = u2
                        [2] = u35
                        [3] = u36
                        [4] = u6
                    --]]
                    u2:Create(u35, TweenInfo.new(0.6, Enum.EasingStyle.Quad, Enum.EasingDirection.InOut), {
                        ["Size"] = Vector3.new(0.01, 0.01, 0.01)
                    }):Play()
                    u2:Create(u36, TweenInfo.new(0.6, Enum.EasingStyle.Quad, Enum.EasingDirection.InOut), {
                        ["Size"] = Vector3.new(0.01, 0.01, 0.01),
                        ["CFrame"] = u35.CFrame * CFrame.new(0, u35.Size.Y / 2 - u35.Size.Y / 2.1, 0)
                    }):Play()
                    task.delay(0.6, function() --[[Anonymous function at line 143]]
                        --[[
                        Upvalues:
                            [1] = u6
                            [2] = u35
                            [3] = u36
                        --]]
                        u6:ReturnPart(u35)
                        u6:ReturnPart(u36)
                    end)
                end)
            end
        end
    end)()
    v31()
end
return v1