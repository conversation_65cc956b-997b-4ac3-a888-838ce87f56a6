-- Full Path: -Grow-a-Garden-\\FollowPlayer-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
local v2 = game:GetService("Players")
local u3 = require(v1.Modules.PetServices.ActivePetsService)
local u4 = v2.LocalPlayer
return {
    ["Verifier"] = function(p5) --[[Function name: Verifier, line 10]]
        --[[
        Upvalues:
            [1] = u4
        --]]
        return p5:GetAttribute("OWNER") == u4.Name
    end,
    ["Activate"] = function(p6) --[[Function name: Activate, line 15]]
        --[[
        Upvalues:
            [1] = u3
        --]]
        print("Follow Player")
        u3:SetPetState(p6:GetAttribute("UUID"), "FollowPlayer")
    end
}