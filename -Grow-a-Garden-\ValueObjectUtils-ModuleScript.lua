-- Full Path: -Grow-a-Garden-\\ValueObjectUtils-ModuleScript.lua
local v1 = {}
local u2 = {
    ["nil"] = "StringValue",
    ["boolean"] = "BoolValue",
    ["BrickColor"] = "BrickColorValue",
    ["CFrame"] = "CFrameValue",
    ["Color3"] = "Color3Value",
    ["number"] = "NumberValue",
    ["Instance"] = "ObjectValue",
    ["Ray"] = "RayValue",
    ["string"] = "StringValue",
    ["Vector3"] = "Vector3Value"
}
function v1.GetClassFromType(p3) --[[Anonymous function at line 19]]
    --[[
    Upvalues:
        [1] = u2
    --]]
    return u2[p3]
end
function v1.ConvertTypeToClass(p4) --[[Anonymous function at line 23]]
    --[[
    Upvalues:
        [1] = u2
    --]]
    return Instance.new(u2[p4])
end
return v1