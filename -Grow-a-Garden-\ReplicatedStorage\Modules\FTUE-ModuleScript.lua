-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\FTUE-ModuleScript.lua
local u1 = game:GetService("Players").LocalPlayer
local u2 = {
    workspace.NPCS:Wait<PERSON><PERSON><PERSON>hil<PERSON>("Gear Stands", 5),
    workspace.MapDecorations:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("QuestSign", 5),
    workspace.MapDecorations:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("GearShopSign", 5),
    workspace.NPCS:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("<PERSON>", 5),
    workspace.NPCS:WaitFor<PERSON>hild("Eloise", 5),
    workspace.NPCS:WaitFor<PERSON>hild("Quest Stand", 5),
    workspace.NPCS:<PERSON><PERSON><PERSON><PERSON>hild("Pet Stand", 5),
    workspace.Interaction.UpdateItems:<PERSON><PERSON><PERSON><PERSON>hil<PERSON>("CosmeticStand", 5)
}
local function v4() --[[Anonymous function at line 18]]
    --[[
    Upvalues:
        [1] = u1
        [2] = u2
    --]]
    if u1:Get<PERSON>ttribute("FirstTimePlayer") then
        for _, v3 in u2 do
            v3.Parent = nil
        end
    end
end
task.spawn(v4)
u1:Get<PERSON><PERSON>ri<PERSON><PERSON><PERSON><PERSON>edSignal("FirstTimePlayer"):Connect(v4)
return {}