-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\Settings\SettingsUserInterfaceService\Classes\MultipleSetting-ModuleScript.lua
local v1 = game:GetService("Players")
local v2 = game:GetService("ReplicatedStorage")
local v3 = v1.LocalPlayer:Wait<PERSON><PERSON><PERSON><PERSON><PERSON>("PlayerGui"):Wait<PERSON><PERSON><PERSON><PERSON><PERSON>("SettingsUI")
local u4 = require(v2.Modules.WaitForDescendant)
local u5 = require(v2.Modules.SetupSounds)
local u6 = require(v2.Modules.SetupHoverAnimations)
local u7 = require(v2.Modules.FindDescendantsWithTag)
local u8 = require(v2.Modules.Settings.SettingsService)
local u9 = u4(v3, "SETTING_INSERTION_POINT")
local u10 = u4(v3, "MULTIPLE_SETTING_TEMPLATE")
local u11 = {}
u11.__index = u11
function u11.new() --[[Anonymous function at line 19]]
    --[[
    Upvalues:
        [1] = u11
        [2] = u10
        [3] = u7
        [4] = u4
        [5] = u5
        [6] = u6
    --]]
    local v12 = u11
    local u13 = setmetatable({}, v12)
    local v14 = u10:Clone()
    u13.Asset = v14
    u13.CurrentValue = "All"
    local v15 = {}
    u13.UIData = v15
    v15.AllStrokes = u7(v14, "ColorStroke")
    v15.NONE_BUTTON = u4(v14, "NONE_BUTTON")
    v15.LESS_BUTTON = u4(v14, "LESS_BUTTON")
    v15.ALL_BUTTON = u4(v14, "ALL_BUTTON")
    v15.SETTING_TITLE = u4(v14, "SETTING_TITLE")
    v15.SETTING_DESCRIPTION = u4(v14, "SETTING_DESCRIPTION")
    v15.BACKGROUND_TEXTURE = u4(v14, "BACKGROUND_TEXTURE")
    for _, v16 in pairs({ v15.NONE_BUTTON, v15.LESS_BUTTON, v15.ALL_BUTTON }) do
        u5(v16)
        u6(v16)
    end
    v15.NONE_BUTTON.SENSOR.MouseButton1Click:Connect(function() --[[Anonymous function at line 42]]
        --[[
        Upvalues:
            [1] = u13
        --]]
        u13:Set("None")
    end)
    v15.LESS_BUTTON.SENSOR.MouseButton1Click:Connect(function() --[[Anonymous function at line 46]]
        --[[
        Upvalues:
            [1] = u13
        --]]
        u13:Set("Less")
    end)
    v15.ALL_BUTTON.SENSOR.MouseButton1Click:Connect(function() --[[Anonymous function at line 50]]
        --[[
        Upvalues:
            [1] = u13
        --]]
        u13:Set("All")
    end)
    return u13
end
function u11.Set(p17, p18) --[[Anonymous function at line 57]]
    --[[
    Upvalues:
        [1] = u8
    --]]
    if p17.CurrentValue ~= p18 then
        p17.CurrentValue = p18
        local v19 = p17.UIData
        for v20, v21 in {
            ["None"] = v19.NONE_BUTTON,
            ["Less"] = v19.LESS_BUTTON,
            ["All"] = v19.ALL_BUTTON
        } do
            v21.BackgroundTransparency = v20 == p18 and 0 or 0.8
        end
        if p17.Id then
            u8:SetSetting(p17.Id, p18)
        end
    end
end
function u11.SetId(p22, p23) --[[Anonymous function at line 80]]
    p22.Id = p23
    return p22
end
function u11.Update(p24, p25) --[[Anonymous function at line 85]]
    p24:Set(p25)
    return p24
end
function u11.SetDescription(p26, p27) --[[Anonymous function at line 90]]
    p26.UIData.SETTING_DESCRIPTION.Text = p27
    return p26
end
function u11.SetTitle(p28, p29) --[[Anonymous function at line 95]]
    p28.UIData.SETTING_TITLE.Text = p29
    return p28
end
function u11.SetBackgroundImage(p30, p31) --[[Anonymous function at line 100]]
    p30.UIData.BACKGROUND_TEXTURE.Image = p31
    return p30
end
function u11.SetLayoutOrder(p32, p33) --[[Anonymous function at line 105]]
    p32.Asset.LayoutOrder = p33
    return p32
end
function u11.Complete(p34) --[[Anonymous function at line 110]]
    --[[
    Upvalues:
        [1] = u9
    --]]
    local v35 = p34.Asset
    v35.Visible = true
    v35.Parent = u9
    return p34
end
return u11