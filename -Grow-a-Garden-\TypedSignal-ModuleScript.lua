-- Full Path: -Grow-a-Garden-\\TypedSignal-ModuleScript.lua
local u1 = nil
local function u4(p2, ...) --[[Anonymous function at line 44]]
    --[[
    Upvalues:
        [1] = u1
    --]]
    local v3 = u1
    u1 = nil
    p2(...)
    u1 = v3
end
local function u5(...) --[[Anonymous function at line 55]]
    --[[
    Upvalues:
        [1] = u4
    --]]
    u4(...)
    while true do
        u4(coroutine.yield())
    end
end
local u6 = {}
u6.__index = u6
function u6.Disconnect(p7) --[[Anonymous function at line 81]]
    if p7.Connected then
        p7.Connected = false
        if p7._signal._handlerListHead == p7 then
            p7._signal._handlerListHead = p7._next
        else
            local v8 = p7._signal._handlerListHead
            while v8 and v8._next ~= p7 do
                v8 = v8._next
            end
            if v8 then
                v8._next = p7._next
            end
        end
    else
        return
    end
end
u6.Destroy = u6.Disconnect
setmetatable(u6, {
    ["__index"] = function(_, p9) --[[Function name: __index, line 108]]
        error(("Attempt to get Connection::%s (not a valid member)"):format((tostring(p9))), 2)
    end,
    ["__newindex"] = function(_, p10, _) --[[Function name: __newindex, line 111]]
        error(("Attempt to set Connection::%s (not a valid member)"):format((tostring(p10))), 2)
    end
})
local u11 = {}
u11.__index = u11
function u11.new() --[[Anonymous function at line 153]]
    --[[
    Upvalues:
        [1] = u11
    --]]
    local v12 = u11
    return setmetatable({
        ["_handlerListHead"] = false,
        ["_proxyHandler"] = nil,
        ["_yieldedThreads"] = nil
    }, v12)
end
function u11.Wrap(p13) --[[Anonymous function at line 176]]
    --[[
    Upvalues:
        [1] = u11
    --]]
    local v14 = typeof(p13) == "RBXScriptSignal"
    local v15 = "Argument #1 to Signal.Wrap must be a RBXScriptSignal; got " .. typeof(p13)
    assert(v14, v15)
    local u16 = u11.new()
    u16._proxyHandler = p13:Connect(function(...) --[[Anonymous function at line 183]]
        --[[
        Upvalues:
            [1] = u16
        --]]
        u16:Fire(...)
    end)
    return u16
end
function u11.Is(p17) --[[Anonymous function at line 196]]
    --[[
    Upvalues:
        [1] = u11
    --]]
    local v18
    if type(p17) == "table" then
        v18 = getmetatable(p17) == u11
    else
        v18 = false
    end
    return v18
end
function u11.Connect(p19, p20) --[[Anonymous function at line 212]]
    --[[
    Upvalues:
        [1] = u6
    --]]
    local v21 = u6
    local v22 = setmetatable({
        ["Connected"] = true,
        ["_signal"] = p19,
        ["_fn"] = p20,
        ["_next"] = false
    }, v21)
    if not p19._handlerListHead then
        p19._handlerListHead = v22
        return v22
    end
    v22._next = p19._handlerListHead
    p19._handlerListHead = v22
    return v22
end
function u11.ConnectOnce(p23, p24) --[[Anonymous function at line 235]]
    return p23:Once(p24)
end
function u11.Once(p25, u26) --[[Anonymous function at line 254]]
    local u27 = nil
    local u28 = false
    u27 = p25:Connect(function(...) --[[Anonymous function at line 258]]
        --[[
        Upvalues:
            [1] = u28
            [2] = u27
            [3] = u26
        --]]
        if not u28 then
            u28 = true
            u27:Disconnect()
            u26(...)
        end
    end)
    return u27
end
function u11.GetConnections(p29) --[[Anonymous function at line 271]]
    local v30 = p29._handlerListHead
    local v31 = {}
    while v30 do
        table.insert(v31, v30)
        v30 = v30._next
    end
    return v31
end
function u11.DisconnectAll(p32) --[[Anonymous function at line 291]]
    local v33 = p32._handlerListHead
    while v33 do
        v33.Connected = false
        v33 = v33._next
    end
    p32._handlerListHead = false
    local v34 = rawget(p32, "_yieldedThreads")
    if v34 then
        for v35 in v34 do
            if coroutine.status(v35) == "suspended" then
                warn(debug.traceback(v35, "signal disconnected; yielded thread cancelled", 2))
                task.cancel(v35)
            end
        end
        table.clear(p32._yieldedThreads)
    end
end
function u11.Fire(p36, ...) --[[Anonymous function at line 326]]
    --[[
    Upvalues:
        [1] = u1
        [2] = u5
    --]]
    local v37 = p36._handlerListHead
    while v37 do
        if v37.Connected then
            if not u1 then
                u1 = coroutine.create(u5)
            end
            task.spawn(u1, v37._fn, ...)
        end
        v37 = v37._next
    end
end
function u11.FireDeferred(p38, ...) --[[Anonymous function at line 347]]
    local u39 = p38._handlerListHead
    while u39 do
        task.defer(function(...) --[[Anonymous function at line 351]]
            --[[
            Upvalues:
                [1] = u39
            --]]
            if u39.Connected then
                u39._fn(...)
            end
        end, ...)
        u39 = u39._next
    end
end
function u11.Wait(p40) --[[Anonymous function at line 375]]
    local u41 = rawget(p40, "_yieldedThreads")
    if not u41 then
        u41 = {}
        rawset(p40, "_yieldedThreads", u41)
    end
    local u42 = coroutine.running()
    u41[u42] = true
    p40:Once(function(...) --[[Anonymous function at line 385]]
        --[[
        Upvalues:
            [1] = u41
            [2] = u42
        --]]
        u41[u42] = nil
        task.spawn(u42, ...)
    end)
    return coroutine.yield()
end
function u11.Destroy(p43) --[[Anonymous function at line 405]]
    p43:DisconnectAll()
    local v44 = rawget(p43, "_proxyHandler")
    if v44 then
        v44:Disconnect()
    end
end
setmetatable(u11, {
    ["__index"] = function(_, p45) --[[Function name: __index, line 416]]
        error(("Attempt to get Signal::%s (not a valid member)"):format((tostring(p45))), 2)
    end,
    ["__newindex"] = function(_, p46, _) --[[Function name: __newindex, line 419]]
        error(("Attempt to set Signal::%s (not a valid member)"):format((tostring(p46))), 2)
    end
})
return table.freeze({
    ["new"] = u11.new,
    ["Wrap"] = u11.Wrap,
    ["Is"] = u11.Is
})