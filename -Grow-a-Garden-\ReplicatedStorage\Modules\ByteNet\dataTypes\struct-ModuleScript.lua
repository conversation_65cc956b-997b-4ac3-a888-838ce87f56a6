-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\ByteNet\dataTypes\struct-ModuleScript.lua
local v1 = game:GetService("RunService")
local u2 = require(script.Parent.Parent.namespaces.namespacesDependencies)
local u3 = require(script.Parent.Parent.replicated.values)
require(script.Parent.Parent.types)
local u4 = v1:IsServer() and "server" or "client"
return function(u5) --[[Anonymous function at line 13]]
    --[[
    Upvalues:
        [1] = u4
        [2] = u2
        [3] = u3
    --]]
    local u6 = {}
    local u7 = {}
    if u4 == "server" then
        local v8 = 0
        local v9 = {}
        for v10 in u5 do
            v8 = v8 + 1
            v9[v10] = v8
            u6[v8] = u5[v10]
            u7[v8] = v10
        end
        u2.add(v9)
    elseif u4 == "client" then
        u2.add(u5)
        local v11 = u2.currentName()
        for v12, v13 in u3.access(v11):read().structs[u2.currentLength()] do
            u6[v13] = u5[v12]
            u7[v13] = v12
        end
    end
    return {
        ["read"] = function(p14, p15) --[[Function name: read, line 75]]
            --[[
            Upvalues:
                [1] = u5
                [2] = u6
                [3] = u7
            --]]
            local v16 = table.clone(u5)
            local v17 = p15
            for v18, v19 in u6 do
                local v20, v21 = v19.read(p14, p15)
                v16[u7[v18]] = v20
                p15 = p15 + v21
            end
            return v16, p15 - v17
        end,
        ["write"] = function(p22) --[[Function name: write, line 90]]
            --[[
            Upvalues:
                [1] = u6
                [2] = u7
            --]]
            for v23, v24 in u6 do
                v24.write(p22[u7[v23]])
            end
        end
    }
end