-- Full Path: -Grow-a-Garden-\\Frame_Popup_Module-ModuleScript.lua
local v1 = {}
local u2 = require(game.ReplicatedStorage.Field_Of_View_Module)
local u3 = require(game.ReplicatedStorage.Blur_Module)
local u4 = {}
function v1.Show(p5) --[[Anonymous function at line 6]]
    --[[
    Upvalues:
        [1] = u4
        [2] = u2
        [3] = u3
    --]]
    if table.find(u4, p5) == nil then
        local v6 = u4
        table.insert(v6, p5)
    end
    u2.Change_FOV(60, 0.3)
    u3.Blur(15, 0.1)
end
function v1.Hide(p7) --[[Anonymous function at line 17]]
    --[[
    Upvalues:
        [1] = u4
        [2] = u2
        [3] = u3
    --]]
    local v8 = table.find(u4, p7)
    if v8 then
        table.remove(u4, v8)
    end
    if #u4 == 0 then
        u2.Change_FOV_CORE(70, 0.3)
        u2.Change_FOV(70, 0.3)
        u3.Blur(0, 0.3)
    end
end
function v1.Can() --[[Anonymous function at line 32]]
    --[[
    Upvalues:
        [1] = u4
    --]]
    return #u4 == 0
end
return v1