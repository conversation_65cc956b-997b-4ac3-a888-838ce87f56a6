-- Full Path: -Grow-a-Garden-\\Night-ModuleScript.lua
local v1 = {}
local _ = workspace.CurrentCamera
local u2 = require(game.ReplicatedStorage.Modules.SkyboxManager)
local u3 = script.Sky
u2.AddSkybox(u3)
local u4 = script.Ambience:Clone()
local u5 = false
u3:GetPropertyChangedSignal("Parent"):Connect(function() --[[Anonymous function at line 20]]
    print("Test")
end)
local function u7(p6) --[[Anonymous function at line 24]]
    --[[
    Upvalues:
        [1] = u5
        [2] = u2
        [3] = u3
        [4] = u4
    --]]
    if u5 == true then
        return
    else
        u5 = true
        if p6 then
            game.Lighting.ClockTime = 16
            game.TweenService:Create(game.Lighting, TweenInfo.new(0.1), {
                ["Ambient"] = Color3.fromRGB(110, 61, 138),
                ["ExposureCompensation"] = 1,
                ["Brightness"] = 0.6
            }):Play()
            game.TweenService:Create(game.Lighting.NightColor, TweenInfo.new(0.1), {
                ["Brightness"] = 0.15,
                ["TintColor"] = Color3.fromRGB(206, 178, 255),
                ["Contrast"] = 0.1
            }):Play()
            game.Lighting.SunRays.Intensity = 0.028
            u2.UpdateSkybox(u3, 4)
            u4.Parent = workspace
            u4:Play()
            u4.Volume = 0.1
        else
            game.TweenService:Create(game.Lighting, TweenInfo.new(5), {
                ["ClockTime"] = 21
            }):Play()
            task.wait(5)
            u2.UpdateSkybox(u3, 4)
            game.Lighting.ClockTime = 3
            game.TweenService:Create(game.Lighting, TweenInfo.new(5), {
                ["Ambient"] = Color3.fromRGB(110, 61, 138),
                ["ExposureCompensation"] = 1,
                ["Brightness"] = 0.6,
                ["ClockTime"] = 16
            }):Play()
            game.Lighting:SetAttribute("DefaultAmbient", Color3.fromRGB(110, 61, 138))
            game.Lighting:SetAttribute("DefaultExposure", 1)
            game.Lighting:SetAttribute("DefaultBrightness", 0.6)
            game.TweenService:Create(game.Lighting.NightColor, TweenInfo.new(5), {
                ["Brightness"] = 0.15,
                ["TintColor"] = Color3.fromRGB(206, 178, 255),
                ["Contrast"] = 0.1
            }):Play()
            game.Lighting.SunRays.Enabled = true
            u4.Parent = workspace
            u4:Play()
            game.TweenService:Create(u4, TweenInfo.new(1), {
                ["Volume"] = 0.1
            }):Play()
            task.delay(13, function() --[[Anonymous function at line 88]]
                game.TweenService:Create(game.Lighting.SunRays, TweenInfo.new(2), {
                    ["Intensity"] = 0.028
                }):Play()
            end)
        end
    end
end
local function u9(p8) --[[Anonymous function at line 96]]
    --[[
    Upvalues:
        [1] = u5
        [2] = u4
        [3] = u2
        [4] = u3
    --]]
    if u5 == false then
        return
    else
        u5 = false
        if not p8 then
            game.TweenService:Create(game.Lighting, TweenInfo.new(5), {
                ["ClockTime"] = 21
            }):Play()
            game.TweenService:Create(game.Lighting.NightColor, TweenInfo.new(5), {
                ["Brightness"] = 0,
                ["Contrast"] = 0,
                ["TintColor"] = Color3.fromRGB(255, 255, 255)
            }):Play()
            game.TweenService:Create(game.Lighting.SunRays, TweenInfo.new(2), {
                ["Intensity"] = 0
            }):Play()
            task.wait(5)
            game.TweenService:Create(u4, TweenInfo.new(1), {
                ["Volume"] = 0
            }):Play()
            task.delay(1, function() --[[Anonymous function at line 125]]
                --[[
                Upvalues:
                    [1] = u4
                --]]
                u4.Parent = script
                u4:Stop()
            end)
            u2.UpdateSkybox(u3, 0)
            game.Lighting.ClockTime = 3
            game.Lighting:SetAttribute("DefaultAmbient", Color3.fromRGB(138, 138, 138))
            game.Lighting:SetAttribute("DefaultExposure", 0.2)
            game.Lighting:SetAttribute("DefaultBrightness", 2)
            game.TweenService:Create(game.Lighting, TweenInfo.new(5), {
                ["Ambient"] = Color3.fromRGB(138, 138, 138),
                ["ExposureCompensation"] = 0.2,
                ["Brightness"] = 2,
                ["ClockTime"] = 14
            }):Play()
            task.delay(6, function() --[[Anonymous function at line 146]]
                game.Lighting.SunRays.Enabled = false
            end)
        end
    end
end
workspace:GetAttributeChangedSignal("NightEvent"):Connect(function() --[[Anonymous function at line 154]]
    --[[
    Upvalues:
        [1] = u7
        [2] = u9
    --]]
    if workspace:GetAttribute("NightEvent") then
        u7()
    else
        u9()
    end
end)
if workspace:GetAttribute("NightEvent") then
    task.defer(function() --[[Anonymous function at line 163]]
        --[[
        Upvalues:
            [1] = u7
        --]]
        u7(true)
    end)
else
    u9(true)
end
return v1