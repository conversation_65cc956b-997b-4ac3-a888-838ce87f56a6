-- Full Path: -Grow-a-Garden-\\Trowel_Client-LocalScript.lua
local v1 = game:GetService("Players")
local u2 = game:GetService("UserInputService")
local u3 = game:GetService("CollectionService")
local v4 = game:GetService("ReplicatedStorage")
local u5 = game:GetService("TweenService")
local u6 = game:GetService("RunService")
game:GetService("ProximityPromptService")
local u7 = v4:WaitForChild("GameEvents"):WaitForChild("TrowelRemote")
local u8 = require(v4.Modules.GetFarm)
local u9 = require(v4.Modules.Notification)
local u10 = require(v4.Modules.EffectController.Libraries.Default)
local u11 = require(v4.Modules.ProximityPromptController)
local u12 = v1.LocalPlayer
local u13 = workspace.CurrentCamera
local u14 = u12.PlayerGui:WaitForChild("Trowel_Client")
local v15 = u14.Frame:WaitForChild("Cancel")
local u16 = nil
local u17 = nil
local u18 = nil
local u19 = script.Highlight
local u20 = {}
local u21 = nil
local u22 = false
local function u25(p23) --[[Anonymous function at line 49]]
    --[[
    Upvalues:
        [1] = u8
        [2] = u12
    --]]
    local v24 = u8(u12)
    if v24 then
        if p23:IsDescendantOf(v24) then
            if p23:FindFirstChild("Grow") then
                if p23:FindFirstChild("Fruits") then
                    return p23:FindFirstChild("Fruit_Spawn") and true or false
                else
                    return false
                end
            else
                return false
            end
        else
            return false
        end
    else
        return false
    end
end
local function u30(p26) --[[Anonymous function at line 58]]
    --[[
    Upvalues:
        [1] = u13
        [2] = u3
    --]]
    local v27 = u13:ViewportPointToRay(p26.X, p26.Y)
    local v28 = RaycastParams.new()
    v28.FilterType = Enum.RaycastFilterType.Exclude
    v28.FilterDescendantsInstances = { u3:GetTagged("ShovelIgnore") }
    local v29 = workspace:Raycast(v27.Origin, v27.Direction * 500, v28)
    if v29 and v29.Instance then
        return v29
    end
end
local function u35(p31) --[[Anonymous function at line 72]]
    --[[
    Upvalues:
        [1] = u13
        [2] = u8
        [3] = u12
    --]]
    local v32 = u13:ViewportPointToRay(p31.X, p31.Y)
    local v33 = RaycastParams.new()
    v33.FilterType = Enum.RaycastFilterType.Include
    v33.FilterDescendantsInstances = { u8(u12).Important.Plant_Locations:GetChildren() }
    local v34 = workspace:Raycast(v32.Origin, v32.Direction * 500, v33)
    if v34 and v34.Instance then
        return v34
    end
end
local function u38() --[[Anonymous function at line 86]]
    --[[
    Upvalues:
        [1] = u16
        [2] = u22
        [3] = u17
        [4] = u12
        [5] = u7
        [6] = u9
        [7] = u20
        [8] = u19
    --]]
    if u16 and (not u22 and u17) then
        local v36 = u16
        u16 = nil
        if u12.Character:FindFirstChildOfClass("Humanoid") then
            u12.Character:FindFirstChildOfClass("Humanoid"):UnequipTools()
        end
        u7:InvokeServer("Cancel", u17, v36)
        u9:CreateNotification((("Canceled moving %*!"):format(v36.Name)))
        for _, v37 in u20 or {} do
            v37.CanCollide = true
            v37.CanQuery = true
        end
        u19.Adornee = nil
    end
end
u6.RenderStepped:Connect(function() --[[Anonymous function at line 110]]
    --[[
    Upvalues:
        [1] = u14
        [2] = u16
        [3] = u17
        [4] = u21
        [5] = u38
        [6] = u2
        [7] = u35
        [8] = u18
        [9] = u30
        [10] = u25
        [11] = u19
        [12] = u5
    --]]
    u14.Enabled = u16 ~= nil
    if u17 then
        if not u21 then
            u21 = u17.Destroying:Once(function() --[[Anonymous function at line 119]]
                --[[
                Upvalues:
                    [1] = u21
                    [2] = u38
                --]]
                u21:Disconnect()
                u21 = nil
                u38()
            end)
        end
        local v39 = u2:GetMouseLocation()
        if u16 then
            local v40 = u35(v39)
            if v40 and v40.Position then
                u16:PivotTo(CFrame.new(v40.Position) * CFrame.new(0, 3, 0) * u18.Rotation)
            else
                u16:PivotTo(u18)
            end
        else
            local v41 = u30(v39)
            if v41 then
                local v42 = v41.Instance.Parent
                if u25(v42) then
                    if u19.Adornee ~= v42 then
                        u19.FillTransparency = 1
                        u5:Create(u19, TweenInfo.new(0.25), {
                            ["FillTransparency"] = 0.65
                        }):Play()
                    end
                    u19.Adornee = v42
                else
                    u19.Adornee = nil
                end
            else
                return
            end
        end
    else
        if u16 then
            u38()
        end
        u19.Adornee = nil
        return
    end
end)
v15.Activated:Connect(u38)
local function v59(p43, p44) --[[Anonymous function at line 166]]
    --[[
    Upvalues:
        [1] = u12
        [2] = u22
        [3] = u9
        [4] = u2
        [5] = u16
        [6] = u35
        [7] = u8
        [8] = u19
        [9] = u18
        [10] = u6
        [11] = u5
        [12] = u7
        [13] = u17
        [14] = u10
        [15] = u13
        [16] = u20
        [17] = u30
        [18] = u25
    --]]
    if u12.Character then
        local v45 = u12.Character:FindFirstChildOfClass("Tool")
        local v46
        if v45 then
            v46 = v45.Name:match("Trowel")
        else
            v46 = v45
        end
        if v46 and v45 and not p44 then
            if u22 then
                u9:CreateNotification("Please wait before trying again!")
                return
            else
                local v47 = p43 or u2:GetMouseLocation()
                if u16 then
                    local u48 = u35(v47)
                    if u48 and u48.Instance.Name == "Can_Plant" then
                        local v49 = RaycastParams.new()
                        v49.FilterType = Enum.RaycastFilterType.Include
                        v49.FilterDescendantsInstances = { u8(u12).Important.Plants_Physical:GetChildren() }
                        if not workspace:Raycast(u48.Position + Vector3.new(0, 20, 0), Vector3.new(0, -25, 0), v49) then
                            u19.Adornee = nil
                            u22 = true
                            task.spawn(function() --[[Anonymous function at line 203]]
                                --[[
                                Upvalues:
                                    [1] = u48
                                    [2] = u18
                                    [3] = u16
                                    [4] = u6
                                    [5] = u5
                                    [6] = u7
                                    [7] = u17
                                    [8] = u9
                                    [9] = u10
                                    [10] = u13
                                    [11] = u22
                                --]]
                                local v50 = CFrame.new(u48.Position) * CFrame.new(0, 3, 0) * u18.Rotation
                                local v51 = CFrame.new(u48.Position.X, u18.Y, u48.Position.Z) * u18.Rotation
                                local v52 = u16
                                v52:PivotTo(v50)
                                local v53 = 0
                                while v53 < 0.15 do
                                    v53 = v53 + u6.Heartbeat:Wait()
                                    v52:PivotTo(v50:Lerp(v51, (u5:GetValue(v53 / 0.1, Enum.EasingStyle.Quad, Enum.EasingDirection.In))))
                                end
                                u7:InvokeServer("Place", u17, v52, v51)
                                u9:CreateNotification((("Successfully moved %*!"):format(v52.Name)))
                                u10:SetScale(u10:CreateEffect({
                                    ["Object"] = script.Place,
                                    ["Emit"] = true,
                                    ["Position"] = u48.Position,
                                    ["DebrisTime"] = 1
                                }), 3, {})
                                u10:PlaySound(script.PlantTrowel, u13)
                                task.wait(1)
                                u22 = false
                            end)
                            for _, v54 in u20 or {} do
                                v54.CanCollide = true
                                v54.CanQuery = true
                            end
                            u16 = nil
                        end
                    else
                        u9:CreateNotification("Can\'t place here!")
                        return
                    end
                else
                    local v55 = u30(v47)
                    if v55 then
                        local v56 = v55.Instance.Parent
                        if u25(v56) then
                            if u17 and u17.Name then
                                u22 = true
                                u7:InvokeServer("Pickup", u17, v56)
                                u22 = false
                                u9:CreateNotification((("Picked up %*!"):format(v56.Name)))
                                u18 = v56:GetPivot()
                                for _, v57 in v56:GetDescendants() do
                                    if v57:IsA("BasePart") and v57.CanCollide then
                                        v57.CanCollide = false
                                        v57.CanQuery = false
                                        local v58 = u20
                                        table.insert(v58, v57)
                                    elseif v57:IsA("BasePart") then
                                        v57.CanQuery = false
                                    end
                                end
                                u16 = v56
                            else
                                u9:CreateNotification("Trowel not detected, please try again or report if this is an error!")
                            end
                        else
                            u9:CreateNotification("Can\'t move this!")
                            return
                        end
                    else
                        return
                    end
                end
            end
        end
    end
end
if u2.TouchEnabled then
    u2.TouchTapInWorld:Connect(v59)
else
    u12:GetMouse().Button1Down:Connect(v59)
end
task.spawn(function() --[[Anonymous function at line 309]]
    --[[
    Upvalues:
        [1] = u12
        [2] = u8
        [3] = u17
        [4] = u11
        [5] = u38
    --]]
    local v60 = false
    while not v60 do
        task.wait(0.1)
        local v61 = u12.Character:FindFirstChildOfClass("Tool")
        local v62
        if v61 then
            v62 = v61.Name:match("Trowel")
        else
            v62 = v61
        end
        local u63 = v62 and v61
        u8(u12)
        if u12.Character and u63 then
            u63.Equipped:Connect(function() --[[Anonymous function at line 319]]
                --[[
                Upvalues:
                    [1] = u17
                    [2] = u63
                    [3] = u11
                --]]
                u17 = u63
                u11:AddDisabler("Trowel")
            end)
            u63.Unequipped:Connect(function() --[[Anonymous function at line 325]]
                --[[
                Upvalues:
                    [1] = u11
                    [2] = u38
                    [3] = u17
                --]]
                u11:RemoveDisabler("Trowel")
                u38()
                u17 = nil
            end)
            v60 = true
        end
    end
end)