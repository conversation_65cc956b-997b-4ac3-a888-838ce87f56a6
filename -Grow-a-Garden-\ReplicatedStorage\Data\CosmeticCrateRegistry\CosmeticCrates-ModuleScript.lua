-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Data\CosmeticCrateRegistry\CosmeticCrates-ModuleScript.lua
local v1 = {
    ["Mysterious Crate"] = {
        ["Color"] = Color3.fromRGB(255, 170, 0),
        ["Icon"] = "",
        ["OpenTime"] = 1800,
        ["CosmeticRolls"] = {
            ["Items"] = {
                ["Beta Gnome"] = {
                    ["ItemOdd"] = 5,
                    ["Name"] = "Beta Gnome"
                },
                ["Green Gnome"] = {
                    ["ItemOdd"] = 5,
                    ["Name"] = "Green Gnome"
                },
                ["Blue Gnome"] = {
                    ["ItemOdd"] = 5,
                    ["Name"] = "Blue Gnome"
                },
                ["Wheelbarrow"] = {
                    ["ItemOdd"] = 30,
                    ["Name"] = "Wheelbarrow"
                },
                ["Frog Fountain"] = {
                    ["ItemOdd"] = 15,
                    ["Name"] = "Frog Fountain"
                },
                ["Small Wood Table"] = {
                    ["ItemOdd"] = 30,
                    ["Name"] = "Small Wood Table"
                }
            }
        }
    },
    ["Sign Crate"] = {
        ["Color"] = Color3.fromRGB(255, 255, 255),
        ["Icon"] = "rbxassetid://105096268891832",
        ["OpenTime"] = 1800,
        ["CosmeticRolls"] = {
            ["Items"] = {
                ["No Trespassing Sign"] = {
                    ["ItemOdd"] = 20,
                    ["Name"] = "No Trespassing Sign"
                },
                ["Beware Of Dog Sign"] = {
                    ["ItemOdd"] = 20,
                    ["Name"] = "Beware Of Dog Sign"
                },
                ["Mean Gardener Sign"] = {
                    ["ItemOdd"] = 20,
                    ["Name"] = "Mean Gardener Sign"
                },
                ["Keep Out Sign"] = {
                    ["ItemOdd"] = 20,
                    ["Name"] = "Keep Out Sign"
                },
                ["Smile On Cam Sign"] = {
                    ["ItemOdd"] = 20,
                    ["Name"] = "Smile On Cam Sign"
                },
                ["No Stealing Sign"] = {
                    ["ItemOdd"] = 1,
                    ["Name"] = "No Stealing Sign"
                }
            }
        }
    },
    ["Common Gnome Crate"] = {
        ["Color"] = Color3.fromRGB(255, 255, 255),
        ["Icon"] = "rbxassetid://79712703111915",
        ["OpenTime"] = 1800,
        ["CosmeticRolls"] = {
            ["Items"] = {
                ["Sleepy Gnome"] = {
                    ["ItemOdd"] = 25,
                    ["Name"] = "Sleepy Gnome"
                },
                ["Silly Gnome"] = {
                    ["ItemOdd"] = 25,
                    ["Name"] = "Silly Gnome"
                },
                ["Crazy Gnome"] = {
                    ["ItemOdd"] = 25,
                    ["Name"] = "Crazy Gnome"
                },
                ["Grumpy Gnome"] = {
                    ["ItemOdd"] = 25,
                    ["Name"] = "Grumpy Gnome"
                },
                ["Fearless Gnome"] = {
                    ["ItemOdd"] = 1,
                    ["Name"] = "Fearless Gnome"
                }
            }
        }
    },
    ["Farmers Gnome Crate"] = {
        ["Color"] = Color3.fromRGB(255, 255, 255),
        ["Icon"] = "rbxassetid://75833747822247",
        ["OpenTime"] = 3600,
        ["CosmeticRolls"] = {
            ["Items"] = {
                ["Raphael Gnome"] = {
                    ["ItemOdd"] = 25,
                    ["Name"] = "Raphael Gnome"
                },
                ["Steven Gnome"] = {
                    ["ItemOdd"] = 25,
                    ["Name"] = "Steven Gnome"
                },
                ["Eloise Gnome"] = {
                    ["ItemOdd"] = 25,
                    ["Name"] = "Eloise Gnome"
                },
                ["Sam Gnome"] = {
                    ["ItemOdd"] = 25,
                    ["Name"] = "Sam Gnome"
                },
                ["Cleetus Gnome"] = {
                    ["ItemOdd"] = 1,
                    ["Name"] = "Cleetus Gnome"
                }
            }
        }
    },
    ["Fun Crate"] = {
        ["Color"] = Color3.fromRGB(255, 255, 255),
        ["Icon"] = "rbxassetid://95673817494918",
        ["OpenTime"] = 3600,
        ["CosmeticRolls"] = {
            ["Items"] = {
                ["Red Flag"] = {
                    ["ItemOdd"] = 25,
                    ["Name"] = "Red Flag"
                },
                ["Blue Flag"] = {
                    ["ItemOdd"] = 25,
                    ["Name"] = "Blue Flag"
                },
                ["Roundabout"] = {
                    ["ItemOdd"] = 10,
                    ["Name"] = "Roundabout"
                },
                ["Seesaw"] = {
                    ["ItemOdd"] = 10,
                    ["Name"] = "Seesaw"
                },
                ["Swing set"] = {
                    ["ItemOdd"] = 10,
                    ["Name"] = "Swing set"
                },
                ["Trampoline"] = {
                    ["ItemOdd"] = 1,
                    ["Name"] = "Trampoline"
                }
            }
        }
    },
    ["Classic Gnome Crate"] = {
        ["Color"] = Color3.fromRGB(255, 255, 255),
        ["Icon"] = "rbxassetid://135502960444244",
        ["OpenTime"] = 5400,
        ["CosmeticRolls"] = {
            ["Items"] = {
                ["Builderman Gnome"] = {
                    ["ItemOdd"] = 25,
                    ["Name"] = "Builderman Gnome"
                },
                ["Bacon Gnome"] = {
                    ["ItemOdd"] = 25,
                    ["Name"] = "Bacon Gnome"
                },
                ["Zombie Gnome"] = {
                    ["ItemOdd"] = 25,
                    ["Name"] = "Zombie Gnome"
                },
                ["Noob Gnome"] = {
                    ["ItemOdd"] = 25,
                    ["Name"] = "Noob Gnome"
                },
                ["Gold Gnome"] = {
                    ["ItemOdd"] = 1,
                    ["Name"] = "Gold Gnome"
                }
            }
        }
    },
    ["Statue Crate"] = {
        ["Color"] = Color3.fromRGB(255, 255, 255),
        ["Icon"] = "rbxassetid://131078632073571",
        ["OpenTime"] = 5400,
        ["CosmeticRolls"] = {
            ["Items"] = {
                ["Female Scarecrow"] = {
                    ["ItemOdd"] = 25,
                    ["Name"] = "Female Scarecrow"
                },
                ["Male Scarecrow"] = {
                    ["ItemOdd"] = 25,
                    ["Name"] = "Male Scarecrow"
                },
                ["Stone Mango Statue"] = {
                    ["ItemOdd"] = 10,
                    ["Name"] = "Stone Mango Statue"
                },
                ["Mossy Stone Blossom Statue"] = {
                    ["ItemOdd"] = 10,
                    ["Name"] = "Mossy Stone Blossom Statue"
                },
                ["Stone Candy Blossom Statue"] = {
                    ["ItemOdd"] = 1,
                    ["Name"] = "Stone Candy Blossom Statue"
                }
            }
        }
    },
    ["Exclusive Cosmetic Crate"] = {
        ["Color"] = Color3.fromRGB(255, 255, 255),
        ["Icon"] = "rbxassetid://140481838960536",
        ["OpenTime"] = 30,
        ["RobuxOnly"] = true,
        ["CosmeticRolls"] = {
            ["Items"] = {
                ["Stone Bunny Statue"] = {
                    ["ItemOdd"] = 37,
                    ["Name"] = "Stone Bunny Statue"
                },
                ["Gold Pillar"] = {
                    ["ItemOdd"] = 25,
                    ["Name"] = "Gold Pillar"
                },
                ["Gold Fire Pit"] = {
                    ["ItemOdd"] = 25,
                    ["Name"] = "Gold Fire Pit"
                },
                ["Gold Torii"] = {
                    ["ItemOdd"] = 5,
                    ["Name"] = "Gold Torii"
                },
                ["Gold Water Fountain"] = {
                    ["ItemOdd"] = 5,
                    ["Name"] = "Gold Water Fountain"
                },
                ["Gold Candy Blossom Statue"] = {
                    ["ItemOdd"] = 2.5,
                    ["Name"] = "Gold Candy Blossom Statue"
                },
                ["Platinum Candy Blossom Statue"] = {
                    ["ItemOdd"] = 0.5,
                    ["Name"] = "Platinum Candy Blossom Statue"
                }
            }
        }
    },
    ["Twilight Crate"] = {
        ["Color"] = Color3.fromRGB(255, 255, 255),
        ["Icon"] = "rbxassetid://140481838960536",
        ["OpenTime"] = 600,
        ["CosmeticRolls"] = {
            ["Items"] = {
                ["Twilight Fire Pit"] = {
                    ["ItemOdd"] = 20,
                    ["Name"] = "Twilight Fire Pit"
                },
                ["Twilight Fence"] = {
                    ["ItemOdd"] = 20,
                    ["Name"] = "Twilight Fence"
                },
                ["Twilight Pillar"] = {
                    ["ItemOdd"] = 20,
                    ["Name"] = "Twilight Pillar"
                },
                ["Twilight Night Owl Statue"] = {
                    ["ItemOdd"] = 20,
                    ["Name"] = "Twilight Night Owl Statue"
                },
                ["Twilight Ring Walkway"] = {
                    ["ItemOdd"] = 20,
                    ["Name"] = "Twilight Ring Walkway"
                }
            }
        }
    },
    ["Bloodmoon Crate"] = {
        ["Color"] = Color3.fromRGB(255, 255, 255),
        ["Icon"] = "rbxassetid://140481838960536",
        ["OpenTime"] = 600,
        ["CosmeticRolls"] = {
            ["Items"] = {
                ["Blood Chain"] = {
                    ["ItemOdd"] = 20,
                    ["Name"] = "Blood Chain"
                },
                ["Blood Fire Pit"] = {
                    ["ItemOdd"] = 20,
                    ["Name"] = "Blood Fire Pit"
                },
                ["Blood Fence"] = {
                    ["ItemOdd"] = 20,
                    ["Name"] = "Blood Fence"
                },
                ["Blood Bench"] = {
                    ["ItemOdd"] = 20,
                    ["Name"] = "Blood Bench"
                },
                ["Blood Lamp"] = {
                    ["ItemOdd"] = 20,
                    ["Name"] = "Blood Lamp"
                }
            }
        }
    }
}
local function v8(p2) --[[Anonymous function at line 350]]
    local v3 = p2.Items
    local v4 = 0
    for _, v5 in v3 do
        v4 = v4 + v5.ItemOdd
    end
    for v6, v7 in v3 do
        v7.NormalizedOdd = v7.ItemOdd / v4 * 100
        v7.Name = v6
    end
    p2.TotalOdds = v4
    return p2
end
for _, v9 in v1 do
    v9.Color = v9.Color or Color3.fromRGB(255, 252, 252)
    v9.OpenTime = v9.OpenTime or 20
    v9.CosmeticRolls = v9.CosmeticRolls or {
        ["Items"] = {}
    }
    v8(v9.CosmeticRolls)
end
return v1