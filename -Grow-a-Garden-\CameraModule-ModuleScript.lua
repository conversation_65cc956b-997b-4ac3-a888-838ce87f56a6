-- Full Path: -Grow-a-Garden-\\CameraModule-ModuleScript.lua
local u1 = {}
u1.__index = u1
local u2 = {
    "CameraMinZoomDistance",
    "CameraMaxZoomDistance",
    "CameraMode",
    "DevCameraOcclusionMode",
    "DevComputerCameraMode",
    "DevTouchCameraMode",
    "DevComputerMovementMode",
    "DevTouchMovementMode",
    "DevEnableMouseLock"
}
local u3 = {
    "ComputerCameraMovementMode",
    "ComputerMovementMode",
    "ControlMode",
    "GamepadCameraSensitivity",
    "MouseSensitivity",
    "RotationType",
    "TouchCameraMovementMode",
    "TouchMovementMode"
}
local u4 = game:GetService("Players")
local u5 = game:GetService("RunService")
local u6 = game:GetService("UserInputService")
local u7 = game:GetService("VRService")
local u8 = UserSettings():GetService("UserGameSettings")
local v9 = script.Parent:WaitForChild("CommonUtils")
local u10 = require(v9:WaitForChild("ConnectionUtil"))
local v11 = require(v9:WaitForChild("FlagUtil"))
local u12 = require(script:WaitForChild("CameraUtils"))
local u13 = require(script:WaitForChild("CameraInput"))
local u14 = require(script:WaitForChild("ClassicCamera"))
local u15 = require(script:WaitForChild("OrbitalCamera"))
local u16 = require(script:WaitForChild("LegacyCamera"))
local u17 = require(script:WaitForChild("VehicleCamera"))
local u18 = require(script:WaitForChild("VRCamera"))
local u19 = require(script:WaitForChild("VRVehicleCamera"))
local u20 = require(script:WaitForChild("Invisicam"))
local u21 = require(script:WaitForChild("Poppercam"))
local u22 = require(script:WaitForChild("TransparencyController"))
local u23 = require(script:WaitForChild("MouseLockController"))
local u24 = {}
local u25 = {}
if not u4.LocalPlayer then
    return {}
end
local v26 = u4.LocalPlayer
assert(v26, "Strict typing check")
local v27 = u4.LocalPlayer:WaitForChild("PlayerScripts")
v27:RegisterTouchCameraMovementMode(Enum.TouchCameraMovementMode.Default)
v27:RegisterTouchCameraMovementMode(Enum.TouchCameraMovementMode.Follow)
v27:RegisterTouchCameraMovementMode(Enum.TouchCameraMovementMode.Classic)
v27:RegisterComputerCameraMovementMode(Enum.ComputerCameraMovementMode.Default)
v27:RegisterComputerCameraMovementMode(Enum.ComputerCameraMovementMode.Follow)
v27:RegisterComputerCameraMovementMode(Enum.ComputerCameraMovementMode.Classic)
v27:RegisterComputerCameraMovementMode(Enum.ComputerCameraMovementMode.CameraToggle)
local u28 = v11.getUserFlag("UserRespectLegacyCameraOptions")
local u29 = v11.getUserFlag("UserPlayerConnectionMemoryLeak")
function u1.new() --[[Anonymous function at line 144]]
    --[[
    Upvalues:
        [1] = u22
        [2] = u29
        [3] = u10
        [4] = u1
        [5] = u4
        [6] = u6
        [7] = u23
        [8] = u28
        [9] = u5
        [10] = u2
        [11] = u3
        [12] = u8
    --]]
    local v30 = {
        ["activeTransparencyController"] = u22.new()
    }
    local v31
    if u29 then
        v31 = u10.new()
    else
        v31 = nil
    end
    v30.connectionUtil = v31
    local v32 = u1
    local u33 = setmetatable(v30, v32)
    u33.activeCameraController = nil
    u33.activeOcclusionModule = nil
    u33.activeMouseLockController = nil
    u33.currentComputerCameraMovementMode = nil
    u33.cameraSubjectChangedConn = nil
    u33.cameraTypeChangedConn = nil
    for _, v34 in pairs(u4:GetPlayers()) do
        u33:OnPlayerAdded(v34)
    end
    u4.PlayerAdded:Connect(function(p35) --[[Anonymous function at line 167]]
        --[[
        Upvalues:
            [1] = u33
        --]]
        u33:OnPlayerAdded(p35)
    end)
    if u29 then
        u4.PlayerRemoving:Connect(function(p36) --[[Anonymous function at line 172]]
            --[[
            Upvalues:
                [1] = u33
            --]]
            u33:OnPlayerRemoving(p36)
        end)
    end
    u33.activeTransparencyController:Enable(true)
    if not u6.TouchEnabled then
        u33.activeMouseLockController = u23.new()
        local v37 = u33.activeMouseLockController
        assert(v37, "Strict typing check")
        local v38 = u33.activeMouseLockController:GetBindableToggleEvent()
        if v38 then
            v38:Connect(function() --[[Anonymous function at line 185]]
                --[[
                Upvalues:
                    [1] = u33
                --]]
                u33:OnMouseLockToggled()
            end)
        end
    end
    if u28 then
        u33:ActivateCameraController()
    else
        u33:ActivateCameraController(u33:GetCameraControlChoice())
    end
    u33:ActivateOcclusionModule(u4.LocalPlayer.DevCameraOcclusionMode)
    u33:OnCurrentCameraChanged()
    u5:BindToRenderStep("cameraRenderUpdate", Enum.RenderPriority.Camera.Value, function(p39) --[[Anonymous function at line 198]]
        --[[
        Upvalues:
            [1] = u33
        --]]
        u33:Update(p39)
    end)
    for _, u40 in pairs(u2) do
        u4.LocalPlayer:GetPropertyChangedSignal(u40):Connect(function() --[[Anonymous function at line 202]]
            --[[
            Upvalues:
                [1] = u33
                [2] = u40
            --]]
            u33:OnLocalPlayerCameraPropertyChanged(u40)
        end)
    end
    for _, u41 in pairs(u3) do
        u8:GetPropertyChangedSignal(u41):Connect(function() --[[Anonymous function at line 208]]
            --[[
            Upvalues:
                [1] = u33
                [2] = u41
            --]]
            u33:OnUserGameSettingsPropertyChanged(u41)
        end)
    end
    game.Workspace:GetPropertyChangedSignal("CurrentCamera"):Connect(function() --[[Anonymous function at line 212]]
        --[[
        Upvalues:
            [1] = u33
        --]]
        u33:OnCurrentCameraChanged()
    end)
    return u33
end
function u1.GetCameraMovementModeFromSettings(_) --[[Anonymous function at line 219]]
    --[[
    Upvalues:
        [1] = u4
        [2] = u12
        [3] = u6
        [4] = u8
    --]]
    if u4.LocalPlayer.CameraMode == Enum.CameraMode.LockFirstPerson then
        return u12.ConvertCameraModeEnumToStandard(Enum.ComputerCameraMovementMode.Classic)
    else
        local v42, v43
        if u6.TouchEnabled then
            v42 = u12.ConvertCameraModeEnumToStandard(u4.LocalPlayer.DevTouchCameraMode)
            v43 = u12.ConvertCameraModeEnumToStandard(u8.TouchCameraMovementMode)
        else
            v42 = u12.ConvertCameraModeEnumToStandard(u4.LocalPlayer.DevComputerCameraMode)
            v43 = u12.ConvertCameraModeEnumToStandard(u8.ComputerCameraMovementMode)
        end
        if v42 == Enum.DevComputerCameraMovementMode.UserChoice then
            return v43
        else
            return v42
        end
    end
end
function u1.ActivateOcclusionModule(p44, p45) --[[Anonymous function at line 244]]
    --[[
    Upvalues:
        [1] = u21
        [2] = u20
        [3] = u25
        [4] = u4
    --]]
    local v46
    if p45 == Enum.DevCameraOcclusionMode.Zoom then
        v46 = u21
    else
        if p45 ~= Enum.DevCameraOcclusionMode.Invisicam then
            warn("CameraScript ActivateOcclusionModule called with unsupported mode")
            return
        end
        v46 = u20
    end
    p44.occlusionMode = p45
    if p44.activeOcclusionModule and p44.activeOcclusionModule:GetOcclusionMode() == p45 then
        if not p44.activeOcclusionModule:GetEnabled() then
            p44.activeOcclusionModule:Enable(true)
        end
    else
        local v47 = p44.activeOcclusionModule
        p44.activeOcclusionModule = u25[v46]
        if not p44.activeOcclusionModule then
            p44.activeOcclusionModule = v46.new()
            if p44.activeOcclusionModule then
                u25[v46] = p44.activeOcclusionModule
            end
        end
        if p44.activeOcclusionModule then
            if p44.activeOcclusionModule:GetOcclusionMode() ~= p45 then
                warn("CameraScript ActivateOcclusionModule mismatch: ", p44.activeOcclusionModule:GetOcclusionMode(), "~=", p45)
            end
            if v47 then
                if v47 == p44.activeOcclusionModule then
                    warn("CameraScript ActivateOcclusionModule failure to detect already running correct module")
                else
                    v47:Enable(false)
                end
            end
            if p45 == Enum.DevCameraOcclusionMode.Invisicam then
                if u4.LocalPlayer.Character then
                    p44.activeOcclusionModule:CharacterAdded(u4.LocalPlayer.Character, u4.LocalPlayer)
                end
            else
                for _, v48 in pairs(u4:GetPlayers()) do
                    if v48 and v48.Character then
                        p44.activeOcclusionModule:CharacterAdded(v48.Character, v48)
                    end
                end
                p44.activeOcclusionModule:OnCameraSubjectChanged(game.Workspace.CurrentCamera.CameraSubject)
            end
            p44.activeOcclusionModule:Enable(true)
        end
    end
end
function u1.ShouldUseVehicleCamera(p49) --[[Anonymous function at line 323]]
    local v50 = workspace.CurrentCamera
    if not v50 then
        return false
    end
    local v51 = v50.CameraType
    local v52 = v50.CameraSubject
    local v53 = v51 == Enum.CameraType.Custom and true or v51 == Enum.CameraType.Follow
    local v54 = v52 and v52:IsA("VehicleSeat") or false
    local v55 = p49.occlusionMode ~= Enum.DevCameraOcclusionMode.Invisicam
    if v54 then
        if not v53 then
            v55 = v53
        end
    else
        v55 = v54
    end
    return v55
end
function u1.ActivateCameraController(p56, p57, p58) --[[Anonymous function at line 339]]
    --[[
    Upvalues:
        [1] = u28
        [2] = u16
        [3] = u7
        [4] = u18
        [5] = u14
        [6] = u15
        [7] = u19
        [8] = u17
        [9] = u24
    --]]
    if u28 then
        p58 = workspace.CurrentCamera.CameraType
        p57 = p56:GetCameraMovementModeFromSettings()
    end
    local v59 = nil
    if u28 and true or p58 ~= nil then
        if p58 == Enum.CameraType.Scriptable then
            if p56.activeCameraController then
                p56.activeCameraController:Enable(false)
                p56.activeCameraController = nil
            end
            return
        end
        if p58 == Enum.CameraType.Custom then
            p57 = p56:GetCameraMovementModeFromSettings()
        elseif p58 == Enum.CameraType.Track then
            p57 = Enum.ComputerCameraMovementMode.Classic
        elseif p58 == Enum.CameraType.Follow then
            p57 = Enum.ComputerCameraMovementMode.Follow
        elseif p58 == Enum.CameraType.Orbital then
            p57 = Enum.ComputerCameraMovementMode.Orbital
        elseif p58 == Enum.CameraType.Attach or (p58 == Enum.CameraType.Watch or p58 == Enum.CameraType.Fixed) then
            v59 = u16
        else
            warn("CameraScript encountered an unhandled Camera.CameraType value: ", p58)
        end
    end
    if not v59 then
        if u7.VREnabled then
            v59 = u18
        elseif p57 == Enum.ComputerCameraMovementMode.Classic or (p57 == Enum.ComputerCameraMovementMode.Follow or (p57 == Enum.ComputerCameraMovementMode.Default or p57 == Enum.ComputerCameraMovementMode.CameraToggle)) then
            v59 = u14
        else
            if p57 ~= Enum.ComputerCameraMovementMode.Orbital then
                warn("ActivateCameraController did not select a module.")
                return
            end
            v59 = u15
        end
    end
    if p56:ShouldUseVehicleCamera() then
        if u7.VREnabled then
            v59 = u19
        else
            v59 = u17
        end
    end
    local v60
    if u24[v59] then
        v60 = u24[v59]
        if v60.Reset then
            v60:Reset()
        end
    else
        v60 = v59.new()
        u24[v59] = v60
    end
    if p56.activeCameraController then
        if p56.activeCameraController == v60 then
            if not p56.activeCameraController:GetEnabled() then
                p56.activeCameraController:Enable(true)
            end
        else
            p56.activeCameraController:Enable(false)
            p56.activeCameraController = v60
            p56.activeCameraController:Enable(true)
        end
    elseif v60 ~= nil then
        p56.activeCameraController = v60
        local v61 = p56.activeCameraController
        assert(v61, "Strict typing check")
        p56.activeCameraController:Enable(true)
    end
    if p56.activeCameraController then
        if u28 then
            p56.activeCameraController:SetCameraMovementMode(p57)
            p56.activeCameraController:SetCameraType(p58)
            return
        end
        if p57 ~= nil then
            p56.activeCameraController:SetCameraMovementMode(p57)
            return
        end
        if p58 ~= nil then
            p56.activeCameraController:SetCameraType(p58)
        end
    end
end
function u1.OnCameraSubjectChanged(p62) --[[Anonymous function at line 453]]
    local v63 = workspace.CurrentCamera
    local v64
    if v63 then
        v64 = v63.CameraSubject
    else
        v64 = nil
    end
    if p62.activeTransparencyController then
        p62.activeTransparencyController:SetSubject(v64)
    end
    if p62.activeOcclusionModule then
        p62.activeOcclusionModule:OnCameraSubjectChanged(v64)
    end
    local v65 = nil
    local v66
    if v63 then
        v66 = v63.CameraType
    else
        v66 = nil
    end
    p62:ActivateCameraController(v65, v66)
end
function u1.OnCameraTypeChanged(p67, p68) --[[Anonymous function at line 468]]
    --[[
    Upvalues:
        [1] = u6
        [2] = u12
    --]]
    if p68 == Enum.CameraType.Scriptable and u6.MouseBehavior == Enum.MouseBehavior.LockCenter then
        u12.restoreMouseBehavior()
    end
    p67:ActivateCameraController(nil, p68)
end
function u1.OnCurrentCameraChanged(u69) --[[Anonymous function at line 480]]
    local u70 = game.Workspace.CurrentCamera
    if u70 then
        if u69.cameraSubjectChangedConn then
            u69.cameraSubjectChangedConn:Disconnect()
        end
        if u69.cameraTypeChangedConn then
            u69.cameraTypeChangedConn:Disconnect()
        end
        u69.cameraSubjectChangedConn = u70:GetPropertyChangedSignal("CameraSubject"):Connect(function() --[[Anonymous function at line 492]]
            --[[
            Upvalues:
                [1] = u69
            --]]
            u69:OnCameraSubjectChanged()
        end)
        u69.cameraTypeChangedConn = u70:GetPropertyChangedSignal("CameraType"):Connect(function() --[[Anonymous function at line 496]]
            --[[
            Upvalues:
                [1] = u69
                [2] = u70
            --]]
            u69:OnCameraTypeChanged(u70.CameraType)
        end)
        u69:OnCameraSubjectChanged()
        u69:OnCameraTypeChanged(u70.CameraType)
    end
end
function u1.OnLocalPlayerCameraPropertyChanged(p71, p72) --[[Anonymous function at line 504]]
    --[[
    Upvalues:
        [1] = u4
        [2] = u12
    --]]
    if p72 == "CameraMode" then
        if u4.LocalPlayer.CameraMode ~= Enum.CameraMode.LockFirstPerson then
            if u4.LocalPlayer.CameraMode == Enum.CameraMode.Classic then
                local v73 = p71:GetCameraMovementModeFromSettings()
                p71:ActivateCameraController(u12.ConvertCameraModeEnumToStandard(v73))
            else
                warn("Unhandled value for property player.CameraMode: ", u4.LocalPlayer.CameraMode)
            end
        end
        if not p71.activeCameraController or p71.activeCameraController:GetModuleName() ~= "ClassicCamera" then
            p71:ActivateCameraController(u12.ConvertCameraModeEnumToStandard(Enum.DevComputerCameraMovementMode.Classic))
        end
        if p71.activeCameraController then
            p71.activeCameraController:UpdateForDistancePropertyChange()
            return
        end
    else
        if p72 == "DevComputerCameraMode" or p72 == "DevTouchCameraMode" then
            local v74 = p71:GetCameraMovementModeFromSettings()
            p71:ActivateCameraController(u12.ConvertCameraModeEnumToStandard(v74))
            return
        end
        if p72 == "DevCameraOcclusionMode" then
            p71:ActivateOcclusionModule(u4.LocalPlayer.DevCameraOcclusionMode)
            return
        end
        if p72 == "CameraMinZoomDistance" or p72 == "CameraMaxZoomDistance" then
            if p71.activeCameraController then
                p71.activeCameraController:UpdateForDistancePropertyChange()
                return
            end
        else
            if p72 == "DevTouchMovementMode" then
                return
            end
            if p72 == "DevComputerMovementMode" then
                return
            end
            local _ = p72 == "DevEnableMouseLock"
        end
    end
end
function u1.OnUserGameSettingsPropertyChanged(p75, p76) --[[Anonymous function at line 548]]
    --[[
    Upvalues:
        [1] = u12
    --]]
    if p76 == "ComputerCameraMovementMode" then
        local v77 = p75:GetCameraMovementModeFromSettings()
        p75:ActivateCameraController(u12.ConvertCameraModeEnumToStandard(v77))
    end
end
function u1.Update(p78, p79) --[[Anonymous function at line 561]]
    --[[
    Upvalues:
        [1] = u13
    --]]
    if p78.activeCameraController then
        p78.activeCameraController:UpdateMouseBehavior()
        local v80, v81 = p78.activeCameraController:Update(p79)
        if p78.activeOcclusionModule then
            v80, v81 = p78.activeOcclusionModule:Update(p79, v80, v81)
        end
        local v82 = game.Workspace.CurrentCamera
        v82.CFrame = v80
        v82.Focus = v81
        if p78.activeTransparencyController then
            p78.activeTransparencyController:Update(p79)
        end
        if u13.getInputEnabled() then
            u13.resetInputForFrameEnd()
        end
    end
end
function u1.GetCameraControlChoice(_) --[[Anonymous function at line 589]]
    --[[
    Upvalues:
        [1] = u28
        [2] = u6
        [3] = u4
        [4] = u12
        [5] = u8
    --]]
    local v83 = not u28
    assert(v83, "CameraModule:GetCameraControlChoice should not be called when FFlagUserRespectLegacyCameraOptions is enabled")
    if u6:GetLastInputType() == Enum.UserInputType.Touch or u6.TouchEnabled then
        if u4.LocalPlayer.DevTouchCameraMode == Enum.DevTouchCameraMovementMode.UserChoice then
            return u12.ConvertCameraModeEnumToStandard(u8.TouchCameraMovementMode)
        else
            return u12.ConvertCameraModeEnumToStandard(u4.LocalPlayer.DevTouchCameraMode)
        end
    else
        if u4.LocalPlayer.DevComputerCameraMode ~= Enum.DevComputerCameraMovementMode.UserChoice then
            return u12.ConvertCameraModeEnumToStandard(u4.LocalPlayer.DevComputerCameraMode)
        end
        local v84 = u12.ConvertCameraModeEnumToStandard(u8.ComputerCameraMovementMode)
        return u12.ConvertCameraModeEnumToStandard(v84)
    end
end
function u1.OnCharacterAdded(p85, p86, p87) --[[Anonymous function at line 609]]
    if p85.activeOcclusionModule then
        p85.activeOcclusionModule:CharacterAdded(p86, p87)
    end
end
function u1.OnCharacterRemoving(p88, p89, p90) --[[Anonymous function at line 615]]
    if p88.activeOcclusionModule then
        p88.activeOcclusionModule:CharacterRemoving(p89, p90)
    end
end
function u1.OnPlayerAdded(u91, u92) --[[Anonymous function at line 621]]
    --[[
    Upvalues:
        [1] = u29
    --]]
    if u29 then
        if u91.connectionUtil then
            u91.connectionUtil:trackConnection(("%*CharacterAdded"):format(u92.UserId), u92.CharacterAdded:Connect(function(p93) --[[Anonymous function at line 625]]
                --[[
                Upvalues:
                    [1] = u91
                    [2] = u92
                --]]
                u91:OnCharacterAdded(p93, u92)
            end))
            u91.connectionUtil:trackConnection(("%*CharacterRemoving"):format(u92.UserId), u92.CharacterRemoving:Connect(function(p94) --[[Anonymous function at line 628]]
                --[[
                Upvalues:
                    [1] = u91
                    [2] = u92
                --]]
                u91:OnCharacterRemoving(p94, u92)
            end))
            return
        end
    else
        u92.CharacterAdded:Connect(function(p95) --[[Anonymous function at line 633]]
            --[[
            Upvalues:
                [1] = u91
                [2] = u92
            --]]
            u91:OnCharacterAdded(p95, u92)
        end)
        u92.CharacterRemoving:Connect(function(p96) --[[Anonymous function at line 636]]
            --[[
            Upvalues:
                [1] = u91
                [2] = u92
            --]]
            u91:OnCharacterRemoving(p96, u92)
        end)
    end
end
function u1.OnPlayerRemoving(p97, p98) --[[Anonymous function at line 642]]
    if p97.connectionUtil then
        p97.connectionUtil:disconnect((("%*CharacterAdded"):format(p98.UserId)))
        p97.connectionUtil:disconnect((("%*CharacterRemoving"):format(p98.UserId)))
    end
end
function u1.OnMouseLockToggled(p99) --[[Anonymous function at line 650]]
    if p99.activeMouseLockController then
        local v100 = p99.activeMouseLockController:GetIsMouseLocked()
        local v101 = p99.activeMouseLockController:GetMouseLockOffset()
        if p99.activeCameraController then
            p99.activeCameraController:SetIsMouseLocked(v100)
            p99.activeCameraController:SetMouseLockOffset(v101)
        end
    end
end
u1.new()
return {}