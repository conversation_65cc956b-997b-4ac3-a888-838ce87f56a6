-- Full Path: -Grow-a-Garden-\StarterPlayer\StarterPlayerScripts\DiscoAnimator-LocalScript.lua
local u1 = game:GetService("CollectionService")
local u2 = game:GetService("TweenService")
game:GetService("RunService")
local u3 = TweenInfo.new(1, Enum.EasingStyle.Linear, Enum.EasingDirection.InOut)
local u4 = TweenInfo.new(2, Enum.EasingStyle.Linear, Enum.EasingDirection.InOut)
local u5 = 1
local u6 = {
    Color3.fromHex("#ff50b3"),
    Color3.fromHex("#fff94a"),
    Color3.fromHex("#49ff46"),
    Color3.fromHex("#4ee2ff"),
    Color3.fromHex("#ff4646")
}
workspace:GetAttributeChangedSignal("DiscoEvent"):Connect(function() --[[Anonymous function at line 18]]
    --[[
    Upvalues:
        [1] = u2
        [2] = u3
        [3] = u4
    --]]
    local v7 = {
        Color3.fromHex("#FF007F"),
        Color3.fromHex("#FFD700"),
        Color3.fromHex("#00FFFF"),
        Color3.fromHex("#7FFF00"),
        Color3.fromHex("#8A2BE2")
    }
    local v8 = nil
    if workspace:GetAttribute("DiscoEvent") then
        u2:Create(game.Lighting.ColorCorrection, u3, {
            ["Saturation"] = 0.3
        }):Play()
        while workspace:GetAttribute("DiscoEvent") do
            for _, v9 in v7 do
                u2:Create(game.Lighting, u4, {
                    ["ColorShift_Top"] = v9
                }):Play()
                task.wait(1)
                if not workspace:GetAttribute("DiscoEvent") then
                    break
                end
            end
        end
    else
        if v8 then
            v8:Cancel()
        end
        u2:Create(game.Lighting.ColorCorrection, u3, {
            ["Saturation"] = 0
        }):Play()
        task.delay(1.5, function() --[[Anonymous function at line 51]]
            --[[
            Upvalues:
                [1] = u2
                [2] = u3
            --]]
            u2:Create(game.Lighting, u3, {
                ["ColorShift_Top"] = Color3.fromRGB(0, 0, 0)
            }):Play()
        end)
    end
end)
task.spawn(function() --[[Anonymous function at line 57]]
    --[[
    Upvalues:
        [1] = u1
        [2] = u6
        [3] = u5
    --]]
    while true do
        local v10 = u1:GetTagged("Discofied")
        local v11 = u6[u5]
        for _, v12 in v10 do
            v12.Color = v11
        end
        task.wait(1)
        u5 = u5 + 1
        if u5 > 5 then
            u5 = 1
        end
    end
end)