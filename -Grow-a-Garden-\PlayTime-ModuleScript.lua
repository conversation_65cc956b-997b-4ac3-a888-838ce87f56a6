-- Full Path: -Grow-a-Garden-\\PlayTime-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
require(v1.Data.QuestData.Types)
local u2 = require(v1.Modules.TimeHelper)
return {
    ["Type"] = "PlayTime",
    ["Display"] = function(_, p3, p4) --[[Function name: Display, line 11]]
        --[[
        Upvalues:
            [1] = u2
        --]]
        local v5 = u2:GenerateColonFormatFromTime(p4)
        return {
            ["Title"] = ("Play for %*"):format(v5),
            ["Bar"] = ("%*/%*"):format(u2:GenerateColonFormatFromTime(p3), v5)
        }
    end,
    ["Use"] = function(p6, p7) --[[Function name: Use, line 20]]
        local v8 = table.clone(p7)
        v8.Type = p6.Type
        return v8
    end
}