-- Full Path: -Grow-a-Garden-\ReplicatedStorage\CandySunflowerRandomizer-ModuleScript.lua
local u55 = {
    ["RunWhitePetals"] = function(p1, p2, p3) --[[Function name: RunWhitePetals, line 8]]
        local v4 = p1.Head.Head.Petals
        for _, v5 in v4:GetChildren() do
            if v5.Name == "FlowerPedalWhite" then
                v5:Destroy()
            end
        end
        local v6 = p1.Head.Head.Pivot
        local v7 = script.FlowerPedalWhite
        local v8 = math.random(-15, 30) * 0.01
        for v9 = 0, p2 - 1, 2 do
            local v10 = 6.283185307179586 / p2 * v9
            local v11 = v6.Position
            local v12 = v6.CFrame
            local v13 = math.cos(v10) * 0.5
            local v14 = math.sin(v10) * 0.5
            local v15 = v11 + v12:VectorToWorldSpace(Vector3.new(v13, 0, v14) + Vector3.new(0, 0.25, 0))
            local _ = (v6.Position - v15).Unit
            local v16 = v7:Clone()
            v16.CFrame = CFrame.lookAt(v15, v6.Position, v6.CFrame.UpVector) * CFrame.new(0, -0.3, 0)
            v16.CFrame = v16.CFrame * CFrame.Angles(0, p3, 0)
            if p2 < 20 then
                local v17 = v16.Size
                local v18 = p2 * 0.005
                v16.Size = v17 + Vector3.new(v18, 0, v8)
            end
            v16.Parent = v4
        end
    end,
    ["RunRedPetals"] = function(p19, p20, p21) --[[Function name: RunRedPetals, line 46]]
        local v22 = p19.Head.Head.Petals
        for _, v23 in v22:GetChildren() do
            if v23.Name == "FlowerPedalRed" then
                v23:Destroy()
            end
        end
        local v24 = p19.Head.Head.Pivot
        local v25 = script.FlowerPedalRed
        local v26 = math.random(-15, 30) * 0.01
        for v27 = 1, p20 - 1, 2 do
            local v28 = 6.283185307179586 / p20 * v27
            local v29 = v24.Position
            local v30 = v24.CFrame
            local v31 = math.cos(v28) * 0.7
            local v32 = math.sin(v28) * 0.7
            local v33 = v29 + v30:VectorToWorldSpace(Vector3.new(v31, 0, v32) + Vector3.new(0, 0.12, 0))
            local _ = (v24.Position - v33).Unit
            local v34 = v25:Clone()
            v34.CFrame = CFrame.lookAt(v33, v24.Position, v24.CFrame.UpVector) * CFrame.new(0, -0.15, 0)
            v34.CFrame = v34.CFrame * CFrame.Angles(0, p21, 0)
            if p20 < 20 then
                local v35 = v34.Size
                local v36 = p20 * 0.008
                v34.Size = v35 - Vector3.new(v36, 0, v26)
            end
            v34.Parent = v22
        end
    end,
    ["RunSize"] = function(p37) --[[Function name: RunSize, line 85]]
        p37:ScaleTo(math.random(90, 115) * 0.01)
        local v38 = p37.Stem.Stem
        local v39 = p37.Stem.Stem.Size.Y
        v38.Size = Vector3.new(0.15, v39, 0.15)
    end,
    ["RunLeaves"] = function(p40) --[[Function name: RunLeaves, line 91]]
        local v41 = p40.Stem.Stem
        local v42 = p40.Stem.Ground
        for _, v43 in p40.Leaves:GetChildren() do
            local v44 = (v42.Position.Y + v41.Size.Y) * 0.6
            local v45 = v43.Position.X
            local v46 = v44 + math.random(-20, 10) * 0.1
            local v47 = v43.Position.Z
            v43.Position = Vector3.new(v45, v46, v47)
        end
    end,
    ["RunAll"] = function(p48) --[[Function name: RunAll, line 104]]
        --[[
        Upvalues:
            [1] = u55
        --]]
        local v49 = math.random(16, 24)
        p48:ScaleTo(1)
        if v49 % 2 ~= 0 then
            v49 = v49 + 1
        end
        local v50 = p48.Head.Head.Pivot
        local v51 = v50.CFrame
        local v52 = CFrame.Angles
        local v53 = math.random(-180, 180)
        v50.CFrame = v51 * v52(0, math.rad(v53), 0)
        local v54 = 3.141592653589793 / math.random(4, 10)
        if math.random(1, 2) == 1 then
            v54 = -v54
        end
        u55.RunWhitePetals(p48, v49, v54)
        u55.RunRedPetals(p48, v49, v54)
        u55.RunSize(p48)
        u55.RunLeaves(p48)
    end
}
return u55