-- Full Path: -Grow-a-Garden-\\RobloxTranslator-ModuleScript.lua
local u1 = game:GetService("LocalizationService")
local u2 = game.Players.LocalPlayer.PlayerGui
u2:WaitFor<PERSON>hild("BackpackGui")
local v3 = game:GetService("Players")
if v3.LocalPlayer == nil then
    v3:GetPropertyChangedSignal("LocalPlayer"):Wait()
end
local u4 = nil
local u5 = {}
return {
    ["FormatByKey"] = function(_, p6, p7) --[[Function name: FormatByKey, line 54]]
        --[[
        Upvalues:
            [1] = u4
            [2] = u2
            [3] = u1
        --]]
        if u4 == nil then
            u4 = u2.CoreScriptLocalization:GetTranslator(u1.RobloxLocaleId)
        end
        return u4:FormatByKey(p6, p7)
    end,
    ["FormatByKeyForLocale"] = function(_, p8, p9, p10) --[[Function name: FormatByKeyForLocale, line 62]]
        --[[
        Upvalues:
            [1] = u5
            [2] = u2
        --]]
        local v11 = u5[p9]
        if not v11 then
            v11 = u2.CoreScriptLocalization:GetTranslator(p9)
            u5[p9] = v11
        end
        return v11:FormatByKey(p8, p10)
    end
}