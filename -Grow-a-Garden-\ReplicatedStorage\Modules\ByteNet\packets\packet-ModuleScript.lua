-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\ByteNet\packets\packet-ModuleScript.lua
local u1 = game:GetService("Players")
local v2 = game:GetService("RunService")
require(script.Parent.Parent.types)
local u3 = require(script.Parent.Parent.process.client)
local u4 = require(script.Parent.Parent.process.server)
local u5 = v2:IsServer() and "server" or "client"
return function(p6, u7) --[[Anonymous function at line 16]]
    --[[
    Upvalues:
        [1] = u4
        [2] = u3
        [3] = u5
        [4] = u1
    --]]
    local v8 = p6.reliabilityType or "reliable"
    local u9 = {}
    local u10
    if v8 == "reliable" then
        u10 = u4.sendPlayerReliable
    else
        u10 = u4.sendPlayerUnreliable
    end
    local u11
    if v8 == "reliable" then
        u11 = u4.sendAllReliable
    else
        u11 = u4.sendAllUnreliable
    end
    local u12
    if v8 == "reliable" then
        u12 = u3.sendReliable
    else
        u12 = u3.sendUnreliable
    end
    local u13 = p6.value.write
    local v14 = {}
    local v16 = {
        ["__index"] = function(p15) --[[Function name: __index, line 43]]
            --[[
            Upvalues:
                [1] = u5
            --]]
            if (p15 == "sendTo" or (p15 == "sendToAllExcept" or p15 == "sendToAll")) and u5 == "client" then
                error("You cannot use sendTo, sendToAllExcept, or sendToAll on the client")
            elseif p15 == "send" and u5 == "server" then
                error("You cannot use send on the server")
            end
        end
    }
    setmetatable(v14, v16)
    v14.reader = p6.value.read
    if u5 == "server" then
        function v14.sendToList(p17, p18) --[[Anonymous function at line 59]]
            --[[
            Upvalues:
                [1] = u10
                [2] = u7
                [3] = u13
            --]]
            for _, v19 in p18 do
                u10(v19, u7, u13, p17)
            end
        end
        function v14.sendTo(p20, p21) --[[Anonymous function at line 65]]
            --[[
            Upvalues:
                [1] = u10
                [2] = u7
                [3] = u13
            --]]
            u10(p21, u7, u13, p20)
        end
        function v14.sendToAllExcept(p22, p23) --[[Anonymous function at line 69]]
            --[[
            Upvalues:
                [1] = u1
                [2] = u10
                [3] = u7
                [4] = u13
            --]]
            for _, v24 in u1:GetPlayers() do
                if v24 ~= p23 then
                    u10(v24, u7, u13, p22)
                end
            end
        end
        function v14.sendToAll(p25) --[[Anonymous function at line 77]]
            --[[
            Upvalues:
                [1] = u11
                [2] = u7
                [3] = u13
            --]]
            u11(u7, u13, p25)
        end
    elseif u5 == "client" then
        function v14.send(p26) --[[Anonymous function at line 81]]
            --[[
            Upvalues:
                [1] = u12
                [2] = u7
                [3] = u13
            --]]
            u12(u7, u13, p26)
        end
    end
    function v14.wait() --[[Anonymous function at line 86]]
        --[[
        Upvalues:
            [1] = u9
        --]]
        local u27 = nil
        local u28 = coroutine.running()
        local v29 = u9
        local function v32(p30, p31) --[[Anonymous function at line 91]]
            --[[
            Upvalues:
                [1] = u28
                [2] = u9
                [3] = u27
            --]]
            task.spawn(u28, p30, p31)
            table.remove(u9, u27)
        end
        table.insert(v29, v32)
        u27 = #u9
        return coroutine.yield()
    end
    function v14.listen(p33) --[[Anonymous function at line 105]]
        --[[
        Upvalues:
            [1] = u9
        --]]
        local v34 = u9
        table.insert(v34, p33)
    end
    function v14.getListeners() --[[Anonymous function at line 109]]
        --[[
        Upvalues:
            [1] = u9
        --]]
        return u9
    end
    return v14
end