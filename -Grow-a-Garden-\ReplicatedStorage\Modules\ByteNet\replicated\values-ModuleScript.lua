-- Full Path: -Grow-a-Garden-\ReplicatedStorage\Modules\ByteNet\replicated\values-ModuleScript.lua
local u1 = game:GetService("ReplicatedStorage")
local v2 = game:GetService("RunService")
local u3 = require(script.Parent.replicatedValue)
local u4 = v2:IsServer() and "server" or "client"
local u5 = nil
local u6 = {}
return {
    ["start"] = function() --[[Function name: start, line 14]]
        --[[
        Upvalues:
            [1] = u4
            [2] = u1
            [3] = u5
        --]]
        if u4 == "server" then
            local v7 = Instance.new("Folder")
            v7.Name = "BytenetStorage"
            v7.Parent = u1
            u5 = v7
        elseif u4 == "client" then
            u5 = u1:WaitForChild("BytenetStorage")
        end
    end,
    ["access"] = function(p8) --[[Function name: access, line 26]]
        --[[
        Upvalues:
            [1] = u6
            [2] = u4
            [3] = u5
            [4] = u3
        --]]
        if u6[p8] then
            return u6[p8]
        end
        if u4 == "client" then
            local v9 = u5:FindFirstChild(p8)
            if v9 and v9:IsA("StringValue") then
                local v10 = u3(v9)
                u6[p8] = v10
                return v10
            end
        elseif u4 == "server" then
            local v11 = Instance.new("StringValue")
            v11.Name = p8
            v11.Parent = u5
            local v12 = u3(v11)
            u6[p8] = v12
            return v12
        end
        return u6[p8]
    end
}