-- Full Path: -Grow-a-Garden-\\EasterData-ModuleScript.lua
local v1 = game.ReplicatedStorage.Seed_Models
local _ = game.ReplicatedStorage.ObjectModels
local v2 = {
    ["Chocolate Carrot"] = {
        ["SeedName"] = "Chocolate Carrot Seed",
        ["SeedRarity"] = "Common",
        ["StockChance"] = 1,
        ["StockAmount"] = { 5, 25 },
        ["Price"] = 10000,
        ["PurchaseID"] = 3268186353,
        ["DisplayInShop"] = true,
        ["LayoutOrder"] = 1,
        ["Asset"] = v1["Chocolate Carrot"],
        ["IsGear"] = false,
        ["Stack"] = 1,
        ["Description"] = ""
    },
    ["Red Lollipop"] = {
        ["SeedName"] = "Red Lollipop",
        ["SeedRarity"] = "Uncommon",
        ["StockChance"] = 2,
        ["StockAmount"] = { 5, 25 },
        ["Price"] = 45000,
        ["PurchaseID"] = 3268186603,
        ["DisplayInShop"] = true,
        ["LayoutOrder"] = 2,
        ["Asset"] = v1["Red Lollipop"],
        ["IsGear"] = false,
        ["Stack"] = 1,
        ["Description"] = ""
    },
    ["Candy Sunflower"] = {
        ["SeedName"] = "Candy Sunflower",
        ["SeedRarity"] = "Rare",
        ["StockChance"] = 3,
        ["StockAmount"] = { 3, 10 },
        ["Price"] = 75000,
        ["PurchaseID"] = 3268187175,
        ["DisplayInShop"] = true,
        ["LayoutOrder"] = 4,
        ["Asset"] = v1["Candy Sunflower"],
        ["IsGear"] = false,
        ["Stack"] = 1,
        ["Description"] = ""
    },
    ["Easter Egg"] = {
        ["SeedName"] = "Easter Egg",
        ["SeedRarity"] = "Legendary",
        ["StockChance"] = 4,
        ["StockAmount"] = { 3, 5 },
        ["Price"] = 500000,
        ["PurchaseID"] = 3268187332,
        ["DisplayInShop"] = true,
        ["LayoutOrder"] = 5,
        ["Asset"] = v1["Easter Egg"],
        ["IsGear"] = false,
        ["Stack"] = 1,
        ["Description"] = ""
    },
    ["Chocolate Sprinkler"] = {
        ["SeedName"] = "Chocolate Sprinkler",
        ["SeedRarity"] = "Mythical",
        ["StockChance"] = 1,
        ["StockAmount"] = { 1, 3 },
        ["Price"] = 500000,
        ["PurchaseID"] = 3268187887,
        ["DisplayInShop"] = true,
        ["LayoutOrder"] = 6,
        ["Asset"] = "rbxassetid://115881753102059",
        ["IsGear"] = true,
        ["Stack"] = 1,
        ["Description"] = "Covers plants in chocolate! Lasts 1 minute"
    },
    ["Candy Blossom"] = {
        ["SeedName"] = "Candy Blossom",
        ["SeedRarity"] = "Divine",
        ["StockChance"] = 12,
        ["StockAmount"] = { 1, 1 },
        ["Price"] = 10000000,
        ["PurchaseID"] = 3268187638,
        ["DisplayInShop"] = true,
        ["LayoutOrder"] = 7,
        ["Asset"] = v1["Candy Blossom"],
        ["IsGear"] = false,
        ["Stack"] = 1,
        ["Description"] = ""
    },
    ["Night Staff"] = {
        ["GearName"] = "Night Staff",
        ["GearRarity"] = "Common",
        ["StockChance"] = 1,
        ["StockAmount"] = { 1, 3 },
        ["Price"] = 1000,
        ["PurchaseID"] = 0,
        ["Stack"] = 1,
        ["LayoutOrder"] = 4,
        ["Asset"] = "rbxassetid://115261280019001",
        ["GearDescription"] = ""
    },
    ["Star Caller"] = {
        ["GearName"] = "Star Caller",
        ["GearRarity"] = "Common",
        ["StockChance"] = 1,
        ["StockAmount"] = { 1, 3 },
        ["Price"] = 1000,
        ["PurchaseID"] = 0,
        ["Stack"] = 1,
        ["LayoutOrder"] = 4,
        ["Asset"] = "rbxassetid://115261280019001",
        ["GearDescription"] = ""
    }
}
return v2