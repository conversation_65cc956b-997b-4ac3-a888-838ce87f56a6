-- Full Path: -Grow-a-Garden-\ReplicatedStorage\CmdrClient\Types\Mutation-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
local u2 = require(script.Parent.Parent.Shared.Util)
local v3 = require(v1.Modules.MutationHandler)
require(v1.Data.GearData)
local u4 = { "Gold", "Rainbow" }
for v5 in v3:GetMutations() do
    table.insert(u4, v5)
end
local u10 = {
    ["Transform"] = function(p6) --[[Function name: Transform, line 19]]
        --[[
        Upvalues:
            [1] = u2
            [2] = u4
        --]]
        return u2.MakeFuzzyFinder(u4)(p6)
    end,
    ["Validate"] = function(p7) --[[Function name: Validate, line 25]]
        return #p7 > 0, "No seeds could be found."
    end,
    ["Autocomplete"] = function(p8) --[[Function name: Autocomplete, line 29]]
        --[[
        Upvalues:
            [1] = u2
        --]]
        return u2.GetNames(p8)
    end,
    ["Parse"] = function(p9) --[[Function name: Parse, line 33]]
        return p9[1]
    end,
    ["Default"] = function(_) --[[Function name: Default, line 37]]
        return "Gold"
    end,
    ["ArgumentOperatorAliases"] = {
        ["me"] = ".",
        ["all"] = "*",
        ["others"] = "**",
        ["random"] = "?"
    }
}
return function(p11) --[[Anonymous function at line 49]]
    --[[
    Upvalues:
        [1] = u10
    --]]
    p11:RegisterType("mutation", u10)
end