-- Auto Sell Fruits Loader
-- This script loads and initializes the AutoSellController

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")

local player = Players.LocalPlayer

-- Wait for character to spawn
local function waitF<PERSON><PERSON><PERSON><PERSON>()
    if player.Character then
        return player.Character
    end
    return player.CharacterAdded:Wait()
end

-- Wait for essential services to load
local function waitForServices()
    -- Wait for ReplicatedStorage modules to load
    local modules = ReplicatedStorage:WaitForChild("Modules", 10)
    if not modules then
        warn("AutoSell: Failed to find Modules folder")
        return false
    end
    
    -- Wait for essential modules
    local dataService = modules:WaitForChild("DataService", 5)
    local inventoryService = modules:WaitForChild("InventoryService", 5)
    local notification = modules:WaitForChild("Notification", 5)
    
    if not (dataService and inventoryService and notification) then
        warn("AutoSell: Failed to load essential modules")
        return false
    end
    
    return true
end

-- Main initialization
local function initialize()
    -- Wait for character
    waitF<PERSON><PERSON><PERSON><PERSON>()
    
    -- Wait for services
    if not waitForServices() then
        return
    end
    
    -- Small delay to ensure everything is loaded
    task.wait(2)
    
    -- Load the AutoSellController
    local success, autoSellController = pcall(function()
        return require(ReplicatedStorage:WaitForChild("AutoSellController"))
    end)
    
    if success and autoSellController then
        print("AutoSell: Successfully loaded AutoSellController")
        
        -- The controller initializes itself, but we can add additional setup here if needed
        
        -- Optional: Add keybind to toggle the UI
        local UserInputService = game:GetService("UserInputService")
        
        UserInputService.InputBegan:Connect(function(input, gameProcessed)
            if gameProcessed then return end
            
            -- Toggle UI with F4 key
            if input.KeyCode == Enum.KeyCode.F4 then
                local ui = player.PlayerGui:FindFirstChild("AutoSellUI")
                if ui then
                    local mainFrame = ui:FindFirstChild("MainFrame")
                    if mainFrame then
                        mainFrame.Visible = not mainFrame.Visible
                    end
                end
            end
        end)
        
    else
        warn("AutoSell: Failed to load AutoSellController:", autoSellController)
    end
end

-- Start initialization
spawn(initialize)
