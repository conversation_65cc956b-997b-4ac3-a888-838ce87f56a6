-- Full Path: -Grow-a-Garden-\\Queue-ModuleScript.lua
local u1 = {}
u1.__index = u1
function u1.new() --[[Anonymous function at line 14]]
    --[[
    Upvalues:
        [1] = u1
    --]]
    local v2 = u1
    return setmetatable({
        ["_first"] = 0,
        ["_last"] = -1,
        ["_queue"] = {}
    }, v2)
end
function u1.isEmpty(p3) --[[Anonymous function at line 25]]
    return p3._first > p3._last
end
function u1.enqueue(p4, p5) --[[Anonymous function at line 30]]
    local v6 = p4._last + 1
    p4._last = v6
    p4._queue[v6] = p5
end
function u1.dequeue(p7) --[[Anonymous function at line 37]]
    if p7:isEmpty() then
        error("Cannot dequeue from empty queue")
    end
    local v8 = p7._first
    local v9 = p7._queue[v8]
    p7._queue[v8] = nil
    p7._first = v8 + 1
    return v9
end
return u1