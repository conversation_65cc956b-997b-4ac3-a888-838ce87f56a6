-- Full Path: -Grow-a-Garden-\\PetDynamicHeightMap-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
local v2 = {}
local v3 = require(v1.Modules.SpatialHashmap)
local v4 = require(v1.Modules.CreateTagHandler)
local u5 = require(v1.Modules.TwoPointCast)
local v6 = require(v1.Data.PetRegistry).PetConfig.HEIGHT_MAP_CONFIG
local u7 = v6.TOP_OFFSET
local u8 = v6.BOTTOM_OFFSET
local v9 = v6.MAP_QUALITY
local u10 = v6.CHANGE_QUEUE_TIME
local u11 = v6.RESET_AMOUNT_PER_QUEUE
local u12 = v3.new({
    ["Scale"] = v9,
    ["Mode"] = "Vector2"
})
local v13 = workspace:WaitForChild("Farm")
local u14 = RaycastParams.new()
u14.FilterType = Enum.RaycastFilterType.Exclude
v4({
    ["Tag"] = "PetTargetable",
    ["OnInstanceAdded"] = function(p15) --[[Function name: OnInstanceAdded, line 31]]
        --[[
        Upvalues:
            [1] = u14
        --]]
        u14:AddToFilter(p15)
    end
})
v4({
    ["Tag"] = "PlayerCharacters",
    ["OnInstanceAdded"] = function(p16) --[[Function name: OnInstanceAdded, line 38]]
        --[[
        Upvalues:
            [1] = u14
        --]]
        u14:AddToFilter(p16)
    end
})
v4({
    ["Tag"] = "PetModel",
    ["OnInstanceAdded"] = function(p17) --[[Function name: OnInstanceAdded, line 45]]
        --[[
        Upvalues:
            [1] = u14
        --]]
        u14:AddToFilter(p17)
    end
})
v4({
    ["Tag"] = "TooBigPlantModel",
    ["OnInstanceAdded"] = function(p18) --[[Function name: OnInstanceAdded, line 52]]
        --[[
        Upvalues:
            [1] = u14
        --]]
        u14:AddToFilter(p18)
    end
})
function v2.GetYForPosition(_, p19) --[[Anonymous function at line 57]]
    --[[
    Upvalues:
        [1] = u12
        [2] = u7
        [3] = u8
        [4] = u5
        [5] = u14
    --]]
    local v20 = u12:GetOne({
        ["Position"] = p19,
        ["Range"] = 1
    })
    local v21 = p19.X
    local v22 = p19.Y
    local v23 = Vector3.new(v21, 0, v22)
    if not v20 then
        local v24 = u5(v23 + u7, v23 + u8, u14)
        if v24 then
            v24 = v24.Position.Y
        end
        if v24 then
            u12:Insert({
                ["Hashed"] = v24,
                ["Position"] = p19
            })
        end
    end
    return v20
end
local u25 = {}
local function u30(u26) --[[Anonymous function at line 108]]
    --[[
    Upvalues:
        [1] = u25
    --]]
    if u26:IsA("Model") then
        local v27 = u26:GetExtentsSize()
        local v28 = v27.X
        local v29 = v27.Z
        if math.max(v28, v29) > 20 then
            u26:AddTag("TooBigPlantModel")
        else
            u26:WaitForChild("Grow"):WaitForChild("Age"):GetPropertyChangedSignal("Value"):Connect(function() --[[Anonymous function at line 120]]
                --[[
                Upvalues:
                    [1] = u25
                    [2] = u26
                --]]
                u25[u26] = true
            end)
        end
    else
        return warn("a non valid model cant be setup", u26)
    end
end
local function u38(p31) --[[Anonymous function at line 125]]
    --[[
    Upvalues:
        [1] = u30
        [2] = u25
    --]]
    local v32 = p31:WaitForChild("Important")
    if v32 then
        local v33 = v32:WaitForChild("Plants_Physical")
        local v34 = v32:WaitForChild("Objects_Physical")
        for _, u35 in v33:GetChildren() do
            task.spawn(function() --[[Anonymous function at line 131]]
                --[[
                Upvalues:
                    [1] = u30
                    [2] = u35
                --]]
                u30(u35)
            end)
        end
        v33.ChildAdded:Connect(u30)
        v33.ChildRemoved:Connect(function(p36) --[[Anonymous function at line 137]]
            --[[
            Upvalues:
                [1] = u25
            --]]
            if not p36:HasTag("TooBigPlantModel") then
                u25[p36] = true
            end
        end)
        v34.ChildRemoved:Connect(function(p37) --[[Anonymous function at line 142]]
            --[[
            Upvalues:
                [1] = u25
            --]]
            if not p37:HasTag("TooBigPlantModel") then
                u25[p37] = true
            end
        end)
    end
end
local function u47(p39) --[[Anonymous function at line 96]]
    --[[
    Upvalues:
        [1] = u12
    --]]
    if not p39:HasTag("TooBigPlantModel") then
        if not p39:IsA("Model") then
            return warn("a non valid model that just cleaned up", p39)
        end
        local v40 = p39:GetPivot().Position
        Vector2.new(v40.X, v40.Z)
        local v41 = u12
        local v42 = {}
        local v43 = p39:GetPivot().Position
        v42.Position = Vector2.new(v43.X, v43.Z)
        local v44 = p39:GetExtentsSize()
        local v45 = v44.X
        local v46 = v44.Z
        v42.Range = math.max(v45, v46) * 1.5
        v41:ClearNeighbors(v42)
    end
end
for _, u48 in v13:GetChildren() do
    task.spawn(function() --[[Anonymous function at line 149]]
        --[[
        Upvalues:
            [1] = u38
            [2] = u48
        --]]
        u38(u48)
    end)
end
task.spawn(function() --[[Anonymous function at line 154]]
    --[[
    Upvalues:
        [1] = u25
        [2] = u47
        [3] = u11
        [4] = u10
    --]]
    while true do
        local v49 = 0
        for v50 in u25 do
            v49 = v49 + 1
            u47(v50)
            u25[v50] = nil
            if u11 <= v49 then
                goto l3
            end
        end
        ::l3::
        task.wait(u10)
    end
end)
return v2