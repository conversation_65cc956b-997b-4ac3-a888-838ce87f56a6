-- Full Path: -Grow-a-Garden-\\SkyboxManager-ModuleScript.lua
local v1 = {}
local u2 = require(game.ReplicatedStorage.Modules.WeightedTable).new()
function v1.AddSkybox(p3, p4) --[[Anonymous function at line 7]]
    --[[
    Upvalues:
        [1] = u2
    --]]
    u2:Add(p3, p4 or 0)
end
function v1.UpdateSkybox(p5, p6) --[[Anonymous function at line 12]]
    --[[
    Upvalues:
        [1] = u2
    --]]
    u2:SetWeight(p5, p6)
end
local u7 = nil
local function v9() --[[Anonymous function at line 18]]
    --[[
    Upvalues:
        [1] = u2
        [2] = u7
    --]]
    local v8, _ = u2:GetHighestObject()
    if u7 then
        u7.Parent = script
    end
    u7 = v8
    u7.Parent = game.Lighting
end
v1.AddSkybox(game.Lighting.Cartoon, 1)
u2.OnUpdate:Connect(v9)
local v10, _ = u2:GetHighestObject()
if u7 then
    u7.Parent = script
end
u7 = v10
u7.Parent = game.Lighting
return v1