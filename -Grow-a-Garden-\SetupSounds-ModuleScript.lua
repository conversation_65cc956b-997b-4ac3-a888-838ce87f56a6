-- Full Path: -Grow-a-Garden-\\SetupSounds-ModuleScript.lua
local v1 = game:GetService("ReplicatedStorage")
local u2 = require(v1.Modules.PlayHoverSound)
local u3 = require(v1.Modules.PlayClickSound)
return function(p4) --[[Function name: SetupSounds, line 6]]
    --[[
    Upvalues:
        [1] = u2
        [2] = u3
    --]]
    local v5 = p4:WaitFor<PERSON>hild("SENSOR")
    v5.MouseEnter:Connect(function() --[[Anonymous function at line 9]]
        --[[
        Upvalues:
            [1] = u2
        --]]
        u2()
    end)
    v5.MouseButton1Click:Connect(function() --[[Anonymous function at line 13]]
        --[[
        Upvalues:
            [1] = u3
        --]]
        u3()
    end)
    return v5
end